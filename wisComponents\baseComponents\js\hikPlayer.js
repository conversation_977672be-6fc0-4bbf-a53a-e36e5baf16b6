// eslint-disable-next-line no-unused-vars
var WebVideoPlayer = (function () {
  var videoElement = null
  var newObject_this = null
  var timer = null
  var heartbeat = null
  var streamInfo = {
    // signalURL: '',
    // mgcID: '',
    // selfRtcClientID: '',
    // dstName: '',
    // toPeerId: ''
  }
  var cseq = 1
  var RPC = {}
  var ws = {}
  var optional = {
    optional: [{
      DtlsSrtpKeyAgreement: true
    }]
  }
  var iceServers = {
    iceServers: []
  }
  var offerOptions = {
    offerToReceiveAudio: 1,
    offerToReceiveVideo: 1
  }
  var rtpTransceiverVideo = null
  var rtpTransceiverAudio = null

  function signIn() {
    videoElement = document.getElementById(newObject_this.szId)
    if (!streamInfo[newObject_this.szId].signalURL || !streamInfo[newObject_this.szId].selfRtcClientID) {
      return '"signalURL" and "selfRtcClientID" is required.'
    }

    // create websocket connect
    var key = newObject_this.szId

    // ws = Object.assign(ws, { key: null})
    ws[newObject_this.szId] = new WebSocket(streamInfo[newObject_this.szId].signalURL)
    ws[newObject_this.szId].addEventListener('open', _onOpen.bind(this))
    ws[newObject_this.szId].addEventListener('message', _onMessage.bind(this))
    ws[newObject_this.szId].addEventListener('close', _onClose.bind(this))
    ws[newObject_this.szId].addEventListener('error', _onError.bind(this))

    // init heartbeat
    heartbeat = {
      timer: null,
      iTime: 30000, // ms
      reset() {
        if (timer) {
          clearInterval(timer)
          timer = null
        }
        return this
      },
      start() {
        timer = setInterval(() => {
          let msg = {
            type: 'heartbeat',
            selfRtcClientId: streamInfo[newObject_this.szId].selfRtcClientID,
            cseq: cseq++
          }
          _send(msg)
        }, this.iTime)
      }
    }
  }
  var _init = function (that) {
    let url = that.url
    let data = dealUrl(url)
    streamInfo[newObject_this.szId] = {
      signalURL: '',
      mgcID: '',
      selfRtcClientID: '',
      dstName: '',
      toPeerId: ''
    }
    streamInfo[newObject_this.szId] = Object.assign(streamInfo[newObject_this.szId], data)
    streamInfo[newObject_this.szId].selfRtcClientID = getUUID()
    _destroy()
    signIn()
  }
  var _destroy = function () {
    if (ws[newObject_this.szId]) {
      ws[newObject_this.szId].close()
      ws[newObject_this.szId] = null
    }
    if (RPC[newObject_this.szId]) {
      RPC[newObject_this.szId].close()
      RPC[newObject_this.szId] = null
    }
  }
  function _signOut(id) {
    let msg = {
      type: 'signOut',
      selfRtcClientId: streamInfo[id].selfRtcClientID,
      cseq: cseq++
    }
    _send(msg, id)
    heartbeat.reset()
  }

  function preCall() {
    if (!streamInfo[newObject_this.szId].mgcID) {
      return '"mgcID" is required.'
    }

    let msg = {
      type: 'preCall',
      selfRtcClientId: streamInfo[newObject_this.szId].selfRtcClientID,
      cseq: cseq++,
      body: {
        peerCode: streamInfo[newObject_this.szId].mgcID
      }
    }
    _send(msg)
  }

  async function call() {
    _createRPC()
    // addTransceiver
    rtpTransceiverVideo = RPC[newObject_this.szId].addTransceiver("video")
    rtpTransceiverAudio = RPC[newObject_this.szId].addTransceiver("audio")
    // create offer
    let offer
    try {
      offer = await RPC[newObject_this.szId].createOffer(offerOptions)
      await RPC[newObject_this.szId].setLocalDescription(offer)
      requestStream()
    } catch (e) {
      _handleRPCError(e)
    }

    let msg = {
      type: offer.type,
      selfRtcClientId: streamInfo[newObject_this.szId].selfRtcClientID,
      toPeerId: streamInfo[newObject_this.szId].toPeerId,
      sdp: offer.sdp
    }
    _send(msg)
  }

  function requestStream(dstName) {
    if (!streamInfo[newObject_this.szId].dstName) {
      return '"dstName" is required.'
    }

    let msg = {
      type: 'requestStream',
      selfRtcClientId: streamInfo[newObject_this.szId].selfRtcClientID,
      toPeerId: streamInfo[newObject_this.szId].toPeerId,
      body: {
        userName: streamInfo[newObject_this.szId].dstName,
        video: true,
        audio: true
      }
    }
    _send(msg)
  }

  function _hangup(id) {
    let msg = {
      type: 'hangup',
      selfRtcClientId: streamInfo[id].selfRtcClientID,
      toPeerId: streamInfo[id].toPeerId,
    }
    _send(msg, id)
    // destroy RPC
    console.log('2', RPC, id)
    if (RPC[id]) {
      RPC[id].close()
      RPC[id] = null
    }

    // ??? 原代码中没有找到localStream赋值
    // if (this.localStream) {
    //   this.localStream.getTracks().forEach(track => {
    //     track.stop()
    //   })
    // }
  }
  // websocket events
  var _onOpen = function (e) {
    let msg = {
      type: 'signIn',
      selfRtcClientId: streamInfo[newObject_this.szId].selfRtcClientID,
      rtcClientType: 4,
      cseq: cseq++,
      body: {
        userName: '',
        token: ''
      }
    }
    _send(msg)
    heartbeat.reset().start()
  }

  var _onMessage = function (message) {
    let dataJson = JSON.parse(message.data),
      { body = null, type = '' } = dataJson
    // newObject_this.callback(dataJson)
    if (type) {
      switch (type) {
        case 'offer':
          _onReceiveOffer(dataJson)
          break
        case 'answer':
          _onReceiveAnswer(dataJson)
          break
        case 'candidate':
          _onReceiveCandidate(dataJson)
          break
        case 'hangup':
          break
      }
    } else if (body && body.type) {
      switch (body.type) {
        case 'signIn':
          let { username, password, turnServer, stunServer } = body.coturn
          iceServers.iceServers = [{
            urls: [turnServer],
            username,
            credential: password
          },
          {
            urls: stunServer
          }
          ]
          console.log('signin=================')
          preCall()
          break
        case 'preCall':
          streamInfo[newObject_this.szId].toPeerId = body.toPeerId
          console.log('precall=================')
          call()
          break
        case 'signOnt':
          break
        case 'heartbeat':
          break
      }
    }
  }

  var _onClose = function (e) {
    console.log('close：websocket连接关闭')
    // newObject_this.callback({
    //   funcName: 'connectClose',
    //   msg: '连接断开！'
    // })
  }

  var _onError = function (e) {
    console.log('error：websocket连接失败')
    // newObject_this.callback({
    //   funcName: 'connectClose',
    //   msg: '连接关闭！'
    // })
    return 'websocket connect error'
  }

  var _send = function (msg, id) {
    if (id) {
      ws[id].send(JSON.stringify(msg))
    } else {
      ws[newObject_this.szId].send(JSON.stringify(msg))
    }
  }
  // RPC
  async function _onReceiveOffer(dataOffer) {
    _createRPC()
    try {
      await RPC[newObject_this.szId].setRemoteDescription(new RTCSessionDescription(dataOffer))
      let answer = await RPC.createAnswer()
      await RPC[newObject_this.szId].setLocalDescription(answer)
    } catch (e) {
      _handleRPCError(e)
    }

    let msg = {
      type: answer.type,
      selfRtcClientId: streamInfo[newObject_this.szId].selfRtcClientID,
      toPeerId: streamInfo[newObject_this.szId].toPeerId,
      sdp: answer.sdp
    }
    _send(msg)
  }

  async function _onReceiveAnswer(dataAnswer) {
    try {
      await RPC[newObject_this.szId].setRemoteDescription(new RTCSessionDescription(dataAnswer))
    } catch (e) {
      _handleRPCError(e)
    }
  }

  async function _onReceiveCandidate(dataCondidate) {
    try {
      await RPC[newObject_this.szId].addIceCandidate(new RTCIceCandidate(dataCondidate.candidate))
    } catch (e) {
      _handleRPCError(e)
    }
  }

  var _createRPC = function () {
    // RPC = Object.assign(RPC, {newObject_this.szId: null})
    RPC[newObject_this.szId] = new RTCPeerConnection(iceServers, optional);
    RPC[newObject_this.szId].addEventListener('icecandidate', _onIceCandidate.bind(this));
    RPC[newObject_this.szId].addEventListener('iceconnectionstatechange', _onIceStateChange.bind(this));
    RPC[newObject_this.szId].addEventListener('track', _onGotRemoteStream.bind(this));
  }

  function _onIceCandidate(e) {
    if (!e.candidate) {
      return console.log('candidate in null')
    }
    let msg = {
      type: 'candidate',
      selfRtcClientId: streamInfo[newObject_this.szId].selfRtcClientID,
      toPeerId: streamInfo[newObject_this.szId].toPeerId,
      candidate: e.candidate
    }
    _send(msg)
  }

  function _onIceStateChange() {
    console.log('ice connection state change')
    // to do
  }

  function _onGotRemoteStream(e) {
    console.log('videoElement', videoElement)
    if (videoElement.srcObject !== e.streams[0]) {
      videoElement.srcObject = e.streams[0]
    }
  }

  function _handleRPCError(e) {
    console.error('RPC error: ', e)
    // to do
  }
  var VideoPlayerFun = function () {
    this.szId = ''
    this.url = ''
    this.callback = null
  }
  VideoPlayerFun.prototype.create = function (config) {
    // eslint-disable-next-line camelcase
    newObject_this = this
    // 将config中传的参数赋值给newObject_this
    for (var i in config) {
      this[i] = config[i]
    }
    console.log('22')
    return this
  }
  VideoPlayerFun.prototype.play = function () {
    _init(this)
  }
  VideoPlayerFun.prototype.signOut = function (id) {
    _signOut(id)
  }
  VideoPlayerFun.prototype.hangup = function (id) {
    _hangup(id)
  }
  VideoPlayerFun.prototype.destroy = function (id) {
    _destroy(id)
  }
  // 返回构造函数
  return VideoPlayerFun
})()

function getUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  })
}
function dealUrl(url) {
  let index = url.indexOf('?')
  let signalURL = url.slice(0, index).replace(/webrtc/, 'wss')
  let query = url.slice(index + 1).split('&url=')
  let mgcID = query[0].split('=')[1]
  let base64 = new Base64Model()
  let dstName = base64.decode(query[1])
  return { signalURL, mgcID, dstName }
}

function Base64Model() {
  // private property  
  _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

  // public method for encoding  
  this.encode = function (input) {
    var output = "";
    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
    var i = 0;
    input = _utf8_encode(input);
    while (i < input.length) {
      chr1 = input.charCodeAt(i++);
      chr2 = input.charCodeAt(i++);
      chr3 = input.charCodeAt(i++);
      enc1 = chr1 >> 2;
      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
      enc4 = chr3 & 63;
      if (isNaN(chr2)) {
        enc3 = enc4 = 64;
      } else if (isNaN(chr3)) {
        enc4 = 64;
      }
      output = output +
        _keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
        _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
    }
    return output;
  }

  // public method for decoding  
  this.decode = function (input) {
    var output = "";
    var chr1, chr2, chr3;
    var enc1, enc2, enc3, enc4;
    var i = 0;
    input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
    while (i < input.length) {
      enc1 = _keyStr.indexOf(input.charAt(i++));
      enc2 = _keyStr.indexOf(input.charAt(i++));
      enc3 = _keyStr.indexOf(input.charAt(i++));
      enc4 = _keyStr.indexOf(input.charAt(i++));
      chr1 = (enc1 << 2) | (enc2 >> 4);
      chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
      chr3 = ((enc3 & 3) << 6) | enc4;
      output = output + String.fromCharCode(chr1);
      if (enc3 != 64) {
        output = output + String.fromCharCode(chr2);
      }
      if (enc4 != 64) {
        output = output + String.fromCharCode(chr3);
      }
    }
    output = _utf8_decode(output);
    return output;
  }

  // private method for UTF-8 encoding  
  _utf8_encode = function (string) {
    string = string.replace(/\r\n/g, "\n");
    var utftext = "";
    for (var n = 0; n < string.length; n++) {
      var c = string.charCodeAt(n);
      if (c < 128) {
        utftext += String.fromCharCode(c);
      } else if ((c > 127) && (c < 2048)) {
        utftext += String.fromCharCode((c >> 6) | 192);
        utftext += String.fromCharCode((c & 63) | 128);
      } else {
        utftext += String.fromCharCode((c >> 12) | 224);
        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
        utftext += String.fromCharCode((c & 63) | 128);
      }

    }
    return utftext;
  }

  // private method for UTF-8 decoding  
  _utf8_decode = function (utftext) {
    var string = "";
    var i = 0;
    var c = c1 = c2 = 0;
    while (i < utftext.length) {
      c = utftext.charCodeAt(i);
      if (c < 128) {
        string += String.fromCharCode(c);
        i++;
      } else if ((c > 191) && (c < 224)) {
        c2 = utftext.charCodeAt(i + 1);
        string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
        i += 2;
      } else {
        c2 = utftext.charCodeAt(i + 1);
        c3 = utftext.charCodeAt(i + 2);
        string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
        i += 3;
      }
    }
    return string;
  }
}