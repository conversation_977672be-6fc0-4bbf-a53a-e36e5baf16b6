/**
 * @description 通用图表类
 * <AUTHOR>
 * @date 2020-01-17
 * @class Graph
 * @version 1.0
 */
class Graph {
  constructor(name, container, opts) {
    this._name = name;
    this._container = container;
    this._setupDefaultValues();
    this._initProperty();
    this.property = $.extend(true, this.property, opts);
    this._draw();
  }
  /**
   * 生成默认数据
   */
  _setupDefaultValues() {
    this.graph = null;
    this.markPointMap = {};
    this.markLineMap = {};
    this.keys = [];
  }
  /**
   * @description 初始化组件配置项
   */
  _initProperty() {
    this.property = {
      fontScale: 1,
      type: 'line',
      frame: [0, 0, 1920, 1080],
      padding: [30, 30, 30, 30],
      offset: [0, 0],
      xRange: null,
      yRange: null,
      xRangeName: '',
      yRangeName: '',
      xRangeType: '',
      yRangeType: '',
      isUpdateAxisX: true,
      isUpdateAxisY: true,
      data: [],
      markPoint: {},
      markLine: {},
      axisList: [],
      needFillZero: true,
      showZero: true
    };

    this._optionDic = [
      {
        name: 'type',
        displayName: '图形类型',
        description: '图表图形表现类型',
        type: OptionType.enum,
        options: [
          {
            name: '曲线',
            value: 'line',
          },
          {
            name: '柱',
            value: 'bar',
          },
          {
            name: '面积',
            value: 'area',
          },
          {
            name: '河流图',
            value: 'river',
          },
        ],
        show: true,
        editable: true,
      },
      {
        name: 'offset',
        displayName: '偏移',
        description: '图表偏移(x,y)',
        type: OptionType.doubleArray,
        placeholder: ['x', 'y'],
        show: true,
        editable: true,
      },
      {
        name: 'xRangeName',
        displayName: 'x轴名称',
        description: '定义的作为x轴的坐标轴的名称',
        type: OptionType.enum,
        options: this.property.axisList,
        show: true,
        editable: true,
      },
      {
        name: 'yRangeName',
        displayName: 'y轴名称',
        description: '定义的作为y轴的坐标轴的名称',
        type: OptionType.enum,
        options: this.property.axisList,
        show: true,
        editable: true,
      },
      {
        name: 'isUpdateAxisX',
        displayName: '是否更新x轴',
        description: 'x轴是否跟随数据更新',
        type: OptionType.boolean,
        show: true,
        editable: true,
      },
      {
        name: 'isUpdateAxisY',
        displayName: '是否更新y轴',
        description: 'y轴是否跟随数据更新',
        type: OptionType.boolean,
        show: true,
        editable: true,
      },
      {
        name: 'markPoint',
        displayName: '标记点组',
        action: [
          {
            text: '新增',
            style: 'success',
            action: 'addMarkPoint',
            param: [this._name],
          },
        ],
        children: [],
        show: true,
        editable: true,
      },
      {
        name: 'markLine',
        displayName: '标记线',
        action: [
          {
            text: '新增',
            style: 'success',
            action: 'addMarkLine',
            param: [this._name],
          },
        ],
        children: [],
        show: true,
        editable: true,
      },
      {
        name: 'needFillZero',
        displayName: '数据补0',
        description: '空数据是否需要补0',
        type: OptionType.boolean,
        show: true,
        editable: true,
      },
      {
        name: 'showZero',
        displayName: '显示0数字',
        description: '数据为0时或者数据补0后是否显示数字',
        type: OptionType.boolean,
        show: true,
        editable: true,
      },
    ];
  }

  /**
   * @description 绘制组件
   */
  _draw() {
    this._container.style('transform', `translate(${this.property.padding[2] + this.property.offset[0]}px,${this.property.padding[0] + this.property.offset[1]}px)`);
    this._graphContainer = this._container.append('g').attr('class', 'graph');
    this._markPointContainer = this._container.append('g').attr('class', 'markPoints');
    this._markLineContainer = this._container.append('g').attr('class', 'markLines');
    switch (this.property.type) {
      case 'line':
        //line
        this.graph = new Line(this._graphContainer, this.property);
        break;
      case 'bar':
        //bar
        this.graph = new Bar(this._graphContainer, this.property);
        break;
      case 'area':
        //area
        this.graph = new Area(this._graphContainer, this.property);
        break;
      case 'river':
        //area
        this.graph = new River(this._graphContainer, this.property);
        break;
    }
    this.property = $.extend(true, this.property, this.graph.property);
    this._optionDic = this._optionDic.concat(this.graph._optionDic);
    //生成标记点
    for (const markName in this.property.markPoint) {
      if (this.property.markPoint.hasOwnProperty(markName)) {
        this._generateMarkPoint(markName, this.property.markPoint[markName]);
      }
    }
    //生成标记线
    for (const markName in this.property.markLine) {
      if (this.property.markLine.hasOwnProperty(markName)) {
        this._generateMarkLine(markName, this.property.markLine[markName]);
      }
    }
    this.keys = this.graph.keys;
  }
  /**
   * @description 更新数据
   */
  update(data = []) {
    //如果数据不需要补0则删除数据中所有value为null的数据
    if (!this.property.needFillZero) {
      data = data.filter((d) => {
        let flag = true;
        for (let key in d) {
          if (key !== 'x' && (d[key] === null || d[key] === '' || d[key] === undefined)) {
            flag = false;
            break;
          }
        }
        return flag;
      });
    }

    if (data.length === 0) {
      data = this.property.data;
    } else {
      this.property.data = data;
    }

    $(this.graph._container.node()).empty();

    this.graph.setOption(this.property);
    this.property.data = data;
    try {
      this.graph.update(data);
    } catch (error) {

    }
    for (const markPointName in this.markPointMap) {
      if (this.markPointMap.hasOwnProperty(markPointName)) {
        let opts = this.property.markPoint[markPointName];
        opts.xRange = this.property.xRange;
        opts.yRange = this.property.yRange;
        opts.data = this.property.data;
        this.markPointMap[markPointName].setOption(opts);
        this.markPointMap[markPointName].update();
      }
    }
  }

  /**
   * 初始化标记点
   * @param name
   * @param opts
   * @private
   */
  _generateMarkPoint(name, opts) {
    opts.xRange = this.property.xRange;
    opts.yRange = this.property.yRange;
    opts.data = this.property.data;
    let markPoint = new MarkPoint(this._markPointContainer, opts);
    this.property.markPoint[name] = markPoint.property;
    let optionDic = WisCompUtil.findPropertyDictionary(`markPoint.${name}`, this._optionDic);
    if (optionDic === undefined) {
      WisCompUtil.findPropertyDictionary('markPoint', this._optionDic).children.push({
        name: name,
        displayName: name,
        action: [
          {
            text: '删除',
            style: 'warning',
            action: 'deleteMarkPoint',
            param: [this._name],
          },
        ],
        children: markPoint._optionDic,
      });
    } else {
      optionDic = {
        name: name,
        displayName: name,
        action: [
          {
            text: '删除',
            style: 'warning',
            action: 'deleteMarkPoint',
            param: [this._name],
          },
        ],
        children: markPoint._optionDic,
      };
    }
    this.markPointMap[name] = markPoint;
  }

  /**
   * @description 新增MarkPoint标记点（配置工具调用）
   */
  addMarkPoint() {
    let markPointOptionDic = WisCompUtil.findPropertyDictionary('markPoint', this._optionDic);
    let lastIndex = 1;
    if (markPointOptionDic.children.length > 0) {
      lastIndex = d3.max(markPointOptionDic.children.map((d) => parseInt(d.name.split('_')[1]))) + 1;
    }
    let markPointName = `markPoint_${lastIndex}`;
    let markPointContainer = this._container.append('g').attr('class', markPointName);
    let markPoint = new MarkPoint(markPointContainer, this.property.markPoint[markPointName]);
    this.property.markPoint[markPointName] = markPoint.property;
    markPointOptionDic.children.push({
      name: markPointName,
      displayName: markPointName,
      action: [
        {
          text: '删除',
          style: 'warning',
          action: 'deleteMarkPoint',
          param: [this._name],
        },
      ],
      children: markPoint._optionDic,
    });
  }

  /**
   * @description 删除MarkPoint标记点（配置工具调用）
   * @param index {number} 标记点index
   */
  deleteMarkPoint(index) {
    let markPointOptionDic = WisCompUtil.findPropertyDictionary('markPoint', this._optionDic);
    let markPointName = markPointOptionDic.children[index].name;
    markPointOptionDic.children.splice(index, 1);
    delete this.property.markPoint[markPointName];
  }

  /**
   * @description 生成标记线
   * @param name {string} 标记线名称
   * @param opts 标记线属性
   * @private
   */
  _generateMarkLine(name, opts) {
    opts.xRange = this.property.xRange;
    opts.yRange = this.property.yRange;
    opts.data = this.property.data;
    let markLine = new MarkLine(this._markLineContainer, opts);
    this.property.markLine[name] = markLine.property;
    let optionDic = WisCompUtil.findPropertyDictionary(`markLine.${name}`, this._optionDic);
    if (optionDic === undefined) {
      WisCompUtil.findPropertyDictionary('markLine', this._optionDic).children.push({
        name: name,
        displayName: name,
        action: [
          {
            text: '删除',
            style: 'warning',
            action: 'deleteMarkLine',
            param: [this._name],
          },
        ],
        children: markLine._optionDic,
      });
    } else {
      optionDic = {
        name: name,
        displayName: name,
        action: [
          {
            text: '删除',
            style: 'warning',
            action: 'deleteMarkLine',
            param: [this._name],
          },
        ],
        children: markLine._optionDic,
      };
    }
    this.markLineMap[name] = markLine;
  }

  /**
   * @description 新增标记线(配置工具调用)
   */
  addMarkLine() {
    let markLineOptionDic = WisCompUtil.findPropertyDictionary('markLine', this._optionDic);
    let lastIndex = 1;
    if (markLineOptionDic.children.length > 0) {
      lastIndex = d3.max(markLineOptionDic.children.map((d) => parseInt(d.name.split('_')[1]))) + 1;
    }
    let markLineName = `markLine_${lastIndex}`;
    let markLineContainer = this._container.append('g').attr('class', markLineName);
    let markLine = new MarkLine(markLineContainer, this.property.markLine[markLineName]);
    this.property.markLine[markLineName] = markLine.property;
    markLineOptionDic.children.push({
      name: markLineName,
      displayName: markLineName,
      action: [
        {
          text: '删除',
          style: 'warning',
          action: 'deleteMarkLine',
          param: [this._name],
        },
      ],
      children: markLine._optionDic,
    });
  }

  /**
   * @description 删除MarkLine标记线（配置工具调用）
   * @param index {number} 标记线index
   */
  deleteMarkLine(index) {
    let markLineOptionDic = WisCompUtil.findPropertyDictionary('markLine', this._optionDic);
    let markLineName = markLineOptionDic.children[index].name;
    markLineOptionDic.children.splice(index, 1);
    delete this.property.markLine[markLineName];
  }

  /**
   * 新增坐标轴
   * @param axisName
   */
  addAxisList(axisName) {
    this.property.axisList.push({
      name: axisName,
      value: axisName,
    });
    _.uniq(this.property.axisList);
  }
  /**
   * 删除坐标轴
   * @param axisName
   */
  deleteAxisList(axisName) {
    _.remove(this.property.axisList, (d) => d.name === axisName);
  }
}
