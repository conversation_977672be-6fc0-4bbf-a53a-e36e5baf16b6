<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 101 101" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1,0,0,1,-38893,-3823)">
        <g id="水电" transform="matrix(0.804574,0,0,0.434783,34202.4,2000.86)">
            <g transform="matrix(-0.225981,-5.12125e-17,2.76747e-17,-0.418182,8429.66,4456.05)">
                <g>
                    <g id="背景" transform="matrix(-2.26939,2.77921e-16,-2.79749e-16,-2.28432,86556.4,10530.6)">
                        <path d="M33314.3,4358.05C33314.3,4345.71 33304.2,4335.69 33291.8,4335.69L33097.7,4335.69C33085.3,4335.69 33075.2,4345.71 33075.2,4358.05L33075.2,4550.85C33075.2,4563.18 33085.3,4573.19 33097.7,4573.19L33291.8,4573.19C33304.2,4573.19 33314.3,4563.18 33314.3,4550.85L33314.3,4358.05Z" style="fill:rgb(34,32,32);"/>
                    </g>
                    <path d="M10949.4,578.483C10949.4,607.05 10972.6,630.242 11001.1,630.242L11447.6,630.242C11476.2,630.242 11499.4,607.05 11499.4,578.483L11499.4,132.001C11499.4,103.434 11476.2,80.242 11447.6,80.242L11001.1,80.242C10972.6,80.242 10949.4,103.434 10949.4,132.001L10949.4,578.483ZM10967.7,570.816C10967.7,593.478 10986.1,611.878 11008.8,611.878L11439.9,611.878C11462.6,611.878 11481,593.478 11481,570.816L11481,139.668C11481,117.006 11462.6,98.606 11439.9,98.606L11008.8,98.606C10986.1,98.606 10967.7,117.006 10967.7,139.668L10967.7,570.816Z" style="fill:url(#_Linear1);"/>
                    <g transform="matrix(1,0,-2.46519e-32,1,0.0323855,0.594035)">
                        <g>
                            <g transform="matrix(-3.24542,3.97449e-16,-1.48168e-16,-1.20989,118802,5623.36)">
                                <path d="M33225.6,4428.83L33069.2,4428.83L33081.4,4372.83L33215.2,4372.83L33225.6,4428.83Z" style="fill:url(#_Linear2);"/>
                            </g>
                            <g transform="matrix(-5.06102,6.19796e-16,-3.89891e-16,-3.1837,180082,14637.5)">
                                <path d="M33414.6,4514.8L33314.3,4514.8L33314.3,4550.9C33314.3,4559.12 33317.8,4565.8 33322,4565.8L33406.8,4565.8C33411.1,4565.8 33414.6,4559.06 33414.6,4550.76L33414.6,4514.8Z" style="fill:url(#_Linear3);"/>
                            </g>
                            <g transform="matrix(-2.2686,2.77824e-16,-2.9285e-16,-2.3913,93717.3,10193.4)">
                                <ellipse cx="36362.9" cy="4152.16" rx="111.824" ry="0.255" style="fill:white;"/>
                            </g>
                        </g>
                        <g transform="matrix(-9.15345,1.12097e-15,-1.12097e-15,-9.15345,314625,42399.3)">
                            <g transform="matrix(14,0,0,14,33166.2,4617.42)">
                            </g>
                            <text x="33127.5px" y="4617.42px" style="font-family:'MicrosoftYaHeiUI-Bold', 'Microsoft YaHei UI', sans-serif;font-weight:700;font-size:14px;fill:rgb(29,29,29);">3<tspan x="33137.5px 33147.6px 33157.6px " y="4617.42px 4617.42px 4617.42px ">254</tspan></text>
                        </g>
                    </g>
                </g>
            </g>
            <g transform="matrix(0.717694,0,0,1.28814,5651.33,3819.25)">
                <path d="M293.217,323.195L296.294,347.807L300.602,382.275L315.325,382.275L298.548,323.172L296.625,322.555L293.217,323.195ZM337.555,382.298L322.279,328.395L320.738,327.958L318.639,328.38L323.515,367.389C320.788,361.999 320.167,358.601 319.445,352.824L316.168,326.607C317.724,326.295 319.282,325.982 320.838,325.669C321.919,325.977 323.001,326.284 324.083,326.591L324.937,329.597L337.531,332.701L338.436,339.945L337.562,339.744C336.017,336.555 334.582,334.747 334.363,334.848L333.384,334.677C338.61,343.843 339.646,348.649 341.125,357.373C342.17,363.539 344.464,375.924 348.675,379.909C350.64,381.77 353.356,382.291 358.134,382.275L344.388,333.844L342.618,333.377L341.399,333.731L346.297,372.922C343.667,367.331 342.812,363.035 342.067,357.069L338.949,332.128C340.164,331.774 341.379,331.419 342.594,331.065C343.792,331.382 344.989,331.699 346.186,332.015L347.045,335.046L357.867,337.712L358.729,344.61L358.582,344.576C357.179,341.612 355.91,339.943 355.709,340.041L354.93,339.907C356.656,343.092 358.097,346.308 359.252,349.656C362.151,358.06 361.829,367.014 365.056,374.672C366.91,379.069 369.918,382.296 376.452,382.275L364.147,338.926L363.076,338.638L361.44,338.927L365.719,373.154C364.065,368.4 362.983,363.354 362.448,358.407C362.083,355.048 361.352,352.011 360.501,349.416L358.962,337.099L360.726,336.779L359.2,330.1C335.418,323.847 310.823,317.737 287.042,311.48L287.042,309.752C311.291,316.133 336.354,322.367 360.603,328.742L362.374,336.488L363.173,336.353C364.097,336.603 365.022,336.851 365.946,337.099L378.775,382.298L386.903,382.843L386.903,389.883L325.756,392.101L290.562,382.497L298.38,382.497L294.079,348.078L290.743,321.393L296.765,320.262C297.966,320.647 299.167,321.032 300.368,321.417L301.016,323.703L314.814,327.102L315.767,334.733L314.937,334.543C313.192,330.915 311.442,328.831 311.178,328.939L309.981,328.729C309.981,328.729 311.848,331.247 313.792,335.42C318.185,344.849 317.486,352.37 320.069,361.806C322.364,370.201 327.468,382.298 337.555,382.298Z" style="fill:rgb(33,196,255);"/>
            </g>
        </g>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-1.00704e-13,-548.206,548.206,-1.00704e-13,11219.8,628.505)"><stop offset="0" style="stop-color:rgb(105,105,105);stop-opacity:1"/><stop offset="0.02" style="stop-color:rgb(158,158,158);stop-opacity:1"/><stop offset="0.05" style="stop-color:rgb(208,208,208);stop-opacity:1"/><stop offset="0.12" style="stop-color:rgb(229,229,229);stop-opacity:1"/><stop offset="0.2" style="stop-color:rgb(171,171,171);stop-opacity:1"/><stop offset="0.29" style="stop-color:rgb(109,109,109);stop-opacity:1"/><stop offset="0.51" style="stop-color:rgb(79,79,79);stop-opacity:1"/><stop offset="0.74" style="stop-color:rgb(105,105,105);stop-opacity:1"/><stop offset="0.82" style="stop-color:rgb(165,165,165);stop-opacity:1"/><stop offset="0.89" style="stop-color:rgb(229,229,229);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(105,105,105);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear2" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.05245e-15,-39.3447,17.1877,2.40917e-15,33148.3,4418.96)"><stop offset="0" style="stop-color:rgb(63,63,63);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(63,63,63);stop-opacity:0"/></linearGradient>
        <linearGradient id="_Linear3" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(3.18408e-15,52,-52,3.18408e-15,33371.3,4513.8)"><stop offset="0" style="stop-color:rgb(143,143,143);stop-opacity:1"/><stop offset="0.18" style="stop-color:rgb(168,168,168);stop-opacity:1"/><stop offset="0.39" style="stop-color:rgb(175,175,175);stop-opacity:1"/><stop offset="0.72" style="stop-color:rgb(160,160,160);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(111,111,111);stop-opacity:1"/></linearGradient>
    </defs>
</svg>
