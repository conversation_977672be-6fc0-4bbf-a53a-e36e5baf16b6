/**
 * @description 电厂
 * <AUTHOR>
 * @date 2023-05-31
 * @class BasePowerPlant
 * @extends {SVGComponentBase}
 */
class BasePowerPlant {
  constructor(id, code, container, workMode, option) {
    this.id = id;
    // this.con = con;
    this.container = container;
    this.data = [];

    this.option = {};

    let meta = {
      lowBusDesc1: "江苏.上河/220kV.Ⅲ段母线,_220KV",
      lowBusDesc2: "江苏.上河/220kV.Ⅰ段母线,_220KV",
      lowBusDesc3: "江苏.上河/220kV.Ⅱ段母线,_220KV",
      lowBusVolType2: "_220KV",
      lowBusVolType1: "_220KV",
      switchVoltype8: "_220KV",
      lowBusVolType3: "_220KV",
      num: "11",
      type: "TransStation",
      switchRunningStatus9: "RUNNING",
      xfmrRunningStatus6: "RUNNING",
      xfmrRunningStatus7: "RUNNING",
      switchRunningStatus8: "RUNNING",
      id: 97008092,
      switchDesc8: "江苏.上河.220kVI、III段分段2500开关,_220KV",
      switchDesc9: "江苏.上河.220kVI、II段母联2630开关,_220KV",
      switchRunningStatus10: "RUNNING",
      rtKeyId3: "1301:320000000002700000",
      rtKeyId2: "1301:320000000002690000",
      rtKeyId1: "1301:320000000002710000",
      xfmrNo: "6,7",
      rtKeyId0: "112:32000000120000",
      lowBusRunningStatus1: "RUNNING",
      rtKeyId7: "1311:320000000000950000",
      highBusDesc5: "江苏.上河/500kV.Ⅱ段母线,_500KV",
      lowBusRunningStatus2: "RUNNING",
      rtKeyId6: "1311:320000000000940000",
      switchVoltype10: "_220KV",
      highBusDesc4: "江苏.上河/500kV.Ⅰ段母线,_500KV",
      lowBusRunningStatus3: "RUNNING",
      rtKeyId5: "1301:320000000001300000",
      rtKeyId4: "1301:320000000001310000",
      switch_num: "3",
      highbus_num: "2",
      layerId: "113997366104686638",
      volt: "_500KV",
      name: "江苏.上河",
      switchVoltype9: "_220KV",
      xfmrDesc7: "江苏.上河.2号主变,_500KV",
      xfmrDesc6: "江苏.上河.1号主变,_500KV",
      highBusNo: "4,5",
      highBusVoltype4: "_500KV",
      highBusRunningStatus5: "RUNNING",
      xfmrMvarate7: "750.0000",
      highBusRunningStatus4: "RUNNING",
      xfmrMvarate6: "750.0000",
      highBusVoltype5: "_500KV",
      rtKeyId10: "1321:320000000009510000",
      lowBusNo: "2,3,1",
      rtKeyId9: "1321:320000000009530000",
      rtKeyId8: "1321:320000000009520000",
      xfmr_num: "2",
      lowbus_num: "3",
      switchDesc10: "江苏.上河.220kVII、III段母联2650开关,_220KV",
      xfmrVoltype6: "_500KV",
      switchNo: "8_1_3,9_1_2,10_2_3",
      xfmrVoltype7: "_500KV",
    };

    try {
      this.option.data = JSON.parse(option.metaData);
      this.option.data.no.split(",").forEach((d) => {
        this.data.push({
          mvarate: 100,
          value: 0,
        });
      });
      this.option.size = option.size;
    } catch (error) {
      // console.log(error);
      this.option.size = option.size || 120;
      this.option.data = meta;
      let str = "1,2,3,4";
      str.split(",").forEach((d) => {
        this.data.push({
          mvarate: 100,
          value: 0,
        });
      });
    }
    this._foldPath = WisUtil.scriptPath("BasePowerPlant");
    this._draw();
  }

  _generateDefines() {
    let defs = this.con.append("defs");
    d3.text(`${this._foldPath}/defs.html`).then((data) => {
      defs.html(data);
    });
  }

  _draw() {
    // this._generateDefines();
    let imageCon = this.container
      .append("image")
      .attr("width", 246)
      .attr("height", 246)
      .style("transform-origin", "0 0")
      .style("transform", `scale(${this.option.size / 246})`);
    // .node();
    if (this.option.data.powerType !== "THERMAL") {
      this.bgSrc = `${this._foldPath}/images/powerPlant_${
        this.option.data.powerType || "WIND"
      }.svg`;
      imageCon.attr("href", this.bgSrc);
      return;
    }
    let canvas = document.createElement("canvas");
    canvas.width = 246;
    canvas.height = 246;

    let context = canvas.getContext("2d");
    let stationCon = d3.select(document.createElement("div"));
    this.con = stationCon
      .append("svg")
      .attr("width", 246)
      .attr("height", 246)
      .attr("viewBox", "0 0 246 246");
    let defs = this.con.append("defs");
    d3.text(`${this._foldPath}/defs.html`).then((data) => {
      defs.html(data);
    });
    // this.con.attr("viewBox", "0 0 246 246");
    // this._setSize(this.option.size);

    this.bgSrc = "";

    this.bgSrc = `${this._foldPath}/images/powerPlant_500KV.svg`;

    // .append("image")
    // .style("width", "246px")
    // .style("height", "246px")
    // .attr("src", `${this._foldPath}/images/powerPlant_500KV.svg`);
    if (this.option.data.powerType !== "THERMAL") {
      // return;
    }

    this.drawRect = {
      x: 15,
      y: 17,
      width: 176,
      height: 176,
    };

    this.mainG = this.con
      .append("g")
      .attr("class", "container")
      .attr("transform", `translate(${this.drawRect.x}, ${this.drawRect.y})`);
    this.barNum = Number(this.option.data.num);
    this._genRange();
    this._drawBar();
    //this._drawName();
    this._drawValue();

    const img = new Image();
    const svgData = new XMLSerializer().serializeToString(stationCon.select("svg").node());
    const replaceStr = svgData.replace(
      "http://www.w3.org/1999/xhtml",
      "http://www.w3.org/2000/svg"
    );

    const bgImg = new Image();
    bgImg.onload = () => {
      context.drawImage(bgImg, 0, 0);
      const svgURL = URL.createObjectURL(new Blob([replaceStr], { type: "image/svg+xml" }));
      // const svgURL = `data:image/svg+xml;base64,${btoa(replaceStr)}`;
      img.src = svgURL;
      img.onload = () => {
        context.drawImage(img, 0, 0);
        imageCon.attr("href", canvas.toDataURL());
        // imageCon.style("background-image", `url("${svgURL}")`).style("background-size", "100%");
      };
    };
    bgImg.src = this.bgSrc;
  }

  _setSize(l) {
    this.con
      //   .attr("x", this.option.x - (l - this.option.w) / 2)
      //   .attr("y", this.option.y - (l - this.option.h) / 2)
      .attr("width", l)
      .attr("height", l);
  }

  _genRange() {
    this.xRange = [];
    this.data.forEach((d) => {
      this.xRange.push(
        d3
          .scaleLinear()
          .domain([0, Number(d.mvarate)])
          .range([this.drawRect.x, this.drawRect.width])
      );
    });
    this.yRange = d3
      .scaleBand()
      .domain(this.data.map((d, i) => i))
      .range([this.drawRect.y, this.drawRect.y + this.drawRect.height]);
  }

  _drawBar() {
    let barG = this.mainG.append("g").attr("class", "powerplant_bar");
    barG
      .selectAll(".rect")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("rect")
            .attr("class", "rect")
            .attr("x", 0)
            .attr("y", (d, i) => this.yRange.bandwidth() * i)
            .attr("width", (d, i) => this.xRange[i](d.value))
            .attr("height", this.yRange.bandwidth())
            .style("fill", `url(#powerplant-bar${this.option.data.volt})`),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .attr("y", (d, i) => this.yRange.bandwidth() * i)
            .attr("height", this.yRange.bandwidth())
            .attr("width", (d, i) => this.xRange[i](d.value)),
        (exit) => exit.remove()
      );
  }

  _drawName() {
    let nameG = this.mainG.append("g").attr("class", "powerplant_name");
    nameG
      .selectAll(".name")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("text")
            .attr("class", "name")
            .attr("x", 20)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .style("fill", "#fff")
            .style("font-size", `${60 - this.data.length * 5}px`)
            .style("dominant-baseline", "central")
            .text((d, i) => `#${i}`),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .style("font-size", `${60 - this.data.length * 5}px`)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .attr("width", (d, i) => this.xRange[i](d.value))
            .text((d, i) => `#${i}`),
        (exit) => exit.remove()
      );
  }

  _drawValue() {
    let valueG = this.mainG.append("g").attr("class", "powerplant_value");
    valueG
      .selectAll(".value")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("text")
            .attr("class", "value")
            .attr("x", this.drawRect.width)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .style("fill", "#fff")
            .style("font-size", `${60 - this.data.length * 5}px`)
            .style("dominant-baseline", "central")
            .style("text-anchor", "end")
            .text((d) => `${Math.round(d.value)}`),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .style("font-size", `${60 - this.data.length * 5}px`)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .text((d) => `${Math.round(d.value)}`),
        (exit) => exit.remove()
      );
  }

  update(data) {
    if (data === undefined) return;
    this.data = data.machineList;
    if (this.mainG === undefined) return;
    $(this.mainG.node()).empty();
    this._genRange();
    this._drawBar();
    //this._drawName();
    this._drawValue();
  }
}
