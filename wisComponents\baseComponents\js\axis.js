/**
 * @description 坐标轴
 * <AUTHOR>
 * @date 2020-03-17
 * @class Axis
 */
class Axis {
  constructor(container, opts, obj) {
    this._initProperty();
    this._container = container;
    this._obj = obj;
    this.property = $.extend(true, this.property, opts);
    this._draw();
  }
  /**
   * @description 初始化组件配置项
   */
  _initProperty() {
    this.property = {
      //显示隐藏
      showAxis: true,
      //自动对齐0刻度
      autoTransform: false,
      //文字比例
      fontScale: 1,
      //描述
      description: '',
      //大小
      frame: [0, 0, 1920, 1080],
      //边框间距
      padding: [30, 30, 30, 30],
      //文字相对于坐标轴位置
      position: 'bottom',
      //坐标轴线宽
      axisStrokeWidth: 1,
      //坐标轴颜色
      axisColor: '#fff',
      //单位
      units: '',
      //单位文字颜色
      unitsColor: '#fff',
      //单位文字大小
      unitsFontSize: 20,
      //比例尺类型
      scaleType: 'linear',
      //坐标轴最小值
      min: 0,
      //最小值系数
      minCoefficient: 0,
      //坐标轴最大值
      max: 100,
      //最大值系数
      maxCoefficient: 0,
      //坐标轴自适应
      adaptive: false,
      //刻度阶段
      tickStep: 0,
      //是否绑定数据
      isBindData: false,
      //数据
      data: [],
      //刻度值内容
      tickValues: '',
      //刻度值个数
      ticks: 0,
      //刻度格式化
      tickFormat: '',
      //刻度长度
      tickSize: 6,
      //文字距坐标轴距离
      tickPadding: 3,
      //文字偏移
      tickTextOffset: [0, 0],
      //网格类型 none无 dash虚线 line实线
      grid: 'line',
      //网格颜色
      gridColor: 'rgba(255,255,255,0.5)',
      //文字大小
      fontSize: 12,
      //文字粗细
      fontWeight: 'bold',
      //文字颜色
      fontColor: '#fff',
      //文字旋转角度
      fontRotate: 0,
      //修改最后一个刻度值从0转为24
      change0To24: false,
    };

    this._optionDic = [
      {
        name: 'showAxis',
        displayName: '是否显示坐标轴',
        description: '是否显示坐标轴',
        type: OptionType.boolean,
        show: true,
        editable: true,
      },
      {
        name: 'autoTransform',
        displayName: '是否自动对齐0刻度',
        description: '是否自动对齐0刻度',
        type: OptionType.boolean,
        show: true,
        editable: true,
      },
      {
        name: 'description',
        displayName: '描述',
        description: '坐标轴文字描述',
        type: OptionType.string,
        show: true,
        editable: true,
      },
      {
        name: 'position',
        displayName: '坐标轴位置',
        description: '文字相对于坐标轴的位置',
        type: OptionType.enum,
        options: [
          {
            name: '上',
            value: 'top',
          },
          {
            name: '下',
            value: 'bottom',
          },
          {
            name: '左',
            value: 'left',
          },
          {
            name: '右',
            value: 'right',
          },
        ],
        show: true,
        editable: true,
      },
      {
        name: 'axisColor',
        displayName: '坐标轴颜色',
        description: '坐标轴线和刻度的颜色',
        type: OptionType.color,
        show: true,
        editable: true,
      },
      {
        name: 'axisStrokeWidth',
        displayName: '坐标轴线宽',
        description: '坐标轴线和刻度的宽度',
        type: OptionType.int,
        show: true,
        editable: true,
      },
      {
        name: 'units',
        displayName: '单位',
        description: '坐标轴文字单位',
        type: OptionType.string,
        show: true,
        editable: true,
      },
      {
        name: 'unitsColor',
        displayName: '单位文字颜色',
        description: '坐标轴单位文字颜色',
        type: OptionType.color,
        show: true,
        editable: true,
      },
      {
        name: 'unitsFontSize',
        displayName: '单位文字大小',
        description: '坐标轴文字单位',
        type: OptionType.int,
        show: true,
        editable: true,
      },
      {
        name: 'scaleType',
        displayName: '坐标轴类型',
        description: '坐标轴类型',
        type: OptionType.enum,
        options: [
          {
            name: '线性坐标轴',
            value: 'linear',
          },
          {
            name: '时间坐标轴',
            value: 'time',
          },
          {
            name: '顺序坐标轴',
            value: 'ordinal',
          },
          {
            name: '指数坐标轴',
            value: 'pow',
          },
          {
            name: '对数坐标轴',
            value: 'log',
          },
          {
            name: '阶段坐标轴',
            value: 'band',
          },
          {
            name: '点坐标轴',
            value: 'point',
          },
        ],
        show: true,
        editable: true,
      },
      {
        name: 'min',
        displayName: '最小值',
        description: '坐标轴最小值',
        type: OptionType.string,
        show: true,
        editable: true,
      },
      {
        name: 'minCoefficient',
        displayName: '最小值系数',
        description: '坐标轴最小值系数，0为不启用自适应',
        type: OptionType.double,
        show: true,
        editable: true,
      },
      {
        name: 'max',
        displayName: '最大值',
        description: '坐标轴最大值',
        type: OptionType.string,
        show: true,
        editable: true,
      },
      {
        name: 'maxCoefficient',
        displayName: '最大值系数',
        description: '坐标轴最大值系数，0为不启用自适应',
        type: OptionType.double,
        show: true,
        editable: true,
      },
      {
        name: 'adaptive',
        displayName: '坐标轴自适应',
        description: '坐标轴根据数据自适应',
        type: OptionType.boolean,
        show: true,
        editable: true,
      },
      {
        name: 'ticks',
        displayName: '刻度值个数',
        description: '特定刻度值个数(0为自动设置)',
        type: OptionType.int,
        show: true,
        editable: true,
      },
      {
        name: 'tickStep',
        displayName: '刻度值间隔',
        description: '刻度值间隔n个显示',
        type: OptionType.double,
        show: true,
        editable: true,
      },
      {
        name: 'isBindData',
        displayName: '是否绑定数据',
        description: '刻度值是否绑定数据',
        type: OptionType.boolean,
        show: true,
        editable: true,
      },
      {
        name: 'tickFormat',
        displayName: '刻度值格式化',
        description: '刻度值格式化',
        type: OptionType.enum,
        options: [
          {
            name: '无',
            value: '',
          },
          {
            name: '年',
            value: '%Y',
          },
          {
            name: '月',
            value: '%m',
          },
          {
            name: '日',
            value: '%d',
          },
          {
            name: '时',
            value: '%_H',
          },
          {
            name: '月/日',
            value: '%m/%d',
          },
          {
            name: '年-月-日',
            value: '%Y-%m-%d',
          },
          {
            name: '时:分:秒',
            value: '%H:%M:%S',
          },
        ],
        show: true,
        editable: true,
      },
      {
        name: 'tickSize',
        displayName: '刻度长度',
        description: '刻度线长度',
        type: OptionType.double,
        show: true,
        editable: true,
      },
      {
        name: 'tickPadding',
        displayName: '坐标轴文字间距',
        description: '坐标轴与文字直接的距离',
        type: OptionType.double,
        show: true,
        editable: true,
      },
      {
        name: 'tickTextOffset',
        displayName: '坐标轴文字偏移',
        description: '坐标轴与文字偏移微调',
        type: OptionType.doubleArray,
        placeholder: ['x', 'y'],
        show: true,
        editable: true,
      },
      {
        name: 'grid',
        displayName: '网格线类型',
        description: '网格线的线条类型',
        type: OptionType.enum,
        options: [
          {
            name: '无',
            value: 'none',
          },
          {
            name: '实线',
            value: 'line',
          },
          {
            name: '虚线',
            value: 'dash',
          },
        ],
        show: true,
        editable: true,
      },
      {
        name: 'gridColor',
        displayName: '网格线颜色',
        description: '网格线的颜色',
        type: OptionType.color,
        show: true,
        editable: true,
      },
      {
        name: 'fontSize',
        displayName: '文字大小',
        description: '坐标轴文字大小',
        type: OptionType.int,
        show: true,
        editable: true,
      },
      {
        name: 'fontWeight',
        displayName: '文字粗细',
        description: '坐标轴文字粗细',
        type: OptionType.enum,
        options: [
          {
            name: '正常',
            value: 'normal',
          },
          {
            name: '加粗',
            value: 'bold',
          },
          {
            name: '最粗',
            value: 'bolder',
          },
        ],
        show: true,
        editable: true,
      },
      {
        name: 'fontColor',
        displayName: '文字颜色',
        description: '坐标轴文字颜色',
        type: OptionType.color,
        show: true,
        editable: true,
      },
      {
        name: 'fontRotate',
        displayName: '文字旋转',
        description: '坐标轴文字旋转角度',
        type: OptionType.int,
        show: true,
        editable: true,
      },
      {
        name: 'change0To24',
        displayName: '修改最后一个刻度值从0转为24',
        description: '修改最后一个刻度值从0转为24',
        type: OptionType.boolean,
        show: true,
        editable: true,
      },
    ];
  }
  /**
   * @description 配置项自适应显示
   */
  _handleOpts() {
    if (this.property.adaptive) {
      this._optionDic[7].show = false;
      this._optionDic[9].show = false;
    } else {
      this._optionDic[7].show = true;
      this._optionDic[9].show = true;
    }
  }

  /**
   * @description 绘制坐标轴
   */
  _draw() {
    // this._handleOpts();
    let axis = null;
    let scale = null;
    if (!this.property.showAxis) {
      this._container.style('opacity', 0);
    }
    this.x = this.property.padding[2];
    this.y = this.property.padding[0];
    this.width = this.property.frame[2] - this.property.padding[2] - this.property.padding[3];
    this.height = this.property.frame[3] - this.property.padding[0] - this.property.padding[1];
    let min =
      this.property.minCoefficient === 0
        ? this.property.min
        : this.property.min * this.property.minCoefficient;
    let max =
      this.property.maxCoefficient === 0
        ? this.property.max
        : this.property.max * this.property.maxCoefficient;
    let obj = this._obj;
    let domainArr = [];
    let tickValues = []
    //判断坐标系类型
    switch (this.property.scaleType) {
      case 'linear':
        //线性坐标系
        scale = d3.scaleLinear();
        domainArr = [min, max];
        break;
      case 'time':
        //时间坐标系
        // let timeParse = d3.timeParse(this.property.tickFormat)
        scale = d3.scaleTime();
        // domainArr = [timeParse(this.property.min), timeParse(this.property.max)];
        // domainArr = [this.property.min, this.property.max];
        domainArr = [this.property.data[0], this.property.data[this.property.data.length - 1]];
        tickValues = scale.ticks(this.property.data.length);
        tickValues.unshift(this.property.data[0])
        break;
      case 'ordinal':
        //顺序坐标系
        scale = d3.scaleOrdinal();
        domainArr = this.property.data;
        break;
      case 'pow':
        //指数坐标系
        scale = d3.scalePow();
        break;
      case 'log':
        //对数坐标系
        scale = d3.scaleLog();
        break;
      case 'band':
        //阶段坐标系
        scale = d3.scaleBand();
        domainArr = this.property.data;
        break;
      case 'point':
        //点坐标系
        scale = d3.scalePoint();
        domainArr = this.property.data;
        break;
    }
    if (scale) {
      let range = [];
      //判断坐标轴方向
      switch (this.property.position) {
        case 'top':
          axis = d3.axisTop(scale);
          range = [0, this.width - this.x];
          if (this.property.scaleType === 'ordinal') {
            range = this.property.data.map(
              (d, i) => ((this.width - this.x) / (this.property.data.length - 1)) * i
            );
          }
          this._container.style('transform', `translate(${this.x}px, ${this.y}px)`);
          break;
        case 'left':
          axis = d3.axisLeft(scale);
          range = [this.height - this.y, 0];
          if (this.property.scaleType === 'ordinal') {
            range = this.property.data.map(
              (d, i) => ((this.width - this.x) / (this.property.data.length - 1)) * i
            );
          }
          this._container.style('transform', `translate(${this.x}px, ${this.y}px)`);
          break;
        case 'right':
          axis = d3.axisRight(scale);
          range = [this.height - this.y, 0];
          if (this.property.scaleType === 'ordinal') {
            range = this.property.data.map(
              (d, i) => ((this.width - this.x) / (this.property.data.length - 1)) * i
            );
          }
          this._container.style('transform', `translate(${this.width}px, ${this.y}px)`);
          break;
        case 'bottom':
        default:
          axis = d3.axisBottom(scale);
          range = [0, this.width - this.x];
          if (this.property.scaleType === 'ordinal') {
            range = this.property.data.map(
              (d, i) => ((this.width - this.x) / (this.property.data.length - 1)) * i
            );
          }
          this._container.style('transform', `translate(${this.x}px, ${this.height}px)`);
          break;
      }
      //生成比例尺
      let axisRange = scale.range(range);
      axisRange.domain(domainArr);
      //设置刻度内容
      // if (this.property.tickValues) axis.tickValues(this.property.tickValues);
      //设置刻度数据格式化
      if (this.property.tickFormat !== '')
        axis.tickFormat(
          d3.timeFormat(this.property.tickFormat === '%H' ? '%-H' : this.property.tickFormat)
        );
      //设置坐标轴个数
      if (this.property.ticks !== 0) {
        axis.ticks(this.property.ticks)
      };
      //时间刻度尺在绘制坐标轴的时候会漏掉第一个刻度值
      if (this.property.scaleType == "time" && this.property.ticks == 0) {
        let tickValues = scale.ticks(this.property.data.length);
        if (tickValues.length < this.property.data.length) {
          tickValues.unshift(new Date(this.property.data[0]));
          axis.tickValues(tickValues);
        }
      }
      //设置刻度大小
      if (this.property.tickSize) axis.tickSize(this.property.tickSize);
      //设置刻度文字距坐标轴距离
      axis.tickPadding(this.property.tickPadding);

      this._container.call(axis);

      let tickNumber = this._container.selectAll('text').nodes().length;

      this._container
        .selectAll('.tick')
        .select('text')
        .style('fill', this.property.fontColor)
        .style('font-size', `${this.property.fontSize * this.property.fontScale}px`)
        .style('font-weight', this.property.fontWeight)
        .style(
          'transform',
          `translate(${this.property.tickTextOffset[0]}px, ${this.property.tickTextOffset[1]}px) rotate(${this.property.fontRotate}deg)`
        )
        .style('opacity', (d, index) => {
          if (this.property.tickStep != 0 && index != tickNumber - 1) {
            if (index % (this.property.tickStep + 1) != 0) {
              return 0;
            }
            return 1;
          }
          return 1;
        });

      if (this.property.change0To24) {
        let arr = this._container.selectAll('.tick').select('text').nodes();
        d3.select(arr[arr.length - 1]).text('24');
      }

      this._container
        .selectAll('line')
        .style('stroke', this.property.axisColor)
        .style('stroke-width', this.property.axisStrokeWidth)
        .style('opacity', (d, index) => {
          if (this.property.tickStep != 0 && index != tickNumber - 1) {
            if (index % (this.property.tickStep + 1) != 0) {
              return 0;
            }
            return 1;
          }
          return 1;
        });
      this._container
        .selectAll('path')
        .style('stroke', this.property.axisColor)
        .style('stroke-width', this.property.axisStrokeWidth);

      // 绘制范围
      if (obj !== undefined) {
        let axis1 = obj.axis;
        let minValue = obj.minValue;
        let maxValue = obj.maxValue;
        let color = obj.color;
        let strokewidth = obj.strokewidth;
        let strokeDasharray = obj.strokeDasharray;
        if (axis1 == 'x') {
          this._container
            .append('path')
            .attr('class', 'range')
            .attr('d', `M 0,0 L 0,${this.y - this.height}`)
            .style('stroke', color)
            .style('stroke-width', `${strokewidth}px`)
            .style('stroke-dasharray', strokeDasharray)
            .attr(
              'transform',
              `translate(${axisRange(minValue) +
              (this.property.scaleType === 'band' ? axisRange.bandwidth() / 2 : 0)
              },0)`
            );
          this._container
            .append('path')
            .attr('class', 'range')
            .attr('d', `M 0,0 L 0,${this.y - this.height}`)
            .style('stroke', color)
            .style('stroke-width', `${strokewidth}px`)
            .style('stroke-dasharray', strokeDasharray)
            .attr(
              'transform',
              `translate(${axisRange(maxValue) +
              (this.property.scaleType === 'band' ? axisRange.bandwidth() / 2 : 0)
              },0)`
            );
        } else if (axis1 == 'y') {
          this._container
            .append('path')
            .attr('class', 'range')
            .attr('d', `M 0,0 L ${this.width - this.x},0`)
            .style('stroke', color)
            .style('stroke-width', `${strokewidth}px`)
            .style('stroke-dasharray', strokeDasharray)
            .attr('transform', `translate(0,${axisRange(minValue)})`);
          this._container
            .append('path')
            .attr('class', 'range')
            .attr('d', `M 0,0 L ${this.width - this.x},0`)
            .style('stroke', color)
            .style('stroke-width', `${strokewidth}px`)
            .style('stroke-dasharray', strokeDasharray)
            .attr('transform', `translate(0,${axisRange(maxValue)})`);
        }
      }
      //绘制网格
      if (this.property.grid !== 'none') {
        this._container.selectAll('.tick').selectAll('.axisGrid').remove();
        this._container
          .selectAll('.tick')
          .append('line')
          .attr('class', 'axisGrid')
          .attr(
            this.property.position === 'left' || this.property.position === 'right' ? 'x2' : 'y2',
            (this.property.position === 'bottom' || this.property.position === 'right' ? -1 : 1) *
            (this.property.position === 'left' || this.property.position === 'right'
              ? this.width - this.x
              : this.height - this.y)
          )
          .style('stroke', this.property.gridColor)
          .style('stroke-dasharray', this.property.grid === 'dash' ? '2 2' : 'none')
          .style('opacity', (d, index) => {
            if (this.property.tickStep != 0 && index != tickNumber - 1) {
              if (index % (this.property.tickStep + 1) != 0) {
                return 0;
              }
              return 1;
            }
            return 1;
          });
      } else {
        this._container.selectAll('.tick').selectAll('line').remove();
      }
      this.scale = scale;
      //绘制单位
      if (!this._container.select('.units').empty()) {
        this._container.select('.units').remove();
      }
      this._container
        .append('text')
        .attr('class', 'units')
        .text(this.property.units)
        .style(
          'transform',
          `translate(${this.property.position === 'bottom' ? this.width : 0}px, 0px)`
        )
        .style(
          'dominant-baseline',
          this.property.position === 'bottom' ? 'text-before-edge' : 'text-after-edge'
        )
        .style('text-anchor', 'end')
        .style('fill', this.property.unitsColor)
        .style('font-size', this.property.unitsFontSize);
    }
  }
  /**
   * @description 获取0刻度偏移量
   */
  getZeroScale() {
    let scaleType = 'noLinear';
    if (this.property.scaleType === 'linear') {
      scaleType = this.scale(0);
    }
    return scaleType;
  }
  /**
   * @description 设置坐标轴偏移量
   */
  setTransform(temp) {
    switch (this.property.position) {
      case 'left':
      case 'right':
        this._container.style('transform', `translate(${this.x + temp}px, ${this.y}px)`);
        break;
      case 'top':
      case 'bottom':
      default:
        this._container.style('transform', `translate(${this.x}px, ${this.y + temp}px)`);
        break;
    }
  }
  /**
   * @description 配置坐标轴属性
   */
  setOption(options) {
    this.property = $.extend(true, this.property, options);
    if (options.hasOwnProperty('data')) this.property.data = options.data;
    this._draw();
  }
}
