<!-- 区块内阴影 -->
<filter id="inset-shadow-block">
  <feOffset dx="50" dy="50" />
  <feGaussianBlur stdDeviation="15" result="offset-blur" />
  <feComposite operator="out" in="SourceGraphic" in2="offset-blur" result="inverse" />
  <feFlood flood-color="#000000" flood-opacity="0.3" result="color" />
  <feComposite operator="in" in="color" in2="inverse" result="shadow" />
  <feComposite operator="over" in="shadow" in2="SourceGraphic" />
</filter>
<!--  换电站母线红色内发光 -->
<filter id="inset-shadow-red">
  <feOffset dx="0" dy="0" />
  <feGaussianBlur stdDeviation="8" result="offset-blur" />
  <feComposite operator="out" in="SourceGraphic" in2="offset-blur" result="inverse" />
  <feFlood flood-color="#ff005b" flood-opacity="1" result="color" />
  <feComposite operator="in" in="color" in2="inverse" result="shadow" />
  <feComposite operator="over" in="shadow" in2="SourceGraphic" />
</filter>
<!-- 换电站母线 阴影-->
<filter id="shadow-xfmr" x="-50%" y="-50%" width="200%" height="200%">
  <feOffset result="offOut" in="SourceGraphic" dx="0" dy="0" />
  <feGaussianBlur result="blurOut" in="offOut" stdDeviation="5" />
  <feBlend in="SourceGraphic" in2="blurOut" mode="normal" />
</filter>
<!-- 换电站母线 蓝色内发光 -->
<filter id="inset-shadow-blue">
  <feOffset dx="0" dy="0" />
  <feGaussianBlur stdDeviation="8" result="offset-blur" />
  <feComposite operator="out" in="SourceGraphic" in2="offset-blur" result="inverse" />
  <feFlood flood-color="#00b4ff" flood-opacity="1" result="color" />
  <feComposite operator="in" in="color" in2="inverse" result="shadow" />
  <feComposite operator="over" in="shadow" in2="SourceGraphic" />
</filter>
<!-- 换电站母线 绿色内发光 -->
<filter id="inset-shadow-green">
  <feOffset dx="0" dy="0" />
  <feGaussianBlur stdDeviation="8" result="offset-blur" />
  <feComposite operator="out" in="SourceGraphic" in2="offset-blur" result="inverse" />
  <feFlood flood-color="#83ff00" flood-opacity="1" result="color" />
  <feComposite operator="in" in="color" in2="inverse" result="shadow" />
  <feComposite operator="over" in="shadow" in2="SourceGraphic" />
</filter>
<!-- 换电站母线 紫色内发光 -->
<filter id="inset-shadow-purple">
  <feOffset dx="0" dy="0" />
  <feGaussianBlur stdDeviation="8" result="offset-blur" />
  <feComposite operator="out" in="SourceGraphic" in2="offset-blur" result="inverse" />
  <feFlood flood-color="#ff2cb5" flood-opacity="1" result="color" />
  <feComposite operator="in" in="color" in2="inverse" result="shadow" />
  <feComposite operator="over" in="shadow" in2="SourceGraphic" />
</filter>
<!-- 换电站中心球背景渐变色 -->
<linearGradient id="center-bg" x1="0%" y1="0%" x2="0%" y2="100%">
  <stop offset="0%" style="stop-color: #505050; stop-opacity: 1" />
  <stop offset="23%" style="stop-color: #282b3f; stop-opacity: 1" />
  <stop offset="100%" style="stop-color: #000105; stop-opacity: 1" />
</linearGradient>
<!-- 换电站中心球背景内发光 -->
<filter id="center-bg-inset">
  <feOffset dx="0" dy="0" />
  <feGaussianBlur stdDeviation="12" result="offset-blur" />
  <feComposite operator="out" in="SourceGraphic" in2="offset-blur" result="inverse" />
  <feFlood flood-color="#ffffff" flood-opacity="0.2" result="color" />
  <feComposite operator="in" in="color" in2="inverse" result="shadow" />
  <feComposite operator="over" in="shadow" in2="SourceGraphic" />
</filter>
<!-- 换电站中心球绿色内发光 -->
<filter id="inset-water-0">
  <feOffset dx="0" dy="0" />
  <feGaussianBlur stdDeviation="12" result="offset-blur" />
  <feComposite operator="out" in="SourceGraphic" in2="offset-blur" result="inverse" />
  <feFlood flood-color="#83ff00" flood-opacity="1" result="color" />
  <feComposite operator="in" in="color" in2="inverse" result="shadow" />
  <feComposite operator="over" in="shadow" in2="SourceGraphic" />
</filter>
<!-- 换电站中心球黄色内发光 -->
<filter id="inset-water-1">
  <feOffset dx="0" dy="0" />
  <feGaussianBlur stdDeviation="12" result="offset-blur" />
  <feComposite operator="out" in="SourceGraphic" in2="offset-blur" result="inverse" />
  <feFlood flood-color="#ffc400" flood-opacity="1" result="color" />
  <feComposite operator="in" in="color" in2="inverse" result="shadow" />
  <feComposite operator="over" in="shadow" in2="SourceGraphic" />
</filter>
<!-- 换电站中心球红色内发光 -->
<filter id="inset-water-2">
  <feOffset dx="0" dy="0" />
  <feGaussianBlur stdDeviation="12" result="offset-blur" />
  <feComposite operator="out" in="SourceGraphic" in2="offset-blur" result="inverse" />
  <feFlood flood-color="#ff003c" flood-opacity="1" result="color" />
  <feComposite operator="in" in="color" in2="inverse" result="shadow" />
  <feComposite operator="over" in="shadow" in2="SourceGraphic" />
</filter>
<!-- 换电站中心球填充渐变色 -->
<linearGradient id="water-bg" x1="0%" y1="0%" x2="0%" y2="100%">
  <stop offset="0%" style="stop-color: #ff133b; stop-opacity: 1" />
  <stop offset="60%" style="stop-color: #c57800; stop-opacity: 1" />
  <stop offset="100%" style="stop-color: #00c405; stop-opacity: 1" />
</linearGradient>
<!-- 电厂柱状图220kv填充渐变 -->
<linearGradient id="powerplant-bar_220KV" x1="0%" y1="0%" x2="100%" y2="0%">
  <stop offset="0%" style="stop-color: #0e4878; stop-opacity: 1" />
  <stop offset="100%" style="stop-color: #00c5fd; stop-opacity: 1" />
</linearGradient>
<!-- 电厂柱状图500kv填充渐变 -->
<linearGradient id="powerplant-bar_500KV" x1="0%" y1="0%" x2="100%" y2="0%">
  <stop offset="0%" style="stop-color: #9d021f; stop-opacity: 1" />
  <stop offset="100%" style="stop-color: #fc2e4f; stop-opacity: 1" />
</linearGradient>
<!-- 500kv变电站名称填充渐变 -->
<linearGradient id="text_500KV_trans_name" x1="0%" y1="0%" x2="0%" y2="100%">
  <stop offset="0%" style="stop-color: #b38b4d; stop-opacity: 1" />
  <stop offset="34%" style="stop-color: #d7c791; stop-opacity: 1" />
  <stop offset="50%" style="stop-color: #e3d5a6; stop-opacity: 1" />
  <stop offset="60%" style="stop-color: #d7c791; stop-opacity: 1" />
  <stop offset="100%" style="stop-color: #b38b4d; stop-opacity: 1" />
</linearGradient>
<!-- 分区板名称填充渐变 -->
<linearGradient id="area_board_name" x1="0%" y1="0%" x2="0%" y2="100%">
  <stop offset="0%" style="stop-color: #cebc9a; stop-opacity: 1" />
  <stop offset="29%" style="stop-color: #caac7f; stop-opacity: 1" />
  <stop offset="65%" style="stop-color: #f3eec6; stop-opacity: 1" />
  <stop offset="100%" style="stop-color: #af9270; stop-opacity: 1" />
</linearGradient>
<!-- 分区板名称阴影-->
<filter id="area_board_name_shadow" x="-50%" y="-50%" width="200%" height="200%">
  <feOffset result="offOut" in="SourceAlpha" dx="0" dy="0" />
  <feGaussianBlur result="blurOut" in="offOut" stdDeviation="10" />
  <feBlend in="SourceGraphic" in2="blurOut" mode="normal" />
</filter>
<!-- 分区板数值填充渐变 -->
<linearGradient id="area_board_value" x1="0%" y1="0%" x2="0%" y2="100%">
  <stop offset="0%" style="stop-color: #5b5b5b; stop-opacity: 1" />
  <stop offset="13%" style="stop-color: #898989; stop-opacity: 1" />
  <stop offset="28%" style="stop-color: #474747; stop-opacity: 1" />
  <stop offset="46%" style="stop-color: #000000; stop-opacity: 1" />
  <stop offset="76%" style="stop-color: #3a3a3a; stop-opacity: 1" />
  <stop offset="88%" style="stop-color: #292929; stop-opacity: 1" />
  <stop offset="100%" style="stop-color: #1a1a1a; stop-opacity: 1" />
</linearGradient>
<!-- 箭头填充渐变 -->
<linearGradient id="arrow-fill-white" x1="0%" y1="0%" x2="100%" y2="0%">
  <stop offset="0%" style="stop-color: #ffffff00; stop-opacity: 1" />
  <stop offset="50%" style="stop-color: #ffffff; stop-opacity: 1" />
  <stop offset="100%" style="stop-color: #ffffff; stop-opacity: 1" />
</linearGradient>
<!-- 箭头阴影-->
<filter id="arrow_shadow" x="-50%" y="-50%" width="200%" height="200%">
  <feOffset result="offOut" in="SourceAlpha" dx="0" dy="0" />
  <feGaussianBlur result="blurOut" in="offOut" stdDeviation="10" />
  <feBlend in="SourceGraphic" in2="blurOut" mode="normal" />
</filter>
