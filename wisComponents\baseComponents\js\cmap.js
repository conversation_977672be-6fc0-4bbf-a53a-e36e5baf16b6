'use strict'
window.CmapVersion = "2.0.1";

class Cmap extends MapWidget {
  constructor(id, code, container, workMode, option = {}, useDefaultOpt = true) {
    super(id, code, container, workMode, option, useDefaultOpt);

    console.info(`地图组件基类版本: ${window.CmapVersion}`);

    //影响服务地址
    this._config = this.property.config;

    this._draw();

  }

  _draw() {
    super._draw(); // 构造地图容器

    if (this.workMode == 2) {
      $(this.container).empty().append(`<div id="viewer-placeHolder" style="width: ${this.property.basic.frame[2]}px; height: ${this.property.basic.frame[3]}px;"></div>`);
      return;
    }

    const waitContainer = setInterval(() => {
      if (document.getElementById(`viewer-${this.id}`) !== null) {
        clearInterval(waitContainer);
        this._drawMap();
      }
    }, 200);

  }

  _drawMap() {
    // 初始化地图容器
    this.viewer = new Cesium.Viewer(`viewer-${this.id}`, {
      geocoder: false,         //右上角 搜索
      homeButton: false,       //右上角 Home
      sceneModePicker: false,  //右上角 2D/3D切换
      baseLayerPicker: false,  //右上角 地形
      navigationHelpButton: false, //右上角 Help
      animation: false,        //左下角 圆盘动画控件
      timeline: false,         //时间轴
      fullscreenButton: false, //右小角 全屏
      vrButton: false,
      infoBox: false,          //点击要素后提示信息      
      imageryProvider: new Cesium.UrlTemplateImageryProvider({          //初始化底图
        url: `${window.origin}/tiles/${this._config.tile}/{z}/{x}/{y}.png`
      })
    });

    this.viewer._cesiumWidget._creditContainer.style.display = "none"; //去除cesium的logo
    this.viewer.scene.screenSpaceCameraController.minimumZoomDistance = 100;

    this.viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(this.property.camera.lng, this.property.camera.lat, this.property.camera.height),
      orientation: {
        heading: this.property.camera.heading,
        pitch: this.property.camera.pitch,
        roll: this.property.camera.roll
      }
    });

    // /* 开启多屏同步*/
    if (this._config.multiStation) {
      this._multiScreenSynchronization();
    }

  }

  /* 生成地图容器，添加分辨率控制*/
  _generateBasicDIV() {
    this.$container = $(this.container).css("pointer-events", "auto").html(`<div id="viewer-${this.id}" style="width: ${this.property.basic.frame[2]}px; height: ${this.property.basic.frame[3]}px;"></div>`);
  }

  _initProperty() {  // 组件属性
    super._initProperty();

    const options = {
      basic: {
        className: 'Cmap'
      },
      camera: {
        lng: 118.776969,
        lat: 32.043004,
        z: 15000,
        heading: 0,
        pitch: -45,
        roll: 0
      },
      config: {
        tile: 'terrain',
        multiStation: false
      }
    }

    const optionDic = [
      {
        name: 'camera',
        displayName: '相机',
        description: '相机',
        children: [
          {
            name: 'lng',
            displayName: '经度',
            description: '经度',
            type: OptionType.double,
            show: true,
            editable: true
          }, {
            name: 'lat',
            displayName: '纬度',
            description: '纬度',
            type: OptionType.double,
            show: true,
            editable: true
          }, {
            name: 'z',
            displayName: '高度',
            description: '高度',
            type: OptionType.double,
            show: true,
            editable: true
          }, {
            name: 'heading',
            displayName: '相机弧度方向',
            description: '相机弧度方向',
            type: OptionType.double,
            show: true,
            editable: true
          }, {
            name: 'pitch',
            displayName: '相机弧度',
            description: '相机弧度',
            type: OptionType.double,
            show: true,
            editable: true
          }, {
            name: 'roll',
            displayName: '相机弧度角',
            description: '相机弧度角',
            type: OptionType.double,
            show: true,
            editable: true
          }
        ]
      },
      {
        name: 'config',
        displayName: '地图配置',
        description: '地图配置',
        children: [
          {
            name: 'tile',
            displayName: '默认瓦片',
            description: '默认瓦片文件夹名',
            type: OptionType.string,
            show: true,
            editable: true
          },
          {
            name: 'multiStation',
            displayName: '多屏同步',
            description: '多屏同步',
            type: OptionType.boolean,
            show: true,
            editable: true
          }
        ]
      }
    ]

    this._addProperty(options, optionDic);

  }

  _initEvents() {
    super._initEvents();

    const funcs = [
      {
        name: 'turnLeft',
        displayName: '向左旋转',
        params: []
      },
      {
        name: 'turnRight',
        displayName: '向右旋转',
        params: []
      },
      {
        name: 'recover',
        displayName: '复位',
        params: []
      },
      {
        name: 'zoomIn',
        displayName: '放大',
        params: []
      },
      {
        name: 'zoomOut',
        displayName: '缩小',
        params: []
      },
      {
        name: 'birdEye',
        displayName: '鸟瞰',
        params: []
      }
    ];

    this.invokeFunc = this.invokeFunc.concat(funcs);

  }

  /* 绑定地图交互事件*/
  _multiScreenSynchronization() {
    this._selfFlag = Cmapd.uuid();  // 防止自动收到广播消息重复操作

    const _syncPosition = () => {
      this._sendCommand('_sync', [
          this._selfFlag,
          'position',
          this.viewer.camera.position,
          this.viewer.scene.camera.heading,
          this.viewer.scene.camera.pitch,
          this.viewer.scene.camera.roll
      ]);

      // 发送结束后，解绑所有相机上绑定的事件
      this.viewer.camera.changed.removeEventListener(_syncPosition);
      this.viewer.camera.moveEnd.removeEventListener(_syncPosition);
    }

    // 获取事件对象
    const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.canvas);

    // 拖拽事件，仅在鼠标操作的时绑事件，并在结束后解绑事件，保证广播后各屏反复触发再广播
    handler.setInputAction((e) => {
      handler.setInputAction(_syncPosition, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

      handler.setInputAction((e) => {
        // 相机移动结束再发一次最后的同步结果，避免甩动拖拽后不同的情况，可能会稍有延迟
        this.viewer.camera.moveEnd.addEventListener(_syncPosition);

        // 移除事件
        handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_UP);
        handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);

      }, Cesium.ScreenSpaceEventType.MOUSE_UP);

    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

    // 缩放事件，滚动时绑定，发送结束后解除
    handler.setInputAction((e) => {
      this.viewer.camera.changed.addEventListener(_syncPosition);
    }, Cesium.ScreenSpaceEventType.WHEEL);

  }

  _sync(params) {
    // 防止自动收到广播消息重复操作
    if (params[0] == this._selfFlag) {
      return;
    }

    switch (params.method) {
      case 'position': {
        this.viewer.camera.setView({
          destination: params[2],
          orientation: {
            heading: params[3],
            pitch: params[4],
            roll: params[5]
          }
        });

        break;
      }
    }
  }

  /* 向左旋转*/
  turnLeft() {
    this.viewer.camera.rotateLeft();
  }

  /* 向右旋转*/
  turnRight() {
    this.viewer.camera.rotateRight();
  }

  /* 放大*/
  zoomIn() {
    this.viewer.camera.zoomIn(500); //有bug，待优化
  }

  /* 缩小*/
  zoomOut() {
    this.viewer.camera.zoomOut(500);
  }

  /* 复位*/
  initPosition() {
    //初始化视角
    this.viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(this.property.camera.lng, this.property.camera.lat, this.property.camera.z),
      orientation: {
        heading: this.property.camera.heading,
        pitch: this.property.camera.pitch,
        roll: this.property.camera.roll
      }
    });
  }

  /* 鸟瞰*/
  birdEye() {
    const pick1 = new Cesium.Cartesian2(this.property.basic.frame[2] / 2, this.property.basic.frame[3] / 2);
    const cartesian3 = this.viewer.scene.globe.pick(this.viewer.camera.getPickRay(pick1), this.viewer.scene);
    const entity = this.viewer.entities.add(new Cesium.Entity({
      point: new Cesium.PointGraphics({}),
      position: cartesian3
    }));

    this.viewer.flyTo(entity, {
      offset: {
        heading: this.viewer.camera.heading,
        pitch: Cesium.Math.toRadians(-90),
        range: 5000
      }
    }).then(function () {
      this.viewer.entities.remove(entity);
    })

  }

  cleanup() {
    super.cleanup();

  }

}