/**
 * <AUTHOR>
 * @description 图表标注类
 * @date 2020/5/18
 */
class MarkPoint {
  constructor(container, opts) {
    this._initProperty();
    this._container = container;
    this.property = $.extend(true, this.property, opts);
    this._drawMark();
  }
  /**
   * @description 初始化组件配置项
   */
  _initProperty() {
    this.property = {
      description: '',
      position: [100, 100],
      type: 0,
      symbol: 0,
      symbolSize: 50,
      symbolOffset: [0, 0],
      symbolColor: '#ff0000',
      label: {
        text: 'name1',
        color: '#ffffff',
        offset: [0, 0],
        fontSize: 12,
      },
      xRange: null,
      yRange: null,
      data: [],
    };

    this._optionDic = [
      {
        name: 'description',
        displayName: '标记点描述',
        description: '标记点描述',
        type: OptionType.string,
        show: true,
        editable: true,
      },
      {
        name: 'type',
        displayName: '标注类型',
        description: '标注位置类型',
        type: OptionType.enum,
        options: [
          {
            name: '最大值',
            value: 'max',
          },
          {
            name: '最小值',
            value: 'min',
          },
          {
            name: '最新值',
            value: 'latest',
          },
          {
            name: '自定义',
            value: 'custom',
          },
        ],
        show: true,
        editable: true,
      },
      {
        name: 'position',
        displayName: '标记点位置',
        description: '标记点位置(在标注类型为自定义时生效)',
        type: OptionType.doubleArray,
        placeholder: ['x', 'y'],
        show: true,
        editable: true,
      },
      {
        name: 'symbol',
        displayName: '图形',
        description: '标注图形类型',
        type: OptionType.enum,
        options: [
          {
            name: '无',
            value: '',
          },
          {
            name: '圆形',
            value: 'circle',
          },
          {
            name: '三角形',
            value: 'triangle',
          },
          {
            name: '矩形',
            value: 'rect',
          },
          {
            name: '大头针',
            value: 'pin',
          },
        ],
        show: true,
        editable: true,
      },
      {
        name: 'symbolSize',
        displayName: '宽高',
        description: '标注图形宽高',
        type: OptionType.int,
        show: true,
        editable: true,
      },
      {
        name: 'symbolOffset',
        displayName: '偏移',
        description: '标注偏移量',
        type: OptionType.intArray,
        placeholder: ['x', 'y'],
        show: true,
        editable: true,
      },
      {
        name: 'symbolColor',
        displayName: '颜色',
        description: '标注填充颜色',
        type: OptionType.color,
        show: true,
        editable: true,
      },
      {
        name: 'label',
        displayName: '文字属性',
        description: '显示文字属性',
        children: [
          {
            name: 'text',
            displayName: '显示文字',
            description: '标注显示文字',
            type: OptionType.enum,
            options: [
              {
                name: 'y',
                value: 'y',
              },
              {
                name: 'x-y',
                value: 'x-y',
              },
            ],
            show: true,
            editable: true,
          },
          {
            name: 'color',
            displayName: '颜色',
            description: '显示文字的颜色',
            type: OptionType.color,
            show: true,
            editable: true,
          },
          {
            name: 'offset',
            displayName: '偏移量',
            description: '文字的偏移量',
            type: OptionType.intArray,
            placeholder: ['x', 'y'],
            show: true,
            editable: true,
          },
          {
            name: 'fontSize',
            displayName: '文字大小',
            description: '标注显示文字大小',
            type: OptionType.int,
            show: true,
            editable: true,
          },
        ],
        show: true,
        editable: true,
      },
    ];
  }
  /**
   * @description 更新配置
   */
  setOption(opt) {
    this.property = $.extend(true, this.property, opt);
  }
  /**
   * @description 生成标记点
   */
  _drawMark() {
    this.point = this._container.append('path').attr('d', WisCompUtil.getSymbol(this.property.symbol)).style('fill', this.property.symbolColor);

    this.text = this._container.append('text').style('text-anchor', 'middle').style('dominant-baseline', 'central').style('fill', this.property.label.color).style('font-size', this.property.label.fontSize);

    this._setMarkPoint();
  }
  /**
   * @description 更新数据
   */
  update() {
    this._container.select('path').attr('d', WisCompUtil.getSymbol(this.property.symbol)).style('fill', this.property.symbolColor);
    this._container.select('text').style('text-anchor', 'middle').style('dominant-baseline', 'central').style('fill', this.property.label.color).style('font-size', this.property.label.fontSize);

    this._setMarkPoint();
  }
  /**
   * @description 配置标记信息
   */
  _setMarkPoint() {
    let labelText = '';
    let x = 0;
    let y = 0;
    switch (this.property.type) {
      case 'max':
        //max
        if (this.property.data.length > 0) {
          let maxData = this.property.data.reduce((p, v) => (p.y < v.y ? v : p));
          x = this.property.xRange(maxData.x);
          y = this.property.yRange(maxData.y);
          labelText = maxData;
        }
        break;
      case 'min':
        //min
        if (this.property.data.length > 0) {
          let minData = this.property.data.reduce((p, v) => (p.y > v.y ? v : p));
          x = this.property.xRange(minData.x);
          y = this.property.yRange(minData.y);
          labelText = minData;
        }
        break;
      case 'latest':
        //latest
        if (this.property.data.length > 0) {
          let latestData = this.property.data[this.property.data.length - 1];
          x = this.property.xRange(latestData.x);
          y = this.property.yRange(latestData.y);
          labelText = latestData;
        }
        break;
      case 'custom':
      default:
        //自定义
        x = this.property.position[0];
        y = this.property.position[1];
    }

    switch (this.property.label.text) {
      case 'y':
        labelText = `${labelText.y}`;
        break;
      case 'x-y':
        labelText = `${labelText.x}-${labelText.y}`;
        break;
      default:
        labelText = `${labelText.y}`;
    }

    this.point.style('transform', `translate(${x + this.property.symbolOffset[0]}px, ${y + this.property.symbolOffset[1]}px) scale(${this.property.symbolSize})`);

    this.text.style('transform', `translate(${x + this.property.symbolOffset[0] + this.property.label.offset[0]}px, ${y + this.property.symbolOffset[1] + +this.property.label.offset[1] - this.property.symbolSize}px)`).text(labelText);
  }
}
