<!--
 * @Author: 陆晓夫
 * @Date: 2022-03-01 10:22:52
 * @LastEditors: 陆晓夫
 * @LastEditTime: 2022-05-26 17:26:25
-->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <link rel="stylesheet" type="text/css" href="v1.0/baseTransformerSubstation.css" />
    <link rel="stylesheet" type="text/css" href="../baseComponents/css/fonts.css" />
</head>

<body style="background-color: #000000">
    <svg id="con"></svg>
    <script src="../../WisVisual/libs/d3.v5.min.js"></script>
    <script src="../../WisVisual/libs/anime.min.js"></script>
    <script src="../../WisVisual/libs/jquery.min.js"></script>
    <script src="../../WisVisual/libs/lodash.min.js"></script>
    <script src="../../WisVisual/Util/CompUtil.js"></script>
    <script src="../../WisVisual/Util/Util.js"></script>
    <!-- <script src="../../WisVisual/libs/reconnecting-websocket.min.js"></script> -->
    <!-- <script src="../../WisVisual/libs/stomp.js"></script> -->
    <!-- <script src="../../WisVisual/API/API.js"></script> -->
    <script src="../base/optionType.js"></script>
    <script src="../base/componentBase.js"></script>
    <!-- <script src="../imageView/v1.0/imageView.js"></script> -->
    <script src="../powerPlantStateBoard/v1.0/powerPlantStateBoard.js"></script>
    <!-- <script src="../cylinderBoard/v1.0/cylinderBoard.js"></script> -->
    <script src="../digitGearBoard/v1.0/digitGearBoard.js"></script>
    <script src="v1.0/baseTransformerSubstation.js"></script>
    <script>
        let mapId = getUrlParam('mapId');
        let w = getUrlParam('w');
        let h = getUrlParam('h');

        // window.initAPIPromise.then(() => {
        draw();
        // });

        function getUrlParam(name) {
            var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }

        function draw() {
            window.currentSceneId = '225';
            console.log(mapId);
            if (mapId === null) {
                mapId = 'XJteTx7qVN';
            }
            if (w === null) {
                w = 14240;
            }
            if (h === null) {
                h = 6400;
            }
            d3.json(`./json/svgData.json`).then((data) => {
                let svgData = data;
                console.log(svgData)
                let meta = {
                    "lowBusDesc1": "江苏.上河/220kV.Ⅲ段母线,_220KV",
                    "lowBusDesc2": "江苏.上河/220kV.Ⅰ段母线,_220KV",
                    "lowBusDesc3": "江苏.上河/220kV.Ⅱ段母线,_220KV",
                    "lowBusVolType2": "_220KV",
                    "lowBusVolType1": "_220KV",
                    "switchVoltype8": "_220KV",
                    "lowBusVolType3": "_220KV",
                    "num": "11",
                    "type": "TransStation",
                    "switchRunningStatus9": "RUNNING",
                    "xfmrRunningStatus6": "RUNNING",
                    "xfmrRunningStatus7": "RUNNING",
                    "switchRunningStatus8": "RUNNING",
                    "id": 97008092,
                    "switchDesc8": "江苏.上河.220kVI、III段分段2500开关,_220KV",
                    "switchDesc9": "江苏.上河.220kVI、II段母联2630开关,_220KV",
                    "switchRunningStatus10": "RUNNING",
                    "rtKeyId3": "1301:320000000002700000",
                    "rtKeyId2": "1301:320000000002690000",
                    "rtKeyId1": "1301:320000000002710000",
                    "xfmrNo": "6,7",
                    "rtKeyId0": "112:32000000120000",
                    "lowBusRunningStatus1": "RUNNING",
                    "rtKeyId7": "1311:320000000000950000",
                    "highBusDesc5": "江苏.上河/500kV.Ⅱ段母线,_500KV",
                    "lowBusRunningStatus2": "RUNNING",
                    "rtKeyId6": "1311:320000000000940000",
                    "switchVoltype10": "_220KV",
                    "highBusDesc4": "江苏.上河/500kV.Ⅰ段母线,_500KV",
                    "lowBusRunningStatus3": "RUNNING",
                    "rtKeyId5": "1301:320000000001300000",
                    "rtKeyId4": "1301:320000000001310000",
                    "switch_num": "3",
                    "highbus_num": "2",
                    "layerId": "113997366104686638",
                    "volt": "_500KV",
                    "name": "江苏.上河",
                    "switchVoltype9": "_220KV",
                    "xfmrDesc7": "江苏.上河.2号主变,_500KV",
                    "xfmrDesc6": "江苏.上河.1号主变,_500KV",
                    "highBusNo": "4,5",
                    "highBusVoltype4": "_500KV",
                    "highBusRunningStatus5": "RUNNING",
                    "xfmrMvarate7": "750.0000",
                    "highBusRunningStatus4": "RUNNING",
                    "xfmrMvarate6": "750.0000",
                    "highBusVoltype5": "_500KV",
                    "rtKeyId10": "1321:320000000009510000",
                    "lowBusNo": "2,3,1",
                    "rtKeyId9": "1321:320000000009530000",
                    "rtKeyId8": "1321:320000000009520000",
                    "xfmr_num": "2",
                    "lowbus_num": "3",
                    "switchDesc10": "江苏.上河.220kVII、III段母联2650开关,_220KV",
                    "xfmrVoltype6": "_500KV",
                    "switchNo": "8_1_3,9_1_2,10_2_3",
                    "xfmrVoltype7": "_500KV"
                };
                window.comp = new BaseTransformerSubstation('858', '123', d3.select('#con'), 0, {
                    metaData: JSON.stringify(meta),
                    svgData: JSON.stringify(svgData),
                    size: 120
                });

            });

        }
    </script>
</body>

</html>