<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 101 101" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1,0,0,1,-39048,-3826)">
        <g id="核能" transform="matrix(-0.760297,-9.31095e-17,7.47291e-17,-0.61021,49671.3,6824.37)">
            <g transform="matrix(0.239141,0,0,0.29796,11221.4,4724.87)">
                <g transform="matrix(-2.26939,2.77921e-16,-2.79749e-16,-2.28432,86556.4,10530.6)">
                    <path d="M33314.3,4358.05C33314.3,4345.71 33304.2,4335.69 33291.8,4335.69L33097.7,4335.69C33085.3,4335.69 33075.2,4345.71 33075.2,4358.05L33075.2,4550.85C33075.2,4563.18 33085.3,4573.19 33097.7,4573.19L33291.8,4573.19C33304.2,4573.19 33314.3,4563.18 33314.3,4550.85L33314.3,4358.05Z" style="fill:rgb(34,32,32);"/>
                </g>
                <path d="M10949.4,578.483C10949.4,607.05 10972.6,630.242 11001.1,630.242L11447.6,630.242C11476.2,630.242 11499.4,607.05 11499.4,578.483L11499.4,132.001C11499.4,103.434 11476.2,80.242 11447.6,80.242L11001.1,80.242C10972.6,80.242 10949.4,103.434 10949.4,132.001L10949.4,578.483ZM10967.7,570.816C10967.7,593.478 10986.1,611.878 11008.8,611.878L11439.9,611.878C11462.6,611.878 11481,593.478 11481,570.816L11481,139.668C11481,117.006 11462.6,98.606 11439.9,98.606L11008.8,98.606C10986.1,98.606 10967.7,117.006 10967.7,139.668L10967.7,570.816Z" style="fill:url(#_Linear1);"/>
                <g transform="matrix(1,-1.2326e-31,-4.93038e-32,1,0.0323855,1.53875)">
                    <g>
                        <g transform="matrix(-3.24542,3.97449e-16,-1.48168e-16,-1.20989,118802,5623.36)">
                            <path d="M33225.6,4428.83L33069.2,4428.83L33081.4,4372.83L33215.2,4372.83L33225.6,4428.83Z" style="fill:url(#_Linear2);"/>
                        </g>
                        <g transform="matrix(-5.06102,6.19796e-16,-3.89891e-16,-3.1837,180082,14637.5)">
                            <path d="M33414.6,4514.8L33314.3,4514.8L33314.3,4550.9C33314.3,4559.12 33317.8,4565.8 33322,4565.8L33406.8,4565.8C33411.1,4565.8 33414.6,4559.06 33414.6,4550.76L33414.6,4514.8Z" style="fill:url(#_Linear3);"/>
                        </g>
                        <g transform="matrix(-2.2686,2.77824e-16,-2.9285e-16,-2.3913,93717.3,10193.4)">
                            <ellipse cx="36362.9" cy="4152.16" rx="111.824" ry="0.255" style="fill:white;"/>
                        </g>
                    </g>
                    <g transform="matrix(-9.15345,1.12097e-15,-1.12097e-15,-9.15345,314625,42399.3)">
                        <g transform="matrix(14,0,0,14,33166.2,4617.42)">
                        </g>
                        <text x="33127.5px" y="4617.42px" style="font-family:'MicrosoftYaHeiUI-Bold', 'Microsoft YaHei UI', sans-serif;font-weight:700;font-size:14px;fill:rgb(29,29,29);">3<tspan x="33137.5px 33147.6px 33157.6px " y="4617.42px 4617.42px 4617.42px ">254</tspan></text>
                    </g>
                </g>
            </g>
            <g transform="matrix(-1.9729,3.01037e-16,-2.4161e-16,-2.45815,15024.4,12900.6)">
                <path d="M576.399,3284.84C573.652,3286.75 570.432,3287.76 567.088,3287.76C563.745,3287.76 560.524,3286.75 557.775,3284.84C557.157,3284.41 557.003,3283.57 557.432,3282.95L562.089,3276.22C562.295,3275.92 562.611,3275.72 562.966,3275.65C563.323,3275.59 563.689,3275.67 563.988,3275.88C565.824,3277.15 568.348,3277.15 570.187,3275.88C570.485,3275.67 570.852,3275.59 571.208,3275.65C571.564,3275.72 571.879,3275.92 572.086,3276.22L576.743,3282.95C577.171,3283.57 577.017,3284.41 576.399,3284.84ZM567.088,3268.67C568.591,3268.67 569.815,3269.89 569.815,3271.39C569.815,3272.9 568.591,3274.12 567.088,3274.12C565.584,3274.12 564.361,3272.9 564.361,3271.39C564.361,3269.89 565.584,3268.67 567.088,3268.67ZM552.142,3273.99C551.44,3273.99 550.844,3273.45 550.785,3272.74C550.508,3269.4 551.245,3266.11 552.916,3263.21C554.587,3260.32 557.071,3258.03 560.096,3256.61C560.776,3256.28 561.589,3256.58 561.912,3257.26L565.408,3264.65C565.563,3264.98 565.582,3265.36 565.459,3265.7C565.337,3266.04 565.083,3266.32 564.757,3266.47C563.745,3266.95 562.918,3267.71 562.363,3268.67C561.809,3269.63 561.564,3270.72 561.657,3271.84C561.688,3272.2 561.573,3272.56 561.339,3272.84C561.106,3273.11 560.771,3273.28 560.411,3273.31L552.255,3273.98C552.217,3273.99 552.18,3273.99 552.142,3273.99ZM583.389,3272.74C583.33,3273.45 582.734,3273.99 582.032,3273.99C581.994,3273.99 581.957,3273.99 581.919,3273.98L573.763,3273.31C573.402,3273.28 573.068,3273.11 572.835,3272.83C572.601,3272.56 572.486,3272.2 572.517,3271.84C572.61,3270.72 572.365,3269.63 571.811,3268.67C571.256,3267.71 570.429,3266.95 569.418,3266.47C569.09,3266.32 568.837,3266.04 568.715,3265.7C568.592,3265.36 568.611,3264.98 568.766,3264.65L572.263,3257.26C572.585,3256.58 573.398,3256.28 574.078,3256.61C577.103,3258.03 579.587,3260.32 581.258,3263.21C582.929,3266.11 583.666,3269.4 583.389,3272.74Z" style="fill:url(#_Linear4);"/>
            </g>
        </g>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-1.00704e-13,-548.206,548.206,-1.00704e-13,11219.8,628.505)"><stop offset="0" style="stop-color:rgb(105,105,105);stop-opacity:1"/><stop offset="0.02" style="stop-color:rgb(158,158,158);stop-opacity:1"/><stop offset="0.05" style="stop-color:rgb(208,208,208);stop-opacity:1"/><stop offset="0.12" style="stop-color:rgb(229,229,229);stop-opacity:1"/><stop offset="0.2" style="stop-color:rgb(171,171,171);stop-opacity:1"/><stop offset="0.29" style="stop-color:rgb(109,109,109);stop-opacity:1"/><stop offset="0.51" style="stop-color:rgb(79,79,79);stop-opacity:1"/><stop offset="0.74" style="stop-color:rgb(105,105,105);stop-opacity:1"/><stop offset="0.82" style="stop-color:rgb(165,165,165);stop-opacity:1"/><stop offset="0.89" style="stop-color:rgb(229,229,229);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(105,105,105);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear2" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.05245e-15,-39.3447,17.1877,2.40917e-15,33148.3,4418.96)"><stop offset="0" style="stop-color:rgb(63,63,63);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(63,63,63);stop-opacity:0"/></linearGradient>
        <linearGradient id="_Linear3" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(3.18408e-15,52,-52,3.18408e-15,33371.3,4513.8)"><stop offset="0" style="stop-color:rgb(143,143,143);stop-opacity:1"/><stop offset="0.18" style="stop-color:rgb(168,168,168);stop-opacity:1"/><stop offset="0.39" style="stop-color:rgb(175,175,175);stop-opacity:1"/><stop offset="0.72" style="stop-color:rgb(160,160,160);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(111,111,111);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear4" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.81388e-15,-29.1898,29.623,1.78736e-15,566.829,3287.05)"><stop offset="0" style="stop-color:rgb(231,160,0);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(255,232,118);stop-opacity:1"/></linearGradient>
    </defs>
</svg>
