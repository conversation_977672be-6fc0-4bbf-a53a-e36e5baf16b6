/*
 * @Author: LuXiaof<PERSON>
 * @Date: 2020-06-10 13:38:42
 * @LastEditors: LuXiaofu
 * @LastEditTime: 2020-07-14 13:46:41
 * @Description: file content
 */
/**
 * <AUTHOR>
 * @description
 * @date 2020/5/18
 */
(function () {
  /**
   * 设置渐变色
   * @param svg {Object} 组件容器的svg标签
   * @param compId {String} 组件id(无效参数)
   * @param gradient {Object} 渐变色对象 {type:0线性，1环形,direction:0上到下，1左到右,stops:[{offset:0到1,color:颜色}]}
   * @returns {string} 渐变色url路径
   */
  let setGradient = function (svg, compId, gradient) {
    let defs = null;
    // let id = "linear_" + WisUtil.guid().substring(0, 16);
    let id = "linear_" + getRadomA();
    if (svg.select("defs").empty()) {
      defs = svg.append("defs");
    } else {
      defs = svg.select("defs");
    }
    let gradientDom = defs
      .append("linearGradient")
      .attr("id", `${id}_gradient`)
      .attr("gradientUnits", "objectBoundingBox")
      .attr("x1", "0%")
      .attr("y1", "0%")
      .attr("x2", gradient.direction === 0 ? "0%" : "100%")
      .attr("y2", gradient.direction === 0 ? "100%" : "0%");

    gradient.stops.forEach((stop) => {
      gradientDom
        .append("stop")
        .attr("offset", stop.offset)
        .style("stop-color", stop.color);
    });

    return `url(#${id}_gradient)`;
  };

  let getRadomA = function () {
    var returnStr = "",
      range = 13,
      arr = [
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n",
        "o",
        "p",
        "q",
        "r",
        "s",
        "t",
        "u",
        "v",
        "w",
        "x",
        "y",
        "z",
        "A",
        "B",
        "C",
        "D",
        "E",
        "F",
        "G",
        "H",
        "I",
        "J",
        "K",
        "L",
        "M",
        "N",
        "O",
        "P",
        "Q",
        "R",
        "S",
        "T",
        "U",
        "V",
        "W",
        "X",
        "Y",
        "Z",
      ];

    for (var i = 0; i < range; i++) {
      var index = Math.round(Math.random() * (arr.length - 1));
      returnStr += arr[index];
    }
    return returnStr;
  };

  /**
   * 通过属性名称获取属性字典列表中的属性对象
   * @param optionName {string} 属性名称
   * @param optionDic {json} 属性字典
   * @returns {null}
   */
  let findPropertyDictionary = (optionName, optionDic) => {
    let nameList = optionName.split(".");
    let res = null;
    for (let i = 0; i < nameList.length; i++) {
      if (i === 0) {
        res = findPropertyDictionaryByName(optionDic, nameList[i]);
      } else {
        res = findPropertyDictionaryByName(res.children, nameList[i]);
      }
    }
    return res;
  };

  let findPropertyDictionaryByName = (list, name) => {
    for (let i = 0; i < list.length; i++) {
      if (list[i].name === name) {
        return list[i];
      }
    }
  };

  /**
   * 获取svg形状的path路径
   * @param type {number} 形状类型 0：无 1： 圆 2：三角 3：矩形 4：大头针
   * @returns {string}
   */
  let getSymbol = (type) => {
    switch (type) {
      case "circle":
        //圆
        return "M 1 0 A 1 1 0 1 0 1 0.0001";
      case "triangle":
        //三角
        return "M 0 -1 L 1 1 L -1 1 Z";
      case "rect":
        //矩形
        return "M -1 -1 L 1 -1 L 1 1 L -1 1 L -1 -1 Z";
      case "pin":
        //大头针
        return "M -0.5421 -0.8857 A 0.6 0.6 0 1 1 0.5421 -0.8857 C 0.3878 -0.5605 0 -0.42 0 0 C 0 -0.42 -0.3878 -0.5605 -0.5421 -0.8857 Z";
      default:
        //默认：无
        return "";
    }
  };

  /**
   *
   * @param {json} json 从ws上收到的数据
   * @param {array} keys 需要截取的字段数组
   * @param {array} cutNums 需要截取的位数数组
   */
  let cutDataDemical = (json, keys, cutNums) => {
    let data = JSON.parse(JSON.parse(json.body).data);
    let body = JSON.parse(json.body);
    for (let i = 0; i < keys.length; i++) {
      data.map((d) => (d[keys[i]] = Number(d[keys[i]]).toFixed(cutNums[i])));
    }
    body.data = JSON.stringify(data);
    json.body = JSON.stringify(body);
    return json;
  };

  /**
   * json数组转树形结构
   * @param {Array} arr json数组
   * @example [{
        name1: 'a',
        name2: 'b',
        name3: 'c',
        value: 1,
      },{
        name1: 'a',
        name2: 'b',
        name3: 'd',
        value: 2,
      },{
        name1: 'e',
        name2: 'f',
        name3: 'g',
        value: 3,
      },{
        name1: 'e',
        name2: 'f',
        name3: 'h',
        value: 4,
      }]
   * @returns 树形结构
   * @example {
        name: 'root',
        children: [{
          name: 'a',
          children: [{
            name: 'b',
            children: [{
              name: 'c',
              value: 1
            },{
              name: 'd',
              value: 2
            }]
          }]
        },{
          name: 'e',
          children: [{
            name: 'f',
            children: [{
              name: 'g',
              value: 3
            },{
              name: 'h',
              value: 4
            }]
          }]
        }]
      }
   */
  let jsonToTree = (arr) => {
    const obj = {};
    const res = [];
    const keyList = arr.map((d) => Object.keys(d).length - 1);
    for (let i = 0; i < arr.length; i++) {
      for (let j = 0; j <= keyList[i]; j++) {
        const item = arr[i][`name${j + 1}`];
        if (!obj[item]) {
          obj[item] = {
            name: item,
            children: [],
          };
        }
        if (j > 0) {
          const parent = obj[arr[i][`name${j}`]];
          if (parent) {
            if (parent.children === undefined) debugger;
            if (parent.children.indexOf(obj[item]) < 0) {
              if (item === undefined) {
                parent.value = arr[i].value;
              } else {
                parent.children.push(obj[item]);
              }
            }
          }
        } else {
          if (res.indexOf(obj[item]) < 0) {
            res.push(obj[item]);
          }
        }
      }
    }
    return {
      name: "root",
      children: res,
    };
  };

  /**
   * 坐标转换
   * @param {'bd09togcj02'|'gcj02tobd09'|'wgs84togcj02'|'gcj02towgs84'} method 坐标系转化AtoB的字符串
   * @param {number} lng 经度
   * @param {number} lat 纬度
   * @returns 经纬度json {lng:number,lat:number}
   */
  let coordinateTransfrom = (method, lng, lat) => {
    const xPI = (3.14159265358979324 * 3000.0) / 180.0;
    const PI = 3.1415926535897932384626;
    const a = 6378245.0;
    const ee = 0.00669342162296594323;

    const transformlat = function (lng, lat) {
      let ret =
        -100.0 +
        2.0 * lng +
        3.0 * lat +
        0.2 * lat * lat +
        0.1 * lng * lat +
        0.2 * Math.sqrt(Math.abs(lng));
      ret +=
        ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) *
          2.0) /
        3.0;
      ret +=
        ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) *
          2.0) /
        3.0;
      ret +=
        ((160.0 * Math.sin((lat / 12.0) * PI) +
          320 * Math.sin((lat * PI) / 30.0)) *
          2.0) /
        3.0;
      return ret;
    };

    const transformlng = function (lng, lat) {
      let ret =
        300.0 +
        lng +
        2.0 * lat +
        0.1 * lng * lng +
        0.1 * lng * lat +
        0.1 * Math.sqrt(Math.abs(lng));
      ret +=
        ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) *
          2.0) /
        3.0;
      ret +=
        ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) *
          2.0) /
        3.0;
      ret +=
        ((150.0 * Math.sin((lng / 12.0) * PI) +
          300.0 * Math.sin((lng / 30.0) * PI)) *
          2.0) /
        3.0;
      return ret;
    };

    // 判断是否在国内，不在国内则不做偏移
    const outOfChina = function (lng, lat) {
      return (
        lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271 || false
      );
    };

    switch (method) {
      // BD-09转GCJ-02
      case "bd09togcj02": {
        const xPI = (3.14159265358979324 * 3000.0) / 180.0;
        const x = lng - 0.0065;
        const y = lat - 0.006;
        const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * xPI);
        const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * xPI);
        const ggLng = z * Math.cos(theta);
        const ggLat = z * Math.sin(theta);

        return { lng: ggLng, lat: ggLat };
      }
      // GCJ-02转BD-09
      case "gcj02tobd09": {
        const z =
          Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * xPI);
        const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * xPI);
        const bdLng = z * Math.cos(theta) + 0.0065;
        const bdLat = z * Math.sin(theta) + 0.006;
        return { lng: bdLng, lat: bdLat };
      }
      // WGS84转GCj02
      case "wgs84togcj02": {
        if (outOfChina(lng, lat)) {
          return { lng: lng, lat: lat };
        }

        let dLat = transformlat(lng - 105.0, lat - 35.0);
        let dLng = transformlng(lng - 105.0, lat - 35.0);
        const radLat = (lat / 180.0) * PI;
        let magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        let sqrtmagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
        dLng = (dLng * 180.0) / ((a / sqrtmagic) * Math.cos(radLat) * PI);
        const mgLat = lat + dLat;
        const mglng = lng + dLng;
        return { lng: mglng, lat: mgLat };
      }
      // GCJ02转WGS84
      case "gcj02towgs84": {
        if (outOfChina(lng, lat)) {
          return { lng: lng, lat: lat };
        }

        let dLat = transformlat(lng - 105.0, lat - 35.0);
        let dLng = transformlng(lng - 105.0, lat - 35.0);
        const radLat = (lat / 180.0) * PI;
        let magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        const sqrtmagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
        dLng = (dLng * 180.0) / ((a / sqrtmagic) * Math.cos(radLat) * PI);
        const mgLat = lat + dLat;
        const mglng = lng + dLng;
        return { lng: lng * 2 - mglng, lat: lat * 2 - mgLat };
      }
    }
  };

  let imgPathToBase64 = (path, type) => {
    return new Promise((resolve, reject) => {
      fetch(path)
        .then((response) => {
          if (type === "svg") {
            return response.text();
          }
          return response.blob();
        })
        .then((blob) => {
          if (type === "svg") {
            const base64Data = btoa(blob);
            const base64URL = "data:image/svg+xml;base64," + base64Data;
            resolve(base64URL);
          } else {
            const reader = new FileReader();
            reader.onloadend = function () {
              const base64Data = reader.result;
              // 替换 imageElement 的 xlink:href 或 href 属性为 base64Data
              resolve(base64Data);
              // 或者 imageElement.setAttribute("href", base64Data);
            };
            reader.readAsDataURL(blob);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // 检查元素是否在视窗及mainSVG内部
  /**
   * @param {HTMLElements} nodes 需要判断的节点
   * @param {HTMLElement} container 界限元素
   * @param {Boolean} preLoadExternal 是否外面预加载一圈
   * @param {Function} callback 判断超界的回调 nodes的item都会单独触发  参数为nodes的item和isOut(boolean)
   */
  let checkView = (params) => {
    const { nodes, container, preLoadExternal, callback } = params;
    // callback 每个node检查出 是否在 视窗及mainSVG内部 结果后的回调
    let containerRect = container.getBoundingClientRect();
    let checkRect = [
      containerRect.left,
      containerRect.top,
      containerRect.left + containerRect.width,
      containerRect.top + containerRect.height,
    ];
    const viewWidth =
      window.innerWidth ||
      document.documentElement.clientWidth ||
      document.body.clientWidth;
    const viewHeight =
      window.innerHeight ||
      document.documentElement.clientHeight ||
      document.body.clientHeight;

    nodes.forEach((d, i) => {
      let { top, left, width, height } = d.getBoundingClientRect();
      // 是否在mainsvg外
      let isOut =
        top > checkRect[3] + (preLoadExternal ? height : 0) ||
        left > checkRect[2] + (preLoadExternal ? width : 0) ||
        left < checkRect[0] - (width + (preLoadExternal ? width : 0)) ||
        top < checkRect[1] - (height + (preLoadExternal ? height : 0));
      // 是否在视窗外
      isOut =
        isOut ||
        left < -(width + (preLoadExternal ? width : 0)) ||
        top < -(height + (preLoadExternal ? height : 0)) ||
        top > viewHeight + (preLoadExternal ? height : 0) ||
        left > viewWidth + (preLoadExternal ? width : 0);
      callback(d, isOut);
    });
  };

  let WisCompUtil = {};
  WisCompUtil.setGradient = setGradient;
  WisCompUtil.findPropertyDictionary = findPropertyDictionary;
  WisCompUtil.getSymbol = getSymbol;
  WisCompUtil.cutDataDemical = cutDataDemical;
  WisCompUtil.jsonToTree = jsonToTree;
  WisCompUtil.getRadomA = getRadomA;
  WisCompUtil.coordinateTransfrom = coordinateTransfrom;
  WisCompUtil.imgPathToBase64 = imgPathToBase64;
  WisCompUtil.checkView = checkView;
  window.WisCompUtil = WisCompUtil;
})();
