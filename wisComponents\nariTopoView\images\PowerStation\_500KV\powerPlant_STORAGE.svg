<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 101 101" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1,0,0,1,-39186,-3827)">
        <g id="储能" transform="matrix(-0.760297,-9.31095e-17,7.47291e-17,-0.61021,49808.9,6825.07)">
            <g>
                <g transform="matrix(0.239141,0,0,0.29796,11221.4,4724.87)">
                    <g transform="matrix(-2.26939,2.77921e-16,-2.79749e-16,-2.28432,86556.4,10530.6)">
                        <path d="M33314.3,4358.05C33314.3,4345.71 33304.2,4335.69 33291.8,4335.69L33097.7,4335.69C33085.3,4335.69 33075.2,4345.71 33075.2,4358.05L33075.2,4550.85C33075.2,4563.18 33085.3,4573.19 33097.7,4573.19L33291.8,4573.19C33304.2,4573.19 33314.3,4563.18 33314.3,4550.85L33314.3,4358.05Z" style="fill:rgb(34,32,32);"/>
                    </g>
                    <path d="M10949.4,578.483C10949.4,607.05 10972.6,630.242 11001.1,630.242L11447.6,630.242C11476.2,630.242 11499.4,607.05 11499.4,578.483L11499.4,132.001C11499.4,103.434 11476.2,80.242 11447.6,80.242L11001.1,80.242C10972.6,80.242 10949.4,103.434 10949.4,132.001L10949.4,578.483ZM10967.7,570.816C10967.7,593.478 10986.1,611.878 11008.8,611.878L11439.9,611.878C11462.6,611.878 11481,593.478 11481,570.816L11481,139.668C11481,117.006 11462.6,98.606 11439.9,98.606L11008.8,98.606C10986.1,98.606 10967.7,117.006 10967.7,139.668L10967.7,570.816Z" style="fill:url(#_Linear1);"/>
                    <g transform="matrix(1,-7.39557e-32,-4.93038e-32,1,0.0323855,0.0935452)">
                        <g>
                            <g transform="matrix(-3.24542,3.97449e-16,-1.48168e-16,-1.20989,118802,5623.36)">
                                <path d="M33225.6,4428.83L33069.2,4428.83L33081.4,4372.83L33215.2,4372.83L33225.6,4428.83Z" style="fill:url(#_Linear2);"/>
                            </g>
                            <g transform="matrix(-5.06102,6.19796e-16,-3.89891e-16,-3.1837,180082,14637.5)">
                                <path d="M33414.6,4514.8L33314.3,4514.8L33314.3,4550.9C33314.3,4559.12 33317.8,4565.8 33322,4565.8L33406.8,4565.8C33411.1,4565.8 33414.6,4559.06 33414.6,4550.76L33414.6,4514.8Z" style="fill:url(#_Linear3);"/>
                            </g>
                            <g transform="matrix(-2.2686,2.77824e-16,-2.9285e-16,-2.3913,93717.3,10193.4)">
                                <ellipse cx="36362.9" cy="4152.16" rx="111.824" ry="0.255" style="fill:white;"/>
                            </g>
                        </g>
                        <g transform="matrix(-9.15345,1.12097e-15,-1.12097e-15,-9.15345,314625,42399.3)">
                            <g transform="matrix(14,0,0,14,33166.2,4617.42)">
                            </g>
                            <text x="33127.5px" y="4617.42px" style="font-family:'MicrosoftYaHeiUI-Bold', 'Microsoft YaHei UI', sans-serif;font-weight:700;font-size:14px;fill:rgb(29,29,29);">3<tspan x="33137.5px 33147.6px 33157.6px " y="4617.42px 4617.42px 4617.42px ">254</tspan></text>
                        </g>
                    </g>
                </g>
                <g transform="matrix(-1.31528,2.00693e-16,-1.61075e-16,-1.63878,13972,4913.19)">
                    <use xlink:href="#_Image4" x="20.55" y="11.982" width="59px" height="47px"/>
                </g>
            </g>
        </g>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-1.00704e-13,-548.206,548.206,-1.00704e-13,11219.8,628.505)"><stop offset="0" style="stop-color:rgb(105,105,105);stop-opacity:1"/><stop offset="0.02" style="stop-color:rgb(158,158,158);stop-opacity:1"/><stop offset="0.05" style="stop-color:rgb(208,208,208);stop-opacity:1"/><stop offset="0.12" style="stop-color:rgb(229,229,229);stop-opacity:1"/><stop offset="0.2" style="stop-color:rgb(171,171,171);stop-opacity:1"/><stop offset="0.29" style="stop-color:rgb(109,109,109);stop-opacity:1"/><stop offset="0.51" style="stop-color:rgb(79,79,79);stop-opacity:1"/><stop offset="0.74" style="stop-color:rgb(105,105,105);stop-opacity:1"/><stop offset="0.82" style="stop-color:rgb(165,165,165);stop-opacity:1"/><stop offset="0.89" style="stop-color:rgb(229,229,229);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(105,105,105);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear2" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.05245e-15,-39.3447,17.1877,2.40917e-15,33148.3,4418.96)"><stop offset="0" style="stop-color:rgb(63,63,63);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(63,63,63);stop-opacity:0"/></linearGradient>
        <linearGradient id="_Linear3" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(3.18408e-15,52,-52,3.18408e-15,33371.3,4513.8)"><stop offset="0" style="stop-color:rgb(143,143,143);stop-opacity:1"/><stop offset="0.18" style="stop-color:rgb(168,168,168);stop-opacity:1"/><stop offset="0.39" style="stop-color:rgb(175,175,175);stop-opacity:1"/><stop offset="0.72" style="stop-color:rgb(160,160,160);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(111,111,111);stop-opacity:1"/></linearGradient>
        <image id="_Image4" width="59px" height="47px" xlink:href="data:image/png;base64,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"/>
    </defs>
</svg>
