const formatStyle = (styleStr) => {
  let style = {};
  try {
    style = JSON.parse(styleStr);
  } catch (error) {
    console.error("Failed to parse style string", error);
  }
  let styleContent = "";
  for (const key in style) {
    styleContent += `${key}:${style[key]};`;
  }
  return styleContent;
};
const getStyleObj = (styleStr) => {
  let style = {};
  try {
    style = JSON.parse(styleStr);
  } catch (error) {
    console.error("Failed to parse style string", error);
  }
  return style;
};
let timer;
let flashIntervalId;
function blinkElement(element, duration = 5000, blinkInterval = 500, blinkCount = 5) {
  let count = 0;
  if (timer) {
    clearTimeout(timer);
    clearInterval(flashIntervalId);
    count = 0;
  }
  // 闪烁动画效果
  function flash() {
    element.attr("opacity", () => {
      const currentOpacity = element.attr("opacity");
      return currentOpacity === "1" ? "0" : "1"; // 切换透明度
    });
    count++;
    // 停止闪烁条件
    if (count >= blinkCount) {
      clearInterval(flashIntervalId);
    }
  }
  // 每个时间间隔触发一次闪烁
  flashIntervalId = setInterval(flash, blinkInterval);
  // 在指定的持续时间后停止闪烁
  timer = setTimeout(() => {
    clearInterval(flashIntervalId);
  }, duration);
}
const prefix = "/logicEditor";
class LogicCustomComponent {
  constructor(baseComponent, componentData, options) {
    this.svg = null;
    this.path = null;
    this.drawNodeText = (data) => {
      const textStyle = getStyleObj(data.textStyles);
      const nodeStyle = getStyleObj(data.nodeStyles);
      const fontSize = data.fontSize || 12;
      const [w, h] = data.nodeSize.split("*").map((item) => +item);
      this.baseComponent
        .append("foreignObject")
        .attr("width", w)
        .attr("height", h)
        .attr("transform", `translate(${textStyle.x || 0}, ${textStyle.y || 0})`)
        .append("xhtml:div")
        .attr("class", "topo-text-node")
        .attr("style", () => {
          const style = {
            ...textStyle,
            display: nodeStyle.display || "block",
            "justify-content": nodeStyle["justify-content"] || "unset",
            "align-items": nodeStyle["align-items"] || "unset",
            width: `${w}px`,
            height: `${h}px`,
            "font-size": `${fontSize}px`,
            color: data.fontColor || "unset",
            "text-align": textStyle["text-align"] || "left"
          };
          let styleStr = "";
          for (const key in style) {
            const value = style[key];
            styleStr += `${key}:${value};`;
          }
          return styleStr;
        })
        .text(data.nodeText);
    };
    this.baseComponent = baseComponent;
    this.componentData = componentData;
    this.options = options || {
      isDrawImage: true
    };
    this.scriptFunction = new Function(this.componentData?.script || "")();
    this.updataedValue = {};
    if (this.scriptFunction && this.scriptFunction?.draw) {
      this.scriptFunction.draw(baseComponent, componentData);
    } else {
      this.draw();
    }
  }
  drawNode() {
    const { isDrawImage } = this.options;
    const componentData = this.componentData;
    const [w, h] = componentData.nodeSize.split("*").map((item) => +item);
    // 判断componentData.nodeType是否包含text
    if (componentData.nodeType.includes("text")) {
      this.drawNodeText(componentData);
    } else {
      if (isDrawImage) {
        const nodeStyle = getStyleObj(componentData.nodeStyles);
        const imgUrl = componentData.objImg || nodeStyle.image || "";
        this.baseComponent
          .append("image")
          .attr("xlink:href", prefix + imgUrl)
          .attr("width", w)
          .attr("height", h);
        componentData.nodeText && this.drawNodeText(componentData);
      } else {
        this.svg = this.baseComponent
          .html(componentData.svgData || "")
          .select("svg")
          .datum(componentData)
          .attr("width", w)
          .attr("height", h);
      }
    }
  }
  drawLink() {
    const componentData = this.componentData;
    const style = formatStyle(componentData.linkStyles);
    this.path = this.baseComponent
      .append("path")
      .datum(componentData)
      .attr("d", (d) => d.linkPath)
      .attr("style", style);
  }
  draw() {
    if (!this.componentData) return;
    // 判断componentData是否为INode格式
    if ("nodeId" in this.componentData) {
      this.drawNode();
    } else {
      // 拿到componentData.linkPath ，创建path
      this.drawLink();
    }
  }
  update(data) {
    if (!data) return;
    if (!Array.isArray(data)) {
      console.error("data should be an array");
      return;
    }
    if (this.options.isDebug) {
      console.log("update data:", data);
    }
    if (this.scriptFunction && this.scriptFunction.update) {
      this.scriptFunction.update(data);
    } else {
      data.forEach((item) => {
        //   接收数据，执行脚本
        this.updateSvg(item);
      });
    }
  }
  updateSvg(item) {
    // if (this.updataedValue[item.column] === item.value) {
    //   console.log("数据相同，节点未更新");
    //   return;
    // }
    this.updataedValue[item.column] = item.value;
    if (item.dataType === "text") {
      this.updateNodeText(item);
      return;
    }
    if (!this.componentData || !item.conditions) return;
    item.conditions.forEach((condition) => {
      const shouldUpdate = this.evaluateCondition(item.value, item.dataType, condition);
      if (shouldUpdate) {
        if ("nodeId" in this.componentData) {
          // if (item.domId) {
          //   this.applyStyle(item.domId, condition.tagName || "", condition.style);
          // } else {
          // 更新整体样式
          this.applySvgStyle(condition.style);
          // }
        } else {
          this.applyPathStyle(condition.style);
        }
      }
    });
  }
  evaluateCondition(value, dataType = "number", condition) {
    const { comparison, threshold } = condition;
    if (dataType === "text") {
      return true;
    } else if (dataType === "number") {
      if (comparison && threshold !== undefined) {
        return this.compareValues(+value, comparison, threshold);
      }
      return true;
    } else if (dataType === "boolean") {
      if (comparison && threshold !== undefined) {
        return this.compareValues(value ? 1 : 0, comparison, threshold ? 1 : 0);
      }
      return Boolean(value); // Update based on boolean value
    }
    return false;
  }
  compareValues(value, comparison, threshold) {
    if (typeof value === "number" && typeof threshold === "number") {
      switch (comparison) {
        case ">":
        case "greater":
          return value > threshold;
        case "<":
        case "less":
          return value < threshold;
        case "=":
        case "equal":
          return value === threshold;
        case ">=":
        case "greaterEqual":
          return value >= threshold;
        case "<=":
        case "lessEqual":
          return value <= threshold;
        default:
          return false;
      }
    } else if (typeof value === "string" && typeof threshold === "string") {
      switch (comparison) {
        case "=":
        case "equal":
          return value === threshold;
        default:
          return false;
      }
    }
    return false;
  }
  updateNodeText(data) {
    this.baseComponent.select("text").text(data.value);
  }
  applyPathStyle(style) {
    switch (style.type) {
      case "fill":
        this.path?.style("fill", style.data || "#000000");
        break;
      case "stroke":
        this.path?.style("stroke", style.data || "#000000");
        break;
      case "stroke-width":
        this.path?.style("stroke-width", style.data + "px");
        break;
      case "scale":
    }
  }
  applySvgStyle(style) {
    switch (style.type) {
      case "background":
        {
          if ("nodeId" in this.componentData) {
            const { isDrawImage } = this.options;
            const [w, h] = this.componentData.nodeSize.split("*").map((item) => +item);
            if (isDrawImage) {
              this.baseComponent
                .select("image")
                .attr("xlink:href", prefix + style.data || this.componentData.objImg || "")
                .attr("width", w)
                .attr("height", h);
            } else {
              const svgCon = this.baseComponent.html(
                style.svgData || this.componentData.svgData || ""
              );
              this.svg = svgCon
                .select("svg")
                .datum(this.componentData)
                .attr("width", w)
                .attr("height", h);
            }
          }
        }
        break;
      case "text":
        this.baseComponent.select(".topo-text-node").text(style.data);
        break;
      case "animation":
        this.applyAnimation(style.data);
        // element.style("stroke-width", (style.data as number) + "px");
        break;
      default:
        break;
    }
  }
  applyStyle(domId, tagName, style) {
    if (!style || style.data === null || style.data === undefined) return;
    if (!domId) {
      switch (style.type) {
        case "background":
          this.svg?.html(style.svgData || "");
          break;
        default:
          break;
      }
    }
    const element = this.svg?.select(`#${domId}`).select(`${tagName}`);
    if (element) {
      try {
        switch (style.type) {
          case "fill":
          case "stroke":
            element.style(style.type, style.data || "#000000");
            break;
          case "border":
            element.style("stroke", style.data + "px");
            break;
          case "color":
            element.style("fill", style.data);
            break;
          case "scale":
            element.attr("transform", `scale(${style.data})`);
            break;
          case "width":
            element.attr("width", style.data);
            break;
          case "height":
            element.attr("height", style.data);
            break;
          case "radius":
            element.attr("r", style.data);
            break;
          case "display":
            element.style("display", style.data || "block");
            break;
          case "text":
            element.text(style.data);
            break;
          case "opacity":
            element.style("opacity", style.data + "px");
            break;
          case "transform":
            element.attr("transform", style.data);
            break;
          case "stroke-width":
            element.style("stroke-width", style.data + "px");
            break;
          default:
            break;
        }
      } catch (error) {
        console.error(
          `Failed to apply style: ${style.type} with data: ${style.data} on element: ${domId}`,
          error
        );
      }
    }
  }
  applyAnimation(animation) {
    switch (animation) {
      case "blink":
        blinkElement(this.baseComponent);
        break;
      default:
        break;
    }
  }
}
