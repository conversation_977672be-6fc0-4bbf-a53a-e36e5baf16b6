<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <link rel="stylesheet" type="text/css" href="v1.0/nariTopoView.css" />
    <link
      rel="stylesheet"
      type="text/css"
      href="../htmlViewer/v1.0/htmlViewer.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="../baseComponents/css/fonts.css"
    />
  </head>

  <body style="background-color: #000000">
    <button onclick="switchTo220()">220全景</button>
    <button onclick="close500Link()">关闭500连线</button>
    <button onclick="open500Link()">打开500连线</button>
    <button onclick="switchTo500()">500KV全景图</button>
    <select id="hotSpot"></select>
    <button onclick="flyToHotspots()">跳转热点</button>
    <button onclick="switchLinkBolckMode()">线框模式</button>
    <button onclick="switchDivideLink(false)">显示分区联络线</button>
    <button onclick="switchDivideLink(true)">隐藏分区联络线</button>
    <button onclick="switchDivideFill(true)">显示分区填充</button>
    <button onclick="switchDivideFill(false)">隐藏分区填充</button>
    <input type="text" id="eqWink" />
    <button onclick="equipmentTwinkle()">设备闪烁</button>
    <input type="text" id="eqWinkClose" />
    <button onclick="closeEquipmentTwinkle()">关闭设备闪烁</button>
    <button onclick="crossingChannel(true)">显示过江通道</button>
    <button onclick="crossingChannel(false)">隐藏过江通道</button>
    <button onclick="amplify(200)">放大</button>
    <button onclick="reduce(200)">缩小</button>
    <button onclick="update()">更新</button>
    <button onclick="update2()">更新22</button>
    <div id="con" style="width: 9800px; height: 7033px; zoom: 0.25"></div>
    <script src="../../wisVisual/libs/d3.v5.min.js"></script>
    <script src="../../wisVisual/libs/anime.min.js"></script>
    <script src="../../wisVisual/libs/jquery.min.js"></script>
    <script src="../../wisVisual/libs/lodash.min.js"></script>
    <script src="../../wisVisual/Util/CompUtil.js"></script>
    <script src="../../wisVisual/Util/Util.js"></script>
    <script src="../../wisVisual/libs/reconnecting-websocket.min.js"></script>
    <script src="../../wisVisual/libs/stomp.js"></script>
    <script src="../../wisVisual/API/API.js"></script>
    <script src="../base/optionType.js"></script>
    <script src="../base/componentBase.js"></script>
    <script src="../htmlViewer/v1.0/htmlViewer.js"></script>
    <script src="v1.0/nariTopoView.js"></script>
    <!-- <script src="v1.0/nariTopoView-2024-11-25.js"></script> -->
    <script src="../digitGearBoard/v1.0/digitGearBoard.js"></script>
    <script>
      let mapId = getMapId("mapId");

      function getMapId() {
        let url = window.location.href;
        let obj = {};
        if (url.indexOf("?") > 0) {
          let params = url.split("?")[1].split("&");
          params.forEach((val) => {
            obj[val.split("=")[0]] = val.split("=")[1];
          });
        }

        let { mapId } = obj;

        return mapId;
      }

      // window.initAPIPromise.then(() => {
      draw();
      // });

      function draw() {
        window.currentSceneId = "225";

        if (!mapId) {
          //alert('请传入mapId');
          //return;
          mapId = "AY8E52AxU3"; //长图
          //mapId = 'JsQAcFdJYf';//方图
        }

        window.comp = new NariTopoView(
          "858",
          "123",
          document.getElementById("con"),
          0,
          {
            property: {
              basic: {
                frame: [0, 0, 10860, 4000],
                needSync: false,
              },
              viewSetting: {
                loadMapId: mapId,
                w: 10860,
                h: 4000,
              },
              isTest: true,
            },
          }
        );
      }

      function amplify(val) {
        window.comp.toUp(val);
      }
      function reduce(val) {
        window.comp.toDown(val);
      }

      function switchTo220() {
        window.comp.switchTo220();
      }

      function close500Link() {
        window.comp.close500Link();
      }

      function open500Link() {
        window.comp.open500Link();
      }
      function switchTo500() {
        window.comp.switchTo500();
      }

      function flyToHotspots() {
        let hotSpot = document.getElementById("hotSpot").value;
        window.comp.flyToHotspots(hotSpot);
      }
      function switchLinkBolckMode() {
        window.comp.switchLinkBolckMode();
      }
      function switchDivideLink(status) {
        window.comp.switchDivideLink(status);
      }
      function switchDivideFill(status) {
        window.comp.switchDivideFill(status);
      }

      function equipmentTwinkle() {
        let eqWink = document.getElementById("eqWink").value;
        window.comp.equipmentTwinkle(eqWink);
      }

      function closeEquipmentTwinkle() {
        let eqWinkClose = document.getElementById("eqWinkClose").value;
        window.comp.closeEquipmentTwinkle(eqWinkClose);
      }

      function crossingChannel(status) {
        window.comp.crossingChannel(status);
      }

      function update() {
        d3.json("./data.json").then(({ data }) => {
          window.comp._update(data);
        });
      }

      function update2() {
        d3.json("./data1.json").then(({ data }) => {
          window.comp._update(data);
        });
      }

      let interval = setInterval(() => {
        if (window.comp.hotspotsList.length > 0) {
          window.comp.hotspotsList.map((d) => {
            $("#hotSpot").append(
              `<option value='${d.hotspotId}'>${d.hotspotName}</option>`
            );
          });
          clearInterval(interval);
        }
      }, 500);
      //let fl = true;
      //setInterval(() => {
      // fl = !fl
      // if (fl) {
      // update()
      // } else {
      // update2()
      // }

      // }, 5000)

      //setInterval(() => {
      //window.location.reload()
      //}, 60000)
    </script>
  </body>
</html>
