/**
 * <AUTHOR>
 * @description 组件基类
 * @version 1.0
 * @date 2020/1/3
 */
class ComponentBase {
  /**
   * @constructor 组件基类构造函数
   * @param id {string} 组件id
   * @param code {string} 组件编码
   * @param container {HTMLElement} 组件容器
   * @param workMode {number} 组件工作模式 0为测试 1为大屏 2为配置工具 3为控制中心 4为单一组件调试
   * @param option {Object} 组件属性
   * @param useDefaultOpt {boolean} 是否启用默认值
   */
  constructor(
    id,
    code,
    container,
    workMode,
    option = {},
    useDefaultOpt = true
  ) {
    this.id = id;
    this.code = code;
    this.container = container;
    this.workMode = workMode;
    this.useDefaultOpt = useDefaultOpt;
    this.propertyDictionary = [];
    //是否订阅了跨屏同步动画，默认是没开启
    this.isSyncAnime = false;
    this._initProperty();
    this._initEvents();
    this._initConf(option);
    this._setupDefaultValues();
  }

  _initConf(option) {
    this.property = $.extend(true, this.property, option.property);
    if (typeof option.compDataBind === "string") {
      try {
        option.compDataBind = JSON.parse(option.compDataBind);
      } catch (error) {
        console.error("组件数据绑定类型无法json格式化");
      }
    }
    this.dataBind = $.extend(true, this.dataBind, option.compDataBind);
    this.animation = _.union(
      this.animation,
      typeof option.compAnimation === "string"
        ? JSON.parse(option.compAnimation)
        : option.compAnimation
    );
    this.script = $.extend(
      true,
      this.script,
      typeof option.compScript === "string"
        ? JSON.parse(option.compScript)
        : option.compScript
    );
    this.interact = $.extend(
      true,
      this.interact,
      typeof option.compInteract === "string"
        ? JSON.parse(option.compInteract)
        : option.compInteract
    );
    // this.compData = option.compData;
    this._foldPath = WisUtil.scriptPath(this.property.basic.className);
    this.resourceId = option.resourceId;
    this._initConfHandler();
  }

  _initConfHandler() {
    this._compScriptHandler();
  }

  /**
   * @description 组件所需常量初始化
   */
  _setupDefaultValues() {
    //组件文件夹路径
    this._foldPath = WisUtil.scriptPath(this.property.basic.className);
    //组件是否已作为数据联动时数据过滤的条件,true为不更新数据，false为更新数据
    this.hasSelectedForDataLink = false;
    //组件接收到数据后是否要更新数据
    this.onlyRecordData = true;
    //组件记录数据
    this.recordData = "";
    //平台sessionId
    this.sessionId = sessionStorage.getItem("sessionID");
    //public文件夹
    this._publicFolderPath = "";
  }

  /**
   * @description 初始化组件属性列表和属性字典
   */
  _initProperty() {
    //组件基础属性
    let property = {
      basic: {
        code: this.code,
        displayName: "",
        type: "",
        className: "",
        frame: [0, 0, 1920, 1080],
        isVisible: true,
        translateZ: true,
        needSync: false,
        isLocked: false,
        canEdit: false,
        zIndex: 0,
        //组件字体比例
        fontScale: 1,
        //是否发送数据
        isSendData: false,
        //是否有动画
        isAnimate: false,
        //是否有组件联动
        isDataLinked: false,
      },
    };

    //组件基础属性字典
    let propertyDictionary = [
      {
        name: "basic",
        displayName: "基础属性",
        children: [
          {
            name: "code",
            displayName: "组件编码",
            description: "组件编码",
            type: OptionType.string,
            show: true,
            editable: false,
          },
          {
            name: "displayName",
            displayName: "组件名称",
            description: "组件名称",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "type",
            displayName: "组件类型",
            description: "组件类型",
            type: OptionType.string,
            show: true,
            editable: false,
          },
          {
            name: "className",
            displayName: "组件类名",
            description: "组件类名",
            type: OptionType.string,
            show: true,
            editable: false,
          },
          {
            name: "frame",
            displayName: "组件大小",
            description: "组件位置以及大小",
            type: OptionType.doubleArray,
            placeholder: ["x", "y", "宽", "高"],
            show: true,
            editable: true,
          },
          {
            name: "isVisible",
            displayName: "是否可见",
            description: "组件是否可见",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "translateZ",
            displayName: "启用Z轴位移",
            description: "是否启用Z轴位移(启用分层渲染)",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "needSync",
            displayName: "是否同步",
            description: "跨屏组件是否启动事件同步",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "isLocked",
            displayName: "是否锁定",
            description: "锁定后不可修改位置及宽高",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "canEdit",
            displayName: "是否可以编辑",
            description: "是否可以编辑",
            type: OptionType.boolean,
            show: false,
            editable: true,
          },
          {
            name: "zIndex",
            displayName: "组件层级",
            description: "组件的所在画布的层级",
            type: OptionType.int,
            show: true,
            editable: true,
          },
          {
            name: "isSendData",
            displayName: "是否发送数据",
            description: "组件在接收到数据后是否发送数据",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "isAnimate",
            displayName: "是否有动画",
            description: "当前组件是否绑定动画",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "isDataLinked",
            displayName: "是否有组件联动",
            description: "当前组件是否有组件联动",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
        ],
        show: true,
        editable: true,
      },
    ];

    this._addProperty(property, propertyDictionary);
  }

  _initEvents() {
    //事件方法
    this.eventFunc = [
      {
        name: "sendData",
        displayName: "发送数据",
        params: [
          {
            name: "data",
            displayName: "数据",
          },
        ],
      },
    ];
    //可调用方法
    this.invokeFunc = [
      {
        name: "setProperty",
        displayName: "配置属性",
        params: [
          {
            name: "name",
            displayName: "属性名",
            type: "option",
          },
          {
            name: "value",
            displayName: "属性值",
            type: "optionType",
          },
        ],
      },
      {
        name: "showComponent",
        displayName: "是否显示组件",
        params: [
          {
            name: "isShow",
            displayName: "是否显示",
            type: OptionType.boolean,
          },
        ],
      },
    ];
  }

  /**
   * 组件是否显示(命令控制)
   * @param {boolean} isShow 组件是否显示内容
   */
  showComponent(isShow) {
    d3.select(this.container).style("display", isShow ? "block" : "none");
  }

  /**
   * @description 新增属性
   * @param property
   * @param propertyDictionary
   */
  _addProperty(property, propertyDictionary = []) {
    this.property = $.extend(true, this.property, property);
    if (propertyDictionary.length > 0) {
      this.propertyDictionary =
        this.propertyDictionary.concat(propertyDictionary);
    }
  }

  /**
   * @description 通过属性名称获取属性字典的对象
   * @param propertyName 属性名称
   * @returns {JSON} 属性字典对象
   */
  _findPropertyDictionary(propertyName) {
    return WisCompUtil.findPropertyDictionary(
      propertyName,
      this.propertyDictionary
    );
  }

  /**
   * @description 设置属性
   * @param {{}} property 属性列表||属性名
   * @param {any} value 属性值
   */
  setProperty(property, value = null) {
    if (value !== null) {
      property = this._propertyNameToJson(property, value);
    }
    this.property = $.extend(true, this.property, property);

    // if (this.workMode === 2) {
    this._handlePropertyChange();
    // }

    this._draw();
  }

  /**
   * 修改组件配置项属性
   * 调用setProperty方法后，某些配置项根据其他配置项内容更新而自动修改属性(包括show,editable以及属性值)
   */
  _handlePropertyChange() { }

  /**
   * @description 将string类型的propertyName转化为object
   * @param propertyName {{}} 属性名称
   * @param value {any} 属性值
   * @returns {{}}
   */
  _propertyNameToJson(propertyName, value) {
    let propertyArr = propertyName.split(".");
    let propertyObj = {};
    let idx = propertyArr.length - 1;
    while (idx > -1) {
      let temp = {};
      if (idx === propertyArr.length - 1) {
        temp[propertyArr[idx]] = value;
      } else {
        temp[propertyArr[idx]] = propertyObj;
      }
      propertyObj = temp;
      idx--;
    }
    return propertyObj;
  }

  /**
   * @description 设置数据绑定
   * @param {string} key 数据绑定的key
   * @param {JSON} value 数据绑定key的内容
   */
  setDataBind(key, value) {
    this.dataBind[key] = value;
  }

  /**
   * @description 设置组件动画
   */
  _compAnimationHandler() { }

  /**
   * @description 设置组件脚本
   */
  _compScriptHandler() {
    this.getDataScripts = [];
    this.clickScript = [];
    this.beforeDrawScripts = [];
    if (this.script.data === undefined || this.script.data.length === 0) return;
    this.script.data.forEach((scriptObj) => {
      console.log(`组件${this.id}绑定脚本${scriptObj.displayName}`);
      switch (scriptObj.trigger) {
        //点击
        case "click":
          this.clickScript.push(scriptObj.content);
          break;
        //接收数据
        case "getData":
          this.getDataScripts.push(scriptObj.content);
          break;
        //绘制组件前
        case "beforeDraw":
          this.beforeDrawScripts.push(scriptObj.content);
          break;
        //todo 其他触发方式
      }
    });

    //绑定点击事件脚本
    d3.select(this.container).on("click", () => {
      this.clickScript.forEach((s) => {
        eval(s);
      });
    });
  }

  /**
   * @description 设置组件交互
   */
  _compInteractHandler() { }

  /**
   * @description 设置组件默认数据
   * @param compData 默认数据
   */
  setCompData(compData) {
    // this.compData = compData;
    this._setDefaultData();
  }

  _setDefaultData() { }

  /**
   * @description 绘制容器样式
   */
  _draw() {
    //执行绘制组件前的脚本
    if (this.workMode !== 2) {
      this.beforeDrawScripts.forEach((s) => {
        eval(s);
      });
    }
    let d3Container = d3.select(this.container);
    d3Container
      .style("display", this.property.basic.isVisible ? "block" : "none")
      .style("z-index", this.property.basic.zIndex);
    if (this.property.basic.translateZ) {
      d3Container.style("transform", "translateZ(0)");
    }
  }

  /**
   * @description 清空组件内部的数据订阅ws以及数据同步ws
   */
  cleanup() {
    this.unsubscribeDataSource();
    if (window.wiscomWebSocket) {
      if (this.isSyncAnime) {
        this._stopAnimate();
      }
    }
    if (this.webSocket) {
      this.webSocket.close();
      window.wiscomWebSocket.webSocketList.forEach((d, index) => {
        if (d.appName == this.webSocket.appName) {
          window.wiscomWebSocket.webSocketList.splice(index, 1);
        }
      });
      // this.webSocket.heartBeatIntervaler && clearInterval(this.webSocket.heartBeatIntervaler);
      this.webSocket = null;
    }
  }

  /**
   * @description 被动加载
   * @memberof ComponentBase
   */
  passiveLoad() { }

  /**
   * @description 停止所有动画
   * @memberof ComponentBase
   */
  stopAllAni() { }

  /**
   * @description 停止所有动画
   * @memberof ComponentBase
   */
  startAllAni() { }

  /**
   * 组件间交互
   * @memberof DIVComponentBase
   */
  comInteractive() {
    super.comInteractive()
  }

  gridUpdate(data = []) { }

  /**
   * @description 当组件跨屏时创建组件的clipRect以及组件跨屏事件同步ws的初始化
   * @param clipRect
   */
  setClipRect(x, y, w, h) {
    this.clipRect = [x, y, w, h];
    if (this.property.basic.needSync) {
      this._createClipRect();
      this._initEventSync();
    }
  }

  /**
   * @description 创建组件的clipRect的dom元素
   * @private
   */
  _createClipRect() { }

  /**
   * @description 初始化事件同步
   * @private
   */
  _initEventSync() {
    if (!this.webSocket) {
      this.webSocket = centralManager.eventSynchronization(this.id, (data) => {
        this._eventSyncProcess(data);
      });
    }
  }

  /**
   * 组件发送命令
   * 此方法为会商平台中组件模拟命令行向所有客户端发送命令的入口
   * @param {string} funcName 调用组件方法名称
   * @param {array} params 方法参数
   * @param {object} option 命令参数
   */
  _sendCommand(funcName, params = [], option = {}) {
    option = $.extend(
      true,
      { showCanvas: false, recordSteps: false, isWindow: false },
      option
    );
    let message = {
      actions: [
        {
          eventDisplayName: "",
          internalAnimation: [],
          eventName: funcName,
          compParams: params,
          compList: [
            {
              compName: this.id,
              compCode: this.code,
            },
          ],
          performerID: option.isWindow ? "window" : "",
          sceneCode: window.meetingId,
          browsers: [],
        },
      ],
      targetId: "",
      meetingId: window.meetingId,
      controlledUsers: window.controlledUsers,
      commandUUID: this.commandUUID ? this.commandUUID : WisUtil.guid(),
      commandType: window.commandType ? window.commandType : "",
      sessionTransNum: window.commandType == "dp" ? 0 : 1,
      clientID: window.clientID,
      showCanvas: option.showCanvas,
      recordSteps: option.recordSteps,
      resourceId: this.resourceId,
    };
    window.screenOverload
      ? window.screenOverload.centralWebSocketHandler.centralWS.send(
        `5:${JSON.stringify(message)}`
      )
      : window.centralWebSocketHandler.centralWS.send(
        `5:${JSON.stringify(message)}`
      );
    this.commandUUID = null;
  }

  /**
   * 修改全局变量
   * @param {Object} params 入参 {link:资源组,name:变量名称,value:变量值}
   * @returns
   * @memberof ComponentBase
   */
  getRedisLinkHttpData(params) {
    //改变全局变量地址
    if (window.WisActionLoader.callFunction) {
      window.WisActionLoader.callFunction('sendGlobalParam', JSON.stringify([params.link, params.name, params.value]))
    } else {
      let url = `${location.origin}/dataSource/addRedisLinkVariableByKey`;
      return new Promise((resolve, reject) => {
        $.ajax(url, {
          type: "POST",
          async: true,
          contentType: "application/json",
          dataType: "JSON",
          data: JSON.stringify(params),
          success: (res) => {
            resolve(res);
          },
          error: (err) => {
            reject(err);
          },
        });
      });
    }
  }

  /**
   * 批量修改全局变量
   * @param {Array} params 入参 [{scope:资源组,name:变量名称,value:变量值}]
   * @returns
   * @memberof ComponentBase
   */
  addRedisLinkDataByKey(params) {
    //改变全局变量地址
    let url = `${location.origin}/dataSource/addRedisListDataByKey`;
    return new Promise((resolve, reject) => {
      $.ajax(url, {
        type: "POST",
        async: true,
        contentType: "application/json",
        dataType: "JSON",
        data: JSON.stringify(params),
        success: (res) => {
          resolve(res);
        },
        error: (err) => {
          reject(err);
        },
      });
    });
  }

  /**
   * 设置动画同步参数
   * @param {number} duration 动画总时长(ms)
   * @param {number|null} sleepTime 动画同步间隔时间，null为20ms
   */
  _setAnimateSyncParam(duration, sleepTime = null) {
    if (this.workMode !== 2 && this.workMode !== 0) {
      this.isSyncAnime = true;
      window.wiscomWebSocket.sendAnimateParam(this.code, this.resourceId, {
        duration: duration,
        sleepTime: sleepTime,
      });
    }
  }

  /**
   * 接收动画同步回调
   * @param {function} cb 动画回调方法
   * 回调参数data结构:
   * 首次ws连接成功{
   *  body:'OK'
   * }
   * 数据返回{
   *  body: '{
   *    index:number 动画步骤序号
   *    time:number 动画当前时间
   *    finish:boolean 是否结束
   * }'
   * }
   */
  _animateSyncCallback(cb = (data) => { }) {
    if (this.workMode !== 2 && this.workMode !== 0) {
      window.wiscomWebSocket.animateSync(this.code, this.resourceId, cb);
    }
  }

  /**
   * 终止动画
   */
  _stopAnimate() {
    this.isSyncAnime = false;
    if (this.workMode !== 2 && this.workMode !== 0) {
      window.wiscomWebSocket.animateStop(this.code, this.resourceId, (data) => {
        if (data.body === "OK") {
          return;
        }
      });
    }
  }

  /**
   * @description 组件通过ws广播信息
   * @param message {string} 需要广播的信息
   */
  _sendMessageByWS(message) {
    let centralManager = window.centralManager;
    let webSocket = this.webSocket;

    centralManager.sendMessage(webSocket, message);
  }

  /**
   * @description 事件同步执行方法
   * @param data
   * @private
   */
  _eventSyncProcess(data) { }

  /**
   *
   * @param {string} str
   */
  _updateDataSourceParam(str) {
    if (this.workMode !== 2 && this.workMode !== 4) {
      let json = JSON.parse(str);
      if (
        !(
          json.code === "1" &&
          json.param.filter((d) => d.name === "").length > 0
        )
      ) {
        this.hasSelectedForDataLink = json.code === "1";
        // window.wiscomWebSocket.sendLinkedDataMessage(this.code, str);
        $.ajax(`/dataSource/setUnionParam`, {
          type: "POST",
          contentType: "application/json",
          data: JSON.stringify({
            sceneId: this.resourceId,
            componentCode: this.code,
            param: str,
          }),
          success: (data) => {
            if (data.code !== "0000") {
              console.error(`数据联动发送异常，异常信息：${data.message}`);
            }
          },
        });
      }
    } else {
      console.log("组件未订阅数据，无法发送参数修改请求");
    }
  }

  /**
   * 组件是否只记录数据不更新
   * @param {boolean} p 是否只记录数据不更新
   */
  recordDataHandler(p) {
    if (!p) this.recordData = {};
    this.onlyRecordData = p;
  }

  //用组件记录的数据进行数据更新
  updateByRecordData() {
    if (
      this.processFunction &&
      this.recordData &&
      JSON.stringify(this.recordData) !== "{}"
    ) {
      Object.values(this.recordData).forEach((each) => {
        this.processFunction(each);
      });
    }
  }

  /**
   * @description 订阅数据源
   * @param {Function} processFunction 订阅数据源后的回调
   */
  subscribeDataSource(processFunction) {
    //如果dataBind为空则不订阅数据 组件容器的边框为宽度为2px的黄色
    if (JSON.stringify(this.dataBind) === "{}") {
      // d3.select(this.container).style('border', '2px solid #ffff00');
      return;
    }
    //如果正在进行场景进入动画则直接返回
    if (window.sceneHasEnterAnimation) return;

    if (this.workMode !== 4) {
      // if (!this.property.basic.isDataLinked) {
      let component_data_bind_check = 0;
      if (
        window?.serviceAddressConfig?.COMPONENT_DATA_BIND_CHECK !== undefined
      ) {
        component_data_bind_check =
          window.serviceAddressConfig.COMPONENT_DATA_BIND_CHECK;
      }
      //当workMode不为4时进行正常的组件数据订阅
      window.wiscomWebSocket.subscribeData(
        this.code,
        this.resourceId,
        (data) => {
          console.info("subscribeDataSource", "data received");
          if (component_data_bind_check === 1) {
            //检查数据与数据绑定
            this._checkComponentBind(data);
          }
          //首次订阅返回OK表示订阅成功，否则在7s后会重新订阅
          if (data.body === "OK") {
            this.isSubscribeData = true;
            return;
          }
          //如果收到数据中的fresh字段为false表示该组件不会强制渲染 并且hasSelectedForDataLink为true表示该组件以昨晚数据过滤的条件  这种情况下不会渲染数据
          if (
            JSON.parse(data.body).hasOwnProperty("fresh") &&
            !JSON.parse(data.body).fresh &&
            this.hasSelectedForDataLink
          )
            return;

          //如果组件需要发送收到的数据 通过ws进行数据发送
          if (this.property.basic.isSendData) {
            this.postData(JSON.parse(JSON.parse(data.body).data), "sendData");
          }
          //如果组件有接收数据时的脚本则执行脚本
          for (let i = 0; i < this.getDataScripts.length; i++) {
            try {
              eval(this.getDataScripts[i]);
            } catch (error) {
              console.log(`脚本error=${error}:${this.getDataScripts[i]}`);
            }
          }
          if (!this.onlyRecordData) {
            let temp = JSON.parse(data.body);
            this.recordData[temp.group] = data;
            return;
          }
          //执行回调方法
          if (processFunction) {
            processFunction(data);
          }
          if (this.workMode === 3) {
            this.unsubscribeDataSource();
            this.unsubscribeDataByClient();
          }
        }
      );
      // } else {
      //   let component_data_bind_check = 0;
      //   if (window.serviceAddressConfig !== undefined || window.serviceAddressConfig.COMPONENT_DATA_BIND_CHECK !== undefined) {
      //     component_data_bind_check = window.serviceAddressConfig.COMPONENT_DATA_BIND_CHECK;
      //   }
      //   //当workMode不为4时进行正常的组件数据订阅
      //   window.wiscomWebSocket.subscribeUnionData(this.code, (data) => {
      //     console.info('subscribeDataSource', 'data received');
      //     if (component_data_bind_check === 1) {
      //       //检查数据与数据绑定
      //       this._checkComponentBind(data);
      //     }
      //     //首次订阅返回OK表示订阅成功，否则在7s后会重新订阅
      //     if (data.body === 'OK') {
      //       this.isSubscribeData = true;
      //       return;
      //     }
      //     //如果组件需要发送收到的数据 通过ws进行数据发送
      //     if (this.property.basic.isSendData) {
      //       this.postData(JSON.parse(data.body), 'sendData');
      //     }
      //     //如果组件有接收数据时的脚本则执行脚本
      //     for (let i = 0; i < this.getDataScripts.length; i++) {
      //       eval(this.getDataScripts[i]);
      //     }
      //     //执行回调方法
      //     if (processFunction) {
      //       processFunction(data);
      //     }
      //     if (this.workMode === 3) {
      //       this.unsubscribeDataSource();
      //       this.unsubscribeDataByClient();
      //     }
      //   });
      // }
      setTimeout(() => {
        if (!this.isSubscribeData) {
          this.subscribeDataSource(processFunction);
        }
      }, 7000);
    } else {
      //当workMode为4时将组件定义的processFunction暴露出来
      // this.processFunction = processFunction;
    }
    //组件默认暴露processFunction
    this.processFunction = processFunction;
  }

  /**
   * 设置组件数据（配置工具调用）
   * 通过设置推送数据列表向组件设置数据
   * @param {Array} data 推送数据数据列表
   */
  setDataByProcessFunction(data) {
    data.forEach((d) => {
      this.processFunction(d);
    });
  }

  /**
   * 检查数据与数据绑定并根据不同的状态给组件边框赋值不同的颜色
   * @param {JSON}} data 收到的订阅信息
   */
  _checkComponentBind(data) {
    //检查组件所有bindKey中是否有绑定
    let checkBindKeyFlag = false;
    let isOK, body, compData;
    if (data.body === "OK") {
      isOK = true;
    } else {
      body = JSON.parse(data.body);
      compData = JSON.parse(body.data);
    }
    for (const bindGroupId in this.dataBind) {
      if (this.dataBind.hasOwnProperty(bindGroupId)) {
        for (const key in this.dataBind[bindGroupId]) {
          if (this.dataBind[bindGroupId].hasOwnProperty(key)) {
            if (isOK && this.dataBind[bindGroupId][key].bindKey !== "") {
              checkBindKeyFlag = true;
            } else {
              compData.forEach((compDataElement) => {
                if (
                  typeof compDataElement[
                  this.dataBind[bindGroupId][key].bindKey
                  ] !== "number"
                ) {
                  //判断返回值类型是否为数字
                  if (
                    compDataElement.hasOwnProperty(
                      this.dataBind[bindGroupId][key].bindKey
                    )
                  ) {
                    //返回数据类型绑定数据
                    if (
                      compDataElement[this.dataBind[bindGroupId][key].bindKey]
                        .length === 0
                    ) {
                      //返回数据为空数组或者空字符串为粉红
                      d3.select(this.container).style(
                        "border",
                        "2px solid #FF69B4"
                      );
                    } else {
                      //返回数据不是空数据或空字符串为绿色
                      d3.select(this.container).style(
                        "border",
                        "2px solid #00ff00"
                      );
                    }
                  } else {
                    if (this.dataBind[bindGroupId][key].bindKey !== "") {
                      //收到的数据中没有绑定的key且绑定的变量不是空为猩红
                      d3.select(this.container).style(
                        "border",
                        "2px solid #DC143C"
                      );
                    }
                  }
                } else {
                  //返回数据是数字为绿色
                  d3.select(this.container).style(
                    "border",
                    "2px solid #00ff00"
                  );
                }
              });
            }
          }
        }
      }
    }
    if (isOK && checkBindKeyFlag) {
      //如果已绑定bindKey且没收到数据,则组件边框为蓝色
      d3.select(this.container).style("border", "2px solid #0000ff");
    }
  }

  /**
   * @description 组件反订阅数据源
   */
  unsubscribeDataSource() {
    window.wiscomWebSocket &&
      window.dataSourceStompClients &&
      window.wiscomWebSocket.unsubscribeData(this.code, this.resourceId);
  }

  /**
   * @description 通过客户端反订阅数据源
   */
  unsubscribeDataByClient() {
    window.wiscomWebSocket &&
      window.dataSourceStompClients &&
      window.wiscomWebSocket.unsubscribeDataByClient(
        this.code,
        this.resourceId
      );
  }

  postData(data, eventName) {
    let eventActions = this._getActionByEventName(eventName);
    if (eventActions) {
      let actions = this._processActions(eventActions, data);
      let message = { targetId: this.id, actions: actions };
      window.WisActionLoader.sendActionMessage(message);
    } else {
      console.warn("postData", "can not find handler for " + eventName);
    }
  }

  _processActions(actions, data) {
    let oldActions = _.cloneDeep(actions);
    oldActions.forEach(function (action) {
      let oldParam = action.compParams;
      action.compParams = [];
      oldParam.forEach(function (param) {
        let evalString = param;
        //如果evalString的类型为string则去判断里面是否包含@data@字符串并做数据替换，否则直接跳过
        if (typeof evalString === "string") {
          let valueStringList = param.match(/(?<=@)data.[^,]+(?=@)/g);
          let valueReplaceList = param.match(/@data.[^,]+@/g);
          let valueList = [];
          if (valueStringList && valueStringList.length > 0) {
            valueStringList.forEach((eachStr) => {
              valueList.push(eval(eachStr));
            });
            if (valueStringList && valueReplaceList.length > 0) {
              for (let i = 0; i < valueReplaceList.length; i++) {
                if (valueList[i]) {
                  evalString = evalString.replace(
                    valueReplaceList[i],
                    valueList[i]
                  );
                }
              }
            }
          }
        }

        action.compParams.push(evalString);
      });
    });
    // let result = [];
    // oldActions.forEach((action) => {
    //   for (let i = 0; i < action.compList.length; i++) {
    //     result.push({
    //       performerID: action.compList[i].compCode,
    //       compParams: action.compParams,
    //       eventName: action.eventName,
    //     });
    //   }
    // });
    return oldActions;
  }

  _getActionByEventName(eventName) {
    let custom = this.interact ? this.interact.custom : {};
    if (custom && custom.hasOwnProperty(eventName)) {
      let actions = [];
      for (let i = 0; i < custom[eventName].length; i++) {
        actions = [...actions, ...custom[eventName][i].actions];
      }
      return actions;
    }
    return null;
  }
}

/**
 * @class SVGComponentBase
 * @extends ComponentBase
 * @description svg组件基类
 */
class SVGComponentBase extends ComponentBase {
  /**
   * @constructor SVG组件基类构造函数
   * @param id {string} 组件id
   * @param code {string} 组件编码
   * @param container {HTMLElement} 组件容器
   * @param workMode {number} 组件工作模式 0为测试 1为大屏 2为配置工具 3为控制中心
   * @param option {JSON} 组件属性
   * @param useDefaultOpt {boolean} 是否启用默认属性
   */
  constructor(id, code, container, workMode, option, useDefaultOpt) {
    super(id, code, container, workMode, option, useDefaultOpt);
  }

  /**
   * @description 组件所需常量初始化
   */
  _setupDefaultValues() {
    super._setupDefaultValues();
    this.lockViewBox = false;
  }

  /**
   * @description 初始化组件属性列表和属性字典
   */
  _initProperty() {
    super._initProperty();
    let property = {
      basic: {
        type: "SVGComponent",
      },
      svgBasic: {
        isViewBox: true,
        lockViewBox: false,
        viewBox: [0, 0, 1920, 1080],
      },
    };

    let propertyDictionary = [
      {
        name: "svgBasic",
        displayName: "SVG组件基础属性",
        children: [
          {
            name: "isViewBox",
            displayName: "启用ViewBox",
            description: "组件是否启用ViewBox",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "lockViewBox",
            displayName: "锁定ViewBox",
            description: "指定组件viewbox，若不锁定则跟随组件大小变化",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "viewBox",
            displayName: "可视区域",
            description: "可视区域大小",
            placeholder: ["x", "y", "宽", "高"],
            type: OptionType.doubleArray,
            show: true,
            editable: true,
          },
        ],
        show: false,
        editable: true,
      },
    ];

    this._addProperty(property, propertyDictionary);
  }

  /**
   * 修改组件配置项属性
   */
  _handlePropertyChange() {
    super._handlePropertyChange();
    // svgBasic中的isViewBox为true时显示lockViewBox和viewBox属性，反之则隐藏
    // this._findPropertyDictionary('svgBasic.lockViewBox').show = this.property.svgBasic.isViewBox;
    // this._findPropertyDictionary('svgBasic.viewBox').show = this.property.svgBasic.isViewBox;
    // this._findPropertyDictionary('svgBasic.viewBox').editable = this.property.svgBasic.lockViewBox;
    if (!this.lockViewBox) {
      this.property.svgBasic.viewBox = [
        0,
        0,
        this.property.basic.frame[2],
        this.property.basic.frame[3],
      ];
    }
  }

  /**
   * @description 绘制容器样式
   */
  _draw() {
    super._draw();
    //如果该组件不需要锁定viewBox(例如数码管等指定viewBox的组件) 并且 （该组件的宽高比为3:2(编辑工具新增的组件) 或者 该组件大小与viewbox大小一致）时 组件的viewbox大小跟随组件大小变化
    if (
      !this.lockViewBox &&
      (this.property.basic.frame[2] / this.property.basic.frame[3] === 3 / 2 ||
        this.property.svgBasic.viewBox.toString() ===
        [
          0,
          0,
          this.property.basic.frame[2],
          this.property.basic.frame[3],
        ].toString())
    ) {
      this.property.svgBasic.viewBox = [
        0,
        0,
        this.property.basic.frame[2],
        this.property.basic.frame[3],
      ];
    }
    let d3Container = d3.select(this.container);
    d3Container.select("svg").remove();
    this.mainSVG = d3Container
      .append("svg")
      .attr("class", "mainSVG")
      .attr("x", 0)
      .attr("y", 0)
      .attr("width", this.property.basic.frame[2])
      .attr("height", this.property.basic.frame[3])
      .style("pointer-events", "auto");
    // if (this.property.svgBasic.isViewBox) {
    this.mainSVG.attr("viewBox", this.property.svgBasic.viewBox.join(" "));
    // }
  }

  /**
   * @description 创建组件的clipRect的dom元素
   * @private
   */
  _createClipRect() {
    let clipID = this.id + "_clip";
    this.mainSVG
      .append("defs")
      .append("svg:clipPath")
      .attr("id", clipID)
      .append("svg:rect")
      .attr("id", "clip-rect")
      .attr("x", this.clipRect[0])
      .attr("y", this.clipRect[1])
      .attr("width", this.clipRect[2])
      .attr("height", this.clipRect[3]);

    this.mainSVG.attr("clip-path", "url(#" + clipID + ")");
  }
  /**
    * 组件间交互
    * @memberof DIVComponentBase
    */
  comInteractive() {
    super.comInteractive()
  }
}

/**
 * @class DIVComponentBase
 * @extends ComponentBase
 * @description div组件基类
 */
class DIVComponentBase extends ComponentBase {
  /**
   * @constructor SVG组件基类构造函数
   * @param id {string} 组件id
   * @param code {string} 组件编码
   * @param container {HTMLElement} 组件容器
   * @param workMode {number} 组件工作模式 0为测试 1为大屏 2为配置工具 3为控制中心
   * @param option {JSON} 组件属性
   * @param useDefaultOpt {boolean} 是否启用默认属性
   */
  constructor(id, code, container, workMode, option, useDefaultOpt) {
    super(id, code, container, workMode, option, useDefaultOpt);
  }

  /**
   * @description 组件所需常量初始化
   */
  _setupDefaultValues() {
    super._setupDefaultValues();
  }

  /**
   * @description 初始化组件属性列表和属性字典
   */
  _initProperty() {
    super._initProperty();
    let property = {
      basic: {
        type: "DIVComponent",
      },
    };

    this._addProperty(property);
  }

  /**
   * @description 绘制容器样式
   */
  _draw() {
    super._draw();
    let d3Container = d3.select(this.container);
    d3Container.select(".mainDIV").remove();
    this.mainDIV = d3Container
      .append("div")
      .attr("class", "mainDIV")
      .style("position", "absolute")
      .style("left", "0")
      .style("top", "0")
      .style("width", `${this.property.basic.frame[2]}px`)
      .style("height", `${this.property.basic.frame[3]}px`)
      .style("overflow", "hidden")
      .style("pointer-events", "auto");
  }
  passiveLoad() {
    super.passiveLoad();
  }
  stopAllAni() {
    super.stopAllAni();
  }
  /**
   * @description 停止所有动画
   * @memberof ComponentBase
   */
  startAllAni() {
    super.startAllAni();
  }

  /**
   * 组件间交互
   * @memberof DIVComponentBase
   */
  comInteractive() {
    super.comInteractive()
  }
}

/**
 * @class DIVContainerBase
 * @extends DIVComponentBase
 * @description 容器组件基类
 */
class DIVContainerBase extends DIVComponentBase {
  /**
   * @constructor SVG组件基类构造函数
   * @param id {string} 组件id
   * @param code {string} 组件code
   * @param container {HTMLElement} 组件容器
   * @param workMode {number} 组件工作模式 0为测试 1为大屏 2为配置工具 3为控制中心
   * @param option {JSON} 组件属性
   * @param useDefaultOpt {boolean} 是否启用默认属性
   */
  constructor(id, code, container, workMode, option, useDefaultOpt) {
    super(id, code, container, workMode, option, useDefaultOpt);
  }

  /**
   * @description 组件所需常量初始化
   */
  _setupDefaultValues() {
    super._setupDefaultValues();
    //容器内组件列表
    this.childrenComponents = [];
  }

  /**
   * @description 初始化组件属性列表和属性字典
   */
  _initProperty() {
    super._initProperty();
    let property = {
      basic: {
        type: "container",
      },
      basicSetting: {
        isLazyLoad: false,
      },
      panel: {},
      containerJson: [],
    };

    let propertyDictionary = [
      {
        name: "basicSetting",
        displayName: "容器组件设置",
        show: true,
        editable: true,
        children: [
          {
            name: "isLazyLoad",
            displayName: "是否动态加载",
            description: "是否动态加载组件",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
        ],
      },
      {
        name: "panel",
        displayName: "面板组",
        action: [
          {
            text: "新增",
            style: "success",
            action: "addPanel",
            param: [],
          },
        ],
        children: [],
        show: true,
        editable: true,
      },
    ];

    this.panelProperty = {
      panelName: "",
      panelFrame: [0, 0, 100, 100],
      panelBgImage: "",
    };

    this.panelPropertyDictionary = [
      {
        name: "panelName",
        displayName: "面板名称",
        description: "面板名称",
        type: OptionType.string,
        show: true,
        editable: true,
      },
      {
        name: "panelFrame",
        displayName: "面板大小",
        description: "面板位置以及大小",
        type: OptionType.doubleArray,
        placeholder: ["x", "y", "宽", "高"],
        show: true,
        editable: false,
      },
      {
        name: "panelBgImage",
        displayName: "面板背景",
        description: "面板背景图",
        type: OptionType.string,
        show: true,
        editable: true,
      },
    ];

    this._addProperty(property, propertyDictionary);
  }

  /**
   * @description 绘制容器样式
   */
  _draw() {
    super._draw();
    this._initPanelPropertyDictionary();
  }

  /**
   * @description 获取容器内所有子组件
   * @returns {null}
   */
  getAllChildren() {
    return this.childrenComponents;
  }

  /**
   * @description 清空组件信息 清空容器内所有组件的组件信息
   */
  cleanup() {
    super.cleanup();
    let children = this.getAllChildren();
    if (children && children.length > 0) {
      children.forEach(function (component) {
        component && component.cleanup();
        component && d3.select(component.container).remove();
      });
      children = null;
    }
  }

  /**
   * @description 每次绘制面板时初始化面板属性字典
   * @private
   */
  _initPanelPropertyDictionary() {
    let panelProperty = this.property.panel;
    let panelPropertyDictionary = this._findPropertyDictionary("panel");
    for (const panelId in panelProperty) {
      //判断面板属性字典中是否含有该面板id的字典，若没有则新增
      if (
        panelProperty.hasOwnProperty(panelId) &&
        this._findPropertyDictionary(`panel.${panelId}`) === undefined
      ) {
        panelPropertyDictionary.children.push({
          name: panelId,
          displayName: panelId,
          action: [
            {
              text: "删除组",
              style: "warning",
              action: "deletePanel",
              param: ["parentIndex"],
            },
          ],
          children: WisUtil.deepCopy(this.panelPropertyDictionary),
          show: true,
          editable: true,
        });
      }
    }
  }

  /**
   * @description 新增面板
   */
  addPanel() {
    let panelPropertyDictionary = this._findPropertyDictionary("panel");
    let lastIndex = 0;
    if (panelPropertyDictionary.children.length > 0) {
      lastIndex =
        d3.max(
          panelPropertyDictionary.children.map((d) =>
            parseInt(d.name.split("_")[1])
          )
        ) + 1;
    }
    let panelName = `panel_${lastIndex}`;
    //在组件配置中添加新面板
    this.property.panel[panelName] = WisUtil.deepCopy(this.panelProperty);
    //在组件配置字典中添加新面板
    panelPropertyDictionary.children.push({
      name: panelName,
      displayName: panelName,
      action: [
        {
          text: "删除组",
          style: "warning",
          action: "deletePanel",
          param: ["parentIndex"],
        },
      ],
      children: WisUtil.deepCopy(this.panelPropertyDictionary),
      show: true,
      editable: true,
    });
  }

  /**
   * @description 删除面板
   * @param index {number} 面板序号
   */
  deletePanel(index) {
    let panelPropertyDictionary = this._findPropertyDictionary("panel");
    let panelName = panelPropertyDictionary.children[index].name;
    //删除optionDic中panel的对应项
    panelPropertyDictionary.children.splice(index, 1);
    //删除opts中的panel的项
    delete this.property.panel[panelName];
    //删除ContainerJson中的对应项
    for (let i = 0; i < this.property.containerJson.length; i++) {
      if (this.property.containerJson[i].paneId === panelName) {
        this.property.containerJson.splice(i, 1);
        break;
      }
    }
  }

  /**
   * @description 注入容器的子组件
   * @param containerJson {array} 容器的containerJson对象
   * @param index {number|null} 更新的序号
   */
  setChildrenComponents(containerJson, index = null) {
    this.containerJson = containerJson;
    this._loadComponentsFromJson(index);
  }

  /**
   * @description 从组建的containerJson中加载容器内组建
   * @param {number|null} index
   */
  _loadComponentsFromJson(index = null) {
    let childrenComponents = [];
    this.cleanup();
    if (index === null) {
      //当index为null时加载全部组件
      for (let i = 0; i < this.containerJson.length; i++) {
        let info = this.containerJson[i];
        // let container = this.mainDIV.select(`.${info.paneId}`);
        let container = this.mainDIV.select(`.panel_${i}_${this.id}`);
        // if (info.paneId === `panel_${i}`) {
        $(container.node()).empty();
        if (info.children.length > 0) {
          let compList = new ComponentsFactory(
            info.children,
            container,
            this.workMode,
            {
              fontScale: this.property.basic.fontScale,
              resourceId: this.resourceId,
            }
          );
          if (this.workMode === 1) {
            // window.screenOverload.screenManager._bindEvents(info.children);
          }
          compList.components.forEach((comp) => {
            // if (this.clipRect) {
            // let comClipRect = window.screenOverload.screenManager._getClipFrame([comp.property.basic.frame[0] + this.property.basic.frame[0], comp.property.basic.frame[1] + this.property.basic.frame[1], comp.property.basic.frame[2], comp.property.basic.frame[3]], window.screenOverload.dimension);
            // comp.setClipRect(comClipRect);
            // }
            childrenComponents.push(comp);
          });
          this.childrenComponents = childrenComponents;
        }
        // }
      }
    } else {
      //当index不是null时加载指定index的组件
      let info = this.containerJson[index];
      // let container = this.mainDIV.select(`.${info.paneId}`);
      let container = this.mainDIV.select(`.panel_${index}_${this.id}`);
      // if (info.paneId === `panel_${index}`) {
      $(container.node()).empty();
      if (info.children.length > 0) {
        let compList = new ComponentsFactory(
          info.children,
          container,
          this.workMode,
          {
            fontScale: this.property.basic.fontScale,
            resourceId: this.resourceId,
          }
        );
        if (this.workMode === 1) {
          // window.screenOverload.screenManager._bindEvents(info.children);
        }
        compList.components.forEach((comp) => {
          // if (this.clipRect) {
          // let comClipRect = window.screenOverload.screenManager._getClipFrame([comp.property.basic.frame[0] + this.property.basic.frame[0], comp.property.basic.frame[1] + this.property.basic.frame[1], comp.property.basic.frame[2], comp.property.basic.frame[3]], window.screenOverload.dimension);
          // comp.setClipRect(comClipRect);
          // }
          childrenComponents.push(comp);
        });
        this.childrenComponents = childrenComponents;
      }
      // }
    }
    requestAnimationFrame(() => {
      this.childrenComponents.forEach((one) => {
        if (one && one.passiveLoad) {
          one.passiveLoad();
        }
        if (one && one.passiveLoadVideo) {
          one.passiveLoadVideo();
        }
      });
    });
  }
}

class MapWidget extends DIVComponentBase {
  constructor(id, code, container, workMode, option, useDefaultOpt) {
    super(id, code, container, workMode, option, useDefaultOpt);

    try {
      window.initAPIPromise.then(() => {
        this._initEventSync();
      });
    } catch (e) { }
  }

  /**
   * @description 初始化组件属性列表和属性字典
   */
  _initProperty() {
    super._initProperty();
    let property = {
      basic: {
        type: "map",
      },
    };
    this._addProperty(property);
  }

  _draw() {
    super._draw();
    this._generateBasicDIV();
  }

  _generateBasicDIV() { }

  /**
   * 组件间交互
   * @memberof DIVComponentBase
   */
  comInteractive() {
    super.comInteractive()
  }
}

/**
 * @description 自定义组件基类
 */
class CustomComponent {
  /**
   * @description 自定义组件基类构造函数
   * @param id {string} 组件id
   * @param code {string} 组件编码
   * @param container {HTMLElement} 组件容器
   * @param workMode {number} 组件工作模式
   * @param option {Object} 组件属性
   * @param useDefaultOpt {boolean} 是否启用默认值
   */
  constructor(id, code, container, workMode, option, useDefaultOpt) {
    this.id = id;
    this.code = code;
    this.resourceId = option.resourceId;
    this.container = container;
    this.workMode = workMode;
    this.useDefaultOpt = useDefaultOpt;
    this.propertyDictionary = [];
    this._initProperty();
    this.property = $.extend(true, this.property, option.property);
    this._draw();
  }

  _initProperty() {
    let property = {
      basic: {
        code: this.code,
        displayName: "",
        type: "custom",
        className: "",
        frame: [0, 0, 1920, 1080],
        isVisible: true,
        isLocked: false,
        translateZ: true,
        needSync: false,
        zIndex: 0,
        //组件字体比例
        fontScale: 1,
      },
      childComp: [],
    };

    //组件基础属性字典
    let propertyDictionary = [
      {
        name: "basic",
        displayName: "基础属性",
        children: [
          {
            name: "code",
            displayName: "组件编码",
            description: "组件编码",
            type: OptionType.string,
            show: true,
            editable: false,
          },
          {
            name: "displayName",
            displayName: "组件名称",
            description: "组件名称",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "type",
            displayName: "组件类型",
            description: "组件类型",
            type: OptionType.string,
            show: true,
            editable: false,
          },
          {
            name: "className",
            displayName: "组件类名",
            description: "组件类名",
            type: OptionType.string,
            show: true,
            editable: false,
          },
          {
            name: "frame",
            displayName: "组件大小",
            description: "组件位置以及大小",
            type: OptionType.doubleArray,
            placeholder: ["x", "y", "宽", "高"],
            show: true,
            editable: true,
          },
          {
            name: "isVisible",
            displayName: "是否可见",
            description: "组件是否可见",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "translateZ",
            displayName: "启用Z轴位移",
            description: "是否启用Z轴位移(启用分层渲染)",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "needSync",
            displayName: "是否同步",
            description: "跨屏组件是否启动事件同步",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "isVisible",
            displayName: "是否可见",
            description: "组件是否可见",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "isLocked",
            displayName: "是否锁定",
            description: "锁定后不可修改位置及宽高",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "zIndex",
            displayName: "组件层级",
            description: "组件的所在画布的层级",
            type: OptionType.int,
            show: true,
            editable: true,
          },
        ],
        show: true,
        editable: true,
      },
    ];

    this._addProperty(property, propertyDictionary);
  }

  /**
   * @description 新增属性
   * @param property
   * @param propertyDictionary
   */
  _addProperty(property, propertyDictionary = []) {
    this.property = $.extend(true, this.property, property);
    if (propertyDictionary.length > 0) {
      this.propertyDictionary =
        this.propertyDictionary.concat(propertyDictionary);
    }
  }

  _draw() {
    let d3Container = d3.select(this.container);
    if (!d3Container.select(`#comp${this.id}`).empty()) {
      d3Container.select(`#comp${this.id}`).remove();
    }
    d3Container
      .append("div")
      .attr("id", `comp${this.id}`)
      .style("width", this.property.basic.frame[2])
      .style("height", this.property.basic.frame[3])
      .style("display", this.property.basic.isVisible ? "block" : "none")
      .style("z-index", this.property.basic.zIndex);
    if (this.property.basic.translateZ) {
      d3Container.style("transform", "translateZ(0)");
    }
    this._generateChildrenComp();
  }

  _generateChildrenComp() {
    this.childrenComponents = [];
    var fontScale = this.property.basic.fontScale;
    var parentZindex = this.property.basic.zIndex;
    this.property.childComp.forEach((compOpt, i) => {
      let className = compOpt.property.basic.className;
      // let code = compOpt.property.basic.code;
      let code = `${this.code}_${i}`;
      compOpt.property.basic.code = code;
      compOpt.property.basic.fontScale = fontScale;
      var frame = compOpt.property.basic.frame;
      frame = [
        frame[0] * fontScale,
        frame[1] * fontScale,
        frame[2] * fontScale,
        frame[3] * fontScale,
      ];
      compOpt.property.basic.frame = frame;

      var zIndex =
        parseInt(compOpt.property.basic.zIndex) + parseInt(parentZindex);
      compOpt.property.basic.zIndex = zIndex;
      compOpt.resourceId = this.resourceId;

      let container = d3
        .select(this.container)
        .style("z-idnex", parentZindex)
        .select("div")
        .append("div")
        .attr("id", `comp_${this.id}_${code}`)
        .style("position", "absolute")
        .style("left", `${compOpt.property.basic.frame[0]}px`)
        .style("top", `${compOpt.property.basic.frame[1]}px`)
        .style("width", `${compOpt.property.basic.frame[2]}px`)
        .style("height", `${compOpt.property.basic.frame[3]}px`)
        .style("z-idnex", `${compOpt.property.basic.zIndex}`)
        .node();
      let comp = eval(
        `new ${className}(this.id,code,container, this.workMode, compOpt, false)`
      );
      this.childrenComponents.push(comp);
    });
  }

  setProperty(property) {
    // let scale = d3.min([property.basic.frame[2] / this.property.basic.frame[2], property.basic.frame[3] / this.property.basic.frame[3]])
    // scale = Math.round(scale * 100) / 100;
    let widthScale = property.basic.frame[2] / this.property.basic.frame[2];
    let heightScale = property.basic.frame[3] / this.property.basic.frame[3];
    widthScale = Math.round(widthScale * 100) / 100;
    heightScale = Math.round(heightScale * 100) / 100;
    //依次获取childComp下所有子组件的frame中的每个值并乘scale
    for (let i = 0; i < this.property.childComp.length; i++) {
      this.property.childComp[i].property.basic.frame[2] *= widthScale;
      this.property.childComp[i].property.basic.frame[3] *= heightScale;
      // for (let j = 0; j < this.property.childComp[i].property.basic.frame.length; j++) {
      //     this.property.childComp[i].property.basic.frame[j] *= scale
      //     this.property.childComp[i].property.basic.frame[j] = Math.round(this.property.childComp[i].property.basic.frame[j] * 100) / 100;
      // }
    }
    this.property = $.extend(true, this.property, property);
    this._draw();
  }

  cleanup() {
    this.childrenComponents.forEach((comp) => comp.cleanup());
  }
}

/**
 * @class DIVContainerBase
 * @extends DIVComponentBase
 * @description 容器组件基类
 */
class ThreeBase extends DIVComponentBase {
  /**
   * @constructor SVG组件基类构造函数
   * @param id {string} 组件id
   * @param code {string} 组件code
   * @param container {HTMLElement} 组件容器
   * @param workMode {number} 组件工作模式 0为测试 1为大屏 2为配置工具 3为控制中心
   * @param option {JSON} 组件属性
   * @param useDefaultOpt {boolean} 是否启用默认属性
   */
  constructor(id, code, container, workMode, option, useDefaultOpt) {
    super(id, code, container, workMode, option, useDefaultOpt);
  }

  /**
   * @description 组件所需常量初始化
   */
  _setupDefaultValues() {
    super._setupDefaultValues();
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(
      45,
      this.property.basic.frame[2] / this.property.basic.frame[3],
      1,
      500
    );
    //设置渲染器为WebGL渲染器 抗锯齿：antialias 透明通道：alpha
    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    this.renderer.setClearColor(0x000000, 0); //设置窗口背景颜色为透明
    this.renderer.setSize(
      this.property.basic.frame[2],
      this.property.basic.frame[3]
    );
  }

  /**
   * 绘制入口
   */
  _draw() {
    super._draw();
    this.mainDIV.node().appendChild(this.renderer.domElement);
  }

  /**
   * 更新视图
   * @param {function} cb 回调函数
   */
  _updateScene(cb) {
    let animate = () => {
      requestAnimationFrame(animate);
      cb();
      this.renderer.render(this.scene, this.camera);
    };
    animate();
  }

  /**
   * 初始化环境光
   */
  _initAmbientLight() {
    const ambient = new THREE.AmbientLight(0xffffff, 0.1);
    this.scene.add(ambient);
  }

  /**
   * 初始化点光源
   */
  _initSpotLight() {
    const spotLight = new THREE.SpotLight(0xffffff, 1);
    spotLight.position.set(150, 150, 50);
    spotLight.angle = Math.PI / 4;
    spotLight.penumbra = 1;
    spotLight.decay = 1;
    spotLight.distance = 500;
    spotLight.intensity = 2;

    spotLight.castShadow = true;
    spotLight.shadow.mapSize.width = 200;
    spotLight.shadow.mapSize.height = 200;
    spotLight.shadow.camera.near = 10;
    spotLight.shadow.camera.far = 200;
    spotLight.shadow.focus = 1;
    this.scene.add(spotLight);

    // const lightHelper = new THREE.SpotLightHelper(spotLight);
    // this.scene.add(lightHelper);
  }

  /**
   * 绘制立方体
   * @param {object} position 中心位置{x,y,z}
   * @param {number} width 长
   * @param {number} height 宽
   * @param {number} depth 高
   * @param {string} color 颜色
   */
  _drawCube(position, width, height, depth, color) {
    var geometry = new THREE.BoxGeometry(width, height, depth);
    var material = new THREE.MeshPhongMaterial({ color: color });
    let cube = new THREE.Mesh(geometry, material);
    cube.position.set(position.x, position.y, position.z);
    this.scene.add(cube);
  }

  /**
   * 绘制球体
   * @param {object} position 中心位置{x,y,z}
   * @param {number} radius 半径
   * @param {string} color 颜色
   */
  _drawPoint(position, radius, color) {
    var geometry = new THREE.SphereGeometry(radius, 16, 8);
    var material = new THREE.MeshPhongMaterial({ color: color });
    let point = new THREE.Mesh(geometry, material);
    point.position.set(position.x, position.y, position.z);
    this.scene.add(point);
    return point;
  }

  /**
   * 绘制实线
   * @param {array} points 节点列表
   * @param {string} color 颜色
   */
  _drawLine(points, color) {
    var material = new THREE.LineBasicMaterial({ color: color });
    var geometry = new THREE.BufferGeometry();
    const pointArr = new Float32Array(points);
    geometry.setAttribute("position", new THREE.BufferAttribute(pointArr, 3));
    var line = new THREE.Line(geometry, material);
    this.scene.add(line);
    return line;
  }

  /**
   * 绘制虚线
   * @param {array} points 节点列表
   * @param {string} color 颜色
   * @param {number} dash 每节线长
   * @param {number} gap 每节线间隔
   */
  _drawDashedLine(points, color, dash, gap) {
    var material = new THREE.LineDashedMaterial({
      color: color,
      dashSize: dash,
      gapSize: gap,
    });
    var geometry = new THREE.BufferGeometry();
    const pointArr = new Float32Array(points);
    geometry.setAttribute("position", new THREE.BufferAttribute(pointArr, 3));
    var line = new THREE.Line(geometry, material);
    line.computeLineDistances();
    this.scene.add(line);
    return line;
  }

  /**
   * 绘制矩形平面
   * @param {object} position 平面中心位置{x,y,z}
   * @param {number} w 长
   * @param {number} h 宽
   * @param {string} color 颜色
   * @param {number} opacity 透明度
   */
  _drawPlane(position, w, h, color, opacity) {
    var geometry = new THREE.PlaneGeometry(w, h);
    var material = new THREE.MeshPhongMaterial({
      color: color,
      transparent: true,
      opacity: opacity,
    });
    let plane = new THREE.Mesh(geometry, material);
    plane.position.set(position.x, position.y, position.z);
    this.scene.add(plane);
    return plane;
  }

  /**
   * 绘制3D立体文字
   * @param {string} str 文字内容
   * @param {object} position 平面中心位置{x,y,z}
   * @param {number} w 长
   * @param {number} h 宽
   * @param {string} color 颜色
   * @param {number} opacity 透明度
   */
  _draw3DText(str, position, size, bevelSize, color) {
    let hover = 20;
    let textGeo = new THREE.TextGeometry(str, {
      // font: str,
      size: size,
      // height: height,
      // curveSegments: curveSegments,
      // bevelThickness: bevelThickness,
      bevelSize: bevelSize,
      // bevelEnabled: bevelEnabled
    });
    let material = new THREE.MeshPhongMaterial({ color: color });
    textGeo.computeBoundingBox();
    const centerOffset =
      -0.5 * (textGeo.boundingBox.max.x - textGeo.boundingBox.min.x);
    let textMesh = new THREE.Mesh(textGeo, material);
    textMesh.position.x = centerOffset + position.x;
    textMesh.position.y = hover + position.y;
    textMesh.position.z = position.z;
    this.scene.add(textMesh);
    return textMesh;
  }

  /**
   * 加载OBJ模型
   * @param {string} path 模型路径
   */
  _loadObjModal(path) {
    return new Promise((resolve) => {
      let loader = new THREE.OBJLoader();
      loader.load(path, (obj) => {
        resolve(obj);
      });
    });
  }
}

class CanvasComponent extends DIVComponentBase {
  /**
   * @constructor SVG组件基类构造函数
   * @param id {string} 组件id
   * @param code {string} 组件code
   * @param container {HTMLElement} 组件容器
   * @param workMode {number} 组件工作模式 0为测试 1为大屏 2为配置工具 3为控制中心
   * @param option {JSON} 组件属性
   * @param useDefaultOpt {boolean} 是否启用默认属性
   */
  constructor(id, code, container, workMode, option, useDefaultOpt) {
    super(id, code, container, workMode, option, useDefaultOpt);
  }

  /**
   * @description 绘制容器样式
   */
  _draw() {
    super._draw();
    this.mainDIV.append("canvas");
  }
}
