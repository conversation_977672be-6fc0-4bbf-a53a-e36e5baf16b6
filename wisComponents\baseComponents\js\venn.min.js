!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("d3-selection"),require("d3-transition")):"function"==typeof define&&define.amd?define(["exports","d3-selection","d3-transition"],r):r(t.venn=t.venn||{},t.d3,t.d3)}(this,function(t,r,n){"use strict";function e(t,r){var n,e=i(t),s=e.filter(function(r){return a(r,t)}),f=0,l=0,x=[];if(s.length>1){var c=h(s);for(n=0;n<s.length;++n){var v=s[n];v.angle=Math.atan2(v.x-c.x,v.y-c.y)}s.sort(function(t,r){return r.angle-t.angle});var g=s[s.length-1];for(n=0;n<s.length;++n){var p=s[n];l+=(g.x+p.x)*(p.y-g.y);for(var y={x:(p.x+g.x)/2,y:(p.y+g.y)/2},d=null,m=0;m<p.parentIndex.length;++m)if(g.parentIndex.indexOf(p.parentIndex[m])>-1){var M=t[p.parentIndex[m]],z=Math.atan2(p.x-M.x,p.y-M.y),b=Math.atan2(g.x-M.x,g.y-M.y),w=b-z;w<0&&(w+=2*Math.PI);var I=b-w/2,R=o(y,{x:M.x+M.radius*Math.sin(I),y:M.y+M.radius*Math.cos(I)});(null===d||d.width>R)&&(d={circle:M,width:R,p1:p,p2:g})}null!==d&&(x.push(d),f+=u(d.circle.radius,d.width),g=p)}}else{var P=t[0];for(n=1;n<t.length;++n)t[n].radius<P.radius&&(P=t[n]);var A=!1;for(n=0;n<t.length;++n)if(o(t[n],P)>Math.abs(P.radius-t[n].radius)){A=!0;break}A?f=l=0:(f=P.radius*P.radius*Math.PI,x.push({circle:P,p1:{x:P.x,y:P.y+P.radius},p2:{x:P.x-H,y:P.y+P.radius},width:2*P.radius}))}return l/=2,r&&(r.area=f+l,r.arcArea=f,r.polygonArea=l,r.arcs=x,r.innerPoints=s,r.intersectionPoints=e),f+l}function a(t,r){for(var n=0;n<r.length;++n)if(o(t,r[n])>r[n].radius+H)return!1;return!0}function i(t){for(var r=[],n=0;n<t.length;++n)for(var e=n+1;e<t.length;++e)for(var a=l(t[n],t[e]),i=0;i<a.length;++i){var s=a[i];s.parentIndex=[n,e],r.push(s)}return r}function s(t,r){var n=Math.sqrt(t*t-r*r);return r*n+t*t*Math.atan2(r,n)}function u(t,r){return s(t,r-t)-s(t,-t)}function o(t,r){return Math.sqrt((t.x-r.x)*(t.x-r.x)+(t.y-r.y)*(t.y-r.y))}function f(t,r,n){if(n>=t+r)return 0;if(n<=Math.abs(t-r))return Math.PI*Math.min(t,r)*Math.min(t,r);var e=t-(n*n-r*r+t*t)/(2*n),a=r-(n*n-t*t+r*r)/(2*n);return u(t,e)+u(r,a)}function l(t,r){var n=o(t,r),e=t.radius,a=r.radius;if(n>=e+a||n<=Math.abs(e-a))return[];var i=(e*e-a*a+n*n)/(2*n),s=Math.sqrt(e*e-i*i),u=t.x+i*(r.x-t.x)/n,f=t.y+i*(r.y-t.y)/n,l=-(r.y-t.y)*(s/n),h=-(r.x-t.x)*(s/n);return[{x:u+l,y:f-h},{x:u-l,y:f+h}]}function h(t){for(var r={x:0,y:0},n=0;n<t.length;++n)r.x+=t[n].x,r.y+=t[n].y;return r.x/=t.length,r.y/=t.length,r}function x(t,r,n,e){e=e||{};var a=e.maxIterations||100,i=e.tolerance||1e-10,s=t(r),u=t(n),o=n-r;if(s*u>0)throw"Initial bisect points must have opposite signs";if(0===s)return r;if(0===u)return n;for(var f=0;f<a;++f){o/=2;var l=r+o,h=t(l);if(h*s>=0&&(r=l),Math.abs(o)<i||0===h)return l}return r+o}function c(t){for(var r=new Array(t),n=0;n<t;++n)r[n]=0;return r}function v(t,r){return c(t).map(function(){return c(r)})}function g(t,r){for(var n=0,e=0;e<t.length;++e)n+=t[e]*r[e];return n}function p(t){return Math.sqrt(g(t,t))}function y(t,r,n){for(var e=0;e<r.length;++e)t[e]=r[e]*n}function d(t,r,n,e,a){for(var i=0;i<t.length;++i)t[i]=r*n[i]+e*a[i]}function m(t,r,n){function e(t){for(var r=0;r<t.length;r++)g[v][r]=t[r];g[v].fx=t.fx}n=n||{};var a,i=n.maxIterations||200*r.length,s=n.nonZeroDelta||1.05,u=n.zeroDelta||.001,o=n.minErrorDelta||1e-6,f=n.minErrorDelta||1e-5,l=void 0!==n.rho?n.rho:1,h=void 0!==n.chi?n.chi:2,x=void 0!==n.psi?n.psi:-.5,c=void 0!==n.sigma?n.sigma:.5,v=r.length,g=new Array(v+1);g[0]=r,g[0].fx=t(r),g[0].id=0;for(var p=0;p<v;++p){var y=r.slice();y[p]=y[p]?y[p]*s:u,g[p+1]=y,g[p+1].fx=t(y),g[p+1].id=p+1}for(var m=function(t,r){return t.fx-r.fx},M=r.slice(),z=r.slice(),b=r.slice(),w=r.slice(),I=0;I<i;++I){if(g.sort(m),n.history){var R=g.map(function(t){var r=t.slice();return r.fx=t.fx,r.id=t.id,r});R.sort(function(t,r){return t.id-r.id}),n.history.push({x:g[0].slice(),fx:g[0].fx,simplex:R})}for(a=0,p=0;p<v;++p)a=Math.max(a,Math.abs(g[0][p]-g[1][p]));if(Math.abs(g[0].fx-g[v].fx)<o&&a<f)break;for(p=0;p<v;++p){M[p]=0;for(var P=0;P<v;++P)M[p]+=g[P][p];M[p]/=v}var A=g[v];if(d(z,1+l,M,-l,A),z.fx=t(z),z.fx<g[0].fx)d(w,1+h,M,-h,A),w.fx=t(w),e(w.fx<z.fx?w:z);else if(z.fx>=g[v-1].fx){var O=!1;if(z.fx>A.fx?(d(b,1+x,M,-x,A),b.fx=t(b),b.fx<A.fx?e(b):O=!0):(d(b,1-x*l,M,x*l,A),b.fx=t(b),b.fx<z.fx?e(b):O=!0),O){if(c>=1)break;for(p=1;p<g.length;++p)d(g[p],1-c,g[0],c,g[p]),g[p].fx=t(g[p])}}else e(z)}return g.sort(m),{fx:g[0].fx,x:g[0]}}function M(t,r,n,e,a,i,s){function u(u,h,c){for(var v=0;v<16;++v)if(a=(u+h)/2,d(e.x,1,n.x,a,r),l=e.fx=t(e.x,e.fxprime),x=g(e.fxprime,r),l>o+i*a*f||l>=c)h=a;else{if(Math.abs(x)<=-s*f)return a;x*(h-u)>=0&&(h=u),u=a,c=l}return 0}var o=n.fx,f=g(n.fxprime,r),l=o,h=o,x=f,c=0;a=a||1,i=i||1e-6,s=s||.1;for(var v=0;v<10;++v){if(d(e.x,1,n.x,a,r),l=e.fx=t(e.x,e.fxprime),x=g(e.fxprime,r),l>o+i*a*f||v&&l>=h)return u(c,a,h);if(Math.abs(x)<=-s*f)return a;if(x>=0)return u(a,c,l);h=l,c=a,a*=2}return a}function z(t,r,n){var e,a,i,s={x:r.slice(),fx:0,fxprime:r.slice()},u={x:r.slice(),fx:0,fxprime:r.slice()},o=r.slice(),f=1;n=n||{},i=n.maxIterations||20*r.length,s.fx=t(s.x,s.fxprime),e=s.fxprime.slice(),y(e,s.fxprime,-1);for(var l=0;l<i;++l){if(f=M(t,e,s,u,f),n.history&&n.history.push({x:s.x.slice(),fx:s.fx,fxprime:s.fxprime.slice(),alpha:f}),f){d(o,1,u.fxprime,-1,s.fxprime);var h=g(s.fxprime,s.fxprime),x=Math.max(0,g(o,u.fxprime)/h);d(e,x,e,-1,u.fxprime),a=s,s=u,u=a}else y(e,s.fxprime,-1);if(p(s.fxprime)<=1e-5)break}return n.history&&n.history.push({x:s.x.slice(),fx:s.fx,fxprime:s.fxprime.slice(),alpha:f}),s}function b(t,r){r=r||{},r.maxIterations=r.maxIterations||500;var n=r.initialLayout||A;t=I(t);var e,a=n(t),i=[],s=[];for(e in a)a.hasOwnProperty(e)&&(i.push(a[e].x),i.push(a[e].y),s.push(e));for(var u=0,o=m(function(r){u+=1;for(var n={},e=0;e<s.length;++e){var i=s[e];n[i]={x:r[2*e],y:r[2*e+1],radius:a[i].radius}}return j(n,t)},i,r),f=o.x,l=0;l<s.length;++l)e=s[l],a[e].x=f[2*l],a[e].y=f[2*l+1];return a}function w(t,r,n){return Math.min(t,r)*Math.min(t,r)*Math.PI<=n+J?Math.abs(t-r):x(function(e){return f(t,r,e)-n},0,t+r)}function I(t){t=t.slice();var r,n,e,a,i=[],s={};for(r=0;r<t.length;++r){var u=t[r];1==u.sets.length?i.push(u.sets[0]):2==u.sets.length&&(e=u.sets[0],a=u.sets[1],s[[e,a]]=!0,s[[a,e]]=!0)}for(i.sort(function(t,r){return t>r}),r=0;r<i.length;++r)for(e=i[r],n=r+1;n<i.length;++n)a=i[n],[e,a]in s||t.push({sets:[e,a],size:0});return t}function R(t,r,n){var e=v(r.length,r.length),a=v(r.length,r.length);return t.filter(function(t){return 2==t.sets.length}).map(function(t){var i=n[t.sets[0]],s=n[t.sets[1]],u=Math.sqrt(r[i].size/Math.PI),o=Math.sqrt(r[s].size/Math.PI),f=w(u,o,t.size);e[i][s]=e[s][i]=f;var l=0;t.size+1e-10>=Math.min(r[i].size,r[s].size)?l=1:t.size<=1e-10&&(l=-1),a[i][s]=a[s][i]=l}),{distances:e,constraints:a}}function P(t,r,n,e){var a,i=0;for(a=0;a<r.length;++a)r[a]=0;for(a=0;a<n.length;++a)for(var s=t[2*a],u=t[2*a+1],o=a+1;o<n.length;++o){var f=t[2*o],l=t[2*o+1],h=n[a][o],x=e[a][o],c=(f-s)*(f-s)+(l-u)*(l-u),v=Math.sqrt(c),g=c-h*h;x>0&&v<=h||x<0&&v>=h||(i+=2*g*g,r[2*a]+=4*g*(s-f),r[2*a+1]+=4*g*(u-l),r[2*o]+=4*g*(f-s),r[2*o+1]+=4*g*(l-u))}return i}function A(t,r){var n=q(t,r);if(t.length>=8){var e=O(t,r),a=j(e,t),i=j(n,t);a+1e-8<i&&(n=e)}return n}function O(t,r){r=r||{};var n,e=r.restarts||10,a=[],i={};for(n=0;n<t.length;++n){var s=t[n];1==s.sets.length&&(i[s.sets[0]]=a.length,a.push(s))}var u=R(t,a,i),o=u.distances,f=u.constraints,l=p(o.map(p))/o.length;o=o.map(function(t){return t.map(function(t){return t/l})});var h,x,v=function(t,r){return P(t,r,o,f)};for(n=0;n<e;++n){var g=c(2*o.length).map(Math.random);x=z(v,g,r),(!h||x.fx<h.fx)&&(h=x)}var d=h.x,m={};for(n=0;n<a.length;++n){var M=a[n];m[M.sets[0]]={x:d[2*n]*l,y:d[2*n+1]*l,radius:Math.sqrt(M.size/Math.PI)}}if(r.history)for(n=0;n<r.history.length;++n)y(r.history[n].x,l);return m}function q(t){function r(t,r){return r.size-t.size}function n(t){return t.set in p}function e(t,r){i[r].x=t.x,i[r].y=t.y,p[r]=!0}for(var a,i={},s={},u=0;u<t.length;++u){var o=t[u];1==o.sets.length&&(a=o.sets[0],i[a]={x:1e10,y:1e10,rowid:i.length,size:o.size,radius:Math.sqrt(o.size/Math.PI)},s[a]=[])}for(t=t.filter(function(t){return 2==t.sets.length}),u=0;u<t.length;++u){var f=t[u],h=f.hasOwnProperty("weight")?f.weight:1,x=f.sets[0],c=f.sets[1];f.size+J>=Math.min(i[x].size,i[c].size)&&(h=0),s[x].push({set:c,size:f.size,weight:h}),s[c].push({set:x,size:f.size,weight:h})}var v=[];for(a in s)if(s.hasOwnProperty(a)){var g=0;for(u=0;u<s[a].length;++u)g+=s[a][u].size*s[a][u].weight;v.push({set:a,size:g})}v.sort(r);var p={};for(e({x:0,y:0},v[0].set),u=1;u<v.length;++u){var y=v[u].set,d=s[y].filter(n);if(a=i[y],d.sort(r),0===d.length)throw"ERROR: missing pairwise overlap information";for(var m=[],M=0;M<d.length;++M){var z=i[d[M].set],b=w(a.radius,z.radius,d[M].size);m.push({x:z.x+b,y:z.y}),m.push({x:z.x-b,y:z.y}),m.push({y:z.y+b,x:z.x}),m.push({y:z.y-b,x:z.x});for(var I=M+1;I<d.length;++I)for(var R=i[d[I].set],P=w(a.radius,R.radius,d[I].size),A=l({x:z.x,y:z.y,radius:b},{x:R.x,y:R.y,radius:P}),O=0;O<A.length;++O)m.push(A[O])}var q=1e50,k=m[0];for(M=0;M<m.length;++M){i[y].x=m[M].x,i[y].y=m[M].y;var F=j(i,t);F<q&&(q=F,k=m[M])}e(k,y)}return i}function j(t,r){function n(r){return r.map(function(r){return t[r]})}for(var a=0,i=0;i<r.length;++i){var s,u=r[i];if(1!=u.sets.length){if(2==u.sets.length){var l=t[u.sets[0]],h=t[u.sets[1]];s=f(l.radius,h.radius,o(l,h))}else s=e(n(u.sets));var x=u.hasOwnProperty("weight")?u.weight:1;a+=x*(s-u.size)*(s-u.size)}}return a}function k(t,r,n){null===n?t.sort(function(t,r){return r.radius-t.radius}):t.sort(n);var e;if(t.length>0){var a=t[0].x,i=t[0].y;for(e=0;e<t.length;++e)t[e].x-=a,t[e].y-=i}if(t.length>1){var s,u,o=Math.atan2(t[1].x,t[1].y)-r,f=Math.cos(o),l=Math.sin(o);for(e=0;e<t.length;++e)s=t[e].x,u=t[e].y,t[e].x=f*s-l*u,t[e].y=l*s+f*u}if(t.length>2){for(var h=Math.atan2(t[2].x,t[2].y)-r;h<0;)h+=2*Math.PI;for(;h>2*Math.PI;)h-=2*Math.PI;if(h>Math.PI){var x=t[1].y/(1e-10+t[1].x);for(e=0;e<t.length;++e){var c=(t[e].x+x*t[e].y)/(1+x*x);t[e].x=2*c-t[e].x,t[e].y=2*c*x-t[e].y}}}}function F(t){function r(t){return t.parent!==t&&(t.parent=r(t.parent)),t.parent}function n(t,n){var e=r(t),a=r(n);e.parent=a}t.map(function(t){t.parent=t});for(var e=0;e<t.length;++e)for(var a=e+1;a<t.length;++a){var i=t[e].radius+t[a].radius;o(t[e],t[a])+1e-10<i&&n(t[a],t[e])}var s,u={};for(e=0;e<t.length;++e)s=r(t[e]).parent.setid,s in u||(u[s]=[]),u[s].push(t[e]);t.map(function(t){delete t.parent});var f=[];for(s in u)u.hasOwnProperty(s)&&f.push(u[s]);return f}function C(t){var r=function(r){var n=Math.max.apply(null,t.map(function(t){return t[r]+t.radius})),e=Math.min.apply(null,t.map(function(t){return t[r]-t.radius}));return{max:n,min:e}};return{xRange:r("x"),yRange:r("y")}}function D(t,r,n){function e(t,r,n){if(t){var e,a,i,u=t.bounds;r?e=l.xRange.max-u.xRange.min+h:(e=l.xRange.max-u.xRange.max,i=(u.xRange.max-u.xRange.min)/2-(l.xRange.max-l.xRange.min)/2,i<0&&(e+=i)),n?a=l.yRange.max-u.yRange.min+h:(a=l.yRange.max-u.yRange.max,i=(u.yRange.max-u.yRange.min)/2-(l.yRange.max-l.yRange.min)/2,i<0&&(a+=i));for(var o=0;o<t.length;++o)t[o].x+=e,t[o].y+=a,s.push(t[o])}}null===r&&(r=Math.PI/2);var a,i,s=[];for(i in t)if(t.hasOwnProperty(i)){var u=t[i];s.push({x:u.x,y:u.y,radius:u.radius,setid:i})}var o=F(s);for(a=0;a<o.length;++a){k(o[a],r,n);var f=C(o[a]);o[a].size=(f.xRange.max-f.xRange.min)*(f.yRange.max-f.yRange.min),o[a].bounds=f}o.sort(function(t,r){return r.size-t.size}),s=o[0];for(var l=s.bounds,h=(l.xRange.max-l.xRange.min)/50,x=1;x<o.length;)e(o[x],!0,!1),e(o[x+1],!1,!0),e(o[x+2],!0,!0),x+=3,l=C(s);var c={};for(a=0;a<s.length;++a)c[s[a].setid]=s[a];return c}function T(t,r,n,e){var a=[],i=[];for(var s in t)t.hasOwnProperty(s)&&(i.push(s),a.push(t[s]));r-=2*e,n-=2*e;for(var u=C(a),o=u.xRange,f=u.yRange,l=r/(o.max-o.min),h=n/(f.max-f.min),x=Math.min(h,l),c=(r-(o.max-o.min)*x)/2,v=(n-(f.max-f.min)*x)/2,g={},p=0;p<a.length;++p){var y=a[p];g[i[p]]={radius:x*y.radius,x:e+c+(y.x-o.min)*x,y:e+v+(y.y-f.min)*x}}return g}function E(){function t(t){var c=t.datum(),v=y(c);o&&(v=D(v,u,x));var g=T(v,e,a,i),d=G(g,c);t.selectAll("svg").data([g]).enter().append("svg");var m=t.select("svg").attr("width",e).attr("height",a),M={},z=!1;m.selectAll(".venn-area path").each(function(t){var n=r.select(this).attr("d");1==t.sets.length&&n&&(z=!0,M[t.sets[0]]=Z(n))});var b=function(t){return function(r){var n=t.sets.map(function(t){var n=M[t],i=g[t];return n||(n={x:e/2,y:a/2,radius:1}),i||(i={x:e/2,y:a/2,radius:1}),{x:n.x*(1-r)+i.x*r,y:n.y*(1-r)+i.y*r,radius:n.radius*(1-r)+i.radius*r}});return B(n)}},w=m.selectAll(".venn-area").data(c,function(t){return t.sets}),I=w.enter().append("g").attr("class",function(t){return"venn-area venn-"+(1==t.sets.length?"circle":"intersection")}).attr("data-venn-sets",function(t){return t.sets.join("_")}),R=I.append("path"),P=I.append("text").attr("class","label").text(function(t){return n(t)}).attr("text-anchor","middle").attr("dy",".35em").attr("x",e/2).attr("y",a/2);l&&(R.style("fill-opacity","0").filter(function(t){return 1==t.sets.length}).style("fill",function(t){return p(n(t))}).style("fill-opacity",".25"),P.style("fill",function(t){return 1==t.sets.length?p(n(t)):"#444"}));var A=t;z?(A=t.transition("venn").duration(s),A.selectAll("path").attrTween("d",b)):A.selectAll("path").attr("d",function(t){return B(t.sets.map(function(t){return g[t]}))});var O=A.selectAll("text").filter(function(t){return t.sets in d}).text(function(t){return n(t)}).attr("x",function(t){return Math.floor(d[t.sets].x)}).attr("y",function(t){return Math.floor(d[t.sets].y)});f&&(z?"on"in O?O.on("end",L(g,n)):O.each("end",L(g,n)):O.each(L(g,n)));var q=w.exit().transition("venn").duration(s).remove();q.selectAll("path").attrTween("d",b);var j=q.selectAll("text").attr("x",e/2).attr("y",a/2);return null!==h&&(P.style("font-size","0px"),O.style("font-size",h),j.style("font-size","0px")),{circles:g,textCentres:d,nodes:w,enter:I,update:A,exit:q}}function n(t){return t.label?t.label:1==t.sets.length?""+t.sets[0]:void 0}var e=600,a=350,i=15,s=1e3,u=Math.PI/2,o=!0,f=!0,l=!0,h=null,x=null,c={},v=["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],g=0,p=function(t){if(t in c)return c[t];var r=c[t]=v[g];return g+=1,g>=v.length&&(g=0),r},y=b;return t.wrap=function(r){return arguments.length?(f=r,t):f},t.width=function(r){return arguments.length?(e=r,t):e},t.height=function(r){return arguments.length?(a=r,t):a},t.padding=function(r){return arguments.length?(i=r,t):i},t.colours=function(r){return arguments.length?(p=r,t):p},t.fontSize=function(r){return arguments.length?(h=r,t):h},t.duration=function(r){return arguments.length?(s=r,t):s},t.layoutFunction=function(r){return arguments.length?(y=r,t):y},t.normalize=function(r){return arguments.length?(o=r,t):o},t.styled=function(r){return arguments.length?(l=r,t):l},t.orientation=function(r){return arguments.length?(u=r,t):u},t.orientationOrder=function(r){return arguments.length?(x=r,t):x},t}function L(t,n){return function(){for(var e,a=r.select(this),i=a.datum(),s=t[i.sets[0]].radius||50,u=n(i)||"",o=u.split(/\s+/).reverse(),f=3,l=(u.length+o.length)/f,h=o.pop(),x=[h],c=0,v=1.1,g=a.text(null).append("tspan").text(h);;){if(h=o.pop(),!h)break;x.push(h),e=x.join(" "),g.text(e),e.length>l&&g.node().getComputedTextLength()>s&&(x.pop(),g.text(x.join(" ")),x=[h],g=a.append("tspan").text(h),c++)}var p=.35-c*v/2,y=a.attr("x"),d=a.attr("y");a.selectAll("tspan").attr("x",y).attr("y",d).attr("dy",function(t,r){return p+r*v+"em"})}}function S(t,r,n){var e,a,i=r[0].radius-o(r[0],t);for(e=1;e<r.length;++e)a=r[e].radius-o(r[e],t),a<=i&&(i=a);for(e=0;e<n.length;++e)a=o(n[e],t)-n[e].radius,a<=i&&(i=a);return i}function _(t,r){var n,a=[];for(n=0;n<t.length;++n){var i=t[n];a.push({x:i.x,y:i.y}),a.push({x:i.x+i.radius/2,y:i.y}),a.push({x:i.x-i.radius/2,y:i.y}),a.push({x:i.x,y:i.y+i.radius/2}),a.push({x:i.x,y:i.y-i.radius/2})}var s=a[0],u=S(a[0],t,r);for(n=1;n<a.length;++n){var f=S(a[n],t,r);f>=u&&(s=a[n],u=f)}var l=m(function(n){return-1*S({x:n[0],y:n[1]},t,r)},[s.x,s.y],{maxIterations:500,minErrorDelta:1e-10}).x,x={x:l[0],y:l[1]},c=!0;for(n=0;n<t.length;++n)if(o(x,t[n])>t[n].radius){c=!1;break}for(n=0;n<r.length;++n)if(o(x,r[n])<r[n].radius){c=!1;break}if(!c)if(1==t.length)x={x:t[0].x,y:t[0].y};else{var v={};e(t,v),x=0===v.arcs.length?{x:0,y:-1e3,disjoint:!0}:1==v.arcs.length?{x:v.arcs[0].circle.x,y:v.arcs[0].circle.y}:r.length?_(t,[]):h(v.arcs.map(function(t){return t.p1}))}return x}function N(t){var r={},n=[];for(var e in t)n.push(e),r[e]=[];for(var a=0;a<n.length;a++)for(var i=t[n[a]],s=a+1;s<n.length;++s){var u=t[n[s]],f=o(i,u);f+u.radius<=i.radius+1e-10?r[n[s]].push(n[a]):f+i.radius<=u.radius+1e-10&&r[n[a]].push(n[s])}return r}function G(t,r){for(var n={},e=N(t),a=0;a<r.length;++a){for(var i=r[a].sets,s={},u={},o=0;o<i.length;++o){s[i[o]]=!0;for(var f=e[i[o]],l=0;l<f.length;++l)u[f[l]]=!0}var h=[],x=[];for(var c in t)c in s?h.push(t[c]):c in u||x.push(t[c]);var v=_(h,x);n[i]=v,v.disjoint&&r[a].size>0&&console.log("WARNING: area "+i+" not represented on screen")}return n}function V(t,r){function n(t){for(var r=0;r<t.length;++r)if(!(t[r]in a))return!1;return!0}for(var e=N(t.selectAll("svg").datum()),a={},i=0;i<r.sets.length;++i){var s=r.sets[i];for(var u in e)for(var o=e[u],f=0;f<o.length;++f)if(o[f]==s){a[u]=!0;break}}t.selectAll("g").sort(function(t,e){return t.sets.length!=e.sets.length?t.sets.length-e.sets.length:t==r?n(e.sets)?-1:1:e==r?n(t.sets)?1:-1:e.size-t.size})}function W(t,r,n){var e=[];return e.push("\nM",t,r),e.push("\nm",-n,0),e.push("\na",n,n,0,1,0,2*n,0),e.push("\na",n,n,0,1,0,2*-n,0),e.join(" ")}function Z(t){var r=t.split(" ");return{x:parseFloat(r[1]),y:parseFloat(r[2]),radius:-parseFloat(r[4])}}function B(t){var r={};e(t,r);var n=r.arcs;if(0===n.length)return"M 0 0";if(1==n.length){var a=n[0].circle;return W(a.x,a.y,a.radius)}for(var i=["\nM",n[0].p2.x,n[0].p2.y],s=0;s<n.length;++s){var u=n[s],o=u.circle.radius,f=u.width>o;i.push("\nA",o,o,0,f?1:0,1,u.p1.x,u.p1.y)}return i.join(" ")}var H=1e-10,J=1e-10;t.intersectionArea=e,t.circleCircleIntersection=l,t.circleOverlap=f,t.circleArea=u,t.distance=o,t.circleIntegral=s,t.venn=b,t.greedyLayout=q,t.scaleSolution=T,t.normalizeSolution=D,t.bestInitialLayout=A,t.lossFunction=j,t.disjointCluster=F,t.distanceFromIntersectArea=w,t.VennDiagram=E,t.wrapText=L,t.computeTextCentres=G,t.computeTextCentre=_,t.sortAreas=V,t.circlePath=W,t.circleFromPath=Z,t.intersectionAreaPath=B,Object.defineProperty(t,"__esModule",{value:!0})});