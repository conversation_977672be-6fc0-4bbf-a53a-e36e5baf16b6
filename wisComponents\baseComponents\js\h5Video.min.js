/*
 * @Author: your name
 * @Date: 2022-02-11 14:39:07
 * @LastEditTime: 2022-06-24 11:02:07
 * @LastEditors: ss1440 <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \ecs-front-video\public\hk\video.js
 */
//错误编码
var errors = {
  "0x12f900001": "接口调用参数错误",
  "0x12f900002": "不在播放状态",
  "0x12f900003": "仅回放支持该功能",
  "0x12f900004": "普通模式不支持该功能",
  "0x12f900005": "高级模式不支持该功能",
  "0x12f900006": "高级模式的解码库加载失败",
  "0x12f900008": "url格式错误",
  "0x12f900009": "视频播放异常，可能设备未推流或推流较慢超时",
  "0x12f900010": "设置或者是获取音量失败，因为没有开启音频的窗口",
  "0x12f900011": "设置的音量不在1-100范围",
  "0x12f910000": "视频播放异常，播放地址连接失败请确认网络是否连通",
  "0x12f910010": "视频播放异常，可能设备未推流或推流较慢超时",
  "0x12f910011": "视频播放异常，客户端电脑配置过低，视频解码中断，建议升级电脑",
  "0x12f910014": "没有音频数据",
  "0x12f910015": "视频播放异常，播放地址连接被关闭请确认网络是否连通",
  "0x12f910016": "视频播放异常，播放地址连接被关闭请确认网络是否连通",
  "0x12f910017": "不支持智能信息展示",
  "0x12f910018": "视频播放异常，可能设备未推流或推流较慢超时",
  "0x12f910019": "wss连接失败，原因：端口尚未开通、证书未安装、证书不安全",
  "0x12f910020": "单帧回放时不能暂停",
  "0x12f910021": "已是最大倍速",
  "0x12f910022": "已是最小倍速",
  "0x12f910023": "视频播放异常，播放地址连接失败请确认网络是否连通",
  "0x12f910026": "视频播放异常，播放控件不支持当前的视频编码格式",
  "0x12f910027":
    "视频播放异常，设备未推流，可能设备突然离线或重启，网络传输超时",
  "0x12f910028": "设置的缓冲区大小无效，大小0-510241024，不在该范围的报错",
  "0x12f910029": "视频播放异常，设备推送的视频码流存在异常不能播放",
  "0x12f910031": "普通模式下播放卡主会出现",
  "0x12f910032": "码流编码格式普通模式下不支持，可切换高级模式尝试播放",
  "0x12f920015": "未调用停止录像，再次调用开始录像",
  "0x12f920016": "未开启录像调用停止录像接口错误",
  "0x12f920017": "紧急录像目标格式不支持，非ps/mp4",
  "0x12f920018": "紧急录像文件名为null",
  "0x12f930010": "内存不足",
  "0x12f930011": "首帧显示之前无法抓图，请稍后重试",
  "0x12f930000": "视频播放异常，客户端电脑配置过低内存不足，建议升级电脑",
  "0x12f950000": "采集音频失败，可能是在非https域下使用对讲导致",
  "0x12f950001": "对讲不支持这种音频编码格式",
  //0x12f9，才是播放控件的错，其他都是后台服务的错
  "0x01900068": "取流失败，设备接入服务报错误码(0x01900068)请联系系统运维人员",
  "0x0190003e": "取流失败，设备接入服务报错误码(0x0190003e)设备不在线或未推流",
  "0x01b01307": "取流失败，流媒体服务报错误码(0x01b01307)视频地址已过期请重新获取地址重试",
  "0x017308cb": "取流失败，后端服务报错误码(0x017308cb)请联系系统运维人员",
  "0x01900050": "取流失败，后端服务报错误码(0x01900050)请联系系统运维人员"
};
//视频播放中不是视频播放出错的错误码
var playErrorCodes = [
  "0x12f950000",
  "0x12f950001",
  "0x12f930011",
  "0x12f920018",
  "0x12f920017",
  "0x12f920015",
  "0x12f920016",
  "0x12f910021",
  "0x12f910022",
  "0x12f910020",
  "0x12f900010",
  "0x12f900011",
  "0x12f910014"
];
//视频底部工具条图标的数据
var fullBtnBoxData = {
  talk: {
    title: "打开声音",
    title1: "静音",
    className: "h5player-dakaishengyin",
    className1: "h5player-dakaishengyin1",
    id: "talk",
  },
  pic: {
    title: "抓图",
    title1: "抓图",
    className: "h5player-waixiangji",
    className1: "h5player-waixiangji",
    noStatus: true,
    id: "picBtn",
  },
  // big: {
  //   title: "开启电子放大",
  //   title1: "关闭电子放大",
  //   className: "h5player-kaiqidianzifangda",
  //   className1: "h5player-kaiqidianzifangda",
  //   id:'bigBtn'
  // },
  info: {
    title: "显示监控点信息",
    title1: "显示监控点信息",
    className: "h5player-xianshijiankongdianxinxi",
    className1: "h5player-xianshijiankongdianxinxi",
    id: "info",
  },
  recordVideo: {
    title: "录制视频",
    title1: "停止录制视频",
    className: "h5player-kaishijinjiluxiang",
    className1: "h5player-kaishijinjiluxiang",
    id: "recordVideo",
  },
  intercom: {
    title: "开始对讲",
    title1: "关闭对讲",
    className: "h5player-kaishiduijiang",
    className1: "h5player-tingzhiduijiang",
    hide: true,
    id: "intercom",
  },
  holder: {
    title: "开启云台",
    title1: "关闭云台",
    className: "h5player-kaiqiyuntai",
    className1: "h5player-kaiqiyuntai",
    hide: true,
    id: "holder",
  },
  big3d: {
    title: "开启3d放大",
    title1: "关闭3d放大",
    className: "h5player-kaiqi3Dfangda",
    className1: "h5player-kaiqi3Dfangda",
    hide: true,
    id: "big3d",
  },
  replay: {
    title: "开启回放",
    title1: "关闭回放",
    className: "h5player-dakaijishihuifang",
    className1: "h5player-dakaijishihuifang",
  },
};

/**
 *  videoParams 视频参数的对象包含参数如下
 * @param {视频容器id 必填} videoBoxId
 * @param {引用H5player.min.js的js路径 必填} szBasePath
 * @param {最大窗口数量 默认16} maxWinNum
 * @param {0 实时视频  1 录像回放 默认0} mode
 * @param {是否显示底部的操作bar 默认 true} showBottom
 * @param {回放开始时间} startTime
 * @param {回放结束时间} endTime
 * @param {静态图片路径 必填} imgPath
 * @param {工具条按钮数组} buttonCodes
 * @param {视频窗口回调函数}onEventVideoWindowCallback
 * @param {设备code是否支持重复 默认支持}devCodeRepeat
 *
 */
function JSVideoObj(videoParams) {
  let {
    videoBoxId,
    szBasePath,
    maxWinNum = 16,
    mode = 0,
    showBottom = true,
    imgPath = "./images",
    startTime = "",
    endTime = "",
    buttonCodes = [
      "close",
      "talk",
      "pic",
      "big",
      "info",
      "recordVideo",
      "intercom",
      "holder",
      "big3d",
    ],
    devCodeRepeat = true,
  } = videoParams;
  this.maxWinNum = maxWinNum; //最大屏幕数
  this.screenNum = 1; //当前分屏数
  this.videoBoxId = videoBoxId;
  this.color1 = "135";
  this.color2 = "206";
  this.color3 = "235";
  this.opcityValue = "1";
  this.selectBorderWidth = "1";
  this.selectedWinNum = 0; //选中得窗口下标
  this.h5playerArr = [];
  this.mode = mode; //0 实时视频 1录像回放
  this.timelineStep = "24h"; //录像回放时间线间隔时间类型
  this.playbackTimeArr = []; //时间轴选中的阶段数组
  this.monitorInfoTimerArr = new Array(this.maxWinNum); //监控点信息定时器
  this.startTime = startTime; //默认所有的回放窗口的初始开始时间值
  this.endTime = endTime; //默认所有的回放窗口的初始结束时间值
  this.imgPath = imgPath;
  this.buttonCodes = buttonCodes;
  this.showBottom = showBottom;
  this.devCodeRepeat = devCodeRepeat;
  this.onEventVideoWindowCallback = () => { };
  let that = this;
  let c = "";
  $("#" + videoBoxId).html("");
  $("#" + videoBoxId).addClass("h5player-container");
  for (var p = 0; p < this.maxWinNum; p++) {
    c += [
      "<div class='js-video-box js-video-box".concat(p, "'"),
      "id='jsVideoBox".concat(p, "'>"),
      "    <div class='h5player' id='h5player".concat(p, "_", videoBoxId, "'></div>"),
      "    <div class='noVideoMask "
        .concat("", "' id='videoMask")
        .concat(p, "' data-status='0'>"),
      "<span class='iconfont h5player-shexiangtou-01'></span>",
      "<i class='iconfont h5player-jiazai hide'></i>",
      "    </div>",
      "    <div id='canvasPlayerCont"
        .concat(
          p,
          "' class='canvasPlayerCont hide' style='height:100%;position: absolute;top:0;pointer-events: none;z-index: 22;width:100%'><canvas class='canvasPlayer' id='canvasPlayer"
        )
        .concat(p, "'"),
      "          data-winNum='".concat(p, "' data-devCode=''></canvas>"),
      "    </div>",
      "        <div class='fullTopBtnBox hide' id='fullTopBtnBox".concat(
        p,
        "'>"
      ),
      "<span class='video-title' id='videoTitle' title='' data-type='info'></span>",
      createButtons(p, "top", this),
      "</div>",
      "<div class='fullBtnBox hide' id='fullBtnBox".concat(p, "'>"),
      createButtons(p, "bottom", this),
      " </div>",
      "<div class='play-error hide' id='playError'><span>播放异常</span><p><button type='replay'>重新播放</button><button type='close'>关闭窗口</button></p></div>",
      " <img src='' class='big-img' id='bigImg".concat(p, "'/>"),
      " <div class='monitor-info' id='monitorInfo".concat(p, "'>"),
      "</div>",
      " <div class='monitor-ptz hide' id='monitorPtz".concat(p, "'>"),
      "</div>",
      "</div>",
    ].join("");
  }
  var v = "";
  (v += "<div class='js-video-zoom' id='videoZoom'>"),
    (v +=
      "    <div class='video-error hide'><i class='iconfont h5player-guanbi_o'></i><p id='errorContent'></p></div>"),
    (v +=
      "    <div class='video-warning hide' id='videoWarning'><i class='iconfont h5player-jinggao'></i><p></p></div>"),
    (v += "    <div class='js-video-zoomie'>"),
    (v += c),
    (v += "    </div>");

  //录像回放时间标尺
  if (this.mode === 1) {
    (v += "    <div class='playback-container' id='playbackContainer'>"),
      (v += '<ul></ul><div class="center"></div>'),
      (v +=
        '<div id="timeBtn"><i class="disabled">-</i><span>24h</span><i>+</i></div>'),
      (v += "    </div>");
  }
  //底部操作按钮
  if (this.showBottom) {
    (v += "        <div class='moitor-bottom' id='videoBottom'>"),
      (v += "        </div>"),
      (v += "</div>");
  }

  $("#" + videoBoxId).html(v);
  videoInit(that, szBasePath, videoBoxId);
  //初始化创建回放时间标尺
  if (this.mode === 1) {
    let options = dealTimelineData(
      this.startTime,
      this.endTime,
      this,
      this.selectedWinNum
    );
    createPlaybackTimeline(options, [], this);
    changeVideoObjScreen(1, this, "1x1");
  } else {
    changeVideoObjScreen(4, this, "2x2");
  }
  showBottom ? $("#videoBottom").html(showBottomBar(this)) : "";
  eventInit(this);
}
JSVideoObj.prototype = {
  /**
   *
   * @param callbackJson 回调函数（参数为回调类型，回调窗口的一些参数属性）
   */
  JS_SetWindowControlCallback(
    callbackJson = {
      cbIntegrationCallBack: (type, params) => { },
    }
  ) {
    this.onEventVideoWindowCallback = callbackJson.cbIntegrationCallBack;
  },
  /**
   *创建历史回放的时间轴
   * @param {*开始时间} startTime
   * @param {*结束时间} endTime
   * @param {*视频回放时间段集合} playbackTimeArr
   * @param {*时间轴间距} optionWidth
   * @param {*当前回放视频窗口的下标} winNum
   */
  JS_CreatePlaybackTimeline: function (
    startTime,
    endTime,
    playbackTimeArr = [],
    winNum,
    optionWidth = 60
  ) {
    //点击播放时暂存当前回放视频窗口的开始、结束时间
    this.h5playerArr[winNum].startTime = startTime;
    this.h5playerArr[winNum].endTime = endTime;
    this.h5playerArr[winNum].playbackTimeArr = playbackTimeArr; //每次重新播放时存储回放时间段
    let options = dealTimelineData(startTime, endTime, this, winNum);
    createPlaybackTimeline(options, playbackTimeArr, this, optionWidth);
  },
  /**
   * 播放视频
   * @param {*播放得视频窗口下标} winNum
   * @param {*播放得视频url} url
   * @param {* 解码类型：0=普通模式; 1=高级模式 默认为1} videoMode
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   * @returns
   */
  JS_Play: function (
    winNum,
    url,
    videoMode,
    devCode,
    devName,
    isPicture = false,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    playVideo(
      winNum,
      url,
      videoMode,
      devCode,
      devName,
      isPicture,
      this,
      successCallback,
      errCallback
    );
  },
  /**
   * 改变视频窗口分屏数
   * @param {*分屏数的字符串} splitStr
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   * @returns
   */
  JS_ArrangeWindow: function (
    splitStr,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    let data = splitScreenIconList.find((item) => item.tipMsg === splitStr);
    if (!data) {
      showWarningMessage("不支持该分屏");
      return;
    }
    changeVideoObjScreen(
      data.msg,
      this,
      splitStr,
      successCallback,
      errCallback
    );
  },
  /**
   * 关闭单个视频
   * @param {*播放得视频窗口下标} winNum
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   * @returns
   */
  JS_Stop: function (
    winNum,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    stopVideo(winNum, this, successCallback, errCallback);
  },
  /**
   * 关闭/销毁所有视频
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   * @returns
   */
  JS_StopRealPlayAll: function (
    successCallback = () => {
      console.log('stop success')
    },
    errCallback = () => {
      console.log('stop error')
    }
  ) {
    closeAllVideo(this, successCallback, errCallback);
  },
  /**
   * 设置选中窗口的边框
   * @param {*} r
   * @param {*} g
   * @param {*} b
   * @param {*边框透明度} a
   * @param {*边框宽度} w
   * @returns
   */
  JS_SetWinColor: function (r, g, b, a, w) {
    try {
      (this.color1 = r),
        (this.color2 = g),
        (this.color3 = b),
        (this.opcityValue = a),
        (this.selectBorderWidth = w);
    } catch (e) {
      return console.log(e.message), 0;
    }
    return 1;
  },
  /**
   *切换选中窗口
   * @param {*选中窗口的下标} windInd
   * @returns
   */
  JS_SelectWnd: function (windInd) {
    try {
      setSelectWin(windInd, this);
    } catch (e) {
      console.log(e);
    }
  },
  /**
   * 停止对讲
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_StopTalk: function (
    winNum,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    stopTalk(winNum, this, successCallback, errCallback);
  },
  /**
   *
   * 开始对讲
   * @param winNum 窗口下标
   * @param talkUrl 对讲url
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_StartTalk: function (
    winNum,
    talkUrl,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    startTalk(winNum, talkUrl, this, successCallback, errCallback);
  },
  /**
   * 打开声音
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_OpenSound(winNum, successCallback = () => { }, errCallback = () => { }) {
    openSound(winNum, this, successCallback, errCallback);
  },
  /**
   * 关闭声音
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_CloseSound(winNum, successCallback = () => { }, errCallback = () => { }) {
    closeSound(winNum, this, successCallback, errCallback);
  },
  /**
   * 设置音量
   * @param winNum 窗口下标
   *  @param volumn 音量大小 范围1~100
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_SetVolume(
    winNum,
    volumn,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    setVolume(winNum, volumn, this, successCallback, errCallback);
  },
  /**
   * 获取当前音量
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_GetVolume(winNum, successCallback = () => { }, errCallback = () => { }) {
    getVolume(winNum, this, successCallback, errCallback);
  },
  /**
   *开始录像
   * @param winNum 窗口下标
   * @param fileName 文件名(保证全局唯一性) 可不带后缀，默认为.mp4
   * @param idstType 录像文件类型(默认5) 2-ps 5-mp4 ,mp4录制音频限制，仅支持AAC、G711A、G711U
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_StartSaveEx(
    winNum,
    fileName,
    idstType = 5,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    startSaveEx(winNum, fileName, idstType, this, successCallback, errCallback);
  },
  /**
   *停止录像并保存文件
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_StopSave(winNum, successCallback = () => { }, errCallback = () => { }) {
    stopSave(winNum, this, successCallback, errCallback);
  },
  /**
   *抓图
   * @param winNum 窗口下标
   * @param fileName 文件名
   * @param isDownload 是否下载本地(默认下载) 不下载本地会执行 successCallback 参数为imageData抓图的信息是base64
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_CapturePicture(
    winNum,
    fileName,
    isDownload = true,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    capturePicture(
      winNum,
      fileName,
      "JPEG",
      isDownload,
      this,
      successCallback,
      errCallback
    );
  },
  /**
   *设置对讲音量
   * @param winNum 窗口下标
   * @param nVolume 音量大小（0-100）
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_TalkSetVolume(
    winNum,
    nVolume,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    talkSetVolume(winNum, nVolume, this, successCallback, errCallback);
  },
  /**
   *获取对讲音量
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_TalkGetVolume(
    winNum,
    nVolume,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    talkGetVolume(winNum, this, successCallback, errCallback);
  },
  /**
   *设置取流连接超时时间
   * @param winNum 窗口下标
   * @param nTime 超时时间（不传时默认6秒，单位秒）
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_SetConnectTimeOut(
    winNum,
    nTime = 6,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    setConnectTimeOut(winNum, nTime, this, successCallback, errCallback);
  },
  /**
   *获取指定窗口状态
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   * @returns 成功返回选中窗口状态（true 正在播放，false 未播放)，失败返回-1
   */
  JS_GetSelectWinStatus(
    winNum,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    return getSelectWinStatus(winNum, this, successCallback, errCallback);
  },
  /**
   *获取选中窗口
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   * @param return 成功返回选中窗口，失败返回-1
   */
  JS_GetSelectWinNum(successCallback = () => { }, errCallback = () => { }) {
    return getSelectWinNum(this, successCallback, errCallback);
  },
  /**
   *整体全屏
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_FullScreenDisplay(successCallback = () => { }, errCallback = () => { }) {
    fullScreen(
      document.body.querySelector("#" + this.videoBoxId + " .js-video-zoomie"),
      successCallback,
      errCallback
    );
  },
  /**
   *单窗口全屏
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_FullScreenSingle(
    winNum,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    fullScreenSingle(winNum, this, successCallback, errCallback);
  },
  /**
   *获取音视频信息
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_GetVideoInfo(winNum, successCallback = () => { }, errCallback = () => { }) {
    getVideoInfo(winNum, this, successCallback, errCallback);
  },
  /**
   *回放单帧进
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_FrameForward(winNum, successCallback = () => { }, errCallback = () => { }) {
    frameForward(winNum, this, successCallback, errCallback);
  },
  /**
   *回放慢放(调节播放倍速为当前播放速度的1/2倍，最小为1/8倍。）
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_Slow(winNum, successCallback = () => { }, errCallback = () => { }) {
    slow(winNum, this, successCallback, errCallback);
  },
  /**
   *回放快放(调节播放倍速为当前播放速度的2倍，最大为8倍。）
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_Fast(winNum, successCallback = () => { }, errCallback = () => { }) {
    fast(winNum, this, successCallback, errCallback);
  },
  /**
   *回放定位
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_Seek: function (
    winNum,
    stratTime,
    endTime,
    successCallback = () => { },
    errCallback = () => { }
  ) {
    seek(winNum, stratTime, endTime, this, successCallback, errCallback);
  },
  /**
   *恢复回放
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_Resume(winNum, successCallback = () => { }, errCallback = () => { }) {
    resume(winNum, this, successCallback, errCallback);
  },
  /**
   *暂停回放
   * @param winNum 窗口下标
   * @param successCallback 成功回調
   * @param errCallback 失败回调
   */
  JS_Pause(winNum, successCallback = () => { }, errCallback = () => { }) {
    pause(winNum, this, successCallback, errCallback);
  },
  /**
  *自适应窗口大小
  */
  JS_Resize() {
    if (this.h5playerArr.length) {
      this.h5playerArr.forEach((ele) => {
        ele.h5player.JS_Resize();
      });
    }
  },
};
/**
 *
 * 创建工具条按钮
 * @param winInd 播放窗口下标
 * @param buttonPosition（工具条位置top:上工具条 bottom下工具条）
 */
function createButtons(winInd, buttonPosition, that) {
  let templateArr = [];
  if (buttonPosition == "top" && that.buttonCodes.indexOf("close") != -1) {
    templateArr = [
      "<span class='closeBtn iconfont h5player-guanbi_o' title='关闭视频' id='closeBtn' data-type='close' data-status='0'></span>",
    ];
  } else if (buttonPosition == "bottom") {
    templateArr = that.buttonCodes.map((type) => {
      return fullBtnBoxData.hasOwnProperty(type)
        ? `<span id='${fullBtnBoxData[type].id}' class="iconfont ${fullBtnBoxData[type].className
        } ${that.mode == 1 && fullBtnBoxData[type].hide ? "hide" : ""}"
                    title="${fullBtnBoxData[type].title}"  ${!fullBtnBoxData[type].noStatus ? "data-status=0" : ""
        } data-type="${type}"
                ></span>`
        : "";
    });
  }
  return templateArr.join(",");
}
//日期格式化
Date.prototype.format = function (fmt) {
  var o = {
    "M+": this.getMonth() + 1, //月份
    "d+": this.getDate(), //日
    "h+": this.getHours(), //小时
    "m+": this.getMinutes(), //分
    "s+": this.getSeconds(), //秒
    "q+": Math.floor((this.getMonth() + 3) / 3), //季度
    S: this.getMilliseconds(), //毫秒
  };

  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (this.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }

  for (var k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }
  return fmt;
};
/**
 * 处理传入的开始结束时间 分别扩大8小时同时就近取整
 * @param {*开始时间} startTime
 * @param {*结束时间} endTime
 * @param {*} that
 * @returns
 */
function dealTimelineData(startTime, endTime, that, winNum) {
  startTime = startTime.replaceAll("-", "/");
  endTime = endTime.replaceAll("-", "/");
  let intervalTimeObj = {
    "24h": 2,
    "12h": 1,
    "6h": 0.5,
    "2h": 1 / 6,
    "1h": 5 / 60,
  };
  let intervalTime = 2;
  //开始减8小时判断向前一天取整，还是当前取整
  let startDate =
    new Date(startTime).getHours() >= 8
      ? new Date(startTime).format("yyyy/MM/dd").concat(" 00:00:00")
      : new Date(new Date(startTime).valueOf() - 24 * 60 * 60 * 1000)
        .format("yyyy/MM/dd")
        .concat(" 00:00:00");
  //结束时间加8小时 判断向后一天取整，还是向后两天天取整
  let endDate =
    new Date(endTime).getHours() > 16
      ? new Date(new Date(endTime).valueOf() + 48 * 60 * 60 * 1000)
        .format("yyyy/MM/dd")
        .concat(" 00:00:00")
      : new Date(new Date(endTime).valueOf() + 24 * 60 * 60 * 1000)
        .format("yyyy/MM/dd")
        .concat(" 00:00:00");
  let startSeconds = new Date(startDate).getTime();
  let endSeconds = new Date(endDate).getTime();
  let dateOptions = [];
  for (let seconds = startSeconds; seconds <= endSeconds;) {
    let date = new Date(seconds);
    let obj = {
      date: date.format("yyyy/MM/dd hh:mm:ss"),
      text: date.format("hh:mm"),
      value: seconds,
    };
    dateOptions.push(obj);
    seconds =
      seconds + Number(intervalTimeObj[that.timelineStep]) * 60 * 60 * 1000;
  }
  return dateOptions;
}
/**
 *创建录像回放的时间轴
 * @param {时间数据} options
 * @param {*时间轴间隔} optionWidth
 * @param {*视频回放时间段集合} playbackTimeArr
 *
 */
function createPlaybackTimeline(
  options,
  playbackTimeArr = [],
  that,
  optionWidth = 60
) {
  let container = document.querySelector(".playback-container");
  let ul = document.querySelector(".playback-container ul");
  let containerWidth = container.clientWidth;
  let ulWidth = options.length * optionWidth;
  let maxLeft = containerWidth / 2;
  let minLeft = maxLeft - ulWidth + optionWidth;
  //该事件常使用于使目标对象“禁止变蓝”
  container.onselectstart = function () {
    return false;
  };
  ul.innerHTML = "";
  var frag = document.createDocumentFragment();

  ul.style.width = ulWidth + "px";
  ul.style.position = "relative";
  for (const op of options) {
    var li = document.createElement("li");
    li.setAttribute("title", op.date);
    li.innerHTML = `<i data-value="${op.value}">${op.text}</i>`;
    li.style.width = optionWidth + "px";
    frag.appendChild(li);
  }
  let playbackIndexs = []; //放视频阶段的下标
  //渲染有回放视频阶段
  for (let data of playbackTimeArr) {
    let div = document.createElement("div");
    frag.appendChild(div);
    let startIndex = options.findIndex(
      (item) => item.value >= new Date(data.beginTime).getTime()
    );
    startIndex = startIndex === 0 ? startIndex : startIndex - 1;
    let endIndex = options.findIndex(
      (item) => item.value >= new Date(data.endTime).getTime()
    );
    playbackIndexs = playbackIndexs.concat([startIndex, endIndex]);
    div.style.width = Math.abs(endIndex - startIndex) * optionWidth + "px";
    div.style.height = "30px";
    div.style.backgroundColor = "#31638b85";
    div.style.position = "absolute";
    div.style.bottom = "2px";
    div.style.left = startIndex * optionWidth + "px";
  }
  ul.appendChild(frag);
  let minIndex = playbackIndexs.length
    ? Math.min.apply(null, playbackIndexs)
    : 0;
  console.log(options[minIndex].value)
  setChoose(options[minIndex].value); //默认选中的值
  //设置选中的值
  function setChoose(value, t = that) {
    value = value.toString();
    ul.style.transition = ".5s";
    var children = Array.from(ul.children);
    var i = children.findIndex(
      (dom) => dom.querySelector("i").dataset.value === value
    );
    if (i === -1) return;
    //计算margin-left
    var left = containerWidth / 2 - i * optionWidth;
    ul.style.marginLeft = left + "px";
  }
  /**
   * 根据当前ul的位置，计算出选中的对象
   */
  function getChoose() {
    //获取ul的marginLeft
    var left = getComputedStyle(ul).marginLeft;
    left = parseFloat(left) - containerWidth / 2;
    left = Math.abs(left);
    var i = left / optionWidth;
    i = Math.round(i);
    return options[i];
  }

  //拖拽事件
  function regDragEvent(callback) {
    ul.onmousedown = function (e) {
      ul.style.transition = "";
      var x = e.pageX,
        left = parseFloat(getComputedStyle(this).marginLeft);
      window.onmousemove = function (e) {
        var dis = e.pageX - x;
        var newLeft = left + dis;
        if (newLeft < minLeft) {
          newLeft = minLeft;
        } else if (newLeft > maxLeft) {
          newLeft = maxLeft;
        }
        ul.style.marginLeft = newLeft + "px";
        callback && callback(getChoose());
      };
      window.onmouseup = function () {
        window.onmousemove = null;
        var op = getChoose();
        setChoose(op.value);
        console.log(op.value)
        console.log(new Date(op.value).format("YYYY-MM-DD hh:mm:ss"))
        // $(".history-video-picker").val(op.value.format("Y-m-d H:i:s"))
        callback && callback(op);
        window.onmouseup = null;
      };
    };
  }
  regDragEvent();
}

/**
 * 初始化海康视频
 *
 */
function videoInit(that, szBasePath, videoBoxId) {
  for (let i = 0; i < that.maxWinNum; i++) {
    let videoData = {
      h5player: null,
      devCode: "",
      isPicture: false,
      url: "",
      devName: "",
      startTime: that.startTime, //暂存回放查询的开始时间
      endTime: that.endTime, //暂存回放查询的结束时间
      playbackTimeArr: [], //回放时间段
      playIng: 0, //当前视频的状态 0 未播放 1 播放中 2播放出错
    };
    videoData.h5player = new window.JSPlugin({
      // szId: "h5player" + i, //需要英文字母开头 必填
      szId: "h5player" + i + "_" + videoBoxId, //需要英文字母开头 必填

      szBasePath, // 必填,引用H5player.min.js的js相对路径
      // 当容器div#play_window有固定宽高时，可不传iWidth和iHeight，窗口大小将自适应容器宽高
      // iWidth: 600,
      // iHeight: 400,
      // 分屏播放，默认最大分屏4*4
      iMaxSplit: 1,
      iCurrentSplit: 1,
      // 样式
      oStyle: {
        border: "transparent",
        borderSelect: "transparent",
        background: "transparent",
      },
    });

    videoData.h5player.JS_SetWindowControlCallback({
      pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {
        //插件错误回调
        console.log("pluginError callback: ", iWndIndex, iErrorCode, oError);
        that.h5playerArr[i].playIng = 2
        showError(iErrorCode, that.h5playerArr[i].devName, i + 1, that);
        //展示视频播放异常
        if (!playErrorCodes.includes(iErrorCode)) {
          //异常样式打开
          $(`#${that.videoBoxId} #jsVideoBox${i} #playError`)
            .removeClass("hide")
            .addClass("show");
          //关闭正常播放样式
          $(`#${that.videoBoxId} #jsVideoBox${i} #videoMask${i}>i`)
            .addClass("hide")
            .removeClass("show");
          //报错清除云台操作
          let holder = $(`#${that.videoBoxId} #jsVideoBox${i} #fullBtnBox${i} #holder`);
          if (holder && holder.attr("data-status") == 1) {
            holder.attr("data-status", 0);
            $(`#${that.videoBoxId} #jsVideoBox${i} #monitorPtz${i}`)
              .html("")
              .addClass("hide");
          }
          //关闭3d电子放大
          let big3d = $(`#${that.videoBoxId} #jsVideoBox${i} #fullBtnBox${i} #big3d`);
          if (big3d && big3d.attr("data-status") == 1) {
            initDrawReact(that, i, big3d.attr("data-status"));
            big3d.attr("data-status", 0);
          }
        } else {
          //其他异常错误 关闭loading 展示默认样式
          closeLoading(that, i);
        }

        that.onEventVideoWindowCallback(
          "pluginErrorHandler",
          (params = {
            winNum: i,
            devCode: that.h5playerArr[i].devCode,
            iErrorCode,
            oError,
          })
        );
      },
      windowEventSelect: function (iWndIndex) {
        //插件选中窗口回调
        that.onEventVideoWindowCallback(
          "windowEventSelect",
          (params = {
            winNum: i,
            devCode: that.h5playerArr[i].devCode,
          })
        );
      },
      windowEventOver: function (iWndIndex) {
        //鼠标移过回调
        that.onEventVideoWindowCallback(
          "windowEventOver",
          (params = {
            winNum: i,
            devCode: that.h5playerArr[i].devCode,
          })
        );
      },
      windowEventOut: function (iWndIndex) {
        //鼠标移出回调
        that.onEventVideoWindowCallback(
          "windowEventOut",
          i,
          (params = {
            winNum: i,
            devCode: that.h5playerArr[i].devCode,
          })
        );
      },
      windowEventUp: function (iWndIndex) {
        //鼠标mouseup事件回调
        that.onEventVideoWindowCallback(
          "windowEventUp",
          (params = {
            winNum: i,
            devCode: that.h5playerArr[i].devCode,
          })
        );
      },
      firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {
        //首帧显示回调
        that.onEventVideoWindowCallback(
          "firstFrameDisplay",
          (params = {
            winNum: i,
            devCode: that.h5playerArr[i].devCode,
            iWidth,
            iHeight,
          })
        );
      },
      performanceLack: function () {
        //性能不足回调
        that.onEventVideoWindowCallback(
          "performanceLack",
          (params = {
            winNum: i,
            devCode: that.h5playerArr[i].devCode,
          })
        );
      },
    });
    that.h5playerArr.push(videoData);
  }
  // 设置播放容器的宽高并监听窗口大小变化
  window.addEventListener("resize", () => {
    that.h5playerArr.forEach((ele) => {
      ele.h5player.JS_Resize();
    });
  });
  that.h5playerArr.forEach(ele => {
    ele.h5player.JS_SetConnectTimeOut(0, 600).then(
      () => {
        console.info("JS_SetConnectTimeOut success");
      },
      (err) => {
        console.info("JS_SetConnectTimeOut failed");
      }
    );
  })

}

/**
 * 初始化事件
 * @param {*视频窗口} that
 */
function eventInit(that) {
  for (let e = 0; e < that.maxWinNum; e++) {
    $("#" + that.videoBoxId + " #h5player" + e + "_" + that.videoBoxId).click(function (event) {
      setSelectWin(e, that);
      //每次点击回放窗口重新绘制时间标尺
      if (that.mode == 1) {
        let options = dealTimelineData(
          that.h5playerArr[e].startTime,
          that.h5playerArr[e].endTime,
          that,
          e
        );
        createPlaybackTimeline(
          options,
          that.h5playerArr[e].playbackTimeArr,
          that
        );
      }
      //点击id为parentId之外的地方触发
      if ($(event.target).closest("#splitScreenBox").length == 0) {
        $("#splitScreenBox").addClass("hide").removeClass("open");
        $(
          ".moitor-bottom>.h5player-operate-icon.split-screen-selected"
        ).removeClass("split-screen-selected");
      }
    });
    //视频播放异常出现关闭按钮
    $(`#${that.videoBoxId} #jsVideoBox${e} #playError button`).click(function (
      event
    ) {
      setSelectWin(e, that);
      if ($(this).attr("type") == "close") {
        //异常样式关闭
        $(`#${that.videoBoxId} #jsVideoBox${e} #playError`)
          .removeClass("show")
          .addClass("hide");
        //展示正常播放样式
        $(`#${that.videoBoxId} #jsVideoBox${e} #videoMask${e} span`)
          .addClass("show")
          .removeClass("hide");
        stopVideo(e, that);
      } else {
        that.h5playerArr[e].playIng = 0
        that.onEventVideoWindowCallback(
          "repeatPlay",
          (params = {
            winNum: e,
            devCode: that.h5playerArr[e].devCode,
            devName: that.h5playerArr[e].devName,
          })
        )
      }
    });
    //视频播放窗口鼠标进入工具栏出现消失
    document
      .querySelector("#" + that.videoBoxId + " #jsVideoBox" + e)
      .addEventListener("mouseenter", function (event) {
        if (that.h5playerArr[e].devCode && that.h5playerArr[e].playIng) {
          !that.h5playerArr[e].isPicture && that.h5playerArr[e].playIng != 2
            ? $(
              `#${that.videoBoxId} #jsVideoBox${e} #fullBtnBox${e}`
            ).removeClass("hide")
            : "";
          $(
            `#${that.videoBoxId} #jsVideoBox${e} #fullTopBtnBox${e}`
          ).removeClass("hide");
          $(
            `#${that.videoBoxId} #jsVideoBox${e} #fullTopBtnBox${e} #videoTitle`
          ).html(that.h5playerArr[e].devName);
          $(
            `#${that.videoBoxId} #jsVideoBox${e} #fullTopBtnBox${e} #videoTitle`
          ).attr({
            title: that.h5playerArr[e].devName,
          });
        }
      });
    document
      .querySelector("#" + that.videoBoxId + " #jsVideoBox" + e)
      .addEventListener("mouseleave", function (event) {
        $(`#${that.videoBoxId} #jsVideoBox${e} #fullBtnBox${e}`).addClass(
          "hide"
        );
        $(`#${that.videoBoxId} #jsVideoBox${e} #fullTopBtnBox${e}`).addClass(
          "hide"
        );
      });

    //底部工具栏切换图标
    $(`#${that.videoBoxId} #jsVideoBox${e} #fullBtnBox${e}`).click((event) => {
      setSelectWin(e, that);
      let target = event.target;
      if (target.nodeName == "SPAN") {
        let status = target.dataset.status;
        let type = target.dataset.type;
        if (status) {
          target.dataset.status = status == 0 ? 1 : 0;
          if (fullBtnBoxData.hasOwnProperty(type)) {
            target.setAttribute(
              "title",
              target.dataset.status == 0
                ? fullBtnBoxData[type].title
                : fullBtnBoxData[type].title1
            );
            target.dataset.status == 0
              ? (target.classList.remove(fullBtnBoxData[type].className1),
                target.classList.add(fullBtnBoxData[type].className))
              : (target.classList.remove(fullBtnBoxData[type].className),
                target.classList.add(fullBtnBoxData[type].className1));
          }
        }
        toolOperate(that, type, status, e);
      }
    });
    $(`#${that.videoBoxId} #jsVideoBox${e} #fullTopBtnBox${e}`).click(
      (event) => {
        setSelectWin(e, that);
      }
    );
    $(`#${that.videoBoxId} #jsVideoBox${e} #fullTopBtnBox${e} #closeBtn`).click(
      (event) => {
        let status = event.target.dataset.status;
        if (status) {
          event.target.dataset.status = status == 0 ? 1 : 0;
        }
        toolOperate(that, event.target.dataset.type, status, e);
      }
    );
    //云台操作
    $(`#${that.videoBoxId} #jsVideoBox${e} #monitorPtz${e}`)
      // .click(function (event) {
      //   if (event.target.nodeName == "P") {
      //     let type =
      //       event.target.dataset.type == "c" ? "" : event.target.dataset.type;
      //    that.onEventVideoWindowCallback("ptzOperate",params={winNum:e,devCode:that.h5playerArr[e].devCode,ptzControl:type,eventType:'click'});
      //   }
      // })
      .mousedown(function (event) {
        setSelectWin(e, that);
        let type =
          event.target.dataset.type == "c" ? "" : event.target.dataset.type;
        that.h5playerArr[e].devCode ? that.onEventVideoWindowCallback(
          "ptzOperate",
          (params = {
            winNum: e,
            devCode: that.h5playerArr[e].devCode,
            ptzControl: type,
            eventType: "mousedown",
          })
        ) : ''
      })
      .mouseup(function (event) {
        setSelectWin(e, that);
        let type =
          event.target.dataset.type == "c" ? "" : event.target.dataset.type;
        that.h5playerArr[e].devCode ? that.onEventVideoWindowCallback(
          "ptzOperate",
          (params = {
            winNum: e,
            devCode: that.h5playerArr[e].devCode,
            ptzControl: type,
            eventType: "mouseup",
          })
        ) : ''
      });
  }
  //底部按钮的操作
  $("#videoBottom .operate-button").click(function (event) {
    if (!event.isPropagationStopped()) {
      //确定stopPropagation是否被调用过
      let type = $(this).attr("data-type");
      switch (type) {
        case "split":
          // 分屏
          $(this)
            .addClass("split-screen-selected")
            .siblings()
            .removeClass("split-screen-selected");
          $("#splitScreenBox").addClass("open").removeClass("hide");
          break;
        case "splitScreen":
          $(this)
            .addClass("split-screen-selected")
            .siblings()
            .removeClass("split-screen-selected");
          // 分屏
          changeVideoObjScreen(Number($(this).text()), that);
          break;
        case "closeAllVideo":
          // 全部关闭
          closeAllVideo(that);
          break;
        case "fullScreen":
          // 全屏
          fullScreen(
            document.body.querySelector(
              "#" + that.videoBoxId + " .js-video-zoomie"
            )
          );
          break;
        case "screenshots":
          //全部抓图
          that.h5playerArr.forEach((item) => {
            if (item.devCode) {
              item.h5player.JS_CapturePicture(0, item.devName, "JPEG").then(
                () => {
                  console.info("JS_CapturePicture success");
                  // do you want...
                },
                (err) => {
                  console.info("JS_CapturePicture failed");
                  // do you want...
                }
              );
            }
          });
          break;
        case "play":
          that.h5playerArr[that.selectedWinNum].devCode
            ? resume(that.selectedWinNum, that, () => {
              that.h5playerArr[that.selectedWinNum].playIng = 1;
              $(this).addClass("hide").next().removeClass("hide");
            })
            : "";
          break;
        case "pause":
          that.h5playerArr[that.selectedWinNum].devCode
            ? pause(that.selectedWinNum, that, () => {
              that.h5playerArr[that.selectedWinNum].playIng = 0;
              $(this).addClass("hide").prev().removeClass("hide");
            })
            : "";
          break;
        case "playbackSpeed":
          that.h5playerArr[that.selectedWinNum].playIng == 1
            ? fast(that.selectedWinNum, that, (rate) => {
              console.log("当前播放速度" + rate);
            })
            : "";
          break;
        case "playbackSlowSpeed":
          that.h5playerArr[that.selectedWinNum].playIng == 1
            ? slow(that.selectedWinNum, that, (rate) => {
              console.log("当前播放速度" + rate);
            })
            : "";
          break;
        case "singleFrame":
          $(this).prev().addClass("hide").prev().removeClass("hide");
          that.h5playerArr[that.selectedWinNum].playIng == 1
            ? frameForward(that.selectedWinNum, that, () => {
              // that.h5playerArr[that.selectedWinNum].playIng = 0;
              console.info("JS_FrameForward success");
            })
            : "";
          break;
      }
    }
    event.stopPropagation();
  });
  $(document).mousedown(function (e) {
    //点击id为parentId之外的地方触发
    if ($(e.target).closest("#splitScreenBox").length == 0) {
      $("#splitScreenBox").addClass("hide").removeClass("open");
      $(
        ".moitor-bottom>.h5player-operate-icon.split-screen-selected"
      ).removeClass("split-screen-selected");
    }
  });
  //录像回放切换时间间隔
  if (that.mode === 1) {
    $("#playbackContainer #timeBtn i").click(function (event) {
      if (event.target.nodeName == "I") {
        let arr = ["1h", "2h", "6h", "12h", "24h"];
        let type = event.target.innerText;
        let index = arr.findIndex(
          (item) => item == $("#playbackContainer #timeBtn span").text()
        );
        if (
          (index == 0 && type == "+") ||
          (index == arr.length - 1 && type == "-")
        ) {
          return;
        }
        type == "+" ? --index : index++;
        that.timelineStep = arr[index];
        $("#playbackContainer #timeBtn span").html(arr[index]);
        index == 0
          ? $(this).addClass("disabled")
          : index == arr.length - 1
            ? $(this).addClass("disabled")
            : $("#playbackContainer #timeBtn i").removeClass("disabled");

        let options = dealTimelineData(
          that.h5playerArr[that.selectedWinNum].startTime,
          that.h5playerArr[that.selectedWinNum].endTime,
          that,
          that.selectedWinNum
        );

        createPlaybackTimeline(
          options,
          that.h5playerArr[that.selectedWinNum].playbackTimeArr,
          that
        );
      }
    });
    //录像回放初始化定位时间
    //年月日时分秒
    $(".history-video-picker").jqdatetimepicker({
      format: "Y-m-d H:i:s",
      onChangeDateTime: function (dp, $input) {
        let stratTime =
          $(".history-video-picker").val().replace(" ", "T") + "Z";
        let endTime =
          that.h5playerArr[that.selectedWinNum].endTime.replace(" ", "T") + "Z";
        console.log(stratTime)
        console.log(endTime)
        setSelectWin(that.selectedWinNum, that)
        that.h5playerArr[that.selectedWinNum].playIng == 1
          ? seek(that.selectedWinNum, that, stratTime, endTime) : "";
        //     .then(
        //       () => {
        //         console.log("seekTo success");
        //       },
        //       (e) => {
        //         console.error(e);
        //       }
        //     )
        // : "";
      },
      onClose: function (current_time, $input) { },
    });
  }
}

function showError(error, devName, winNum, that) {
  console.log("错误编码：", error);
  $(`#${that.videoBoxId} .video-error #errorContent`).html(
    `${winNum} 号窗口${devName}${errors.hasOwnProperty(error + "")
      ? errors[error]
      : `操作失败，后端服务报错误码(${error})请联系系统运维人员`
    }`
  );
  $(`#${that.videoBoxId} .video-error`).addClass("show").removeClass("hide");
  setTimeout(() => {
    $(`#${that.videoBoxId} .video-error`).addClass("hide").removeClass("show");
  }, 3000);
}

/**
 * 渲染监控点信息数据
 *  @param {*监控点数据} videoInfo
 */
function renderMonitorInfoHtml(that, videoInfo = "") {
  return videoInfo
    ? `<div><label>监控点:</label><p>${videoInfo.devName}</p></div>
        <div><label>帧率:</label><p>${videoInfo.frameRate}</p></div>
        <div><label>分辨率:</label><p>${videoInfo.width}*${videoInfo.height}</p></div>
        <div><label>码率:</label><p>${videoInfo.bitRate.toFixed(2)}Kbps</p></div>
        <div><label>编码格式:</label><p>${videoInfo.VideType}</p></div>
        <div><label>封装格式:</label><p>${videoInfo.systemFormt}</p></div>
        `
    : "";
}

/**
 * @param {*播放窗口的操作图标的状态} status
 * @param {*播放窗口的操作类型} type
 * @param {*播放窗口的下标} e
 * @param {*播放窗口实例} that
 */
function toolOperate(that, type, status, e) {
  //创建云台操作的鼠标范围
  function createHolder() {
    let holders = [
      {
        imgName: "left-up-arrow",
        type: "lu",
      },
      {
        imgName: "up-arrow",
        type: "u",
      },
      {
        imgName: "right-up-arrow",
        type: "ru",
      },
      {
        imgName: "left-arrow",
        type: "l",
      },
      {
        imgName: "",
        type: "c",
      },
      {
        imgName: "right-arrow",
        type: "r",
      },
      {
        imgName: "left-down-arrow",
        type: "ld",
      },
      {
        imgName: "down-arrow",
        type: "d",
      },
      {
        imgName: "right-down-arrow",
        type: "rd",
      },
    ];
    let template = holders
      .map(
        (item) =>
          `<p style="cursor:url(${item.imgName ? `${that.imgPath}/${item.imgName}.png` : ""
          }),auto!important;" data-type="${item.type}"></p>`
      )
      .join("");
    return template;
  }
  let holder = $(`#${that.videoBoxId} #jsVideoBox${e} #fullBtnBox${e} #holder`);
  let bigBtn = $(`#${that.videoBoxId} #jsVideoBox${e} #fullBtnBox${e} #bigBtn`);
  let big3d = $(`#${that.videoBoxId} #jsVideoBox${e} #fullBtnBox${e} #big3d`);
  switch (type) {
    case "big3d":
      //关闭电子放大
      if (bigBtn.attr("data-status") == 1) {
        bigBtn.attr("data-status", 0);
        that.h5playerArr[e].h5player.JS_DisableZoom(0).then(
          () => {
            $(`#${that.videoBoxId} #jsVideoBox${e} #bigImg${e}`).attr({
              src: "",
            });
            console.log("enlargeClose success");
          },
          (e) => {
            console.error(e);
          }
        );
      }
      //关闭云台
      if (holder.attr("data-status") == 1) {
        holder.attr("data-status", 0);
        $(`#${that.videoBoxId} #jsVideoBox${e} #monitorPtz${e}`)
          .html("")
          .addClass("hide");
      }
      //开启关闭3d电子放大
      initDrawReact(that, e, status);
      break;
    case "holder":
      //关闭3d电子放大
      if (big3d.attr("data-status") == 1) {
        initDrawReact(that, e, big3d.attr("data-status"));
        big3d.attr("data-status", 0);
      }
      //关闭电子放大
      if (bigBtn.attr("data-status") == 1) {
        bigBtn.attr("data-status", 0);
        that.h5playerArr[e].h5player.JS_DisableZoom(0).then(
          () => {
            $(`#${that.videoBoxId} #jsVideoBox${e} #bigImg${e}`).attr({
              src: "",
            });
            console.log("enlargeClose success");
          },
          (e) => {
            console.error(e);
          }
        );
      }
      var monitorPtz = $(`#${that.videoBoxId} #jsVideoBox${e} #monitorPtz${e}`);
      status == 0
        ? monitorPtz.html(createHolder()).removeClass("hide")
        : monitorPtz.html("").addClass("hide");
      break;
    case "replay":
      let url = "";
      that.h5playerArr[e].h5player
        .JS_Play(
          url,
          {
            playURL: url,
            mode: 0,
          },
          0
        )
        .then(
          () => {
            console.log("playbackStart success");
          },
          (e) => {
            console.error(e);
          }
        );
      break;
    case "info":
      let monitorInfoBox = $(
        `#${that.videoBoxId} #jsVideoBox${e} #monitorInfo${e}`
      );
      if (status == 0) {
        that.h5playerArr[e].h5player.JS_GetVideoInfo(0).then(
          (videoInfo) => {
            videoInfo.devName = that.h5playerArr[e].devName;
            monitorInfoBox.html(renderMonitorInfoHtml(that, videoInfo));
            console.info("JS_GetVideoInfo success");
            //5秒执行获取一次信息
            that.monitorInfoTimerArr[e] = setInterval(() => {
              that.h5playerArr[e].h5player.JS_GetVideoInfo(0).then(
                (videoInfo) => {
                  videoInfo.devName = that.h5playerArr[e].devName;
                  monitorInfoBox.html(renderMonitorInfoHtml(that, videoInfo));
                },
                (err) => {
                  console.info("JS_GetVideoInfo failed");
                  // do you want...
                }
              );
            }, 5000);
          },
          (err) => {
            console.info("JS_GetVideoInfo failed");
            // do you want...
          }
        );
      } else {
        clearInterval(that.monitorInfoTimerArr[e]);
        monitorInfoBox.html("");
      }
      break;
    case "close":
      stopVideo(e, that);
      break;
    case "talk":
      status == 0 ? openSound(e, that) : closeSound(e, that);
      break;
    case "pic":
      //下载到本地
      capturePicture(e, that.h5playerArr[e].devName, "JPEG", true, that);
      break;
    case "big":
      //关闭3d电子放大
      if (big3d.attr("data-status") == 1) {
        initDrawReact(that, e, big3d.attr("data-status"));
        big3d.attr("data-status", 0);
      }
      //关闭云台
      if (holder.attr("data-status") == 1) {
        holder.attr("data-status", 0);
        $(`#${that.videoBoxId} #jsVideoBox${e} #monitorPtz${e}`)
          .html("")
          .addClass("hide");
      }
      //开启电子放大
      status == 0
        ? that.h5playerArr[e].h5player.JS_EnableZoom().then(
          () => {
            console.info("JS_EnableZoom success", e);
            let canvas = $(
              `#${that.videoBoxId} #jsVideoBox${e} #h5player${e}_${that.videoBoxId} canvas.play-window`
            );
            let src = canvas[0].toDataURL();
            let img = $(`#${that.videoBoxId} #jsVideoBox${e} #bigImg${e}`);
            $(`#${that.videoBoxId} #jsVideoBox${e} #bigImg${e}`)
              .attr({
                src: src,
              })
              .css({
                width: "25%",
              });
            // do you want...
          },
          (err) => {
            console.info("JS_EnableZoom failed");
            // do you want...
          }
        )
        : that.h5playerArr[e].h5player.JS_DisableZoom().then(
          () => {
            $(`#${that.videoBoxId} #jsVideoBox${e} #bigImg${e}`).attr({
              src: "",
            });
            console.log("enlargeClose success");
          },
          (e) => {
            console.error(e);
          }
        );
      break;
    case "recordVideo":
      let fileName = `${that.h5playerArr[e].devName}.mp4`;
      status == 0 ? startSaveEx(e, fileName, 5, that) : stopSave(e, that);
      break;
    case "full":
      that.h5playerArr[e].h5player.JS_FullScreenSingle(0).then(
        () => {
          console.info("JS_FullScreenDisplay success");
        },
        (err) => {
          console.info("JS_FullScreenDisplay failed", err);
        }
      );
      break;
    case "intercom":
      status == 0
        ? that.onEventVideoWindowCallback(
          "getTalkUrl",
          (params = {
            winNum: e,
            devCode: that.h5playerArr[e].devCode,
          })
        )
        : stopTalk(e, that);
      break;
    default:
      break;
  }
}
/**
 *获取音视频信息
 * @param that 视频窗口实例
 * @param winNum 窗口下标
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function getVideoInfo(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_GetVideoInfo(0).then(
    (VideoInfo) => {
      console.info("JS_FullScreenSingle success");
      // videoInfo = {
      //     VideType: 'h264', //视频编码格式
      //     audioType: 'without',//音频编码格式
      //     width: 0,//视频分辨率的宽
      //     height: 0,//视频分辨率的高
      //     frameRate: 25,//视频帧率
      //     bitRate: 2048,//视频码率，单位：Kb/s
      //     systemFormt: "ps"//视频封装格式
      // };
      successCallback(VideoInfo);
    },
    (err) => {
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
      console.info("JS_FullScreenSingle failed", err);
    }
  );
}
/**
 *单窗口全屏
 * @param that 视频窗口实例
 * @param winNum 窗口下标
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function fullScreenSingle(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_FullScreenSingle(0).then(
    () => {
      console.info("JS_FullScreenSingle success");
      successCallback();
    },
    (err) => {
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
      console.info("JS_FullScreenSingle failed", err);
    }
  );
}
/**
 *获取指定窗口状态
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 * @param return 成功返回选中窗口状态（1 正在播放，0 未播放 2播放出错)，失败返回-1
 */
function getSelectWinStatus(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  if (that.h5playerArr.length) {
    successCallback(that.h5playerArr[winNum].playIng);
    return that.h5playerArr[winNum].playIng;
  } else {
    errCallback();
    return -1;
  }
}
/**
 *获取选中窗口
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 * @param return 成功返回选中窗口，失败返回-1
 */
function getSelectWinNum(
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  if (that.h5playerArr.length) {
    successCallback(that.selectedWinNum);
    return that.selectedWinNum;
  } else {
    errCallback();
    return -1;
  }
}
/**
 *设置取流连接超时时间
 * @param winNum 窗口下标
 * @param nTime 超时时间（不传时默认6秒，单位秒）
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function setConnectTimeOut(
  winNum,
  nTime = 6,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_SetConnectTimeOut(0, nTime).then(
    () => {
      console.info("JS_SetConnectTimeOut success");
      successCallback();
    },
    (err) => {
      console.info("JS_SetConnectTimeOut failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 *设置对讲音量
 * @param winNum 窗口下标
 * @param nVolume 音量大小（0-100）
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function talkSetVolume(
  winNum,
  nVolume,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_TalkSetVolume(nVolume).then(
    () => {
      console.info("JS_TalkSetVolume success");
      successCallback();
    },
    (err) => {
      console.info("JS_TalkSetVolume failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 *获取对讲音量
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function talkGetVolume(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_TalkGetVolume().then(
    (nVolume) => {
      console.info("JS_TalkGetVolume success");
      successCallback(nVolume);
    },
    (err) => {
      console.info("JS_TalkGetVolume failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 *抓图
 * @param winNum 窗口下标
 * @param fileName 文件名
 * @param fileType 录像文件类型 'JPEG'
 * @param isDownload 是否下载本地 不下载本地会执行 successCallback 参数为imageData抓图的信息是base64
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function capturePicture(
  winNum,
  fileName,
  fileType,
  isDownload = true,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  if (isDownload) {
    that.h5playerArr[winNum].h5player
      .JS_CapturePicture(0, fileName, fileType)
      .then(
        () => {
          console.info("JS_StopSave success");
          successCallback();
        },
        (err) => {
          console.info("JS_StopSave failed");
          showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
          errCallback(err);
        }
      );
  } else {
    that.h5playerArr[winNum].h5player.JS_CapturePicture(
      0,
      fileName,
      fileType,
      (imageData) => {
        console.log("imageData:", imageData);
        successCallback(imageData);
      }
    );
  }
}
/**
 *停止录像并保存文件
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function stopSave(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_StopSave(0).then(
    () => {
      console.info("JS_StopSave success");
      successCallback();
    },
    (err) => {
      console.info("JS_StopSave failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 *开始录像
 * @param winNum 窗口下标
 * @param fileName 文件名(保证全局唯一性) 可不带后缀，默认为.mp4
 * @param idstType 录像文件类型(默认5) 2-ps 5-mp4 ,mp4录制音频限制，仅支持AAC、G711A、G711U
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function startSaveEx(
  winNum,
  fileName,
  idstType = 5,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_StartSaveEx(0, fileName, idstType).then(
    () => {
      console.info("JS_StartSaveEx success");
      successCallback();
    },
    (err) => {
      console.info("JS_StartSaveEx failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 * 获取当前音量
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function getVolume(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_GetVolume(0).then(
    (volumn) => {
      console.info("JS_GetVolume success");
      successCallback(volumn);
    },
    (err) => {
      console.info("JS_GetVolume failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 * 设置音量
 * @param winNum 窗口下标
 *  @param volumn 音量大小 范围1~100
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function setVolume(
  winNum,
  volumn,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_SetVolume(0, volumn).then(
    () => {
      console.info("JS_SetVolume success");
      successCallback();
    },
    (err) => {
      console.info("JS_SetVolume failed", err);
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 * 打开声音
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function openSound(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_OpenSound(0).then(
    () => {
      console.info("JS_OpenSound success");
      successCallback();
    },
    (err) => {
      console.info("JS_OpenSound failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 * 关闭声音
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function closeSound(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_CloseSound(0).then(
    () => {
      console.info("JS_CloseSound success");
      successCallback();
    },
    (err) => {
      console.info("JS_CloseSound failed", err);
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 * 停止对讲
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function stopTalk(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_StopTalk().then(
    () => {
      console.info("JS_StopTalk success");
      successCallback();
    },
    (err) => {
      console.info("JS_StopTalk failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 *
 * 暂停回放
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 */
function pause(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_Pause(0).then(
    () => {
      console.info("JS_Pause success");
      successCallback();
    },
    (err) => {
      console.info("JS_Pause failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 *
 * 回放定位
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 */
function seek(
  winNum,
  that,
  stratTime,
  endTime,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_Seek(0, stratTime, endTime).then(
    () => {
      console.info("JS_Seek success");
      successCallback();
    },
    (err) => {
      console.info("JS_Seek failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 *
 * 恢复回放
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 */
function resume(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_Resume(0).then(
    () => {
      console.info("JS_Resume success");
      successCallback();
    },
    (err) => {
      console.info("JS_Resume failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 *
 * 回放快放
 * 调节播放倍速为当前播放速度的2倍，最大为8倍。
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 */
function fast(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_Fast(0).then(
    (rate) => {
      console.info("JS_Fast success");
      successCallback(rate);
    },
    (err) => {
      console.info("JS_Fast failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 *
 * 回放慢放
 * 调节播放倍速为当前播放速度的1/2倍，最小为1/8倍。
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 */
function slow(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_Slow(0).then(
    (rate) => {
      console.info("JS_Slow success");
      successCallback(rate);
    },
    (err) => {
      console.info("JS_Slow failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 *
 * 回放单帧进
 * @param winNum 窗口下标
 * @param that 视频窗口实例
 */
function frameForward(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_FrameForward(0).then(
    () => {
      console.info("JS_FrameForward success");
      successCallback();
    },
    (err) => {
      console.info("JS_FrameForward failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 *
 * 开始对讲
 * @param winNum 窗口下标
 * @param talkUrl 对讲url
 * @param that 视频窗口实例
 */
function startTalk(
  winNum,
  talkUrl,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr[winNum].h5player.JS_StartTalk(talkUrl).then(
    () => {
      console.info("JS_StartTalk success");
      successCallback();
    },
    (err) => {
      console.info("JS_StartTalk failed");
      showError(err, that.h5playerArr[winNum].devName, winNum + 1, that);
      errCallback(err);
    }
  );
}
/**
 * 获取视频数据
 * @param {*选中窗口的下标} windInd
 * @param {*当前视频对象} t
 * @returns
 */
function setSelectWin(windInd, t) {
  t.selectedWinNum = windInd;
  t.onEventVideoWindowCallback(
    "windowEventSelect",
    (params = {
      winNum: windInd,
      devCode: t.h5playerArr[windInd].devCode,
    })
  );
  for (let a = 0; a < t.maxWinNum; a++) {
    //设置选中窗口边框颜色
    windInd == a
      ? $("#" + t.videoBoxId + " div#videoMask" + windInd)
        .addClass("selecedVideoMask")
        .css(
          "border",
          ""
            .concat(t.selectBorderWidth, "px solid rgba(")
            .concat(t.color1, ",")
            .concat(t.color2, ",")
            .concat(t.color3, ",")
            .concat(t.opcityValue, ")")
        )
      : $("#" + t.videoBoxId + " div#videoMask" + a)
        .removeClass("selecedVideoMask")
        .css("border", "1px solid #000");
  }
}
//绘制canvas图片
function drawImage(canvas, parent, url) {
  let ctx = canvas.getContext("2d");
  //重新绘制前先清空画布
  ctx.clearRect(0, 0, parent.offsetWidth, parent.offsetHeight);
  canvas.width = 0;
  canvas.height = 0;
  if (url) {
    let width = parent.offsetWidth - 4;
    canvas.width = width;
    let height = parent.offsetHeight - 4;
    canvas.height = height;
    let img = new Image();
    img.setAttribute("crossOrigin", "anonymous");
    img.src = url;
    img.onload = () => {
      //图片加载后绘制图片
      ctx.drawImage(img, 2, 2, width, height);
    };
  }
}

/**
 *
 * @param{展示的提示内容} message
 */
function showWarningMessage(message) {
  $("#videoWarning")
    .removeClass("hide")
    .addClass("show")
    .find("p")
    .html(message);
  setTimeout(() => {
    $("#videoWarning").removeClass("show").addClass("hide").find("p").html("");
  }, 3000);
}
//关闭视频loading
function closeLoading(that, winNum) {
  $(`#${that.videoBoxId} #jsVideoBox${winNum} #videoMask${winNum} span`)
    .addClass("show")
    .removeClass("hide");
  $(`#${that.videoBoxId} #jsVideoBox${winNum} #videoMask${winNum} i`)
    .addClass("hide")
    .removeClass("show");
}
/**
 *播放视频视频播放
 *@param {*当前选中的窗口下标} winInd
 @param {*播放的视频url} winNum
 @param {*是否播放的是图片} isPicture
 @param {*设备code} devCode
 @param {*设备名称} devName
 */
function playVideo(
  winInd,
  url,
  videoMode = 0,
  devCode,
  devName,
  isPicture = false,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  let winNum = winInd; //获取设备播放窗口号，默认当前选中的窗口
  //实时视频实现自动换屏
  if (that.mode == 0) {
    //判断设备是否正在播放
    let ind = that.h5playerArr.findIndex((item) => item.devCode === devCode);
    if (!that.devCodeRepeat && ind != -1) {
      showWarningMessage("该设备已经打开！");
      return;
    }
    //判断是否还存在窗口空闲即未播放状态的窗口
    if (
      that.h5playerArr.filter((item) => item.playIng).length >= that.maxWinNum
    ) {
      showWarningMessage("视频窗口已满，请关闭后继续播放！");
      return;
    }
    //若是当前窗口已经播放过了（即正在播放中和播放出错），就需要找空闲窗口
    if (that.h5playerArr[winNum].playIng) {
      //判断当前分屏数中是否有空窗口
      let h5playerArr = that.h5playerArr.slice(0, that.screenNum);
      let index = h5playerArr.findIndex((item) => !item.playIng);
      if (index != -1) {
        //当前分屏中找到空闲窗口了
        winNum = index;
      } else {
        //分屏数数组
        let splitScreenArr = [
          {
            screenNum: 1,
            splitStr: "1x1",
          },
          {
            screenNum: 4,
            splitStr: "2x2",
          },
          {
            screenNum: 9,
            splitStr: "3x3",
          },
          {
            screenNum: 16,
            splitStr: "4x4",
          },
          {
            screenNum: 25,
            splitStr: "5x5",
          },
          {
            screenNum: 2,
            splitStr: "1x2",
          },
          {
            screenNum: 3,
            splitStr: "1+2",
          },
          {
            screenNum: 6,
            splitStr: "1+5",
          },
          {
            screenNum: 8,
            splitStr: "1+7",
          },
          {
            screenNum: 10,
            splitStr: "1+9",
          },
          {
            screenNum: 13,
            splitStr: "1+12",
          },
          {
            screenNum: 17,
            splitStr: "1+16",
          },
          {
            screenNum: 7,
            splitStr: "3+4",
          },
        ];
        splitScreenArr.sort((a, b) => a.screenNum - b.screenNum);
        let playLength = that.h5playerArr.filter((item) => item.playIng).length;
        let screenNum =
          playLength > that.screenNum ? playLength : that.screenNum;
        let data = splitScreenArr.find((item) => item.screenNum > screenNum);
        winNum = screenNum;
        changeVideoObjScreen(data.screenNum, that, data.splitStr);
      }
    }
  }

  function setData() {
    that.h5playerArr[winNum].isPicture = isPicture;
    that.h5playerArr[winNum].url = url;
    that.h5playerArr[winNum].devCode = devCode;
    that.h5playerArr[winNum].devName = devName;
    that.h5playerArr[winNum].playIng = 1;
  }

  if (!isPicture) {
    //隐藏图片
    $(`#${that.videoBoxId} #jsVideoBox${winNum} #canvasPlayerCont${winNum}`)
      .addClass("hide")
      .removeClass("show");

    //清楚之前播放设置的信息
    setInith5playerArrData(that, winNum, that.h5playerArr[winNum].h5player);
    let startTime = "";
    let endTime = "";
    if (that.mode == 1) {
      startTime = that.startTime.replace(" ", "T") + "Z";
      endTime = that.endTime.replace(" ", "T") + "Z";
    }
    //展示loading
    $(`#${that.videoBoxId} #jsVideoBox${winNum} #playError`)
      .addClass("hide")
      .removeClass("show");
    $(`#${that.videoBoxId} #jsVideoBox${winNum} #videoMask${winNum} span`)
      .addClass("hide")
      .removeClass("show");
    $(`#${that.videoBoxId} #jsVideoBox${winNum} #videoMask${winNum} i`)
      .addClass("show")
      .removeClass("hide");

    that.h5playerArr[winNum].devCode = devCode;
    that.h5playerArr[winNum].devName = devName;
    that.h5playerArr[winNum].playIng = 2;//播放初始是播放出错
    that.h5playerArr[winNum].h5player
      .JS_Play(
        url,
        {
          playURL: url, // 流媒体播放时必传
          mode: videoMode, // 解码类型：0=普通模式; 1=高级模式 默认为0
        },
        0, //当前窗口下标
        startTime,
        endTime
      )
      .then(
        () => {
          console.log("play success");
          setData();
          closeLoading(that, winNum);
          $(
            `#${that.videoBoxId} #jsVideoBox${winNum} #videoMask${winNum}`
          ).attr("data-status", 1);

          if (that.mode == 1) {
            $(
              '#videoBottom #playVideoBox .operate-button[data-type="play"]'
            ).addClass("hide");
            $(
              '#videoBottom #playVideoBox .operate-button[data-type="pause"]'
            ).removeClass("hide");
          }
          successCallback();
        },
        (err) => {
          console.log(err)
          console.log("play error");
          $(`#${that.videoBoxId} #jsVideoBox${winNum} #videoMask${winNum} i`)
            .addClass("hide")
            .removeClass("show");
          errCallback(err);
        }
      );
  } else {
    //该窗口之前在播放视频，需要关闭视频
    if (
      !that.h5playerArr[winNum].isPicture &&
      that.h5playerArr[winNum].playIng
    ) {
      that.h5playerArr[winNum].h5player.JS_StopRealPlayAll().then(
        () => { },
        (err) => { }
      );
    }
    let holder = $(
      `#${that.videoBoxId} #jsVideoBox${winNum} #fullBtnBox${winNum} #holder`
    );
    if (holder.attr("data-status") == 1) {
      $(`#${that.videoBoxId} #jsVideoBox${winNum} #monitorPtz${winNum}`)
        .html("")
        .addClass("hide");
    }
    //设置数据
    setData();
    //展示图片
    $(`#${that.videoBoxId} #jsVideoBox${winNum} #canvasPlayerCont${winNum}`)
      .addClass("show")
      .removeClass("hide");
    let canvasParent = document.querySelector(`#canvasPlayerCont${winNum}`);
    let canvas = document.querySelector(`#canvasPlayer${winNum}`);
    $(`#canvasPlayerCont${winNum}`).removeClass("hide").addClass("show");
    drawImage(canvas, canvasParent, that.h5playerArr[winNum].url);
    let src = that.h5playerArr[winNum].url;
    window.addEventListener("resize", function () {
      drawImage(canvas, canvasParent, src);
    });
  }
}
/**
 * 关闭窗口时给窗口数据重新赋值
 * @param ind 窗口下标
 * @param h5player 该窗口的视频控件对象
 */
function setInith5playerArrData(that, ind, h5player) {
  that.h5playerArr.splice(ind, 1, {
    devCode: "",
    isPicture: false,
    url: "",
    devName: "",
    h5player: h5player,
    startTime: "", //暂存回放查询的开始时间
    endTime: "", //暂存回放查询的结束时间
    playbackTimeArr: [],
    playIng: false,
  });
  $(`#${that.videoBoxId} #jsVideoBox${ind} #monitorPtz${ind}`).html("");
  $(`#${that.videoBoxId} #jsVideoBox${ind} #videoMask${ind}`).attr(
    "data-status",
    0
  );
  $(`#${that.videoBoxId} #jsVideoBox${ind} #monitorInfo${ind}`).html("");
  $(`#${that.videoBoxId} #jsVideoBox${ind} #bigImg${ind}`).attr({
    src: "",
  });
  closeLoading(that, ind)
  //异常样式关闭
  $(`#${that.videoBoxId} #jsVideoBox${ind} #playError`)
    .removeClass("show")
    .addClass("hide");
  $.each(
    $(`#${that.videoBoxId} #jsVideoBox${ind} #fullBtnBox${ind} span`),
    function () {
      let t = $(this);
      let type = t.attr("data-type");
      if (
        fullBtnBoxData.hasOwnProperty(type) &&
        $(this).attr("data-status") == 1
      ) {
        console.log(type, fullBtnBoxData[type]);
        t.attr("title", fullBtnBoxData[type].title);
        $(this)
          .removeClass(fullBtnBoxData[type].className1)
          .addClass(fullBtnBoxData[type].className);
      }
      $(this).attr("data-status") ? $(this).attr("data-status", 0) : "";
      document.querySelector(
        `#${that.videoBoxId} #jsVideoBox${ind} #h5player${ind}_${that.videoBoxId} #h5player${ind}_${that.videoBoxId}_canvas_draw0`
      ).onmousedown = null;
      clearInterval(that.monitorInfoTimerArr[ind]);
    }
  );
}
/**
 * 关闭单个窗口
 * @param winNum 窗口下标
 * @param {*视频窗口} that
 */
function stopVideo(
  winNum,
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  if (that.h5playerArr[winNum].isPicture) {
    let canvasParent = document.querySelector(`#canvasPlayerCont${winNum}`);
    let canvas = document.querySelector(`#canvasPlayer${winNum}`);
    $(`#canvasPlayerCont${winNum}`).removeClass("show").addClass("hide");

    drawImage(canvas, canvasParent, "");
    setInith5playerArrData(that, winNum, that.h5playerArr[winNum].h5player);
  } else {
    that.h5playerArr[winNum].h5player.JS_StopRealPlayAll().then(
      () => {
        setInith5playerArrData(that, winNum, that.h5playerArr[winNum].h5player);
        successCallback();
      },
      (err) => {
        errCallback(err);
        // do you want...
      }
    );
  }
}
/**
 *关闭所有视频播放
 *@param {*视频窗口} that
 */
function closeAllVideo(
  that,
  successCallback = () => { },
  errCallback = () => { }
) {
  that.h5playerArr.forEach((ele, winNum) => {
    stopVideo(winNum, that, successCallback, errCallback);
  });
}
/**
 *
 * @param {*全屏的dom元素} e
 * @param successCallback 成功回調
 * @param errCallback 失败回调
 */
function fullScreen(e, successCallback = () => { }, errCallback = () => { }) {
  let type = true;
  e.requestFullscreen
    ? e.requestFullscreen()
    : e.webkitRequestFullscreen
      ? e.webkitRequestFullScreen()
      : e.mozRequestFullScreen
        ? e.mozRequestFullScreen()
        : e.msRequestFullscreen
          ? e.msRequestFullscreen()
          : e.oRequestFullscreen
            ? e.oRequestFullscreen()
            : ((type = false), alert("浏览器版本太低，无法实现全屏！"));
  if (type) {
    successCallback();
  } else {
    errCallback();
  }
}
/**
 *
 * @param {分屏数} splitNum
 * @param {当前操作对象} that
 * @param {分屏数字符串}splitStr
 * @returns
 */
function changeVideoObjScreen(
  splitNum,
  that,
  splitStr = "",
  successCallback = () => { },
  errCallback = () => { }
) {
  if (!(that.maxWinNum < splitNum)) {
    //手动分屏时设置选中分屏样式
    if (splitStr) {
      $(`#${that.videoBoxId} #splitScreenBox .operate-button`).removeClass(
        "split-screen-selected"
      );
      $(
        `#${that.videoBoxId} #splitScreenBox .operate-button[title='${splitStr}']`
      ).addClass("split-screen-selected");
    }
    try {
      that.screenNum = screenChange(splitNum, that);
      //每次分屏后，若是当前选中的窗口大于分屏数，就选中的窗口是第一个
      // that.selectedWinNum > splitNum ? setSelectWin(0, that) : "";
      successCallback();
    } catch (splitNum) {
      errCallback();
      return console.log("分屏出错"), 0;
    }
    return 1;
  } else {
    errCallback();
    alert("分屏数不应该大于最大屏幕数量！");
  }
}
/**
 *
 * @param {分屏数} splitNum
 * @param {当前操作对象} that
 * @returns
 */
function screenChange(splitNum, that) {
  $("#" + that.videoBoxId).attr({
    splitNum,
    tip: $(
      `#${that.videoBoxId} #splitScreenBox .operate-button.split-screen-selected`
    ).attr("title"),
  });
  for (let ind = 0; ind < that.maxWinNum; ind++) {
    ind >= splitNum
      ? $("#" + that.videoBoxId + " div.js-video-box" + ind).hide()
      : $("#" + that.videoBoxId + " div.js-video-box" + ind).show();
  }
  //海康控件大小自适应
  that.h5playerArr.forEach((ele, winNum) => {
    ele.h5player.JS_Resize();
    //播放得图片自适应父元素
    if (ele.isPicture) {
      let canvasParent = document.querySelector(`#canvasPlayerCont${winNum}`);
      let canvas = document.querySelector(`#canvasPlayer${winNum}`);
      $(`#canvasPlayerCont${winNum}`).removeClass("hide").addClass("show");
      drawImage(canvas, canvasParent, ele.url);
    }
  });
  return splitNum;
}
//3d放大绘制矩形
function initDrawReact(that, ind, status) {
  var drawArear = document.querySelector(
    `#${that.videoBoxId} #jsVideoBox${ind}`
  ); // 获取画布元素
  if (status == 1) {
    let canvasDraw = drawArear.querySelector(
      `#h5player${ind}_${that.videoBoxId} #h5player${ind}_${that.videoBoxId}_canvas_draw0`
    );
    canvasDraw.removeEventListener("mousedown", canvasDrawMouseDown);
    drawArear.removeChild(document.getElementById(`drawReact${ind}`));
    canvasDraw.onmousedown == null;
    return;
  }
  // 创建矩形框
  var drawReact = document.createElement("div");
  drawReact.id = `drawReact${ind}`;
  drawReact.style.boxSizing = "border-box";
  drawReact.style.border = "1px solid #ccc";
  drawReact.style.position = "absolute";
  drawReact.style.display = "none";
  drawArear.appendChild(drawReact);
  var canvasDraw = drawArear.querySelector(
    `#h5player${ind}_${that.videoBoxId} #h5player${ind}_${that.videoBoxId}_canvas_draw0`
  );
  // 绑定鼠标事件--onmousedown
  canvasDraw.onmousedown = canvasDrawMouseDown;

  function canvasDrawMouseDown($event) {
    // 初始化
    var drawReact = document.getElementById(`drawReact${ind}`); // 获取矩形框元素
    var areaInfo = drawArear.getBoundingClientRect(); // 返回元素的大小及其相对于视口的位置
    var reactWidth, reactHeight, reactTop, reactLeft; // 定义矩形框的宽、高、top、left
    // xy坐标是以画布的左上角为原点，方向矢量以向下和向右为正方向。
    var beginPoint = {}; // 标记起点
    var endPoint = {}; // 标记终点
    drawReact.style.display = "block"; // 进入画布按下鼠标显示默认矩形框
    // 鼠标按下的位置作为矩形框的起点，横纵坐标记为 x0, y0
    beginPoint = {
      startX: $event.clientX - areaInfo.x,
      startY: $event.clientY - areaInfo.y,
    };
    // 起点的横坐标
    var x0 = $event.clientX - areaInfo.x;
    // 起点的纵坐标
    var y0 = $event.clientY - areaInfo.y;
    var fullTopBtnBox = drawArear.querySelector(`#fullTopBtnBox${ind}`);
    var fullBtnBox = drawArear.querySelector(`#fullBtnBox${ind}`);
    // 绑定鼠标事件--onmousemove
    canvasDraw.onmousemove = function ($event) {
      // 终点的横坐标
      var x1 = $event.clientX - areaInfo.x;
      // 终点的纵坐标
      var y1 = $event.clientY - areaInfo.y;
      // 对终点相对于起点的位置进行分类讨论
      if (x1 >= x0 && y1 < y0) {
        // x 越界处理
        reactWidth =
          $event.clientX >= areaInfo.right ? areaInfo.width - x0 : x1 - x0;
        reactLeft = x0;
        // y 越界处理
        reactHeight = $event.clientY <= areaInfo.top ? y0 : y0 - y1;
        reactTop = $event.clientY <= areaInfo.top ? 0 : y1;
        // 终点
        endPoint = {
          endX: x0 + reactWidth,
          endY: y0 - reactHeight,
        };
      } else if (x1 < x0 && y1 < y0) {
        // x 越界处理
        reactWidth = $event.clientX <= areaInfo.left ? x0 : x0 - x1;
        reactLeft = $event.clientX <= areaInfo.left ? 0 : x1;
        // y 越界处理
        reactHeight = $event.clientY <= areaInfo.top ? y0 : y0 - y1;
        reactTop = $event.clientY <= areaInfo.top ? 0 : y1;
        // 终点
        endPoint = {
          endX: x0 - reactWidth,
          endY: y0 - reactHeight,
        };
      } else if (x1 < x0 && y1 >= y0) {
        // x 越界处理
        reactWidth = $event.clientX <= areaInfo.left ? x0 : x0 - x1;
        reactLeft = $event.clientX <= areaInfo.left ? 0 : x1;
        // y 越界处理
        reactHeight =
          $event.clientY >= areaInfo.bottom ? areaInfo.height - y0 : y1 - y0;
        reactTop = y0;
        // 终点
        endPoint = {
          endX: x0 - reactWidth,
          endY: y0 + reactHeight,
        };
      } else if (x1 >= x0 && y1 >= y0) {
        // x 越界处理
        reactWidth =
          $event.clientX >= areaInfo.right ? areaInfo.width - x0 : x1 - x0;
        reactLeft = x0;
        // y 越界处理
        reactHeight =
          $event.clientY >= areaInfo.bottom ? areaInfo.height - y0 : y1 - y0;
        reactTop = y0;
        // 终点
        endPoint = {
          endX: x0 + reactWidth,
          endY: y0 + reactHeight,
        };
      }
      drawReact.style.width = reactWidth + "px"; // 宽
      drawReact.style.height = reactHeight + "px"; // 高
      drawReact.style.top = reactTop + "px";
      drawReact.style.left = reactLeft + "px";
    };
    fullTopBtnBox.style.pointerEvents = "none";
    fullBtnBox.style.pointerEvents = "none";
    // 绑定鼠标事件--onmousedown
    document.onmouseup = function ($event) {
      canvasDraw.onmousemove = null;
      document.onmouseup = null;
      // 回调
      var options = Object.assign({}, beginPoint, endPoint);
      // fn(options, that.h5playerArr[ind].devCode);

      that.onEventVideoWindowCallback(
        "amplifier3d",
        (params = {
          winNum: ind,
          devCode: that.h5playerArr[ind].devCode,
          ...options,
        })
      );
      fullTopBtnBox.style.pointerEvents = "auto";
      fullBtnBox.style.pointerEvents = "auto";
      drawReact.style.width = "0px"; // 宽
      drawReact.style.height = "0px"; // 高
      drawReact.style.display = "none"; // 高
    };
  }
}
/**
 * 渲染回放时底部的播放按钮
 */
function showBottomPlayBar(that) {
  let operateIconList = [
    {
      type: "playbackSlowSpeed",
      // 按钮图标样式
      buttonIconClass: "h5player-kuaijin1",
      fontSize: "22px",
      // 鼠标悬浮提示信息
      tipMsg: "慢放",
      disabled: true,
    },
    {
      type: "playbackSpeed",
      // 按钮图标样式
      buttonIconClass: "h5player-kuaijin",
      fontSize: "22px",
      // 鼠标悬浮提示信息
      tipMsg: "快放",
      disabled: true,
    },
    {
      type: "time",
    },
    {
      type: "play",
      // 按钮图标样式
      buttonIconClass: "h5player-kaishi ",
      fontSize: "23px",
      // 鼠标悬浮提示信息
      tipMsg: "播放",
      disabled: true,
    },
    {
      type: "pause",
      // 按钮图标样式
      buttonIconClass: "h5player-zanting",
      fontSize: "30px",
      // 鼠标悬浮提示信息
      tipMsg: "暂停",
      hide: true,
      disabled: true,
    },
    {
      type: "singleFrame",
      // 按钮图标样式
      buttonIconClass: "h5player-xian",
      fontSize: "22px",
      // 鼠标悬浮提示信息
      tipMsg: "单帧步进",
      disabled: true,
    },
  ];
  let html = "";
  $.each(operateIconList, function (commentIndex, comment) {
    if (comment.type == "time") {
      html += `<input class="history-video-picker" value="${that.startTime}" type="text" name="time" id="time"
                     />`;
    } else {
      html += `<div
                class="operate-button h5player-operate-icon ${comment.hide ? "hide" : ""
        }" 
                title="${comment.tipMsg}"
                data-type='${comment.type}'
              >
                <span class='iconfont ${comment.buttonIconClass
        }' style="font-size: ${comment.fontSize};"></span
                ><span
                  class="msg ${comment.buttonMsgClass}"
                  >${comment.msg ? comment.msg : ""}</span
                >
              </div>`;
    }
  });
  return html;
}
/**
 * 渲染底部的按钮
 */
var splitScreenIconList = [
  // 操作按钮集合
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-1",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "1x1",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "1",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-4",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "2x2",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "4",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-9",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "3x3",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "9",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-16",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "4x4",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "16",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-25",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "5x5",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "25",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-2",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "1x2",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "2",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-1_2",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "1+2",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "3",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },

  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-1_5",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "1+5",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "6",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-1_7",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "1+7",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "8",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-1_9",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "1+9",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "10",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-1_12",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "1+12",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "13",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-1_16",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "1+16",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "17",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-1_8",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "1+8",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "9",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a-3_4",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "3+4",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "7",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
  {
    type: "splitScreen",
    // 按钮图标样式
    buttonIconClass: "h5player-a1-4",
    // 按钮文字样式
    buttonMsgClass: "button-msg",
    // 鼠标悬浮提示信息
    tipMsg: "1x4",
    // 是否显示文字
    showMsg: true,
    // 按钮显示文字
    msg: "4",
    // 选中时样式表
    selectedClass: "split-screen-selected",
  },
];

function showBottomBar(that) {
  let operateIconList = [
    {
      type: "screenshots",
      // 按钮图标样式
      buttonIconClass: "h5player-zhuatu",
      fontSize: "26px",
      // 鼠标悬浮提示信息
      tipMsg: "全部抓图",
    },
    {
      type: "closeAllVideo",
      // 按钮图标样式
      buttonIconClass: "h5player-quanbuguanbi",
      fontSize: "26px",
      // 鼠标悬浮提示信息
      tipMsg: "全部关闭",
    },
    {
      type: "split",
      // 按钮图标样式
      buttonIconClass: "h5player-fenping",
      fontSize: "23px",
      // 鼠标悬浮提示信息
      tipMsg: "切换画面分割",
    },
    {
      type: "fullScreen",
      // 按钮图标样式
      buttonIconClass: "h5player-quanping_o",
      fontSize: "30px",
      // 鼠标悬浮提示信息
      tipMsg: "全屏",
    },
  ];
  let html = "";
  if (that.mode == 1) {
    operateIconList.splice(2, 1)
  }
  $.each(operateIconList, function (commentIndex, comment) {
    html += `<div
        class="operate-button h5player-operate-icon ${that.screenNum == comment.msg ? "split-screen-selected" : ""
      }" 
        data-toggle="tooltip"
        title="${comment.tipMsg}"
        data-type='${comment.type}'
      >
        <span class='iconfont ${comment.buttonIconClass}' style="font-size: ${comment.fontSize
      };"></span
        ><span
          class="msg ${comment.buttonMsgClass}"
          >${comment.msg ? comment.msg : ""}</span
        >
      </div>`;
  });

  if (that.mode == 1) {
    html += "<div class='play-video-box' id='playVideoBox'>";
    html += showBottomPlayBar(that);
    html += "</div>";
  }
  html += "<div class='split-screen-box hide' id='splitScreenBox'>";

  $.each(splitScreenIconList, function (commentIndex, comment) {
    html += `<div
        class="operate-button h5player-operate-icon ${that.screenNum == comment.msg ? "split-screen-selected" : ""
      }" 
        title="${comment.tipMsg}"
        data-type='${comment.type}'
        data-splitNum='${comment.msg}'
      >
        <span class='iconfont ${comment.buttonIconClass}' style="font-size: ${comment.fontSize ? comment.fontSize : "16px"
      }"></span
        ><span
          class="msg ${comment.buttonMsgClass}"
          >${comment.msg ? comment.msg : ""}</span
        >
      </div>`;
  });
  html += "</div>";
  return html;
}

/***以下是DateTimePicker代码，直接放在代码中，就不用引用了，由于import引入需要配置太多***/

/**
 * @preserve jQuery DateTimePicker plugin v2.1.9
 * @homepage http://xdsoft.net/jqplugins/datetimepicker/
 * (c) 2014, Chupurnov Valeriy.
 */
(function ($) {
  "use strict";
  var default_options = {
    i18n: {
      ru: {
        // Russian
        months: [
          "Январь",
          "Февраль",
          "Март",
          "Апрель",
          "Май",
          "Июнь",
          "Июль",
          "Август",
          "Сентябрь",
          "Октябрь",
          "Ноябрь",
          "Декабрь",
        ],
        dayOfWeek: ["Вск", "Пн", "Вт", "Ср", "Чт", "Пт", "Сб"],
      },
      en: {
        // English
        months: [
          "January",
          "February",
          "March",
          "April",
          "May",
          "June",
          "July",
          "August",
          "September",
          "October",
          "November",
          "December",
        ],
        dayOfWeek: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
      },
      de: {
        // German
        months: [
          "Januar",
          "Februar",
          "März",
          "April",
          "Mai",
          "Juni",
          "Juli",
          "August",
          "September",
          "Oktober",
          "November",
          "Dezember",
        ],
        dayOfWeek: ["So", "Mo", "Di", "Mi", "Do", "Fr", "Sa"],
      },
      nl: {
        // Dutch
        months: [
          "januari",
          "februari",
          "maart",
          "april",
          "mei",
          "juni",
          "juli",
          "augustus",
          "september",
          "oktober",
          "november",
          "december",
        ],
        dayOfWeek: ["zo", "ma", "di", "wo", "do", "vr", "za"],
      },
      tr: {
        // Turkish
        months: [
          "Ocak",
          "Şubat",
          "Mart",
          "Nisan",
          "Mayıs",
          "Haziran",
          "Temmuz",
          "Ağustos",
          "Eylül",
          "Ekim",
          "Kasım",
          "Aralık",
        ],
        dayOfWeek: ["Paz", "Pts", "Sal", "Çar", "Per", "Cum", "Cts"],
      },
      fr: {
        //French
        months: [
          "Janvier",
          "Février",
          "Mars",
          "Avril",
          "Mai",
          "Juin",
          "Juillet",
          "Août",
          "Septembre",
          "Octobre",
          "Novembre",
          "Décembre",
        ],
        dayOfWeek: ["Dim", "Lun", "Mar", "Mer", "Jeu", "Ven", "Sam"],
      },
      es: {
        // Spanish
        months: [
          "Enero",
          "Febrero",
          "Marzo",
          "Abril",
          "Mayo",
          "Junio",
          "Julio",
          "Agosto",
          "Septiembre",
          "Octubre",
          "Noviembre",
          "Diciembre",
        ],
        dayOfWeek: ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"],
      },
      th: {
        // Thai
        months: [
          "มกราคม",
          "กุมภาพันธ์",
          "มีนาคม",
          "เมษายน",
          "พฤษภาคม",
          "มิถุนายน",
          "กรกฎาคม",
          "สิงหาคม",
          "กันยายน",
          "ตุลาคม",
          "พฤศจิกายน",
          "ธันวาคม",
        ],
        dayOfWeek: ["อา.", "จ.", "อ.", "พ.", "พฤ.", "ศ.", "ส."],
      },
      pl: {
        // Polish
        months: [
          "styczeń",
          "luty",
          "marzec",
          "kwiecień",
          "maj",
          "czerwiec",
          "lipiec",
          "sierpień",
          "wrzesień",
          "październik",
          "listopad",
          "grudzień",
        ],
        dayOfWeek: ["nd", "pn", "wt", "śr", "cz", "pt", "sb"],
      },
      pt: {
        // Portuguese
        months: [
          "Janeiro",
          "Fevereiro",
          "Março",
          "Abril",
          "Maio",
          "Junho",
          "Julho",
          "Agosto",
          "Setembro",
          "Outubro",
          "Novembro",
          "Dezembro",
        ],
        dayOfWeek: ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sab"],
      },
      ch: {
        // Simplified Chinese
        months: [
          "一月",
          "二月",
          "三月",
          "四月",
          "五月",
          "六月",
          "七月",
          "八月",
          "九月",
          "十月",
          "十一月",
          "十二月",
        ],
        dayOfWeek: ["日", "一", "二", "三", "四", "五", "六"],
      },
      se: {
        // Swedish
        months: [
          "Januari",
          "Februari",
          "Mars",
          "April",
          "Maj",
          "Juni",
          "Juli",
          "Augusti",
          "September",
          "Oktober",
          "November",
          "December",
        ],
        dayOfWeek: ["Sön", "Mån", "Tis", "Ons", "Tor", "Fre", "Lör"],
      },
      kr: {
        // Korean
        months: [
          "1월",
          "2월",
          "3월",
          "4월",
          "5월",
          "6월",
          "7월",
          "8월",
          "9월",
          "10월",
          "11월",
          "12월",
        ],
        dayOfWeek: ["일", "월", "화", "수", "목", "금", "토"],
      },
      it: {
        // Italian
        months: [
          "Gennaio",
          "Febbraio",
          "Marzo",
          "Aprile",
          "Maggio",
          "Giugno",
          "Luglio",
          "Agosto",
          "Settembre",
          "Ottobre",
          "Novembre",
          "Dicembre",
        ],
        dayOfWeek: ["Dom", "Lun", "Mar", "Mer", "Gio", "Ven", "Sab"],
      },
    },
    value: "",
    lang: "ch",
    format: "Y/m/d H:i",
    formatTime: "H:i",
    formatDate: "Y/m/d",
    step: 60,
    closeOnDateSelect: 0,
    closeOnWithoutClick: true,
    timepicker: true,
    datepicker: true,
    minDate: false,
    maxDate: false,
    minTime: false,
    maxTime: false,
    allowTimes: [],
    opened: false,
    inline: false,
    onSelectDate: function () { },
    onSelectTime: function () { },
    onChangeMonth: function () { },
    onChangeDateTime: function () { },
    onShow: function () { },
    onClose: function () { },
    onGenerate: function () { },
    withoutCopyright: true,
    inverseButton: false,
    hours12: false,
    next: "xdsoft_next",
    prev: "xdsoft_prev",
    dayOfWeekStart: 0,
    timeHeightInTimePicker: 25,
    timepickerScrollbar: true,
    todayButton: true, // 2.1.0
    defaultSelect: true, // 2.1.0
    scrollMonth: true,
    scrollTime: true,
    scrollInput: true,
    mask: false,
    validateOnBlur: true,
    allowBlank: false,
    yearStart: 1950,
    yearEnd: 2050,
    style: "",
    id: "",
    roundTime: "round", // ceil, floor
    className: "",
    weekends: [],
    yearOffset: 0,
  };
  // fix for ie8
  if (!Array.prototype.indexOf) {
    Array.prototype.indexOf = function (obj, start) {
      for (var i = start || 0, j = this.length; i < j; i++) {
        if (this[i] === obj) {
          return i;
        }
      }
      return -1;
    };
  }
  $.fn.xdsoftScroller = function (_percent) {
    return this.each(function () {
      var timeboxparent = $(this);
      if (!$(this).hasClass("xdsoft_scroller_box")) {
        var pointerEventToXY = function (e) {
          var out = {
            x: 0,
            y: 0,
          };
          if (
            e.type == "touchstart" ||
            e.type == "touchmove" ||
            e.type == "touchend" ||
            e.type == "touchcancel"
          ) {
            var touch =
              e.originalEvent.touches[0] || e.originalEvent.changedTouches[0];
            out.x = touch.pageX;
            out.y = touch.pageY;
          } else if (
            e.type == "mousedown" ||
            e.type == "mouseup" ||
            e.type == "mousemove" ||
            e.type == "mouseover" ||
            e.type == "mouseout" ||
            e.type == "mouseenter" ||
            e.type == "mouseleave"
          ) {
            out.x = e.pageX;
            out.y = e.pageY;
          }
          return out;
        },
          move = 0,
          timebox = timeboxparent.children().eq(0),
          parentHeight = timeboxparent[0].clientHeight,
          height = timebox[0].offsetHeight,
          scrollbar = $('<div class="xdsoft_scrollbar"></div>'),
          scroller = $('<div class="xdsoft_scroller"></div>'),
          maximumOffset = 100,
          start = false;

        scrollbar.append(scroller);

        timeboxparent.addClass("xdsoft_scroller_box").append(scrollbar);
        scroller.on("mousedown.xdsoft_scroller", function (event) {
          if (!parentHeight)
            timeboxparent.trigger("resize_scroll.xdsoft_scroller", [_percent]);
          var pageY = event.pageY,
            top = parseInt(scroller.css("margin-top")),
            h1 = scrollbar[0].offsetHeight;
          $(document.body).addClass("xdsoft_noselect");
          $([document.body, window]).on(
            "mouseup.xdsoft_scroller",
            function arguments_callee() {
              $([document.body, window])
                .off("mouseup.xdsoft_scroller", arguments_callee)
                .off("mousemove.xdsoft_scroller", move)
                .removeClass("xdsoft_noselect");
            }
          );
          $(document.body).on(
            "mousemove.xdsoft_scroller",
            (move = function (event) {
              var offset = event.pageY - pageY + top;
              if (offset < 0) offset = 0;
              if (offset + scroller[0].offsetHeight > h1)
                offset = h1 - scroller[0].offsetHeight;
              timeboxparent.trigger("scroll_element.xdsoft_scroller", [
                maximumOffset ? offset / maximumOffset : 0,
              ]);
            })
          );
        });

        timeboxparent
          .on("scroll_element.xdsoft_scroller", function (event, percent) {
            if (!parentHeight)
              timeboxparent.trigger("resize_scroll.xdsoft_scroller", [
                percent,
                true,
              ]);
            percent =
              percent > 1 ? 1 : percent < 0 || isNaN(percent) ? 0 : percent;
            scroller.css("margin-top", maximumOffset * percent);
            timebox.css(
              "marginTop",
              -parseInt((height - parentHeight) * percent)
            );
          })
          .on(
            "resize_scroll.xdsoft_scroller",
            function (event, _percent, noTriggerScroll) {
              parentHeight = timeboxparent[0].clientHeight;
              height = timebox[0].offsetHeight;
              var percent = parentHeight / height,
                sh = percent * scrollbar[0].offsetHeight;
              if (percent > 1) scroller.hide();
              else {
                scroller.show();
                scroller.css("height", parseInt(sh > 10 ? sh : 10));
                maximumOffset =
                  scrollbar[0].offsetHeight - scroller[0].offsetHeight;
                if (noTriggerScroll !== true)
                  timeboxparent.trigger("scroll_element.xdsoft_scroller", [
                    _percent
                      ? _percent
                      : Math.abs(parseInt(timebox.css("marginTop"))) /
                      (height - parentHeight),
                  ]);
              }
            }
          );
        timeboxparent.mousewheel &&
          timeboxparent.mousewheel(function (event, delta, deltaX, deltaY) {
            var top = Math.abs(parseInt(timebox.css("marginTop")));
            timeboxparent.trigger("scroll_element.xdsoft_scroller", [
              (top - delta * 20) / (height - parentHeight),
            ]);
            event.stopPropagation();
            return false;
          });
        timeboxparent.on("touchstart", function (event) {
          start = pointerEventToXY(event);
        });
        timeboxparent.on("touchmove", function (event) {
          if (start) {
            var coord = pointerEventToXY(event),
              top = Math.abs(parseInt(timebox.css("marginTop")));
            timeboxparent.trigger("scroll_element.xdsoft_scroller", [
              (top - (coord.y - start.y)) / (height - parentHeight),
            ]);
            event.stopPropagation();
            event.preventDefault();
          }
        });
        timeboxparent.on("touchend touchcancel", function (event) {
          start = false;
        });
      }
      timeboxparent.trigger("resize_scroll.xdsoft_scroller", [_percent]);
    });
  };
  $.fn.jqdatetimepicker = function (opt) {
    var KEY0 = 48,
      KEY9 = 57,
      _KEY0 = 96,
      _KEY9 = 105,
      CTRLKEY = 17,
      DEL = 46,
      ENTER = 13,
      ESC = 27,
      BACKSPACE = 8,
      ARROWLEFT = 37,
      ARROWUP = 38,
      ARROWRIGHT = 39,
      ARROWDOWN = 40,
      TAB = 9,
      F5 = 116,
      AKEY = 65,
      CKEY = 67,
      VKEY = 86,
      ZKEY = 90,
      YKEY = 89,
      ctrlDown = false,
      options =
        $.isPlainObject(opt) || !opt
          ? $.extend(true, {}, default_options, opt)
          : $.extend({}, default_options),
      createDateTimePicker = function (input) {
        var datetimepicker = $(
          "<div " +
          (options.id ? 'id="' + options.id + '"' : "") +
          " " +
          (options.style ? 'style="' + options.style + '"' : "") +
          ' class="xdsoft_datetimepicker xdsoft_noselect ' +
          options.className +
          '"></div>'
        ),
          xdsoft_copyright = $(
            '<div class="xdsoft_copyright"><a target="_blank" href="http://xdsoft.net/jqplugins/datetimepicker/">xdsoft.net</a></div>'
          ),
          datepicker = $('<div class="xdsoft_datepicker active"></div>'),
          mounth_picker = $(
            '<div class="xdsoft_mounthpicker"><button type="button" class="xdsoft_prev"></button><button type="button" class="xdsoft_today_button"></button><div class="xdsoft_label xdsoft_month"><span></span></div><div class="xdsoft_label xdsoft_year"><span></span></div><button type="button" class="xdsoft_next"></button></div>'
          ),
          calendar = $('<div class="xdsoft_calendar"></div>'),
          timepicker = $(
            '<div class="xdsoft_timepicker active"><button type="button" class="xdsoft_prev"></button><div class="xdsoft_time_box"></div><button type="button" class="xdsoft_next"></button></div>'
          ),
          timeboxparent = timepicker.find(".xdsoft_time_box").eq(0),
          timebox = $('<div class="xdsoft_time_variant"></div>'),
          scrollbar = $('<div class="xdsoft_scrollbar"></div>'),
          scroller = $('<div class="xdsoft_scroller"></div>'),
          monthselect = $(
            '<div class="xdsoft_select xdsoft_monthselect"><div></div></div>'
          ),
          yearselect = $(
            '<div class="xdsoft_select xdsoft_yearselect"><div></div></div>'
          );

        //constructor lego
        mounth_picker.find(".xdsoft_month span").after(monthselect);
        mounth_picker.find(".xdsoft_year span").after(yearselect);

        mounth_picker
          .find(".xdsoft_month,.xdsoft_year")
          .on("mousedown.xdsoft", function (event) {
            mounth_picker.find(".xdsoft_select").hide();
            var select = $(this).find(".xdsoft_select").eq(0),
              val = 0,
              top = 0;

            if (_xdsoft_datetime.currentTime)
              val =
                _xdsoft_datetime.currentTime[
                  $(this).hasClass("xdsoft_month") ? "getMonth" : "getFullYear"
                ]();

            select.show();
            for (
              var items = select.find("div.xdsoft_option"), i = 0;
              i < items.length;
              i++
            ) {
              if (items.eq(i).data("value") == val) {
                break;
              } else top += items[0].offsetHeight;
            }

            select.xdsoftScroller(
              top / (select.children()[0].offsetHeight - select[0].clientHeight)
            );
            event.stopPropagation();
            return false;
          });

        mounth_picker
          .find(".xdsoft_select")
          .xdsoftScroller()
          .on("mousedown.xdsoft", function (event) {
            event.stopPropagation();
            event.preventDefault();
          })
          .on("mousedown.xdsoft", ".xdsoft_option", function (event) {
            if (_xdsoft_datetime && _xdsoft_datetime.currentTime)
              _xdsoft_datetime.currentTime[
                $(this).parent().parent().hasClass("xdsoft_monthselect")
                  ? "setMonth"
                  : "setFullYear"
              ]($(this).data("value"));
            $(this).parent().parent().hide();
            datetimepicker.trigger("xchange.xdsoft");
            options.onChangeMonth &&
              options.onChangeMonth.call &&
              options.onChangeMonth.call(
                datetimepicker,
                _xdsoft_datetime.currentTime,
                datetimepicker.data("input")
              );
          });

        // set options
        datetimepicker.setOptions = function (_options) {
          options = $.extend(true, {}, options, _options);
          if ((options.open || options.opened) && !options.inline) {
            input.trigger("open.xdsoft");
          }

          if (options.inline) {
            datetimepicker.addClass("xdsoft_inline");
            input.after(datetimepicker).hide();
            datetimepicker.trigger("afterOpen.xdsoft");
          }

          if (options.inverseButton) {
            options.next = "xdsoft_prev";
            options.prev = "xdsoft_next";
          }

          if (options.datepicker) datepicker.addClass("active");
          else datepicker.removeClass("active");

          if (options.timepicker) timepicker.addClass("active");
          else timepicker.removeClass("active");

          if (options.value) {
            input && input.val && input.val(options.value);
            _xdsoft_datetime.setCurrentTime(options.value);
          }

          if (
            isNaN(options.dayOfWeekStart) ||
            parseInt(options.dayOfWeekStart) < 0 ||
            parseInt(options.dayOfWeekStart) > 6
          )
            options.dayOfWeekStart = 0;
          else options.dayOfWeekStart = parseInt(options.dayOfWeekStart);

          if (!options.timepickerScrollbar) scrollbar.hide();

          var tmpDate = [],
            timeOffset;
          if (
            options.minDate &&
            (tmpDate = /^-(.*)$/.exec(options.minDate)) &&
            (tmpDate = Date.parseDate(tmpDate[1], options.formatDate))
          ) {
            timeOffset =
              tmpDate.getTime() + -1 * tmpDate.getTimezoneOffset() * 60000;
            options.minDate = new Date(
              _xdsoft_datetime.now().getTime() - timeOffset
            ).dateFormat(options.formatDate);
          }
          if (
            options.maxDate &&
            (tmpDate = /^\+(.*)$/.exec(options.maxDate)) &&
            (tmpDate = Date.parseDate(tmpDate[1], options.formatDate))
          ) {
            timeOffset =
              tmpDate.getTime() + -1 * tmpDate.getTimezoneOffset() * 60000;
            options.maxDate = new Date(
              _xdsoft_datetime.now().getTime() + timeOffset
            ).dateFormat(options.formatDate);
          }

          mounth_picker
            .find(".xdsoft_today_button")
            .css("visibility", !options.todayButton ? "hidden" : "visible");

          if (options.mask) {
            var e,
              getCaretPos = function (input) {
                try {
                  if (document.selection && document.selection.createRange) {
                    var range = document.selection.createRange();
                    return range.getBookmark().charCodeAt(2) - 2;
                  } else if (input.setSelectionRange)
                    return input.selectionStart;
                } catch (e) {
                  return 0;
                }
              },
              setCaretPos = function (node, pos) {
                var node =
                  typeof node == "string" || node instanceof String
                    ? document.getElementById(node)
                    : node;
                if (!node) {
                  return false;
                } else if (node.createTextRange) {
                  var textRange = node.createTextRange();
                  textRange.collapse(true);
                  textRange.moveEnd(pos);
                  textRange.moveStart(pos);
                  textRange.select();
                  return true;
                } else if (node.setSelectionRange) {
                  node.setSelectionRange(pos, pos);
                  return true;
                }
                return false;
              },
              isValidValue = function (mask, value) {
                var reg = mask
                  .replace(/([\[\]\/\{\}\(\)\-\.\+]{1})/g, "\\$1")
                  .replace(/_/g, "{digit+}")
                  .replace(/([0-9]{1})/g, "{digit$1}")
                  .replace(/\{digit([0-9]{1})\}/g, "[0-$1_]{1}")
                  .replace(/\{digit[\+]\}/g, "[0-9_]{1}");
                return RegExp(reg).test(value);
              };
            input.off("keydown.xdsoft");
            switch (true) {
              case options.mask === true:
                //options.mask = (new Date()).dateFormat( options.format );
                //options.mask = options.mask.replace(/[0-9]/g,'_');
                options.mask = options.format
                  .replace(/Y/g, "9999")
                  .replace(/F/g, "9999")
                  .replace(/m/g, "19")
                  .replace(/d/g, "39")
                  .replace(/H/g, "29")
                  .replace(/i/g, "59")
                  .replace(/s/g, "59");
              case $.type(options.mask) == "string":
                if (!isValidValue(options.mask, input.val()))
                  input.val(options.mask.replace(/[0-9]/g, "_"));

                input.on("keydown.xdsoft", function (event) {
                  var val = this.value,
                    key = event.which;
                  switch (true) {
                    case (key >= KEY0 && key <= KEY9) ||
                      (key >= _KEY0 && key <= _KEY9) ||
                      key == BACKSPACE ||
                      key == DEL:
                      var pos = getCaretPos(this),
                        digit =
                          key != BACKSPACE && key != DEL
                            ? String.fromCharCode(
                              _KEY0 <= key && key <= _KEY9 ? key - KEY0 : key
                            )
                            : "_";
                      if ((key == BACKSPACE || key == DEL) && pos) {
                        pos--;
                        digit = "_";
                      }
                      while (
                        /[^0-9_]/.test(options.mask.substr(pos, 1)) &&
                        pos < options.mask.length &&
                        pos > 0
                      )
                        pos += key == BACKSPACE || key == DEL ? -1 : 1;

                      val = val.substr(0, pos) + digit + val.substr(pos + 1);
                      if ($.trim(val) == "")
                        val = options.mask.replace(/[0-9]/g, "_");
                      else if (pos == options.mask.length) break;

                      pos += key == BACKSPACE || key == DEL ? 0 : 1;
                      while (
                        /[^0-9_]/.test(options.mask.substr(pos, 1)) &&
                        pos < options.mask.length &&
                        pos > 0
                      )
                        pos += key == BACKSPACE || key == DEL ? -1 : 1;
                      if (isValidValue(options.mask, val)) {
                        this.value = val;
                        setCaretPos(this, pos);
                      } else if ($.trim(val) == "")
                        this.value = options.mask.replace(/[0-9]/g, "_");
                      else {
                        input.trigger("error_input.xdsoft");
                      }
                      break;
                    case !!~[AKEY, CKEY, VKEY, ZKEY, YKEY].indexOf(key) &&
                      ctrlDown:
                    case !!~[
                      ESC,
                      ARROWUP,
                      ARROWDOWN,
                      ARROWLEFT,
                      ARROWRIGHT,
                      F5,
                      CTRLKEY,
                      TAB,
                      ENTER,
                    ].indexOf(key):
                      return true;
                  }
                  event.preventDefault();
                  return false;
                });
                break;
            }
          }
          if (options.validateOnBlur) {
            input.off("blur.xdsoft").on("blur.xdsoft", function () {
              if (options.allowBlank && !$.trim($(this).val()).length) {
                $(this).val(null);
                datetimepicker.data("xdsoft_datetime").empty();
              } else if (!Date.parseDate($(this).val(), options.format)) {
                $(this).val(_xdsoft_datetime.now().dateFormat(options.format));
                datetimepicker
                  .data("xdsoft_datetime")
                  .setCurrentTime($(this).val());
              } else {
                datetimepicker
                  .data("xdsoft_datetime")
                  .setCurrentTime($(this).val());
              }
              datetimepicker.trigger("changedatetime.xdsoft");
            });
          }
          options.dayOfWeekStartPrev =
            options.dayOfWeekStart == 0 ? 6 : options.dayOfWeekStart - 1;
          datetimepicker.trigger("xchange.xdsoft");
        };

        datetimepicker
          .data("options", options)
          .on("mousedown.xdsoft", function (event) {
            event.stopPropagation();
            event.preventDefault();
            yearselect.hide();
            monthselect.hide();
            return false;
          });

        var scroll_element = timepicker.find(".xdsoft_time_box");
        scroll_element.append(timebox);
        scroll_element.xdsoftScroller();
        datetimepicker.on("afterOpen.xdsoft", function () {
          scroll_element.xdsoftScroller();
        });

        datetimepicker.append(datepicker).append(timepicker);

        if (options.withoutCopyright !== true)
          datetimepicker.append(xdsoft_copyright);

        datepicker.append(mounth_picker).append(calendar);

        $("body").append(datetimepicker);

        var _xdsoft_datetime = new (function () {
          var _this = this;
          _this.now = function () {
            var d = new Date();
            if (options.yearOffset)
              d.setFullYear(d.getFullYear() + options.yearOffset);
            return d;
          };

          _this.currentTime = this.now();
          _this.isValidDate = function (d) {
            if (Object.prototype.toString.call(d) !== "[object Date]")
              return false;
            return !isNaN(d.getTime());
          };

          _this.setCurrentTime = function (dTime) {
            _this.currentTime =
              typeof dTime == "string"
                ? _this.strtodatetime(dTime)
                : _this.isValidDate(dTime)
                  ? dTime
                  : _this.now();
            datetimepicker.trigger("xchange.xdsoft");
          };

          _this.empty = function () {
            _this.currentTime = null;
          };

          _this.getCurrentTime = function (dTime) {
            return _this.currentTime;
          };

          _this.nextMonth = function () {
            var month = _this.currentTime.getMonth() + 1;
            if (month == 12) {
              _this.currentTime.setFullYear(
                _this.currentTime.getFullYear() + 1
              );
              month = 0;
            }
            _this.currentTime.setDate(
              Math.min(Date.daysInMonth[month], _this.currentTime.getDate())
            );
            _this.currentTime.setMonth(month);
            options.onChangeMonth &&
              options.onChangeMonth.call &&
              options.onChangeMonth.call(
                datetimepicker,
                _xdsoft_datetime.currentTime,
                datetimepicker.data("input")
              );
            datetimepicker.trigger("xchange.xdsoft");
            return month;
          };

          _this.prevMonth = function () {
            var month = _this.currentTime.getMonth() - 1;
            if (month == -1) {
              _this.currentTime.setFullYear(
                _this.currentTime.getFullYear() - 1
              );
              month = 11;
            }
            _this.currentTime.setDate(
              Math.min(Date.daysInMonth[month], _this.currentTime.getDate())
            );
            _this.currentTime.setMonth(month);
            options.onChangeMonth &&
              options.onChangeMonth.call &&
              options.onChangeMonth.call(
                datetimepicker,
                _xdsoft_datetime.currentTime,
                datetimepicker.data("input")
              );
            datetimepicker.trigger("xchange.xdsoft");
            return month;
          };

          _this.strtodatetime = function (sDateTime) {
            var currentTime = sDateTime
              ? Date.parseDate(sDateTime, options.format)
              : _this.now();
            if (!_this.isValidDate(currentTime)) currentTime = _this.now();
            return currentTime;
          };

          _this.strtodate = function (sDate) {
            var currentTime = sDate
              ? Date.parseDate(sDate, options.formatDate)
              : _this.now();
            if (!_this.isValidDate(currentTime)) currentTime = _this.now();
            return currentTime;
          };

          _this.strtotime = function (sTime) {
            var currentTime = sTime
              ? Date.parseDate(sTime, options.formatTime)
              : _this.now();
            if (!_this.isValidDate(currentTime)) currentTime = _this.now();
            return currentTime;
          };

          _this.str = function () {
            return _this.currentTime.dateFormat(options.format);
          };
        })();
        mounth_picker
          .find(".xdsoft_today_button")
          .on("mousedown.xdsoft", function () {
            datetimepicker.data("changed", true);
            _xdsoft_datetime.setCurrentTime(0);
            datetimepicker.trigger("afterOpen.xdsoft");
          })
          .on("dblclick.xdsoft", function () {
            input.val(_xdsoft_datetime.str());
            datetimepicker.trigger("close.xdsoft");
          });
        mounth_picker
          .find(".xdsoft_prev,.xdsoft_next")
          .on("mousedown.xdsoft", function () {
            var $this = $(this),
              timer = 0,
              stop = false;

            (function arguments_callee1(v) {
              var month = _xdsoft_datetime.currentTime.getMonth();
              if ($this.hasClass(options.next)) {
                _xdsoft_datetime.nextMonth();
              } else if ($this.hasClass(options.prev)) {
                _xdsoft_datetime.prevMonth();
              }
              !stop && (timer = setTimeout(arguments_callee1, v ? v : 100));
            })(500);

            $([document.body, window]).on(
              "mouseup.xdsoft",
              function arguments_callee2() {
                clearTimeout(timer);
                stop = true;
                $([document.body, window]).off(
                  "mouseup.xdsoft",
                  arguments_callee2
                );
              }
            );
          });

        timepicker
          .find(".xdsoft_prev,.xdsoft_next")
          .on("mousedown.xdsoft", function () {
            var $this = $(this),
              timer = 0,
              stop = false,
              period = 110;
            (function arguments_callee4(v) {
              var pheight = timeboxparent[0].clientHeight,
                height = timebox[0].offsetHeight,
                top = Math.abs(parseInt(timebox.css("marginTop")));
              if (
                $this.hasClass(options.next) &&
                height - pheight - options.timeHeightInTimePicker >= top
              ) {
                timebox.css(
                  "marginTop",
                  "-" + (top + options.timeHeightInTimePicker) + "px"
                );
              } else if (
                $this.hasClass(options.prev) &&
                top - options.timeHeightInTimePicker >= 0
              ) {
                timebox.css(
                  "marginTop",
                  "-" + (top - options.timeHeightInTimePicker) + "px"
                );
              }
              timeboxparent.trigger("scroll_element.xdsoft_scroller", [
                Math.abs(
                  parseInt(timebox.css("marginTop")) / (height - pheight)
                ),
              ]);
              period = period > 10 ? 10 : period - 10;
              !stop && (timer = setTimeout(arguments_callee4, v ? v : period));
            })(500);
            $([document.body, window]).on(
              "mouseup.xdsoft",
              function arguments_callee5() {
                clearTimeout(timer);
                stop = true;
                $([document.body, window]).off(
                  "mouseup.xdsoft",
                  arguments_callee5
                );
              }
            );
          });

        // base handler - generating a calendar and timepicker
        datetimepicker
          .on("xchange.xdsoft", function (event) {
            var table = "",
              start = new Date(
                _xdsoft_datetime.currentTime.getFullYear(),
                _xdsoft_datetime.currentTime.getMonth(),
                1,
                12,
                0,
                0
              ),
              i = 0,
              today = _xdsoft_datetime.now();
            while (start.getDay() != options.dayOfWeekStart)
              start.setDate(start.getDate() - 1);

            //generate calendar
            table += "<table><thead><tr>";

            // days
            for (var j = 0; j < 7; j++) {
              table +=
                "<th>" +
                options.i18n[options.lang].dayOfWeek[
                j + options.dayOfWeekStart > 6
                  ? 0
                  : j + options.dayOfWeekStart
                ] +
                "</th>";
            }

            table += "</tr></thead>";
            table += "<tbody><tr>";
            var maxDate = false,
              minDate = false;
            if (options.maxDate !== false) {
              maxDate = _xdsoft_datetime.strtodate(options.maxDate);
              maxDate = new Date(
                maxDate.getFullYear(),
                maxDate.getMonth(),
                maxDate.getDate(),
                23,
                59,
                59,
                999
              );
            }
            if (options.minDate !== false) {
              minDate = _xdsoft_datetime.strtodate(options.minDate);
              minDate = new Date(
                minDate.getFullYear(),
                minDate.getMonth(),
                minDate.getDate()
              );
            }
            var d,
              y,
              m,
              classes = [];
            while (
              i < _xdsoft_datetime.currentTime.getDaysInMonth() ||
              start.getDay() != options.dayOfWeekStart ||
              _xdsoft_datetime.currentTime.getMonth() == start.getMonth()
            ) {
              classes = [];
              i++;

              d = start.getDate();
              y = start.getFullYear();
              m = start.getMonth();

              classes.push("xdsoft_date");

              if (
                (maxDate !== false && start > maxDate) ||
                (minDate !== false && start < minDate)
              ) {
                classes.push("xdsoft_disabled");
              }

              if (_xdsoft_datetime.currentTime.getMonth() != m)
                classes.push("xdsoft_other_month");

              if (
                (options.defaultSelect || datetimepicker.data("changed")) &&
                _xdsoft_datetime.currentTime.dateFormat("d.m.Y") ==
                start.dateFormat("d.m.Y")
              ) {
                classes.push("xdsoft_current");
              }

              if (today.dateFormat("d.m.Y") == start.dateFormat("d.m.Y")) {
                classes.push("xdsoft_today");
              }

              if (
                start.getDay() == 0 ||
                start.getDay() == 6 ||
                ~options.weekends.indexOf(start.dateFormat("d.m.Y"))
              ) {
                classes.push("xdsoft_weekend");
              }

              table +=
                '<td data-date="' +
                d +
                '" data-month="' +
                m +
                '" data-year="' +
                y +
                '"' +
                ' class="xdsoft_date xdsoft_day_of_week' +
                start.getDay() +
                " " +
                classes.join(" ") +
                '">' +
                "<div>" +
                d +
                "</div>" +
                "</td>";

              if (start.getDay() == options.dayOfWeekStartPrev) {
                table += "</tr>";
              }

              start.setDate(d + 1);
            }
            table += "</tbody></table>";

            calendar.html(table);

            mounth_picker
              .find(".xdsoft_label span")
              .eq(0)
              .text(
                options.i18n[options.lang].months[
                _xdsoft_datetime.currentTime.getMonth()
                ]
              );
            mounth_picker
              .find(".xdsoft_label span")
              .eq(1)
              .text(_xdsoft_datetime.currentTime.getFullYear());

            // generate timebox
            var time = "",
              h = "",
              m = "",
              line_time = function line_time(h, m) {
                var now = _xdsoft_datetime.now();
                now.setHours(h);
                h = parseInt(now.getHours());
                now.setMinutes(m);
                m = parseInt(now.getMinutes());

                classes = [];
                if (
                  (options.maxTime !== false &&
                    _xdsoft_datetime.strtotime(options.maxTime).getTime() <
                    now.getTime()) ||
                  (options.minTime !== false &&
                    _xdsoft_datetime.strtotime(options.minTime).getTime() >
                    now.getTime())
                )
                  classes.push("xdsoft_disabled");
                if (
                  (options.defaultSelect || datetimepicker.data("changed")) &&
                  parseInt(_xdsoft_datetime.currentTime.getHours()) ==
                  parseInt(h) &&
                  (options.step > 59 ||
                    Math[options.roundTime](
                      _xdsoft_datetime.currentTime.getMinutes() / options.step
                    ) *
                    options.step ==
                    parseInt(m))
                )
                  classes.push("xdsoft_current");
                if (
                  parseInt(today.getHours()) == parseInt(h) &&
                  parseInt(today.getMinutes()) == parseInt(m)
                )
                  classes.push("xdsoft_today");
                time +=
                  '<div class="xdsoft_time ' +
                  classes.join(" ") +
                  '" data-hour="' +
                  h +
                  '" data-minute="' +
                  m +
                  '">' +
                  now.dateFormat(options.formatTime) +
                  "</div>";
              };

            if (
              !options.allowTimes ||
              !$.isArray(options.allowTimes) ||
              !options.allowTimes.length
            ) {
              for (var i = 0, j = 0; i < (options.hours12 ? 12 : 24); i++) {
                for (j = 0; j < 60; j += options.step) {
                  h = (i < 10 ? "0" : "") + i;
                  m = (j < 10 ? "0" : "") + j;
                  line_time(h, m);
                }
              }
            } else {
              for (var i = 0; i < options.allowTimes.length; i++) {
                h = _xdsoft_datetime
                  .strtotime(options.allowTimes[i])
                  .getHours();
                m = _xdsoft_datetime
                  .strtotime(options.allowTimes[i])
                  .getMinutes();
                line_time(h, m);
              }
            }

            timebox.html(time);

            var opt = "",
              i = 0;

            for (
              i = parseInt(options.yearStart, 10) + options.yearOffset;
              i <= parseInt(options.yearEnd, 10) + options.yearOffset;
              i++
            ) {
              opt +=
                '<div class="xdsoft_option ' +
                (_xdsoft_datetime.currentTime.getFullYear() == i
                  ? "xdsoft_current"
                  : "") +
                '" data-value="' +
                i +
                '">' +
                i +
                "</div>";
            }
            yearselect.children().eq(0).html(opt);

            for (i = 0, opt = ""; i <= 11; i++) {
              opt +=
                '<div class="xdsoft_option ' +
                (_xdsoft_datetime.currentTime.getMonth() == i
                  ? "xdsoft_current"
                  : "") +
                '" data-value="' +
                i +
                '">' +
                options.i18n[options.lang].months[i] +
                "</div>";
            }
            monthselect.children().eq(0).html(opt);
            $(this).trigger("generate.xdsoft");
            event.stopPropagation();
          })
          .on("afterOpen.xdsoft", function () {
            if (options.timepicker && timebox.find(".xdsoft_current").length) {
              var pheight = timeboxparent[0].clientHeight,
                height = timebox[0].offsetHeight,
                top =
                  timebox.find(".xdsoft_current").index() *
                  options.timeHeightInTimePicker +
                  1;
              if (height - pheight < top) top = height - pheight;
              timebox.css("marginTop", "-" + parseInt(top) + "px");
              timeboxparent.trigger("scroll_element.xdsoft_scroller", [
                parseInt(top) / (height - pheight),
              ]);
            }
          });
        var timerclick = 0;
        calendar.on("click.xdsoft", "td", function () {
          timerclick++;
          var $this = $(this),
            currentTime = _xdsoft_datetime.currentTime;
          if ($this.hasClass("xdsoft_disabled")) return false;

          currentTime.setFullYear($this.data("year"));
          currentTime.setMonth($this.data("month"));
          currentTime.setDate($this.data("date"));
          datetimepicker.trigger("select.xdsoft", [currentTime]);

          input.val(_xdsoft_datetime.str());
          if (
            (timerclick > 1 ||
              options.closeOnDateSelect === true ||
              (options.closeOnDateSelect === 0 && !options.timepicker)) &&
            !options.inline
          ) {
            datetimepicker.trigger("close.xdsoft");
          }

          if (options.onSelectDate && options.onSelectDate.call) {
            options.onSelectDate.call(
              datetimepicker,
              _xdsoft_datetime.currentTime,
              datetimepicker.data("input")
            );
          }

          datetimepicker.data("changed", true);
          datetimepicker.trigger("xchange.xdsoft");
          datetimepicker.trigger("changedatetime.xdsoft");
          setTimeout(function () {
            timerclick = 0;
          }, 200);
        });

        timebox.on("click.xdsoft", "div", function () {
          var $this = $(this),
            currentTime = _xdsoft_datetime.currentTime;
          if ($this.hasClass("xdsoft_disabled")) return false;
          currentTime.setHours($this.data("hour"));
          currentTime.setMinutes($this.data("minute"));
          datetimepicker.trigger("select.xdsoft", [currentTime]);

          datetimepicker.data("input").val(_xdsoft_datetime.str());

          !options.inline && datetimepicker.trigger("close.xdsoft");

          if (options.onSelectTime && options.onSelectTime.call) {
            options.onSelectTime.call(
              datetimepicker,
              _xdsoft_datetime.currentTime,
              datetimepicker.data("input")
            );
          }
          datetimepicker.data("changed", true);
          datetimepicker.trigger("xchange.xdsoft");
          datetimepicker.trigger("changedatetime.xdsoft");
        });

        datetimepicker.mousewheel &&
          datepicker.mousewheel(function (event, delta, deltaX, deltaY) {
            if (!options.scrollMonth) return true;
            if (delta < 0) _xdsoft_datetime.nextMonth();
            else _xdsoft_datetime.prevMonth();
            return false;
          });

        datetimepicker.mousewheel &&
          timeboxparent
            .unmousewheel()
            .mousewheel(function (event, delta, deltaX, deltaY) {
              if (!options.scrollTime) return true;
              var pheight = timeboxparent[0].clientHeight,
                height = timebox[0].offsetHeight,
                top = Math.abs(parseInt(timebox.css("marginTop"))),
                fl = true;
              if (
                delta < 0 &&
                height - pheight - options.timeHeightInTimePicker >= top
              ) {
                timebox.css(
                  "marginTop",
                  "-" + (top + options.timeHeightInTimePicker) + "px"
                );
                fl = false;
              } else if (
                delta > 0 &&
                top - options.timeHeightInTimePicker >= 0
              ) {
                timebox.css(
                  "marginTop",
                  "-" + (top - options.timeHeightInTimePicker) + "px"
                );
                fl = false;
              }
              timeboxparent.trigger("scroll_element.xdsoft_scroller", [
                Math.abs(
                  parseInt(timebox.css("marginTop")) / (height - pheight)
                ),
              ]);
              event.stopPropagation();
              return fl;
            });

        datetimepicker
          .on("changedatetime.xdsoft", function () {
            if (options.onChangeDateTime && options.onChangeDateTime.call)
              options.onChangeDateTime.call(
                datetimepicker,
                _xdsoft_datetime.currentTime,
                datetimepicker.data("input")
              );
          })
          .on("generate.xdsoft", function () {
            if (options.onGenerate && options.onGenerate.call)
              options.onGenerate.call(
                datetimepicker,
                _xdsoft_datetime.currentTime,
                datetimepicker.data("input")
              );
          });

        var current_time_index = 0;
        input.mousewheel &&
          input.mousewheel(function (event, delta, deltaX, deltaY) {
            if (!options.scrollInput) return true;
            if (!options.datepicker && options.timepicker) {
              current_time_index = timebox.find(".xdsoft_current").length
                ? timebox.find(".xdsoft_current").eq(0).index()
                : 0;
              if (
                current_time_index + delta >= 0 &&
                current_time_index + delta < timebox.children().length
              )
                current_time_index += delta;
              timebox.children().eq(current_time_index).length &&
                timebox.children().eq(current_time_index).trigger("mousedown");
              return false;
            } else if (options.datepicker && !options.timepicker) {
              datepicker.trigger(event, [delta, deltaX, deltaY]);
              input.val && input.val(_xdsoft_datetime.str());
              datetimepicker.trigger("changedatetime.xdsoft");
              return false;
            }
          });
        var setPos = function () {
          var offset = datetimepicker.data("input").offset(),
            top = offset.top + datetimepicker.data("input")[0].offsetHeight - 1,
            left = offset.left;
          if (
            top + datetimepicker[0].offsetHeight >
            $(window).height() + $(window).scrollTop()
          )
            top = offset.top - datetimepicker[0].offsetHeight + 1;
          if (left + datetimepicker[0].offsetWidth > $(window).width())
            left =
              offset.left -
              datetimepicker[0].offsetWidth +
              datetimepicker.data("input")[0].offsetWidth;
          datetimepicker.css({
            left: left,
            top: top,
          });
        };
        datetimepicker
          .on("open.xdsoft", function () {
            var onShow = true;
            if (options.onShow && options.onShow.call) {
              onShow = options.onShow.call(
                datetimepicker,
                _xdsoft_datetime.currentTime,
                datetimepicker.data("input")
              );
            }
            if (onShow !== false) {
              datetimepicker.show();
              datetimepicker.trigger("afterOpen.xdsoft");
              setPos();
              $(window)
                .off("resize.xdsoft", setPos)
                .on("resize.xdsoft", setPos);

              if (options.closeOnWithoutClick) {
                $([document.body, window]).on(
                  "mousedown.xdsoft",
                  function arguments_callee6() {
                    datetimepicker.trigger("close.xdsoft");
                    $([document.body, window]).off(
                      "mousedown.xdsoft",
                      arguments_callee6
                    );
                  }
                );
              }
            }
          })
          .on("close.xdsoft", function (event) {
            var onClose = true;
            if (options.onClose && options.onClose.call) {
              onClose = options.onClose.call(
                datetimepicker,
                _xdsoft_datetime.currentTime,
                datetimepicker.data("input")
              );
            }
            if (onClose !== false && !options.opened && !options.inline) {
              datetimepicker.hide();
            }
            event.stopPropagation();
          })
          .data("input", input);

        var timer = 0,
          timer1 = 0;

        datetimepicker.data("xdsoft_datetime", _xdsoft_datetime);
        datetimepicker.setOptions(options);

        var ct = options.value
          ? options.value
          : input && input.val && input.val()
            ? input.val()
            : "";
        if (
          ct &&
          _xdsoft_datetime.isValidDate(
            (ct = Date.parseDate(ct, options.format))
          )
        ) {
          datetimepicker.data("changed", true);
        } else ct = "";

        _xdsoft_datetime.setCurrentTime(ct ? ct : 0);

        datetimepicker.trigger("afterOpen.xdsoft");

        input
          .data("xdsoft_datetimepicker", datetimepicker)
          .on("open.xdsoft focusin.xdsoft mousedown.xdsoft", function (event) {
            if (
              input.is(":disabled") ||
              input.is(":hidden") ||
              !input.is(":visible")
            )
              return;
            clearTimeout(timer);
            timer = setTimeout(function () {
              if (
                input.is(":disabled") ||
                input.is(":hidden") ||
                !input.is(":visible")
              )
                return;
              _xdsoft_datetime.setCurrentTime(
                input && input.val && input.val() ? input.val() : 0
              );
              datetimepicker.trigger("open.xdsoft");
            }, 100);
          })
          .on("keydown.xdsoft", function (event) {
            var val = this.value,
              key = event.which;
            switch (true) {
              case !!~[ENTER].indexOf(key):
                var elementSelector = $("input:visible,textarea:visible");
                datetimepicker.trigger("close.xdsoft");
                elementSelector.eq(elementSelector.index(this) + 1).focus();
                return false;
              case !!~[TAB].indexOf(key):
                datetimepicker.trigger("close.xdsoft");
                return true;
            }
          });
      },
      destroyDateTimePicker = function (input) {
        var datetimepicker = input.data("xdsoft_datetimepicker");
        if (datetimepicker) {
          datetimepicker.data("xdsoft_datetime", null);
          datetimepicker.remove();
          input
            .data("xdsoft_datetimepicker", null)
            .off(
              "open.xdsoft focusin.xdsoft focusout.xdsoft mousedown.xdsoft blur.xdsoft keydown.xdsoft"
            );
          $(window).off("resize.xdsoft");
          $([window, document.body]).off("mousedown.xdsoft");
          input.unmousewheel && input.unmousewheel();
        }
      };
    $(document)
      .off("keydown.xdsoftctrl keyup.xdsoftctrl")
      .on("keydown.xdsoftctrl", function (e) {
        if (e.keyCode == CTRLKEY) ctrlDown = true;
      })
      .on("keyup.xdsoftctrl", function (e) {
        if (e.keyCode == CTRLKEY) ctrlDown = false;
      });
    return this.each(function () {
      var datetimepicker;
      if ((datetimepicker = $(this).data("xdsoft_datetimepicker"))) {
        if ($.type(opt) === "string") {
          switch (opt) {
            case "show":
              $(this).select().focus();
              datetimepicker.trigger("open.xdsoft");
              break;
            case "hide":
              datetimepicker.trigger("close.xdsoft");
              break;
            case "destroy":
              destroyDateTimePicker($(this));
              break;
            case "reset":
              this.value = this.defaultValue;
              if (
                !this.value ||
                !datetimepicker
                  .data("xdsoft_datetime")
                  .isValidDate(Date.parseDate(this.value, options.format))
              )
                datetimepicker.data("changed", false);
              datetimepicker.data("xdsoft_datetime").setCurrentTime(this.value);
              break;
          }
        } else {
          datetimepicker.setOptions(opt);
        }
        return 0;
      } else $.type(opt) !== "string" && createDateTimePicker($(this));
    });
  };
})(jQuery);

//http://www.xaprb.com/blog/2005/12/12/javascript-closures-for-runtime-efficiency/
/*
 * Copyright (C) 2004 Baron Schwartz <baron at sequent dot org>
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation, version 2.1.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE.  See the GNU Lesser General Public License for more
 * details.
 */
Date.parseFunctions = {
  count: 0,
};
Date.parseRegexes = [];
Date.formatFunctions = {
  count: 0,
};
Date.prototype.dateFormat = function (format) {
  if (Date.formatFunctions[format] == null) {
    Date.createNewFormat(format);
  }
  var func = Date.formatFunctions[format];
  return this[func]();
};
Date.createNewFormat = function (format) {
  var funcName = "format" + Date.formatFunctions.count++;
  Date.formatFunctions[format] = funcName;
  var code = "Date.prototype." + funcName + " = function() {return ";
  var special = false;
  var ch = "";
  for (var i = 0; i < format.length; ++i) {
    ch = format.charAt(i);
    if (!special && ch == "\\") {
      special = true;
    } else if (special) {
      special = false;
      code += "'" + String.escape(ch) + "' + ";
    } else {
      code += Date.getFormatCode(ch);
    }
  }
  eval(code.substring(0, code.length - 3) + ";}");
};
Date.getFormatCode = function (character) {
  switch (character) {
    case "d":
      return "String.leftPad(this.getDate(), 2, '0') + ";
    case "D":
      return "Date.dayNames[this.getDay()].substring(0, 3) + ";
    case "j":
      return "this.getDate() + ";
    case "l":
      return "Date.dayNames[this.getDay()] + ";
    case "S":
      return "this.getSuffix() + ";
    case "w":
      return "this.getDay() + ";
    case "z":
      return "this.getDayOfYear() + ";
    case "W":
      return "this.getWeekOfYear() + ";
    case "F":
      return "Date.monthNames[this.getMonth()] + ";
    case "m":
      return "String.leftPad(this.getMonth() + 1, 2, '0') + ";
    case "M":
      return "Date.monthNames[this.getMonth()].substring(0, 3) + ";
    case "n":
      return "(this.getMonth() + 1) + ";
    case "t":
      return "this.getDaysInMonth() + ";
    case "L":
      return "(this.isLeapYear() ? 1 : 0) + ";
    case "Y":
      return "this.getFullYear() + ";
    case "y":
      return "('' + this.getFullYear()).substring(2, 4) + ";
    case "a":
      return "(this.getHours() < 12 ? 'am' : 'pm') + ";
    case "A":
      return "(this.getHours() < 12 ? 'AM' : 'PM') + ";
    case "g":
      return "((this.getHours() %12) ? this.getHours() % 12 : 12) + ";
    case "G":
      return "this.getHours() + ";
    case "h":
      return "String.leftPad((this.getHours() %12) ? this.getHours() % 12 : 12, 2, '0') + ";
    case "H":
      return "String.leftPad(this.getHours(), 2, '0') + ";
    case "i":
      return "String.leftPad(this.getMinutes(), 2, '0') + ";
    case "s":
      return "String.leftPad(this.getSeconds(), 2, '0') + ";
    case "O":
      return "this.getGMTOffset() + ";
    case "T":
      return "this.getTimezone() + ";
    case "Z":
      return "(this.getTimezoneOffset() * -60) + ";
    default:
      return "'" + String.escape(character) + "' + ";
  }
};
Date.parseDate = function (input, format) {
  if (Date.parseFunctions[format] == null) {
    Date.createParser(format);
  }
  var func = Date.parseFunctions[format];
  return Date[func](input);
};
Date.createParser = function (format) {
  var funcName = "parse" + Date.parseFunctions.count++;
  var regexNum = Date.parseRegexes.length;
  var currentGroup = 1;
  Date.parseFunctions[format] = funcName;
  var code =
    "Date." +
    funcName +
    " = function(input) {\n" +
    "var y = -1, m = -1, d = -1, h = -1, i = -1, s = -1;\n" +
    "var d = new Date();\n" +
    "y = d.getFullYear();\n" +
    "m = d.getMonth();\n" +
    "d = d.getDate();\n" +
    "var results = input.match(Date.parseRegexes[" +
    regexNum +
    "]);\n" +
    "if (results && results.length > 0) {";
  var regex = "";
  var special = false;
  var ch = "";
  for (var i = 0; i < format.length; ++i) {
    ch = format.charAt(i);
    if (!special && ch == "\\") {
      special = true;
    } else if (special) {
      special = false;
      regex += String.escape(ch);
    } else {
      obj = Date.formatCodeToRegex(ch, currentGroup);
      currentGroup += obj.g;
      regex += obj.s;
      if (obj.g && obj.c) {
        code += obj.c;
      }
    }
  }
  code +=
    "if (y > 0 && m >= 0 && d > 0 && h >= 0 && i >= 0 && s >= 0)\n" +
    "{return new Date(y, m, d, h, i, s);}\n" +
    "else if (y > 0 && m >= 0 && d > 0 && h >= 0 && i >= 0)\n" +
    "{return new Date(y, m, d, h, i);}\n" +
    "else if (y > 0 && m >= 0 && d > 0 && h >= 0)\n" +
    "{return new Date(y, m, d, h);}\n" +
    "else if (y > 0 && m >= 0 && d > 0)\n" +
    "{return new Date(y, m, d);}\n" +
    "else if (y > 0 && m >= 0)\n" +
    "{return new Date(y, m);}\n" +
    "else if (y > 0)\n" +
    "{return new Date(y);}\n" +
    "}return null;}";
  Date.parseRegexes[regexNum] = new RegExp("^" + regex + "$");
  eval(code);
};
Date.formatCodeToRegex = function (character, currentGroup) {
  switch (character) {
    case "D":
      return {
        g: 0,
        c: null,
        s: "(?:Sun|Mon|Tue|Wed|Thu|Fri|Sat)",
      };
    case "j":
    case "d":
      return {
        g: 1,
        c: "d = parseInt(results[" + currentGroup + "], 10);\n",
        s: "(\\d{1,2})",
      };
    case "l":
      return {
        g: 0,
        c: null,
        s: "(?:" + Date.dayNames.join("|") + ")",
      };
    case "S":
      return {
        g: 0,
        c: null,
        s: "(?:st|nd|rd|th)",
      };
    case "w":
      return {
        g: 0,
        c: null,
        s: "\\d",
      };
    case "z":
      return {
        g: 0,
        c: null,
        s: "(?:\\d{1,3})",
      };
    case "W":
      return {
        g: 0,
        c: null,
        s: "(?:\\d{2})",
      };
    case "F":
      return {
        g: 1,
        c:
          "m = parseInt(Date.monthNumbers[results[" +
          currentGroup +
          "].substring(0, 3)], 10);\n",
        s: "(" + Date.monthNames.join("|") + ")",
      };
    case "M":
      return {
        g: 1,
        c:
          "m = parseInt(Date.monthNumbers[results[" +
          currentGroup +
          "]], 10);\n",
        s: "(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)",
      };
    case "n":
    case "m":
      return {
        g: 1,
        c: "m = parseInt(results[" + currentGroup + "], 10) - 1;\n",
        s: "(\\d{1,2})",
      };
    case "t":
      return {
        g: 0,
        c: null,
        s: "\\d{1,2}",
      };
    case "L":
      return {
        g: 0,
        c: null,
        s: "(?:1|0)",
      };
    case "Y":
      return {
        g: 1,
        c: "y = parseInt(results[" + currentGroup + "], 10);\n",
        s: "(\\d{4})",
      };
    case "y":
      return {
        g: 1,
        c:
          "var ty = parseInt(results[" +
          currentGroup +
          "], 10);\n" +
          "y = ty > Date.y2kYear ? 1900 + ty : 2000 + ty;\n",
        s: "(\\d{1,2})",
      };
    case "a":
      return {
        g: 1,
        c:
          "if (results[" +
          currentGroup +
          "] == 'am') {\n" +
          "if (h == 12) { h = 0; }\n" +
          "} else { if (h < 12) { h += 12; }}",
        s: "(am|pm)",
      };
    case "A":
      return {
        g: 1,
        c:
          "if (results[" +
          currentGroup +
          "] == 'AM') {\n" +
          "if (h == 12) { h = 0; }\n" +
          "} else { if (h < 12) { h += 12; }}",
        s: "(AM|PM)",
      };
    case "g":
    case "G":
    case "h":
    case "H":
      return {
        g: 1,
        c: "h = parseInt(results[" + currentGroup + "], 10);\n",
        s: "(\\d{1,2})",
      };
    case "i":
      return {
        g: 1,
        c: "i = parseInt(results[" + currentGroup + "], 10);\n",
        s: "(\\d{2})",
      };
    case "s":
      return {
        g: 1,
        c: "s = parseInt(results[" + currentGroup + "], 10);\n",
        s: "(\\d{2})",
      };
    case "O":
      return {
        g: 0,
        c: null,
        s: "[+-]\\d{4}",
      };
    case "T":
      return {
        g: 0,
        c: null,
        s: "[A-Z]{3}",
      };
    case "Z":
      return {
        g: 0,
        c: null,
        s: "[+-]\\d{1,5}",
      };
    default:
      return {
        g: 0,
        c: null,
        s: String.escape(character),
      };
  }
};
Date.prototype.getTimezone = function () {
  return this.toString()
    .replace(/^.*? ([A-Z]{3}) [0-9]{4}.*$/, "$1")
    .replace(/^.*?\(([A-Z])[a-z]+ ([A-Z])[a-z]+ ([A-Z])[a-z]+\)$/, "$1$2$3");
};
Date.prototype.getGMTOffset = function () {
  return (
    (this.getTimezoneOffset() > 0 ? "-" : "+") +
    String.leftPad(
      Math.floor(Math.abs(this.getTimezoneOffset()) / 60),
      2,
      "0"
    ) +
    String.leftPad(Math.abs(this.getTimezoneOffset()) % 60, 2, "0")
  );
};
Date.prototype.getDayOfYear = function () {
  var num = 0;
  Date.daysInMonth[1] = this.isLeapYear() ? 29 : 28;
  for (var i = 0; i < this.getMonth(); ++i) {
    num += Date.daysInMonth[i];
  }
  return num + this.getDate() - 1;
};
Date.prototype.getWeekOfYear = function () {
  var now = this.getDayOfYear() + (4 - this.getDay());
  var jan1 = new Date(this.getFullYear(), 0, 1);
  var then = 7 - jan1.getDay() + 4;
  document.write(then);
  return String.leftPad((now - then) / 7 + 1, 2, "0");
};
Date.prototype.isLeapYear = function () {
  var year = this.getFullYear();
  return (year & 3) == 0 && (year % 100 || (year % 400 == 0 && year));
};
Date.prototype.getFirstDayOfMonth = function () {
  var day = (this.getDay() - (this.getDate() - 1)) % 7;
  return day < 0 ? day + 7 : day;
};
Date.prototype.getLastDayOfMonth = function () {
  var day =
    (this.getDay() + (Date.daysInMonth[this.getMonth()] - this.getDate())) % 7;
  return day < 0 ? day + 7 : day;
};
Date.prototype.getDaysInMonth = function () {
  Date.daysInMonth[1] = this.isLeapYear() ? 29 : 28;
  return Date.daysInMonth[this.getMonth()];
};
Date.prototype.getSuffix = function () {
  switch (this.getDate()) {
    case 1:
    case 21:
    case 31:
      return "st";
    case 2:
    case 22:
      return "nd";
    case 3:
    case 23:
      return "rd";
    default:
      return "th";
  }
};
String.escape = function (string) {
  return string.replace(/('|\\)/g, "\\$1");
};
String.leftPad = function (val, size, ch) {
  var result = new String(val);
  if (ch == null) {
    ch = " ";
  }
  while (result.length < size) {
    result = ch + result;
  }
  return result;
};
Date.daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
Date.monthNames = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];
Date.dayNames = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];
Date.y2kYear = 50;
Date.monthNumbers = {
  Jan: 0,
  Feb: 1,
  Mar: 2,
  Apr: 3,
  May: 4,
  Jun: 5,
  Jul: 6,
  Aug: 7,
  Sep: 8,
  Oct: 9,
  Nov: 10,
  Dec: 11,
};
Date.patterns = {
  ISO8601LongPattern: "Y-m-d H:i:s",
  ISO8601ShortPattern: "Y-m-d",
  ShortDatePattern: "n/j/Y",
  LongDatePattern: "l, F d, Y",
  FullDateTimePattern: "l, F d, Y g:i:s A",
  MonthDayPattern: "F d",
  ShortTimePattern: "g:i A",
  LongTimePattern: "g:i:s A",
  SortableDateTimePattern: "Y-m-d\\TH:i:s",
  UniversalSortableDateTimePattern: "Y-m-d H:i:sO",
  YearMonthPattern: "F, Y",
};

//https://github.com/brandonaaron/jquery-mousewheel/blob/master/jquery.mousewheel.js
/*
 * Copyright (c) 2013 Brandon Aaron (http://brandonaaron.net)
 *
 * Licensed under the MIT License (LICENSE.txt).
 *
 * Thanks to: http://adomas.org/javascript-mouse-wheel/ for some pointers.
 * Thanks to: Mathias Bank(http://www.mathias-bank.de) for a scope bug fix.
 * Thanks to: Seamus Leahy for adding deltaX and deltaY
 *
 * Version: 3.1.3
 *
 * Requires: 1.2.2+
 */
(function (factory) {
  if (typeof define === "function" && define.amd) {
    define(["jquery"], factory);
  } else if (typeof exports === "object") {
    module.exports = factory;
  } else {
    factory(jQuery);
  }
})(function ($) {
  var toFix = ["wheel", "mousewheel", "DOMMouseScroll", "MozMousePixelScroll"];
  var toBind =
    "onwheel" in document || document.documentMode >= 9
      ? ["wheel"]
      : ["mousewheel", "DomMouseScroll", "MozMousePixelScroll"];
  var lowestDelta, lowestDeltaXY;
  if ($.event.fixHooks) {
    for (var i = toFix.length; i;) {
      $.event.fixHooks[toFix[--i]] = $.event.mouseHooks;
    }
  }
  $.event.special.mousewheel = {
    setup: function () {
      if (this.addEventListener) {
        for (var i = toBind.length; i;) {
          this.addEventListener(toBind[--i], handler, false);
        }
      } else {
        this.onmousewheel = handler;
      }
    },
    teardown: function () {
      if (this.removeEventListener) {
        for (var i = toBind.length; i;) {
          this.removeEventListener(toBind[--i], handler, false);
        }
      } else {
        this.onmousewheel = null;
      }
    },
  };
  $.fn.extend({
    mousewheel: function (fn) {
      return fn ? this.bind("mousewheel", fn) : this.trigger("mousewheel");
    },
    unmousewheel: function (fn) {
      return this.unbind("mousewheel", fn);
    },
  });

  function handler(event) {
    var orgEvent = event || window.event,
      args = [].slice.call(arguments, 1),
      delta = 0,
      deltaX = 0,
      deltaY = 0,
      absDelta = 0,
      absDeltaXY = 0,
      fn;
    event = $.event.fix(orgEvent);
    event.type = "mousewheel";
    if (orgEvent.wheelDelta) {
      delta = orgEvent.wheelDelta;
    }
    if (orgEvent.detail) {
      delta = orgEvent.detail * -1;
    }
    if (orgEvent.deltaY) {
      deltaY = orgEvent.deltaY * -1;
      delta = deltaY;
    }
    if (orgEvent.deltaX) {
      deltaX = orgEvent.deltaX;
      delta = deltaX * -1;
    }
    if (orgEvent.wheelDeltaY !== undefined) {
      deltaY = orgEvent.wheelDeltaY;
    }
    if (orgEvent.wheelDeltaX !== undefined) {
      deltaX = orgEvent.wheelDeltaX * -1;
    }
    absDelta = Math.abs(delta);
    if (!lowestDelta || absDelta < lowestDelta) {
      lowestDelta = absDelta;
    }
    absDeltaXY = Math.max(Math.abs(deltaY), Math.abs(deltaX));
    if (!lowestDeltaXY || absDeltaXY < lowestDeltaXY) {
      lowestDeltaXY = absDeltaXY;
    }
    fn = delta > 0 ? "floor" : "ceil";
    delta = Math[fn](delta / lowestDelta);
    deltaX = Math[fn](deltaX / lowestDeltaXY);
    deltaY = Math[fn](deltaY / lowestDeltaXY);
    args.unshift(event, delta, deltaX, deltaY);
    return ($.event.dispatch || $.event.handle).apply(this, args);
  }
});
