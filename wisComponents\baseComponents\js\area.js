/**
 * @description 面积图
 * <AUTHOR>
 * @date 2020-03-17
 * @class Area
 */
class Area {
  constructor(container, opts) {
    this._initProperty();
    this._container = container;
    this.property = $.extend(true, this.property, opts);
    this.keys = ['x', 'y'];
  }
  /**
   * @description 初始化组件配置项
   */
  _initProperty() {
    this.property = {
      strokeColor: '#ff0000',
      fillColor: {
        type: 0,
        direction: 0,
        stops: [
          {
            offset: 0,
            color: '#ff000055',
          },
          {
            offset: 1,
            color: '#00ff0055',
          },
        ],
      },
      curve: false
    };

    this._optionDic = [
      {
        name: 'strokeColor',
        displayName: '边框颜色',
        description: '面积图边框颜色',
        type: OptionType.color,
        show: true,
        editable: true,
      },
      {
        name: 'fillColor',
        displayName: '填充颜色',
        description: '面积图内部的填充颜色',
        type: OptionType.colorGradient,
        show: true,
        editable: true,
      },
      {
        name: 'curve',
        displayName: '是否平滑',
        description: '面积图是否平滑',
        type: OptionType.boolean,
        show: true,
        editable: true,
      },
    ];
  }

  getRadomA() {
    var returnStr = '',
      range = 13,
      arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    for (var i = 0; i < range; i++) {
      var index = Math.round(Math.random() * (arr.length - 1));
      returnStr += arr[index];
    }
    return returnStr;
  };

  /**
   * @description 绘制面积图
   */
  _drawArea(area, data) {
    let fillColor = '';
    let box = this._container.node().parentElement.parentElement.getAttribute('viewBox').split(' ');
    if (typeof this.property.fillColor === 'string') {
      fillColor = this.property.fillColor;
    } else {
      fillColor = WisCompUtil.setGradient(this._container, 'testGradient', this.property.fillColor);
    }
    this.clipPath = '';
    let randomId = 'clip_' + this.getRadomA();
    if (this._container.select('defs').empty()) {
      this.clipPath = this._container.append('defs')
        .append('clipPath')
        .attr('id', `${randomId}`)
        .append('rect')
        .attr('width', 0).attr('height', box[3]);
    } else {
      this.clipPath = this._container.select('defs')
        .append('clipPath')
        .attr('id', `${randomId}`)
        .append('rect')
        .attr('width', 0).attr('height', box[3]);
    }
    this._container.append('path').attr('d', area(data)).attr('stroke', this.property.strokeColor).attr('fill', `${fillColor}`).attr('clip-path', `url(#${randomId})`);
    this.clipPath.transition().duration(1500).attr('width', box[2]);
  }
  /**
   * @description 更新面积图
   */
  _updateArea(area, data) {
    this.clipPath.transition().duration(100).attr('width', 0).transition().duration(1400).attr('width', box[2]);
    this._container.select('path').attr('d', area(data));
  }
  /**
   * @description 生成面积路径
   */
  _getArea() {
    let xRange = this.property.xRange;
    let yRange = this.property.yRange;
    let area = null;
    if (!xRange && !yRange) {
      return area;
    } else {
      area = d3
        .area()
        .x((d) => xRange(d.x))
        .y1((d) => yRange(d.y))
        .y0(yRange.domain()[0] > 0 ? yRange(yRange.domain()[0]) : yRange(0));
      if (this.property.curve) {
        area.curve(d3.curveCardinal);
      }
      return area;
    }
  }
  /**
   * @description 更新数据
   */
  update(data) {
    //如果数据中y值为空字符串则剔除掉
    data = data.filter((d) => d.y !== undefined && d.y !== '');
    let area = this._getArea();
    if (area === null) return;
    if (this._container.select('path').empty()) {
      this._drawArea(area, data);
    } else {
      this._updateArea(area, data);
    }
  }
  /**
   * @description 更新配置
   */
  setOption(opt) {
    this.property = $.extend(true, this.property, opt);
  }
}
