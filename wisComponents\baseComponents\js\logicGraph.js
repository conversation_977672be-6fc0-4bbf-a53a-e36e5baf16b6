/*
 * @Description: 
 * @Version: 
 * @Author: matinayu
 * @Date: 2024-03-18 17:27:40
 * @LastEditors: matianyu
 * @LastEditTime: 2025-07-29 15:57:42
 */
class LogicGraph extends DIVComponentBase {
  constructor(id, code, container, workMode, option = {}, useDefaultOpt = true) {
    super(id, code, container, workMode, option, useDefaultOpt);
    this._setupDefaultValues();

    this._foldPath = WisUtil.scriptPath('logicGraph');  // 组件绝对路径
    this._self = getuuid();

    this.state = {
      scale: 1,
      data: null,
      formatData: null,
      sublayerList: null
    }

    this._allObjects = {
      links: [],
      nodes: [],
      resourceNode: []
    }

    this._draw();

  }

  _initProperty() {
    super._initProperty();
    const options = {
      basic: {
        className: 'LogicGraph',
        frame: [0, 0, 1920, 1080],
      },
      graph: {
        id: "",
        serverUrl: "http://**************:10017",
        isLoadData: false,
        zoomScale: 0.1,
        themeHeight: 0,
        dragging: true,
        scrollWheelZoom: true        
      }
    };

    const optionDic = [
      {
        name: 'graph',
        displayName: '基本配置',
        description: '基本配置',
        children: [
          {
            name: 'id',
            displayName: 'id',
            description: 'id',
            type: OptionType.string,
            show: true,
            editable: true
          },
          {
            name: 'serverUrl',
            displayName: '服务地址',
            description: '服务地址',
            type: OptionType.string,
            show: true,
            editable: true
          },
          {
            name: 'isLoadData',
            displayName: '加载数据',
            description: '加载数据',
            type: OptionType.boolean,
            show: true,
            editable: true
          },
          {
            name: 'zoomScale',
            displayName: '层级比例',
            description: '层级比例',
            type: OptionType.double,
            show: true,
            editable: true
          },
          {
            name: 'themeHeight',
            displayName: '标题高度',
            description: '标题高度',
            type: OptionType.int,
            show: true,
            editable: true
          },
          {
            name: 'dragging',
            displayName: '鼠标拖拽',
            description: '鼠标拖拽',
            type: OptionType.boolean,
            show: true,
            editable: true
          },
          {
            name: 'scrollWheelZoom',
            displayName: '鼠标缩放',
            description: '鼠标缩放',
            type: OptionType.boolean,
            show: true,
            editable: true
          }
        ]
      }

    ];
    this._addProperty(options, optionDic);
  }

  _initEvents() {
    super._initEvents();

    const funcs = [
      {
        name: 'moveLeft',
        displayName: '左移',
        params: []
      }, {
        name: 'moveRight',
        displayName: '右移',
        params: []
      }, {
        name: 'moveTop',
        displayName: '上移',
        params: []
      }, {
        name: 'moveBottom',
        displayName: '下移',
        params: []
      }, {
        name: 'zoomIn',
        displayName: '放大',
        params: [{
          name: 'minX',
          displayName: 'X起点百分比',
          type: OptionType.double
        },{
          name: 'maxX',
          displayName: 'X终点百分比',
          type: OptionType.double
        },{
          name: 'minY',
          displayName: 'Y起点百分比',
          type: OptionType.double
        },{
          name: 'maxY',
          displayName: 'Y终点百分比',
          type: OptionType.double
        }]
      }, {
        name: 'zoomInCenter',
        displayName: '中心放大',
        params: [{
          name: 'scale',
          displayName: '比例',
          type: OptionType.double
        }]
      }, {
        name: 'zoomOutCenter',
        displayName: '中心缩小',
        params: [{
          name: 'scale',
          displayName: '比例',
          type: OptionType.double
        }]
      }, {
        name: 'zoomOut',
        displayName: '缩小',
        params: []
      }, {
        name: 'showAllLayers',
        displayName: '打开所有图层',
        params: []
      }, {
        name: 'hideAllLayers',
        displayName: '关闭所有图层',
        params: []
      }, {
        name: 'showLayer',
        displayName: '打开图层',
        params: [{
          name: 'layerId',
          displayName: '图层名',
          type: OptionType.string
        }]
      }, {
        name: 'hideLayer',
        displayName: '关闭图层',
        params: [{
          name: 'layerId',
          displayName: '图层名',
          type: OptionType.string
        }]
      }, {
        name: 'objectFilter',
        displayName: '过滤',
        params: [{
          name: 'name',
          displayName: '名字',
          type: OptionType.string
        }]
      }, {
        name: 'reset',
        displayName: '复位',
        params: []
      }, {
        name: 'objectFlicker',
        displayName: '对象闪烁',
        params: [{
          name: 'name',
          displayName: 'id',
          type: OptionType.string
        }, {
          name: 'name',
          displayName: '闪烁次数',
          type: OptionType.int
        }]
      }
    ];    

    this.invokeFunc = this.invokeFunc.concat(funcs);

  }

  async _draw() {
    super._draw();

    this.svg = this.mainDIV
      .append('svg')
      .attr('xmlns', "http://www.w3.org/2000/svg")
      .attr('style', "position: absolute; top: 0; left: 0;")
      .attr("width", '100%')
      .attr("height", '100%')
      .attr("viewBox", `0 0 ${this.property.basic.frame[2]} ${this.property.basic.frame[3]}`);

    // 渲染背景图
    const data = await this._getBackground();
    if (data[0].background && data[0].background.length > 0) {
      this._drawBackground(data[0].background);
    }

    // 获取绘制数据
    if (this.property.graph.isLoadData) {
      this.state.data = await this._getNodeLinkListByMap();
      this.state.sublayerList = await this._getExistSublayerList();

      this._layerAdaptionScale();

      // 绘制数据
      if (this.state.data) {
        this._drawObject();
      }
    }

    this._bindEvents();

    this._drawEnd();

    this._subscribeData();

  }

  _drawBackground(data) {
    const pics = data.split(','), num = pics.length,
      width = this.property.basic.frame[2],
      height = this.property.basic.frame[3];

    const g = this.svg.append('g');
    for (let i in pics) {
      g.append('image')
        .attr('xlink:href', `/logicEditor${pics[i]}`)
        .attr('x', width / num * parseInt(i))
        .attr('y', 0)
        .attr('width', width / num)
        .attr('height', height)
    }
  }

  _layerAdaptionScale() {
    this._layerScale = [];  // 放大比例与层级关系

    for(let each of this.state.sublayerList) {
      if(each.scale != undefined) {  // 数据中存在scale属性
        if(!this._layerScale[each.scale]) {
          this._layerScale[each.scale] = [];
        }
  
        this._layerScale[each.scale].push(each.sublayerId);
        
      }
      
    }
  }

  _drawObject() {
    this.linksLayer = this.svg.append('g');

    this.nodesLayer = this.svg.append('g');

    // 对数据按照图层进行整理
    this.state.formatData = this._dataFormat();
    for (let i in this.state.formatData) {
      for (let each of this.state.formatData[i]) {    
        if(!each.sublayerList) {
          each.sublayerList = [{}];
        }

        if (i == 'links') {
          const d3s = this.linksLayer.append('g').attr('opacity', 1).attr('data-linkId', each.linkId);          

          // 根据缩放比例和自图层判断是否显示
          const displayLayers = this._getDisplayLayer();
          if(displayLayers) {
            if($.inArray(each.sublayerList[0].sublayerId, displayLayers) >= 0) {
              d3s.attr('opacity', 1);
            }
          }          

          // 如果所属自图层配置默认隐藏
          for(let each2 of this.state.sublayerList) {
            if(each2.sublayerId == each.sublayerList[0].sublayerId && !each2.isVisible) {
              d3s.attr('opacity', 0);
            }
          }

          if(each.domId.length > 0) {
            d3s.attr('id', `${this.property.graph.id}-g-${each.domId}`);            
          }

          if(each.sublayerList[0].sublayerId) {
            d3s.attr('class', each.sublayerList[0].sublayerId);
          } else {
            d3s.attr('class', 'others');
          }

          this._allObjects.links.push({
            domId: each.domId,
            linkId: each.linkId,
            object: new LogicCustomComponent(d3s, each)
          })

          if(each.mouseEvent && each.mouseEvent.length > 0) {
            for(let evt of each.mouseEvent) {
              d3s.on(evt.eventType, ()=>{        
                this._sendCommand('popupResource', [d3.event.offsetX, d3.event.offsetY, evt.resourceCode, evt.resourceId, evt.width, evt.height, evt.groupName, evt.name, evt.value]);                 
                // this.popupResource(d3.event.offsetX, d3.event.offsetY, evt.resourceCode, evt.resourceId, evt.width, evt.height, evt.groupName, evt.name, evt.value);
             
              })
            }
          }
          
        } else if (i == 'nodes') {
          const d3s = this.nodesLayer.append('g').attr('opacity',1); 

          // 根据缩放比例和自图层判断是否显示
          const displayLayers = this._getDisplayLayer();
          if(displayLayers) {
            if($.inArray(each.sublayerList[0].sublayerId, displayLayers) >= 0) {
              d3s.attr('opacity', 1);
            }
          }  

          // 如果所属自图层配置默认隐藏
          for(let each2 of this.state.sublayerList) {
            if(each2.sublayerId == each.sublayerList[0].sublayerId && !each2.isVisible) {
              d3s.attr('opacity', 0);
            }
          }

          if(each.domId.length > 0) {
            d3s.attr('id', `${this.property.graph.id}-g-${each.domId}`);
          }

          if(each.sublayerList[0].sublayerId) {
            d3s.attr('class', each.sublayerList[0].sublayerId);
          } else {
            d3s.attr('class', 'others');
          }

          if(each.nodeType.includes('-popup')) {
            this._allObjects.resourceNode.push(each);
          } else {
            switch (each.nodeType) {
              case 'rect': {
                d3s.attr('transform', `translate(${each.nodePosition})`)
  
                this._allObjects.nodes.push({
                  domId: each.domId,
                  nodeId: each.nodeId,
                  object: this._drawRect(d3s, each)
                })
                break;
              }
              case 'circle': {
                const position = each.nodePosition.split(','), size = each.nodeSize.split('*');
                d3s.attr('transform', `translate(${parseFloat(position[0]) + parseFloat(size[0] / 2)}, ${parseFloat(position[1]) + parseFloat(size[1] / 2)})`)
  
                this._allObjects.nodes.push({
                  domId: each.domId,
                  nodeId: each.nodeId,
                  object: this._drawCircle(d3s, each)
                })
                break;
              }
              default: {
                d3s.attr('transform', `translate(${each.nodePosition})`);
  
                this._allObjects.nodes.push({
                  domId: each.domId,
                  nodeId: each.nodeId,
                  object: new LogicCustomComponent(d3s, each)
                })
                break;
              }
            }  
          }           
          
          if(each.mouseEvent && each.mouseEvent.length > 0) {
            for(let evt of each.mouseEvent) {
              d3s.on(evt.eventType, ()=>{
                this._sendCommand('popupResource', [d3.event.offsetX, d3.event.offsetY, evt.resourceCode, evt.resourceId, evt.width, evt.height, evt.groupName, evt.name, evt.value]);
                //this.popupResource(d3.event.offsetX, d3.event.offsetY, evt.resourceCode, evt.resourceId, evt.width, evt.height, evt.groupName, evt.name, evt.value);
            
              })
            }
          }
          
        }        
      }
    }
  }

  async _drawEnd() { 
    this._mapEventData = await this._getMapEvent();

    this._filterEvent = [], this._interactionEvent = [];
    for(let each of this._mapEventData) {
      if(each.eventType == 'filter') {
        this._filterEvent.push(each);
      } else if(each.eventType == 'interaction') {
        this._interactionEvent.push(each);
      }
    }
  }

  // 按图层整理数据
  _dataFormat() {
    const result = {
      links: [],
      nodes: []
    };

    const listOrder = {};
    for (let each of this.state.sublayerList) {
      listOrder[each.sublayerId] = each.listOrder;
    }

    const tmpLinks = [], tmpNodes = [];
    for (let i in this.state.data) {
      const each = this.state.data[i];

      for (let each2 of each) {
        if(each2.bindData && each2.bindData.nodeType && each2.bindData.nodeType == 'custom') {
          continue;
        }

        let order = 1;
        if (each2.sublayerList) {
          order = listOrder[each2.sublayerList[0].sublayerId];
        }

        each2.order = order;

        if (i == 'links') {
          if (!tmpLinks[order]) {
            tmpLinks[order] = [];
          }

          tmpLinks[order].push(each2);

        } else if (i == 'nodes') {
          if (!tmpNodes[order]) {
            tmpNodes[order] = [];
          }

          tmpNodes[order].push(each2);
        }
      }
    }

    for (let i in tmpLinks) {
      if (tmpLinks[i]) {
        result.links = result.links.concat(tmpLinks[i]);

      }
    }

    for (let i in tmpNodes) {
      if (tmpNodes[i]) {
        result.nodes = result.nodes.concat(tmpNodes[i]);
      }
    }

    return result;
  }


  _drawRect(d3s, data) {
    const size = data.nodeSize.split('*'), styles = JSON.parse(data.nodeStyles);

    const rect = d3s
      .append('rect')
      .attr('width', size[0])
      .attr('height', size[1])
      .attr('order', data.order);

    eval(`rect${(() => {
      let str = "";
      for (let i in styles) {
        str += `.attr('${i}', '${styles[i]}')`
      }
      return str;
    })()}`)

    return rect;

  }

  _drawCircle(d3s, data) {
    const size = data.nodeSize.split('*'), styles = JSON.parse(data.nodeStyles);

    const circle = d3s
      .append('ellipse')
      .attr('rx', size[0] / 2)
      .attr('ry', size[1] / 2)
      .attr('order', data.order);

    try {
      eval(`circle${(() => {
        let str = "";
        for (let i in styles) {
          str += `.attr('${i}', "${styles[i]}")`
        }
        return str;
      })()}`)    
    } catch (e) { }

    return circle;
  }

  _bindEvents() {
    this.svg
      .on('mousedown', e => {
        if(!this.property.graph.dragging) {
          return;
        }

        const codes = [];  
        for(let i in this.state.data) {
          for(let each of this.state.data[i]) {
            if(each.mouseEvent) {
              for(let each2 of each.mouseEvent) {
                codes.push(each2.resourceCode);
              }
            }
            
          }
        } 

        // this._sendCommand("batchCloseResourceForce", [codes, 0], { isWindow: true });

        let pos0 = this.svg.attr('viewBox').split(' '),
          x0 = parseFloat(pos0[0]),
          y0 = parseFloat(pos0[1]),
          offsetX0 = d3.event.offsetX, offsetY0 = d3.event.offsetY;

        this.svg
          .on('mousemove', e => {
            const basicWidth = this.property.basic.frame[2],            
              basicHeight = this.property.basic.frame[3],
              width = basicWidth / this.state.scale,
              height = basicHeight / this.state.scale;

            this.svg.attr("viewBox", `${offsetX0 - d3.event.offsetX + x0} ${offsetY0 - d3.event.offsetY + y0} ${width} ${height}`);
            this._sendCommand('setViewBox', [`${offsetX0 - d3.event.offsetX + x0} ${offsetY0 - d3.event.offsetY + y0} ${width} ${height}`, this._self]);
          })
          .on('mouseup', () => {
            this.svg.on('mousemove', null).on('mouseup', null);
          })

      }).on('wheel', () => {
        if(!this.property.graph.scrollWheelZoom) {
          return;
        }

        const codes = [];  
        for(let i in this.state.data) {
          for(let each of this.state.data[i]) {
            if(each.mouseEvent) {
              for(let each2 of each.mouseEvent) {
                codes.push(each2.resourceCode);
              }
            }
            
          }
        }     

        // this._sendCommand("batchCloseResourceForce", [codes, 0], { isWindow: true });
        
        if (d3.event.wheelDelta > 0) {
          if (this.state.scale >= 10) {
            return;
          }

          if (this.workMode == 99) {
            this.zoomIn();
            return;
          }

          const vb = this._zoomIn();

          this._sendCommand('setViewBox', [vb]);

        } else if (d3.event.wheelDelta < 0) {

          if (this.state.scale <= 0.5) {
            return;
          }

          if (this.workMode == 99) {
            this.zoomOut();
            return;
          }

          const vb = this._zoomOut();

          this._sendCommand('setViewBox', [vb]);
        }


      })

  }

  async _subscribeData() {
    this._groupData = await this._getMapGroupData();

    for(let each of this._groupData) {      
      if(each.bindData) {
        window.wiscomWebSocket.subscribeDataGroupByInterface(each.groupId, each.bindData.map(v=>v.detailId), 10*1000, (data)=>{this.updateData(data)});
      }
    }
    
  }

  updateData(data) {
    // data = data[this.property.graph.id];
    console.log(this.property.graph.id)
    const groupId = Object.keys(data)[0];

    if(!data[groupId]) {
      return;
    }
    
    for(let each of this._groupData) {
      if(each.groupId == groupId) {
        for(let each2 of each.bindData) {
          const detailId = each2.detailId, key = each2.key;
          
          if(data[groupId][detailId]) {
            for(let each3 of data[groupId][detailId]) {
              const domId = each3[key];

              if(!domId) {
                continue;
              }

              for(let i in this._allObjects) {
                for(let j in this._allObjects[i]) {
                  if(this._allObjects[i][j].domId.length == 0 || domId.length == 0) {
                    continue;
                  }

                  if(this._allObjects[i][j].domId == domId) {  // 查找到图元对象
                    let topoObjDataBindInfos;
                    for(let k in this.state.data) {  // 找到渲染映射关系
                      for(let object of this.state.data[k]) {
                        if(object.domId == domId) {
                          // console.warn(node.topoObjDataBindInfos)
                          topoObjDataBindInfos = object.topoObjDataBindInfos;
                          break;
                        }
                      }
                      
                    }

                    for(let i in topoObjDataBindInfos) { // 将值追加到渲染映射关系
                      topoObjDataBindInfos[i].value = each3[topoObjDataBindInfos[i].column];          
                    }

                    try {
                      this._allObjects[i][j].object.update && this._allObjects[i][j].object.update(topoObjDataBindInfos);
                    } catch (e){

                    }                    
                  }
                }                
              }  
                     
            }
          }
        }
      }
    }

  }

  /* 资源弹窗*/
  popupResource(x, y, resourceCode, resourceId, width=1000, height=1000, groupName, name, value) {
    d3.select(`.popup-foreignObject-resource`).remove();

    const [offsetX, offsetY] = this.svg.attr("viewBox").split(' ');

    if(x + width >= this.property.basic.frame[2]) {
      x -= width;
    }

    if(y + height >= this.property.basic.frame[3]) {
      y -= height;
    }

    // 资源容器外键
    const fObject = this.svg
      .append('foreignObject')
      .attr('id', `foreignObject-${resourceId}`)
      .attr('class', 'popup-foreignObject-resource')
      .attr("requiredExtensions", "http://www.w3.org/1999/xhtml")
      .attr('x', x/this.state.scale + parseInt(offsetX))
      .attr('y', y/this.state.scale + parseInt(offsetY))
      .attr('width', width)
      .attr('height', height)
      .append('xhtml:div')
      .attr('width', '100%')
      .attr('height', '100%')
      .attr('xmlns', "http://www.w3.org/1999/xhtml")
      .attr('style', 'width: 100%; height: 100%;');

    fObject.on('contextmenu', ()=>{
      d3.select(`#foreignObject-${resourceId}`).remove();

      d3.event.preventDefault();

    })      

    try {
       WisUtil.publicResourceCreate('/action/config', resourceCode, resourceId, `foreignObject-${resourceId}`, 1, groupName, name, value);
    } catch(e) {
      console.error(e)
    } 

  }

  focus(id) {
    const d3elem = d3.select(`#${id}>*`);

    switch (d3.select(`#${id}>*`).node().tagName) {
      case 'path': {
        const width = d3elem.attr('stroke-width');
        if (width) {
          d3elem.attr('stroke-width', parseFloat(width.split('px')[0]) * 3);
        }

        break;
      }
      case 'ellipse': {
        const size = [parseInt(d3elem.attr('rx')), parseInt(d3elem.attr('ry'))];

        d3elem
          .attr('rx', size[0] * 1.5)
          .attr('ry', size[1] * 1.5);
          
        break;
      }
    }

  }

  unfocus(id) {
    const d3elem = d3.select(`#${id}>*`);

    switch (d3.select(`#${id}>*`).node().tagName) {
      case 'path': {
        const width = d3elem.attr('stroke-width');
        if (width) {
          d3elem.attr('stroke-width', parseFloat(width.split('px')[0]) / 3);
        }
        break;
      }
      case 'ellipse': {
        const size = [parseInt(d3elem.attr('rx')), parseInt(d3elem.attr('ry'))];

        d3elem
          .attr('rx', size[0] / 1.5)
          .attr('ry', size[1] / 1.5);

        break;
      }
    }

  }

  reset() {
    this.svg.attr("viewBox", `0 0 ${this.property.basic.frame[2]} ${this.property.basic.frame[3]}`);
  }

  setViewBox(viewBox, self) {
    if (self && self == this._self) return;

    this.svg.attr("viewBox", viewBox);

    this.state.scale = this.property.basic.frame[2] / parseFloat(viewBox.split(' ')[2]);

    // 
    if(this.state.scale >= 1) {
      // 根据层级与图层关系筛选出需要显示的图层
      const displayLayers = this._getDisplayLayer();
      if(displayLayers) {
        this.hideAllLayers();

        d3.selectAll(`.others`).attr('opacity', 1);  // 其他图层默认显示
        
        for(let each of displayLayers) {
          d3.selectAll(`.${each}`).attr('opacity', 1);
        }
      } 
    }     

  }

  /* 根据图层判断子图层是否显示*/
  _getDisplayLayer() {
    if(this._layerScale.length > 0) {  // 如果配置了层级与图层的关系
      let subLayerIds = [];
      for(let i in this._layerScale) {
        if(parseFloat((1+((i-1) * this.property.graph.zoomScale)).toFixed(1)) <= parseFloat(this.state.scale.toFixed(1))) {
          subLayerIds = [...subLayerIds, ...this._layerScale[i]];
        }
      }

      if(this._layerScale[0]) {
        subLayerIds = [...subLayerIds, ...this._layerScale[0]];
      } 

      return subLayerIds;
    } 
    
    return false;
    
  }

  _zoomIn(scale) {
    const step = scale ? parseFloat(scale) : 0.1;

    this.state.scale = this.state.scale + step >= 10 ? 10 : this.state.scale + step;

    // 获取当前的位置和宽高
    const pos0 = this.svg.attr('viewBox').split(' '),
      x0 = parseFloat(pos0[0]),
      y0 = parseFloat(pos0[1]),
      width0 = parseFloat(pos0[2]),
      height0 = parseFloat(pos0[3]),
      // 获取配置的宽高
      basicWidth = this.property.basic.frame[2],
      basicHeight = this.property.basic.frame[3],
      // 计算缩小后的宽高
      width = basicWidth / this.state.scale,
      height = basicHeight / this.state.scale,
      // 计算以中心点为原点放大后的位置
      x = x0 + (width0 - width) / 2,
      y = y0 + (height0 - height) / 2;

    return `${x} ${y} ${width} ${height}`;
  }

  zoomIn(minX, maxX, minY, maxY) {
    let vb;
    if(minX && maxX && minY && maxY) {
      const basicWidth = this.property.basic.frame[2], basicHeight = this.property.basic.frame[3];

      minX = basicWidth * parseFloat(minX),
      maxX = basicWidth * parseFloat(maxX),
      minY = basicHeight * parseFloat(minY),
      maxY = basicHeight * parseFloat(maxY);
      
      vb = `${minX} ${minY} ${maxX - minX} ${maxY - minY}`;
    } else {      
      vb = this._zoomIn();     
    }    

    this.svg
      .transition()
      .duration(1000)
      .attr("viewBox", vb);
    
  }

  zoomInCenter(scale) {
    const vb = this._zoomIn(parseFloat(scale));  

    this.svg
      .transition()
      .duration(1000)
      .attr("viewBox", vb);
  }

  _zoomOut(scale) {
    const step = scale ? parseFloat(scale) : 0.1;

    this.state.scale = this.state.scale - step <= 0.5 ? 0.5 : this.state.scale - step;

    // 获取当前的位置和宽高
    const pos0 = this.svg.attr('viewBox').split(' '),
      x0 = parseFloat(pos0[0]),
      y0 = parseFloat(pos0[1]),
      width0 = parseFloat(pos0[2]),
      height0 = parseFloat(pos0[3]),
      // 获取配置的宽高
      basicWidth = this.property.basic.frame[2],
      basicHeight = this.property.basic.frame[3],
      // 计算缩小后的宽高
      width = basicWidth / this.state.scale,
      height = basicHeight / this.state.scale,
      // 计算以中心点为原点缩小后的位置
      x = x0 + (width0 - width) / 2,
      y = y0 + (height0 - height) / 2;

    return `${x} ${y} ${width} ${height}`;
  }
  
  zoomOut() {
    const vb = this._zoomOut();

    this.svg
      .transition()
      .duration(1000)
      .attr("viewBox", vb);
  }

  zoomOutCenter(scale) {
    const vb = this._zoomOut(parseFloat(scale));  

    this.svg
      .transition()
      .duration(1000)
      .attr("viewBox", vb);
  }


  moveTop() {
    const pos0 = this.svg.attr('viewBox').split(' '),
      x0 = parseFloat(pos0[0]),
      y0 = parseFloat(pos0[1]),
      width0 = parseFloat(pos0[2]),
      height0 = parseFloat(pos0[3]);

    this.svg.attr("viewBox", `${x0} ${y0 + height0 / 10} ${width0} ${height0}`);
  }

  moveRight() {
    const pos0 = this.svg.attr('viewBox').split(' '),
      x0 = parseFloat(pos0[0]),
      y0 = parseFloat(pos0[1]),
      width0 = parseFloat(pos0[2]),
      height0 = parseFloat(pos0[3]);

    this.svg.attr("viewBox", `${x0 - width0 / 10} ${y0} ${width0} ${height0}`);
  }

  moveBottom() {
    const pos0 = this.svg.attr('viewBox').split(' '),
      x0 = parseFloat(pos0[0]),
      y0 = parseFloat(pos0[1]),
      width0 = parseFloat(pos0[2]),
      height0 = parseFloat(pos0[3]);

    this.svg.attr("viewBox", `${x0} ${y0 - height0 / 10} ${width0} ${height0}`);
  }

  moveLeft() {
    const pos0 = this.svg.attr('viewBox').split(' '),
      x0 = parseFloat(pos0[0]),
      y0 = parseFloat(pos0[1]),
      width0 = parseFloat(pos0[2]),
      height0 = parseFloat(pos0[3]);

    this.svg.attr("viewBox", `${x0 + width0 / 10} ${y0} ${width0} ${height0}`);
  }

  showLayer(layer) {
    let layerId = null;
    for(let each of this.state.sublayerList) {
      if(each.sublayerName == layer) {
        layerId = each.sublayerId
        break;
      }
    }

    if(layerId) {
      d3.selectAll(`.${layerId}`).attr('opacity', 1);
    }

    // 找到图层下的资源图元并打开
    for(let each of this._allObjects.resourceNode) {
      if(each.sublayerList) {
        for(let each2 of each.sublayerList) { 
          if(each2.sublayerId == layerId && each.mouseEvent) {
            for(let each3 of each.mouseEvent) {            
              if(each3.eventType == "command") {
                const pos = each.nodePosition.split(',').map(v=>parseFloat(v)), size = each.nodeSize.split('*').map(v=>parseFloat(v));

                this._sendCommand('popupResource', [pos[0], pos[1], each3.resourceCode, each3.resourceId, size[0], size[1]]);
  
                // this.popupResource(pos[0], pos[1], each3.resourceCode, each3.resourceId, size[0], size[1]);
                break;
              }
            }
  
            
            break;
          }
        }
      }      
      
    }

  }

  hideLayer(layer) {
    let layerId = null;
    for(let each of this.state.sublayerList) {
      if(each.sublayerName == layer) {
        layerId = each.sublayerId
        break;
      }
    }

    if(layerId) {
      d3.selectAll(`.${layerId}`).attr('opacity', 0);
    }

    // 找到该图层下的资源图元并删除
    for(let each of this._allObjects.resourceNode) {
      if(each.sublayerList) {
        for(let each2 of each.sublayerList) { 
          if(each2.sublayerId == layerId) {
            for(let each3 of each.mouseEvent) {            
              if(each3.eventType == "command") {
                d3.select(`#foreignObject-${each3.resourceId}`).remove();
                break;
              }
            }
  
            
            break;
          }
        }
      }     
      
    }

    
  }

  showAllLayers() {
    this.nodesLayer.selectAll('g').attr('opacity', 1);
    this.linksLayer.selectAll('g').attr('opacity', 1);
  }

  hideAllLayers() {
    this.nodesLayer.selectAll('g').attr('opacity', 0);
    this.linksLayer.selectAll('g').attr('opacity', 0);
  }

  opacityAllLayers() {
    this.nodesLayer.selectAll('g').attr('opacity', 0.1);
    this.linksLayer.selectAll('g').attr('opacity', 0.1);
  }

  async objectFilter(name) {    
    const triggers = [];
    for(let each of this._filterEvent) {
      if(each.name == name) {
        for(let each2 of each.trigger) {
          triggers.push(each2);
        }
      }
         
    }
    
    const data = await this._setSendList(triggers.map(v=>v.detailId));

    if(data) {
      const domIds = [];
      for(let i in data) {
        for(let each of data[i]) {
          for(let j in triggers) {
          
            const counditions = triggers[j].conditions;
            
            let count = 0;
            for(let p = 0; p < counditions.length; p++) {
              for(let each2 of counditions) {
                if(each[each2.key] == each2.value) {
                  count++;
                  break;
                }
              }
            }
    
            if(count == counditions.length) {
              domIds.push(each[triggers[j].domId]);
            }
          }
        }
  
      }

      if(domIds.length > 0) {
        const ids = this._updateDomIds(name, domIds);

        this._selfAdaption(ids);

        this.opacityAllLayers();        

        setTimeout(()=>{
          for(let each of ids) {
            d3.select(`#${this.property.graph.id}-g-${each}`).attr('opacity', 1);
          }
  
          let i = 0;
          const inter = setInterval(()=>{
            if(i == 8) {
              clearInterval(inter);
            } else if(i%2 == 0) {
              for(let each of ids) {
                d3.select(`#${this.property.graph.id}-g-${each}`).attr('opacity', 0);
              }
            } else {
              for(let each of ids) {
                d3.select(`#${this.property.graph.id}-g-${each}`).attr('opacity', 1);
              }
            }
            i++;
            
          }, 500)
          
        }, 2000)       
        
      }
    }  
  }

  _updateDomIds(name, domIds) {
    return domIds;
  }

  _selfAdaption(domIds) {
    this.linksLayer.attr('opacity', 0)
    this.nodesLayer.attr('opacity', 0)

    let minX = null, minY = null, maxX = null, maxY = null;
    for(let i in this.state.data) {
      for(let each of this.state.data[i]) {
        if($.inArray(each.domId, domIds) >=0) {
          if(i == 'nodes') {
            const pos = each.nodePosition.split(',').map(v=>parseFloat(v));
            if(minX == null || pos[0] < minX) {
              minX = pos[0];
            }

            if(maxX == null || pos[0] > maxX) {
              maxX = pos[0];
            }

            if(minY == null || pos[1] < minY) {
              minY = pos[1];
            }

            if(maxY == null || pos[1] > maxY) {
              maxY = pos[1];
            }
  
          } else {          
            const path = each.linkPath.split('M')[1].split('L').map(v=>[parseFloat(v.split(' ')[0]), parseFloat(v.split(' ')[1])]);

            for(let pos of path) {
              if(minX == null || pos[0] < minX) {
                minX = pos[0];
              }
  
              if(maxX == null || pos[0] > maxX) {
                maxX = pos[0];
              }
  
              if(minY == null || pos[1] < minY) {
                minY = pos[1];
              }
  
              if(maxY == null || pos[1] > maxY) {
                maxY = pos[1];
              }
            }
  
          }
        }     

      }
    }

    this.state.scale = this.property.basic.frame[2] / (maxX - minX);
    // this.state.scale = this.property.basic.frame[3] / (maxY - minY);

    this.svg
      .transition()
      .duration(1000)
      .attr("viewBox", `${minX} ${minY - this.property.graph.themeHeight} ${maxX - minX} ${maxY - minY + this.property.graph.themeHeight}`);

    setTimeout(() => {
      this.linksLayer.attr('opacity', 1)
      this.nodesLayer.attr('opacity', 1)
    }, 1500);
    
  }

  objectFlicker(id, num) {
    const d3o = d3.select(`#${this.property.graph.id}-g-${id}`);

    let i = 0;
    d3o.attr('opacity', 0);
    const inter = setInterval(()=>{
      if(i == num*2) {
        clearInterval(inter);
      }

      if(i%2 == 0) {
        d3o.attr('opacity', 1);
      } else {
        d3o.attr('opacity', 0);
      }
      i++;
    }, 500)
  }

  _getMapGroupData() {
    return this._ajaxGet(`${this.property.graph.serverUrl}/topo/topoEdit/getMapGroupData?mapld=${this.property.graph.id}`);
  }

  _getBackground() {
    return this._ajaxGet(`${this.property.graph.serverUrl}/topo/topoEdit/getMapById?mapId=${this.property.graph.id}`);
  }

  _getNodeLinkListByMap() {
    return this._ajaxGet(`${this.property.graph.serverUrl}/topo/topoEdit/getNodeLinkListByMapId?mapId=${this.property.graph.id}`);
  }

  _getExistSublayerList() {
    return this._ajaxGet(`${this.property.graph.serverUrl}/topo/topoEdit/getExistSublayerList?mapId=${this.property.graph.id}`);
  }

  _getMapEvent() {
    return this._ajaxGet(`${this.property.graph.serverUrl}/topo/topoEdit/getMapEvent?mapId=${this.property.graph.id}&eventType=filter`);
  }

  _setSendList(params) {
    return this._ajaxPost('/httpService/httpData/sendList', params);
  } 

  _ajaxGet(url) {
    return new Promise(resolve => {
      $.get({
        url: url,
        dataType: 'json',
        success: (data) => {
          if (data.code == "0000" || data.code == 200) {
            resolve(data.data);
          }
        },
        error: (e) => {
          resolve(false);
        }
      })
    })
  
  }

  _ajaxPost(url, params) {
    return new Promise(resolve=>{
      $.post({
        url: url,
        headers: {
          'Content-Type': 'application/json'
        },
        dataType: 'json',
        data: JSON.stringify(params),
        success: (rep) => {
          if(rep.code == 1 || rep.code == 200) {
            resolve(rep.data)
          } else {
            resolve(false);
          }
          
        },
        error: (e) => {
          resolve(false);
        }
      })
    })
    
  }

}

function getuuid() {
  const s = [];
  const hexDigits = "0123456789abcdef";
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  // bits 12-15 of the time_hi_and_version field to 0010
  s[14] = "4";
  // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
  s[8] = s[13] = s[18] = s[23] = "-";

  const uuid = s.join("");
  return uuid;
}
