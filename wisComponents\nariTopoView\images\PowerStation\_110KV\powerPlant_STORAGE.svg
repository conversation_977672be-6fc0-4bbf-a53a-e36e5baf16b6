<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 101 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1,0,0,1,-39888,-3844)">
        <g id="储能-P" transform="matrix(0.804574,0,0,0.434783,37408.1,2254.75)">
            <g>
                <g transform="matrix(0.287485,0,0,0.531997,2633.29,2633.09)">
                    <path d="M1997.02,1964.64C1997.02,1940.78 1977.65,1921.41 1953.79,1921.41L1607.92,1921.41C1584.06,1921.41 1564.69,1940.78 1564.69,1964.64L1564.69,2310.51C1564.69,2334.37 1584.06,2353.74 1607.92,2353.74L1953.79,2353.74C1977.65,2353.74 1997.02,2334.37 1997.02,2310.51L1997.02,1964.64Z" style="fill:url(#_Linear1);"/>
                </g>
                <g transform="matrix(1.24289,-0,-0,2.3,3082.21,3655.28)">
                    <use xlink:href="#_Image2" x="3" y="2" width="96px" height="96px"/>
                </g>
                <g transform="matrix(2.0685,-5.66994e-32,-3.37037e-31,3.82781,-65420.1,-13816.1)">
                    <g transform="matrix(14,0,0,14,33166.2,4617.42)">
                    </g>
                    <text x="33127.5px" y="4617.42px" style="font-family:'MicrosoftYaHeiUI-Bold', 'Microsoft YaHei UI', sans-serif;font-weight:700;font-size:14px;fill:white;">3<tspan x="33137.5px 33147.6px 33157.6px " y="4617.42px 4617.42px 4617.42px ">254</tspan></text>
                </g>
                <g transform="matrix(1.24289,-0,-0,2.3,3082.21,3655.28)">
                    <use xlink:href="#_Image3" x="27" y="16" width="47px" height="37px"/>
                </g>
            </g>
            <g transform="matrix(0.681487,0,0,1,-18271.7,-67.2092)">
                <clipPath id="_clip4">
                    <path d="M31463.3,3731.74L31398.5,3871.65C31398.5,3871.65 31374.5,3871.65 31358,3871.65C31354,3871.65 31350.2,3869.64 31347.3,3866.08C31344.5,3862.51 31342.9,3857.68 31342.9,3852.64C31342.9,3826.24 31342.9,3778.71 31342.9,3751.78C31342.9,3746.47 31344.6,3741.37 31347.6,3737.61C31350.5,3733.85 31354.6,3731.74 31358.8,3731.74C31391.5,3731.74 31463.3,3731.74 31463.3,3731.74Z"/>
                </clipPath>
                <g clip-path="url(#_clip4)">
                    <g transform="matrix(1.8238,-0,-0,2.3,31334.3,3722.49)">
                        <use xlink:href="#_Image5" x="4.75" y="4.023" width="66px" height="61px"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(717.345,0,0,-724.013,1279.68,2288.8)"><stop offset="0" style="stop-color:rgb(178,178,178);stop-opacity:1"/><stop offset="0.47" style="stop-color:rgb(241,241,241);stop-opacity:1"/><stop offset="0.53" style="stop-color:rgb(241,241,241);stop-opacity:1"/><stop offset="0.87" style="stop-color:rgb(99,99,99);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(178,178,178);stop-opacity:1"/></linearGradient>
        <image id="_Image2" width="96px" height="96px" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAYAAADimHc4AAAACXBIWXMAAA7EAAAOxAGVKw4bAAAIsElEQVR4nO2dX4wVVx3HP/Pn/lkK7AKL7sLiLt1YiGhLs8QoGGlpqTQNcY02lGjMSP88qDSNxhgfNGlMmvShaWzTlxL1vtQ/qaXW1BaxrdGIJg22WbSN1GJZdhe2UBKLwO7ee2fGhzln5py5d5daA8fcOZ8Tdu/MPefM8P2efzPh/HBYgFqttgoYqc/Vt1w4P7NsobyWDM/36ku7F48BfwmC4NWF8jrtTu57fN8DLx86cs/kiekVE8dPcWrqNGEzvCw326l0L1vCmsF+1gz1R+s2XH1gaHhgdxAE5/L5NANqtVrPm0fHn3r6J7/ZdmFihhGuYygeYJABVtLb3i1LCzPMcoIpjjPJUedN3vCPseOzWxs3fuYTD955153fVfOmmtZqtd6Xnv/TxK/3/6768fB67ohHWUTXJS8WExMRa8ediqM0QRdHO16Iw4zxhLOf3uEevnjXaO1b3/7GV7I6Bd+89ztjP37syWvvjr7EJq5rW1GTJk1CYiJCQmIlAR0svY4UzcUVxy4eLj4ePqW2xvyb8zzq/IhFG6vcfe+ukSAIXknrevSRxx55+Ps/3Lvp7EZuj3e2FJ6jToOGkF+aEBMRaQZIOrUXqMLKz66SPDxK+Pj4lCjj42nlT/MO9zsPMfrl7Wc23zAyEARB3a/Vaouf+flv91bOVhmNb9UKNAmZE0ka0KSR9gFpgKRThVfJm+CI9u+Jn4n4JSo0KVOhQjnN/wF6+Xx8G08/+fzKDRuveRj4mg9sP/r6W9wW30QJP83cJGSGi8xRZ4456qIXhGL4CYUNMcUQPo+cARxcXGGCrxiQKJQ00CqVtNyNbOG5mRcZPzb1aQB/5uLslumpM6zlQ2mmmJhZZpllTvyUg1AjFV+2/ogoLVMUZC/IeoCr9IASTZpEIgF4OJRET3BwGGINb596Zz2Af/zY5KcqUZmVrEgvkLT3OS3lDQiJQDEAimNCOwNcXHx8oUw27LjpBJ1NzoPxAOPjE36tVvuof+7d8wOr6Eu/jIlpiAGnTkMMQVJ+vQdE2gQcURzk6idRTRqQKOOnmjg46cQ8Rz0dilbTz6tn/wow6BPjqLN1JnWDBnUx7TZyBqjyF0l4SfJWQM4CUmi5JFGnZp9munxBGKDo7fj5qiPCtECy6JTLzzD3HBAT0/p6opOHoXbrezkEqX9vV5yTq0Z1+Z5fmrYYICcQdaxPxA+F7JHSB0B9/Opk8VUyIxwgxiMWfSHEAUJcRUN11RjCQgbE6aAik7rakVXEufG/WE/BkOgkp2FIBPcIiXGIgAhP00/qFRJTytWlGSDlVgupn2NRTYRuVlFJngBA9v1QGBMrjTQSTwPyqLUODVXsTOTkSF9wFqvNtydSHkQjcSb/iib5PhIz5iUMyIaUiPmGl/ksKSqRGA9IVUmI09+6FXncljM5Mg+Ty1kuxX/3blgzIBvbUVyzvF8yK+J59bxkD7BcXqwBhrEGGMYaYBhrgGGsAYaxBhjGGmAYa4BhrAGGsQYYxhpgGGuAYawBhrEGGMYaYBhrgGGsAYaxBhjGGmAYa4BhrAGGsQYYxhpgGGuAYawBV5B2Gzzc/IGjbbx579vxLa3kN/O14z32AEdktB1mIbKmmxy1y5HH1b+WFbjp53wxtV+4BTdE7o5E0QrQ2ruTS611tKkys0BNnpJZdbrYZE0WaKOa3EcvNc2jbVHy0uxuuvUyC0WRVeEREuIQizLFRW/nnjAi27ztpKpJZfP4enXZrm8PTztO9r1mewE9XMLCb1XKBJfyJ8p5iopqNJVL9ABAFG9NPhExPtAkMSES54prgB6yZj7lpH5JwII8LWdkxI8keWn8A7nDwxE7YR0cYlyKtk9Yn/nkEENqgD9Pkr0hjw9JeAJJiTIl5ihRokxZC0uQXNIV4sfpLkEohvgq+vOSGi3FpyT+lClpSaLq7QOcZJqIKF1WJiY0KVNOxZfLzmQC9lD3DkuKYIIesCnRSx3xfSF8mbL2Ww3cNOGcTD/7kISnmeY0q+gDoEpFiwmUXUQG62hqO2GLFDVLD9znpr9luBrZAyqUqdJFlYoWsAlgnIn0czoHHGM8NQCgSjVt+Q5OOh80RdQIuQMcsk2snS19gj4DyMW5NMAT8bJ8ypSpUqFCFV8ZfkJCjjPJcroBxYBfOQcZia9NQ1V6uFzFIjG2+dSp44sekMmvB+3r9NYvUVc/2RzgpPJLAypiEFJ51nmBC1xsNeBfvMvPnGfYE9+hXaiLLhFwqJSGXMmHLCuK8Cr5p13ZUGUvqFBueVcwziTP8aJ2TluG/pnDrKaPW9iqFZbzeiJ7U0RNCdMeICmKEepbTsRDqZMOQa1rfYBTvM0+54mW8A4tuX/hPMsRXieId2lx5ADhcrkl5EqeTjTi/b75iok5yO/5pXNARM3SaWvXG/yT+52HWM+HtdjR7V4mWVq5yEwSO9qZ4B+8xTSn583bvr+QLE3HeI0x57XLcpOWhCK/yvy/wBpgGGuAYawBhrEGGMYaYBhrgGGsAYaxBhjGGmAYa4BhrAGGsQYYxhpgGGuAYawBhrEGGMYaYBhrgGGsAYaxBhjGFTuNLGaI3aXdiydN30URWb6iB2DcHRoe+KPr2pHoSjMw2NcMguBvbtei6qG+1StN30/h+GB/798hmYQPrt9wteHbKRZdi6oMDq/+A4AbBMGFbbdu/sHy3h7T91UYdn5h25nuniX3gViGfn3vV+/73O5bxhz7b28vOxs2XsPmG0Z2BEHQAOU54GPXr7t55+03z3q+N39py//E0PAAo7u214IgeEWe09p8rVbrPnZ0/Kn9Pz1409SJ6St/hx2K73vsGN1a37bjkw/u2bPne+p3bQedfY/ve+DlQ0fumZqYXjFx/BQnJ08TNlv/92zL/PQsW8qatf0MDPZH6z6y9sDQ8MDuIAjO5fMtOOrXarU+YFOj3th84fzMsst2tx2G53uNJUuvGgMOB0EwtlDe/wAdXqc+E85n8QAAAABJRU5ErkJggg=="/>
        <image id="_Image3" width="47px" height="37px" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC8AAAAlCAYAAADSvLDKAAAACXBIWXMAAA7EAAAOxAGVKw4bAAADO0lEQVRYhd2Yv28TMRTHv+8apKYFqYrKwAgVEDrRga6sHZgMC2JhZWAmSLAwIPFnIFh7AokJCf6BIGDk59ClEiCxkKRBUfIYehf5fM/P9qVIiHeDfT4/+/N9986xQwgYG77B4J7T/CjLs6ch31ibmdkagOcAOmUbgfYAXKWcfvv8KDAoAfgOYJ3B9qOfANaX8iUWHRNtaqZ3ATw8BKog3czy7LHPLwuMu87gCjgfXh0Gn1yAt2IM3iznYOsCcEHzU+EZ3BUGLOvdowCX5nHbfdYKDPoehXonbcDg/Yaskl1ncI2FQGPNaZ5gxUezYUfXLt26dJ9qTn5X7qV6UQ6yPPvowr9j8EUJXBPQRIQLnQAOAjGAnSzPXs5z3s47Xyl9VL4235Xi7+EgAGcBJ+c1cLt061qbbQSq9bHbyugyeN7uKyvwWqRTBGimgUvtWlmB9wFqQpoI8IEX9z0CfWbwNoHuaOA1eFfA31p5POAg0OvlfLl/YA6mTnsF3Js2Tv0Dg+95BDxg8KZPvATs9rFgegT6wuCvRZc+g68BuGS/gdJXTZuyzuAf7by9K8EMzOC2BhsS5Xyor1bylTfls3be3gewOzbjifuRRqVNYcdHZrRlt1vPTzRJHffVF2DdgRlMCfRpNV8djsxojUCnGbwh/SYE06a43wLwNnXJ1MyzLD4pHm8D6AO4zOBn2jJagZdAQmCp/d28F9b180MznDD4jLS6BFebEKi2nMaOIUWxfAMuoA8cELbE2poegoq1mPFjluXoyNtOTdZ7N2qetBGj7rPQSUoFSrGUMWKDEwX/r9r/D5+Sh76DxqJjSM+SIu+eanwTkXOFIKRxtfFLq8G7E8ZELCWqR9k/OvLCWbImMgSl+fhKSYC4PUg9krk+0qSaEJ+Plka2zSOfcHoX+6REPjSXJER6Lv3RU4uqVpYm7bc1ESHAGIFq2sSAp0BrInwCNDEtu6EJuLRnCYGGBMR+xLXI21BNwGPyflEB88jPzOwUg28BOKZFPgSZuiNMyXsh8ldmZvatBeAcge6HtqgaXCq4BhwpYAfApEWgTmg/rYloCi4BS7CKgE4LwC8CvWiS54uAayJCvylFufcHPKZcQtc4fQMAAAAASUVORK5CYII="/>
        <image id="_Image5" width="66px" height="61px" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEIAAAA9CAYAAAAZIL/8AAAACXBIWXMAAA7EAAAOxAGVKw4bAAABnElEQVRoge1ZQQ7DIAxLpf5n/39dd8m2qKJdCSExkJw2LA07pXOEt+M4XvSrjcp1td6CQdV++n6Iz9vFuhUGVTv5iodtzN2JIPJtTGhTzieCKE586Gn5NAJRvGtjPq/GCOK7Nka+GqOJN22M/LMcXXxTYyzsE1V8lSNp7RNFoAVHIqqzTxSBXTj+s08UgXeYye+V7BNF4B1mvlfEQKXFuu7lNVBpMbe9eg5UWiyEh/VApcXCH0LkfUS4eIl530dAiZeYx30ErHi53ss+UQQ+5mhpnygCVRxb7RNFYDNHjX2iCDTl+NQ+UQR24xgZ8ISLlxhSwBP6aiIFPHfYFAOVFnPlgXYfEcYD4T4C4iFE3UdAiKfCiXDd1GGvah697RNavMR62Ocw4iVmZZ9DipdYi30OL15itfY5lXgJPLHPacVL7Mo+lxAvsQx4Cva5nHiJZcDDWAY8jGXAw+sZ8PB6BjxcGfBwZcDDlQFPYaAy/WEnbIqBSotNMVBpsSkGKi3myiMDHq4MeLhWD3i+2IoBTxFbJeD5y3HmgKeK4xtfBUAkntElvwAAAABJRU5ErkJggg=="/>
    </defs>
</svg>
