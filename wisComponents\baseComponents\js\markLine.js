/**
 * @description 标记线
 * <AUTHOR>
 * @date 2020-03-17
 * @class MarkLine
 */
class MarkLine {
  constructor(container, opts) {
    this._initProperty();
    this._container = container;
    this.property = $.extend(true, this.property, opts);
    this._drawMark()
  }
  /**
   * @description 初始化组件配置项
   */
  _initProperty() {
    this.property = {
      description: '',
      direction: 'horizontal',
      endPoints: [100, 100, 200, 100],
      type: 'max',
      symbol: '',
      symbolSize: 50,
      label: {
        text: 'example',
        fontSize: 20,
        color: '#ff0',
        textAnchor: 'end',
        offset: [0, 0]
      },
      lineStyle: {
        color: '#f00',
        width: 1,
        type: 'line'
      },
      xRange: null,
      yRange: null,
      data: []
    };
    this._optionDic = [{
      name: 'description',
      displayName: '标记线描述',
      description: '标记线描述',
      type: OptionType.string,
      show: true,
      editable: true
    }, {
      name: 'type',
      displayName: '标注线类型',
      description: '标注线值的类型',
      type: OptionType.enum,
      options: [{
        name: '最大值',
        value: 'max'
      }, {
        name: '最小值',
        value: 'min'
      }, {
        name: '平均值',
        value: 'avg'
      }, {
        name: '自定义',
        value: 'custom'
      }],
      show: true,
      editable: true
    }, {
      name: 'direction',
      displayName: '标注线方向',
      description: '标注线的方向',
      type: OptionType.enum,
      options: [{
        name: '水平',
        value: 'horizontal'
      }, {
        name: '垂直',
        value: 'vertical'
      }],
      show: true,
      editable: true
    }, {
      name: 'endPoints',
      displayName: '端点坐标',
      description: '标记线两个端点的坐标',
      type: OptionType.doubleArray,
      placeholder: ['起点x', '起点y', '终点x', '终点y'],
      show: true,
      editable: true
    }, {
      name: 'symbol',
      displayName: '端点形状',
      description: '标记点两端端点的形状',
      type: OptionType.enum,
      options: [{
        name: '无',
        value: ''
      }, {
        name: '圆形',
        value: 'circle'
      }, {
        name: '三角形',
        value: 'triangle'
      }, {
        name: '矩形',
        value: 'rect'
      }, {
        name: '大头针',
        value: 'pin'
      }],
      show: true,
      editable: true
    }, {
      name: 'symbolSize',
      displayName: '端点宽高',
      description: '标记点两端端点图形宽高',
      type: OptionType.int,
      show: true,
      editable: true
    }, {
      name: 'label',
      displayName: '文字属性',
      description: '标记线显示文字属性',
      show: true,
      editable: true,
      children: [{
        name: 'text',
        displayName: '显示文字(空值为类型值)',
        description: '标注线显示文字',
        type: OptionType.string,
        show: true,
        editable: true
      }, {
        name: 'color',
        displayName: '颜色',
        description: '显示文字的颜色',
        type: OptionType.color,
        show: true,
        editable: true
      }, {
        name: 'fontSize',
        displayName: '字号',
        description: '显示文字的大小',
        type: OptionType.int,
        show: true,
        editable: true
      }, {
        name: 'textAnchor',
        displayName: '文字位置',
        description: '文字相对于线的位置',
        type: OptionType.enum,
        options: [{
          name: '起点',
          value: 'end'
        }, {
          name: '中点',
          value: 'middle'
        }, {
          name: '终点',
          value: 'start'
        }],
        show: true,
        editable: true
      }, {
        name: 'offset',
        displayName: '偏移量',
        description: '文字的偏移量',
        type: OptionType.intArray,
        placeholder: ['x', 'y'],
        show: true,
        editable: true
      }]
    }, {
      name: 'lineStyle',
      displayName: '标记线样式',
      description: '标记线样式相关属性',
      show: true,
      editable: true,
      children: [{
        name: 'color',
        displayName: '颜色',
        description: '标记线颜色',
        type: OptionType.color,
        show: true,
        editable: true
      }, {
        name: 'width',
        displayName: '线宽',
        description: '标记线宽度',
        type: OptionType.int,
        show: true,
        editable: true
      }, {
        name: 'type',
        displayName: '线类型',
        description: '标记线类型',
        type: OptionType.enum,
        options: [{
          name: '实线',
          value: 'line'
        }, {
          name: '虚线',
          value: 'dash'
        }, {
          name: '点',
          value: 'dot'
        }],
        show: true,
        editable: true
      }]
    }]
  }
  /**
   * @description 更新配置
   */
  setOption(opt) {
    this.property = $.extend(true, this.property, opt);
  }
  /**
   * @description 生成标记线
   */
  _drawMark() {
    this._pathDom = this._container.append('path')
      .style('stroke', this.property.lineStyle.color)
      .style('fill', 'none')
      .style('stroke-width', this.property.lineStyle.width);

    this._textDom = this._container.append('text')
      .style('dominant-baseline', 'middle')
      .style('text-anchor', this.property.label.position)
      .style('fill', this.property.lineStyle.color);

    this._setPath();

  }
  /**
   * @description 设置标记线数据
   */
  _setPath() {
    let type = this.property.type;
    let labelText = '';
    this.path = d3.path();
    let startX, startY, endX, endY;
    if (this.property.xRange === null || this.property.yRange === null || this.property.data.length <= 0) return
    let xDomain = this.property.xRange.domain();
    let yDomain = this.property.yRange.domain();
    if (type !== 'custom' && this.property.data.length > 0) {
      let data = {};
      if (type === 'max') {
        data = this.property.data.reduce((p, v) => p.y < v.y ? v : p)
      } else if (type === 'min') {
        data = this.property.data.reduce((p, v) => p.y > v.y ? v : p)
      } else {
        let avgY = d3.sum(this.property.data.map(d => d.y)) / this.property.data.length
        let avgX = d3.sum(this.property.data.map(d => d.x)) / this.property.data.length
        data = {
          x: avgX,
          y: avgY
        }
      }
      if (this.property.direction === 'horizontal') {
        startX = this.property.xRange(xDomain[0]);
        endX = this.property.xRange(xDomain[1]);
        startY = endY = this.property.yRange(data.y);
        labelText = data.y
      } else {
        startX = endX = this.property.xRange(data.x);
        startY = this.property.yRange(yDomain[0]);
        endY = this.property.yRange(yDomain[1]);
        labelText = data.x
      }
    } else {
      [startX, endX, startY, endY] = this.property.endPoints;
    }
    this.path.moveTo(startX, startY);
    this.path.lineTo(endX, endY)
    this._pathDom.attr('d', this.path.toString())

    this._textDom
      .attr('x', (this.property.label.position === 'end' ? startX : this.property.label.position === 'middle' ? (startX + endX) / 2 : endX) + this.property.label.offset[0])
      .attr('y', startY + this.property.label.offset[1])
      .text(labelText)
  }

}