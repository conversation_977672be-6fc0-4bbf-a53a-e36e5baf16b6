<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 101 101" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:square;stroke-miterlimit:1.5;">
    <g transform="matrix(1,0,0,1,-38892,-3487)">
        <g id="火电" transform="matrix(-0.760297,-9.31095e-17,7.47291e-17,-0.61021,49514.9,6484.99)">
            <g transform="matrix(0.239141,0,0,0.29796,11221.4,4724.87)">
                <g id="背景" transform="matrix(-2.26939,2.77921e-16,-2.79749e-16,-2.28432,86556.4,10530.6)">
                    <path d="M33314.3,4358.05C33314.3,4345.71 33304.2,4335.69 33291.8,4335.69L33097.7,4335.69C33085.3,4335.69 33075.2,4345.71 33075.2,4358.05L33075.2,4550.85C33075.2,4563.18 33085.3,4573.19 33097.7,4573.19L33291.8,4573.19C33304.2,4573.19 33314.3,4563.18 33314.3,4550.85L33314.3,4358.05Z" style="fill:rgb(34,32,32);"/>
                </g>
                <g>
                    <g>
                        <g transform="matrix(-3.24542,3.97449e-16,-1.48168e-16,-1.20989,118802,5623.36)">
                            <path d="M33225.6,4428.83L33069.2,4428.83L33081.4,4372.83L33215.2,4372.83L33225.6,4428.83Z" style="fill:url(#_Linear1);"/>
                        </g>
                        <g transform="matrix(-5.06102,6.19796e-16,-3.89891e-16,-3.1837,180082,14637.5)">
                            <path d="M33414.6,4514.8L33314.3,4514.8L33314.3,4550.9C33314.3,4559.12 33317.8,4565.8 33322,4565.8L33406.8,4565.8C33411.1,4565.8 33414.6,4559.06 33414.6,4550.76L33414.6,4514.8Z" style="fill:url(#_Linear2);"/>
                        </g>
                        <g transform="matrix(-2.2686,2.77824e-16,-2.9285e-16,-2.3913,93717.3,10193.4)">
                            <ellipse cx="36362.9" cy="4152.16" rx="111.824" ry="0.255" style="fill:white;"/>
                        </g>
                    </g>
                    <g transform="matrix(-9.15345,1.12097e-15,-1.12097e-15,-9.15345,314625,42399.3)">
                        <g transform="matrix(14,0,0,14,33166.2,4617.42)">
                        </g>
                        <text x="33127.5px" y="4617.42px" style="font-family:'MicrosoftYaHeiUI-Bold', 'Microsoft YaHei UI', sans-serif;font-weight:700;font-size:14px;fill:rgb(29,29,29);">3<tspan x="33137.5px 33147.6px 33157.6px " y="4617.42px 4617.42px 4617.42px ">254</tspan></text>
                    </g>
                </g>
                <path d="M10949.4,578.483C10949.4,607.05 10972.6,630.242 11001.1,630.242L11447.6,630.242C11476.2,630.242 11499.4,607.05 11499.4,578.483L11499.4,132.001C11499.4,103.434 11476.2,80.242 11447.6,80.242L11001.1,80.242C10972.6,80.242 10949.4,103.434 10949.4,132.001L10949.4,578.483ZM10967.7,570.816C10967.7,593.478 10986.1,611.878 11008.8,611.878L11439.9,611.878C11462.6,611.878 11481,593.478 11481,570.816L11481,139.668C11481,117.006 11462.6,98.606 11439.9,98.606L11008.8,98.606C10986.1,98.606 10967.7,117.006 10967.7,139.668L10967.7,570.816Z" style="fill:url(#_Linear3);"/>
            </g>
            <g transform="matrix(1.70065e-17,0.346051,-0.17434,1.33009e-17,14389,5074.59)">
                <path d="M-751.251,2950.06C-751.641,2950.23 -752.037,2950.32 -752.437,2950.32C-758.947,2950.32 -764.232,2927.31 -764.232,2898.98C-764.232,2870.65 -758.947,2847.64 -752.437,2847.64C-752.111,2847.64 -751.789,2847.7 -751.47,2847.81L-569.011,2847.81L-569.011,2950.06L-751.251,2950.06Z" style="fill:url(#_Linear4);"/>
                <g transform="matrix(1.49891,-3.65984e-32,7.2489e-32,1.23933,-51363,-2888.07)">
                    <ellipse cx="33886.3" cy="4669.48" rx="7.869" ry="41.423" style="fill:url(#_Linear5);"/>
                    <path d="M33886.3,4628.06C33890.6,4628.06 33894.2,4646.62 33894.2,4669.48C33894.2,4692.35 33890.6,4710.91 33886.3,4710.91C33882,4710.91 33878.4,4692.35 33878.4,4669.48C33878.4,4646.62 33882,4628.06 33886.3,4628.06ZM33886.3,4629.6C33885.6,4629.6 33885,4630.23 33884.4,4631.31C33883.6,4632.57 33883,4634.43 33882.4,4636.74C33880.5,4644.13 33879.2,4656.05 33879.2,4669.48C33879.2,4682.92 33880.5,4694.84 33882.4,4702.23C33883,4704.54 33883.6,4706.4 33884.4,4707.66C33885,4708.74 33885.6,4709.37 33886.3,4709.37C33887,4709.37 33887.6,4708.74 33888.3,4707.66C33889,4706.4 33889.6,4704.54 33890.2,4702.23C33892.2,4694.84 33893.4,4682.92 33893.4,4669.48C33893.4,4656.05 33892.2,4644.13 33890.2,4636.74C33889.6,4634.43 33889,4632.57 33888.3,4631.31C33887.6,4630.23 33887,4629.6 33886.3,4629.6Z" style="fill:rgb(0,192,255);"/>
                </g>
            </g>
            <g transform="matrix(1.70065e-17,0.346051,-0.17434,1.33009e-17,14368.1,5074.59)">
                <path d="M-751.251,2950.06C-751.641,2950.23 -752.037,2950.32 -752.437,2950.32C-758.947,2950.32 -764.232,2927.31 -764.232,2898.98C-764.232,2870.65 -758.947,2847.64 -752.437,2847.64C-752.111,2847.64 -751.789,2847.7 -751.47,2847.81L-569.011,2847.81L-569.011,2950.06L-751.251,2950.06Z" style="fill:url(#_Linear6);"/>
                <g transform="matrix(1.49891,-3.65984e-32,7.2489e-32,1.23933,-51363,-2888.07)">
                    <ellipse cx="33886.3" cy="4669.48" rx="7.869" ry="41.423" style="fill:url(#_Linear7);"/>
                    <path d="M33886.3,4628.06C33890.6,4628.06 33894.2,4646.62 33894.2,4669.48C33894.2,4692.35 33890.6,4710.91 33886.3,4710.91C33882,4710.91 33878.4,4692.35 33878.4,4669.48C33878.4,4646.62 33882,4628.06 33886.3,4628.06ZM33886.3,4629.6C33885.6,4629.6 33885,4630.23 33884.4,4631.31C33883.6,4632.57 33883,4634.43 33882.4,4636.74C33880.5,4644.13 33879.2,4656.05 33879.2,4669.48C33879.2,4682.92 33880.5,4694.84 33882.4,4702.23C33883,4704.54 33883.6,4706.4 33884.4,4707.66C33885,4708.74 33885.6,4709.37 33886.3,4709.37C33887,4709.37 33887.6,4708.74 33888.3,4707.66C33889,4706.4 33889.6,4704.54 33890.2,4702.23C33892.2,4694.84 33893.4,4682.92 33893.4,4669.48C33893.4,4656.05 33892.2,4644.13 33890.2,4636.74C33889.6,4634.43 33889,4632.57 33888.3,4631.31C33887.6,4630.23 33887,4629.6 33886.3,4629.6Z" style="fill:rgb(0,192,255);"/>
                </g>
            </g>
            <g transform="matrix(1.70065e-17,0.346051,-0.17434,1.33009e-17,14409.6,5074.59)">
                <path d="M-751.251,2950.06C-751.641,2950.23 -752.037,2950.32 -752.437,2950.32C-758.947,2950.32 -764.232,2927.31 -764.232,2898.98C-764.232,2870.65 -758.947,2847.64 -752.437,2847.64C-752.111,2847.64 -751.789,2847.7 -751.47,2847.81L-616.883,2847.81L-616.883,2950.06L-751.251,2950.06Z" style="fill:url(#_Linear8);"/>
                <g transform="matrix(1.49891,-5.50739e-32,7.2489e-32,1.23933,-51407.9,-2888.07)">
                    <ellipse cx="33886.3" cy="4669.48" rx="7.869" ry="41.423" style="fill:url(#_Linear9);"/>
                    <path d="M33886.3,4628.06C33890.6,4628.06 33894.2,4646.62 33894.2,4669.48C33894.2,4692.35 33890.6,4710.91 33886.3,4710.91C33882,4710.91 33878.4,4692.35 33878.4,4669.48C33878.4,4646.62 33882,4628.06 33886.3,4628.06ZM33886.3,4629.6C33885.6,4629.6 33885,4630.23 33884.4,4631.31C33883.6,4632.57 33883,4634.43 33882.4,4636.74C33880.5,4644.13 33879.2,4656.05 33879.2,4669.48C33879.2,4682.92 33880.5,4694.84 33882.4,4702.23C33883,4704.54 33883.6,4706.4 33884.4,4707.66C33885,4708.74 33885.6,4709.37 33886.3,4709.37C33887,4709.37 33887.6,4708.74 33888.3,4707.66C33889,4706.4 33889.6,4704.54 33890.2,4702.23C33892.2,4694.84 33893.4,4682.92 33893.4,4669.48C33893.4,4656.05 33892.2,4644.13 33890.2,4636.74C33889.6,4634.43 33889,4632.57 33888.3,4631.31C33887.6,4630.23 33887,4629.6 33886.3,4629.6Z" style="fill:rgb(0,192,255);"/>
                </g>
            </g>
            <g transform="matrix(1.70065e-17,0.346051,-0.17434,1.33009e-17,14430.3,5074.59)">
                <g transform="matrix(2.89976e-16,7.54433,-4.73567,4.61957e-16,-466.808,2628.5)">
                    <use xlink:href="#_Image10" x="29.047" y="9.499" width="14px" height="54px"/>
                </g>
            </g>
            <g transform="matrix(1.70065e-17,0.346051,-0.17434,1.33009e-17,14451,5074.59)">
                <path d="M-751.251,2950.06C-751.641,2950.23 -752.037,2950.32 -752.437,2950.32C-758.947,2950.32 -764.232,2927.31 -764.232,2898.98C-764.232,2870.65 -758.947,2847.64 -752.437,2847.64C-752.111,2847.64 -751.789,2847.7 -751.47,2847.81L-644.799,2847.81L-644.799,2950.06L-751.251,2950.06Z" style="fill:url(#_Linear11);"/>
                <g transform="matrix(1.49891,-3.65984e-32,8.0127e-32,1.23933,-51436.2,-2888.07)">
                    <ellipse cx="33886.3" cy="4669.48" rx="7.869" ry="41.423" style="fill:url(#_Linear12);"/>
                    <path d="M33886.3,4628.06C33890.6,4628.06 33894.2,4646.62 33894.2,4669.48C33894.2,4692.35 33890.6,4710.91 33886.3,4710.91C33882,4710.91 33878.4,4692.35 33878.4,4669.48C33878.4,4646.62 33882,4628.06 33886.3,4628.06ZM33886.3,4629.6C33885.6,4629.6 33885,4630.23 33884.4,4631.31C33883.6,4632.57 33883,4634.43 33882.4,4636.74C33880.5,4644.13 33879.2,4656.05 33879.2,4669.48C33879.2,4682.92 33880.5,4694.84 33882.4,4702.23C33883,4704.54 33883.6,4706.4 33884.4,4707.66C33885,4708.74 33885.6,4709.37 33886.3,4709.37C33887,4709.37 33887.6,4708.74 33888.3,4707.66C33889,4706.4 33889.6,4704.54 33890.2,4702.23C33892.2,4694.84 33893.4,4682.92 33893.4,4669.48C33893.4,4656.05 33892.2,4644.13 33890.2,4636.74C33889.6,4634.43 33889,4632.57 33888.3,4631.31C33887.6,4630.23 33887,4629.6 33886.3,4629.6Z" style="fill:rgb(0,192,255);"/>
                </g>
            </g>
        </g>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.05245e-15,-39.3447,17.1877,2.40917e-15,33148.3,4418.96)"><stop offset="0" style="stop-color:rgb(63,63,63);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(63,63,63);stop-opacity:0"/></linearGradient>
        <linearGradient id="_Linear2" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(3.18408e-15,52,-52,3.18408e-15,33371.3,4513.8)"><stop offset="0" style="stop-color:rgb(143,143,143);stop-opacity:1"/><stop offset="0.18" style="stop-color:rgb(168,168,168);stop-opacity:1"/><stop offset="0.39" style="stop-color:rgb(175,175,175);stop-opacity:1"/><stop offset="0.72" style="stop-color:rgb(160,160,160);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(111,111,111);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear3" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-1.00704e-13,-548.206,548.206,-1.00704e-13,11219.8,628.505)"><stop offset="0" style="stop-color:rgb(105,105,105);stop-opacity:1"/><stop offset="0.02" style="stop-color:rgb(158,158,158);stop-opacity:1"/><stop offset="0.05" style="stop-color:rgb(208,208,208);stop-opacity:1"/><stop offset="0.12" style="stop-color:rgb(229,229,229);stop-opacity:1"/><stop offset="0.2" style="stop-color:rgb(171,171,171);stop-opacity:1"/><stop offset="0.29" style="stop-color:rgb(109,109,109);stop-opacity:1"/><stop offset="0.51" style="stop-color:rgb(79,79,79);stop-opacity:1"/><stop offset="0.74" style="stop-color:rgb(105,105,105);stop-opacity:1"/><stop offset="0.82" style="stop-color:rgb(165,165,165);stop-opacity:1"/><stop offset="0.89" style="stop-color:rgb(229,229,229);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(105,105,105);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear4" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(3.56668e-15,102.863,-58.2483,6.29852e-15,-644.998,2847.43)"><stop offset="0" style="stop-color:rgb(2,115,184);stop-opacity:1"/><stop offset="0.13" style="stop-color:rgb(85,177,233);stop-opacity:1"/><stop offset="0.25" style="stop-color:rgb(113,198,250);stop-opacity:1"/><stop offset="0.5" style="stop-color:rgb(29,169,255);stop-opacity:1"/><stop offset="0.68" style="stop-color:rgb(11,114,177);stop-opacity:1"/><stop offset="0.8" style="stop-color:rgb(3,88,140);stop-opacity:1"/><stop offset="0.91" style="stop-color:rgb(2,97,155);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,132,213);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear5" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-14.8608,3.88701e-15,-1.81992e-15,-31.7399,33894.3,4671.09)"><stop offset="0" style="stop-color:rgb(45,171,255);stop-opacity:1"/><stop offset="0.5" style="stop-color:rgb(11,128,206);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,113,189);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear6" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(3.56668e-15,102.863,-58.2483,6.29852e-15,-644.998,2847.43)"><stop offset="0" style="stop-color:rgb(2,115,184);stop-opacity:1"/><stop offset="0.13" style="stop-color:rgb(85,177,233);stop-opacity:1"/><stop offset="0.25" style="stop-color:rgb(113,198,250);stop-opacity:1"/><stop offset="0.5" style="stop-color:rgb(29,169,255);stop-opacity:1"/><stop offset="0.68" style="stop-color:rgb(11,114,177);stop-opacity:1"/><stop offset="0.8" style="stop-color:rgb(3,88,140);stop-opacity:1"/><stop offset="0.91" style="stop-color:rgb(2,97,155);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,132,213);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear7" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-14.8608,3.88701e-15,-1.81992e-15,-31.7399,33894.3,4671.09)"><stop offset="0" style="stop-color:rgb(45,171,255);stop-opacity:1"/><stop offset="0.5" style="stop-color:rgb(11,128,206);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,113,189);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear8" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(3.56668e-15,102.863,-58.2483,6.29852e-15,-644.998,2847.43)"><stop offset="0" style="stop-color:rgb(2,115,184);stop-opacity:1"/><stop offset="0.13" style="stop-color:rgb(85,177,233);stop-opacity:1"/><stop offset="0.25" style="stop-color:rgb(113,198,250);stop-opacity:1"/><stop offset="0.5" style="stop-color:rgb(29,169,255);stop-opacity:1"/><stop offset="0.68" style="stop-color:rgb(11,114,177);stop-opacity:1"/><stop offset="0.8" style="stop-color:rgb(3,88,140);stop-opacity:1"/><stop offset="0.91" style="stop-color:rgb(2,97,155);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,132,213);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear9" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-14.8608,3.88701e-15,-1.81992e-15,-31.7399,33894.3,4671.09)"><stop offset="0" style="stop-color:rgb(45,171,255);stop-opacity:1"/><stop offset="0.5" style="stop-color:rgb(11,128,206);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,113,189);stop-opacity:1"/></linearGradient>
        <image id="_Image10" width="14px" height="54px" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAA2CAYAAAD3YagRAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABPElEQVRIie3WsUoDQRDG8f9s9i65MypqFIOIIURBsbTKI1hb2YqP4lP4Fr6CrYV2ooIEJHjh4pGIkrvL7lqkCaQISSn3wXT7Y2anGmk2Gq4mlgs/pVUyHEjGjkCtBCOBeOzoOo93W+LJeNxlPg6Qm+aGu1YDfGeZFwEiNFd2F3lpKpeb+Wg6jxKiPWcRgcjMBxpYVdBzCu2L0Dg+oZWOYJgwSr5ILCQGtEzG+7YQG+gbSB38VBQagEoAp2cQhFTiiHo/oh5H2M4bzxl8jCeVO1hXk+5qoc9NpYAFLGABC1jAAhbw/0K3BBJZrqMohZIloBJB9ZS3MBzoMuo23Od3gTs38le4D7bRD8EWl5Hj3I852lzjMMvZm3qYIXT9kNdKyGe5Sqdaw4og7XZ7Zq9VcbTUmGGa09EBhtlN/AHxa2dqY0qWhQAAAABJRU5ErkJggg=="/>
        <linearGradient id="_Linear11" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(3.56668e-15,102.863,-58.2483,6.29852e-15,-644.998,2847.43)"><stop offset="0" style="stop-color:rgb(2,115,184);stop-opacity:1"/><stop offset="0.13" style="stop-color:rgb(85,177,233);stop-opacity:1"/><stop offset="0.25" style="stop-color:rgb(113,198,250);stop-opacity:1"/><stop offset="0.5" style="stop-color:rgb(29,169,255);stop-opacity:1"/><stop offset="0.68" style="stop-color:rgb(11,114,177);stop-opacity:1"/><stop offset="0.8" style="stop-color:rgb(3,88,140);stop-opacity:1"/><stop offset="0.91" style="stop-color:rgb(2,97,155);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,132,213);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear12" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-14.8608,3.88701e-15,-1.81992e-15,-31.7399,33894.3,4671.09)"><stop offset="0" style="stop-color:rgb(45,171,255);stop-opacity:1"/><stop offset="0.5" style="stop-color:rgb(11,128,206);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,113,189);stop-opacity:1"/></linearGradient>
    </defs>
</svg>
