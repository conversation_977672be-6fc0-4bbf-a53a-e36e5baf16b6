/**
 * @description 河流图
 * <AUTHOR>
 * @date 2022-10-21
 * @class River
 */
class River {
  constructor(container, opts) {
    this._initProperty();
    this._container = container;
    this.property = $.extend(true, this.property, opts);
    this.keys = ['x', 'min', 'max'];
  }
  /**
   * @description 初始化组件配置项
   */
  _initProperty() {
    this.property = {
      strokeColor: '#ff0000',
      fillColor: {
        type: 0,
        direction: 0,
        stops: [
          {
            offset: 0,
            color: '#ff000055',
          },
          {
            offset: 1,
            color: '#00ff0055',
          },
        ],
      },
    };

    this._optionDic = [
      {
        name: 'strokeColor',
        displayName: '边框颜色',
        description: '河流图边框颜色',
        type: OptionType.color,
        show: true,
        editable: true,
      },
      {
        name: 'fillColor',
        displayName: '填充颜色',
        description: '河流图内部的填充颜色',
        type: OptionType.colorGradient,
        show: true,
        editable: true,
      },
    ];
  }
  /**
   * @description 绘制面积图
   */
  _drawRiver(area, data) {
    let fillColor = '';
    if (typeof this.property.fillColor === 'string') {
      fillColor = this.property.fillColor;
    } else {
      fillColor = WisCompUtil.setGradient(this._container, 'testGradient', this.property.fillColor);
    }
    this._container.append('path').attr('d', area(data)).attr('stroke', this.property.strokeColor).attr('fill', `${fillColor}`);
  }
  /**
   * @description 更新面积图
   */
  _updateRiver(area, data) {
    this._container.select('path').transition().duration(500).attr('d', area(data));
  }
  /**
   * @description 生成面积路径
   */
  _getRiver() {
    let xRange = this.property.xRange;
    let yRange = this.property.yRange;
    return !xRange && !yRange
      ? null
      : d3
        .area()
        .x((d) => xRange(d.x))
        .y1((d) => yRange(d.max))
        .y0((d) => yRange(d.min))
  }
  /**
   * @description 更新数据
   */
  update(data) {
    //如果数据中y值为空字符串则剔除掉
    data = data.filter((d) => d.min !== undefined && d.min !== '' && d.max !== undefined && d.max !== '');
    let area = this._getRiver();
    if (area === null) return;
    if (this._container.select('path').empty()) {
      this._drawRiver(area, data);
    } else {
      this._updateRiver(area, data);
    }
  }
  /**
   * @description 更新配置
   */
  setOption(opt) {
    this.property = $.extend(true, this.property, opt);
  }
}
