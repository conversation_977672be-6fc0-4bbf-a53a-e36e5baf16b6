/*
 * @Descriptoin: 地图组件的工具包，用于将部分功能分离，控制单个文件尺寸，版本号@前版本部分为文件版本号，@后半部分为匹配基类的最低版本号
 * @Version: 2.0.1@2.0.13
 * @Author: matinayu
 * @Date: 2021-06-08 09:24:47
 * @LastEditors: matianyu
 * @LastEditTime: 2025-07-25 11:28:08
 */

/* global Lmapd L exports define VideoPlayer sessionId ReconnectingWebSocket Stomp*/
(function (global, factory) {
  'use strict';
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
    typeof define === 'function' && define.amd ? define(['exports'], factory) : (factory((global.Lmapd = global.Lmapd || {})));
}(this, (function (exports) {
  'use strict';

  const version = '1.0a';
  /** 
   * 用来生成点对象工厂，工厂将属性以及弹窗都挂载在点对象上，以此种方式获得leaflet的原生对象可以正常被其他原生方法使用
   * add by matianyu 2021/3/3
   */
  function markerFactory(data) {
    let latlng = wktToLatLng(data.geom, 'point'),
      iconStyle = data.iconStyle,
      icon = null;

    switch (data.iconType) {
      case 'pic': {
        icon = L.divIcon({
          html: `<div style="background-image: url(${data.iconBase64}); background-size: 100% 100%; width: ${iconStyle.iconWidth}px; height: ${iconStyle.iconHeight}px; transform-origin: center center; transform: rotate(${iconStyle.iconRotate ? iconStyle.iconRotate : 0}deg);">
                </div>`,
          iconSize: [iconStyle.iconWidth, iconStyle.iconHeight],
          iconAnchor: [iconStyle.iconWidth / 2, iconStyle.iconHeight / 2]
        });

        //icon = L.icon({iconUrl: data.iconBase64, iconSize: [iconStyle.iconWidth, iconStyle.iconHeight]});

        break;
      }
      case 'font': {
        const style = formatCss({
          color: iconStyle.fontColor,
          fontWeight: 800,
          fontSize: `${iconStyle.fontSize}px`,
          margin: 0,
          transform: `rotate(${iconStyle.iconRotate ? iconStyle.iconRotate : 0}deg)`
        });

        icon = L.divIcon({
          className: 'div_icon',
          html: `<div style="text-align: center;">
                              <p style="${style}">${data.name}</p>
                            </div>`,
          //根据名字长度计算宽度
          iconSize: [data.name.length * iconStyle.fontSize + 10, iconStyle.fontSize],
          iconAnchor: [(data.name.length * iconStyle.fontSize + 10) / 2, iconStyle.fontSize / 2]
        });
        break;
      }
      case 'mix': {
        const height = iconStyle.iconHeight > iconStyle.fontSize ? iconStyle.iconHeight : iconStyle.fontSize;
        const width = iconStyle.iconWidth + data.name.length * iconStyle.fontSize + 20;

        const htmlStr = `<div">
                          <img src="${data.iconBase64}" style="width: ${iconStyle.iconWidth}px; height: ${iconStyle.iconHeight}px; vertical-align: top !important;"/>
                          <p style="color: ${iconStyle.fontColor}; font-size: ${iconStyle.fontSize}px; vertical-align: top !important;">${data.name}</p>                                            
                        </div>`;

        icon = L.divIcon({
          className: 'div_icon',
          html: htmlStr,
          iconSize: [width, height],
          iconAnchor: [iconStyle.iconWidth / 2, iconStyle.iconHeight / 2]
        });

        break;
      }
    }

    const marker = L.marker(latlng, { prop: data, icon: icon });

    marker.sendCommand = function (event, params) { };

    /* 绑定视频*/
    marker.bindPopup = function (data) {
      this.popupOn = false;

      if (!this.popupGroup) {
        this.popupGroup = {};
      }
      this.popupGroup[data.id] = data.popupType === 'pic' ? new PopupImage(data, this.getLatLng()) : new PopupVideo(data, this.getLatLng(), { borderColor: this.options.prop.fontStyle.fontColor });
      this.popupGroup[data.id].sendCommand = marker.sendCommand;

      return this.popupGroup[data.id];
    };

    /* 弹出所有弹窗*/
    marker.popupAll = function (map) {
      this.popupOn = true;

      if (this.popupGroup) {
        for (let i in this.popupGroup) {
          this.popupGroup[i].popup(map);
        }
      }
    };

    /* 收起所有弹窗*/
    marker.packupAll = function (map) {
      this.popupOn = false;

      if (this.popupGroup) {
        for (let i in this.popupGroup) {
          // 收起连线视频
          this.popupGroup[i].packup(map);
          // 收起放大的视频
          this.popupGroup[i].zoomOut();

        }
      }
    };

    /* 清理所有视频，断开视频服务*/
    marker.cleanVideo = function () {
      if (this.popupGroup) {
        for (let i in this.popupGroup) {
          this.popupGroup[i].cleanup();

        }
      }
    };

    marker.mark = function () {
      if (this.markFlag) {
        $(`#map-div-markerIcon_${this.options.prop.id}`).css('border', 'none');
        this.markFlag = false;
      } else {
        $(`#map-div-markerIcon_${this.options.prop.id}`).css('border', '3px solid green');
        this.markFlag = true;
      }

    }

    marker.on('contextmenu', function (e) {
      if (e.originalEvent.button == 2) {  // 鼠标右键
        if (!this.popupOn) {
          this.sendCommand('objectAllPopup', [this.options.prop.id]);
        } else {
          this.sendCommand('objectAllPackup', [this.options.prop.id]);
        }
      }
    });

    marker.isNew = true;

    return marker;
  }

  /** 
   * 用来生成点线象工厂，类似点对象工厂
   * add by matianyu 2021/3/3
   */
  function lineFactory(data) {
    let latlngs = wktToLatLng(data.geom, 'line');
    let line = L.polyline(latlngs, { prop: data })
      .setStyle({
        color: data.lineStyle.color,
        weight: data.lineStyle.weight,
        dashArray: data.lineStyle.dashArray
      });

    line.isNew = true;

    return line;
  }

  /** 
   * 用来生成多边形线象工厂，类似点对象工厂
   * add by matianyu 2021/3/3
   */
  function polygonFactory(data) {
    let latlngs = wktToLatLng(data.geom, 'polygon');
    let polygon = L.polygon(latlngs, { prop: data })
      .setStyle({
        color: data.lineStyle.color,
        weight: data.lineStyle.weight,
        dashArray: data.lineStyle.dashArray,
        fillColor: data.fillStyle.fillColor,
        fillOpacity: data.fillStyle.fillOpacity
      });

    polygon.isNew = true;

    return polygon;
  }

  /** 
   * 生成可以沿线移动的点对象
   * add by matianyu 2021/3/3
   */
  function movingMarkerFactory(data) {
    const object = L.Marker.movingMarker(wktToLatLng(data.geom, 'line'), Math.random() * 100000, { loop: true, autostart: true });

    object.setIcon(L.icon({
      iconUrl: "data:image/png;base64,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",
      iconSize: [30, 30]
    }));

    return object;
  }

  /** 
   * 地图对象，连线弹窗用的对象，无法独立使用，需要结合地图对象
   * add by matianyu 2021/3/3
   */
  class PopupVideo {
    constructor(data, target, options = { borderColor: 'black' }) {
      this.prop = data;
      this.target = target;
      this.options = options;
      this.on = false;

      let latLng = wktToLatLng(this.prop.popupGps, 'point');

      this.m = L.marker(latLng, { zIndexOffset: 998, draggable: false })
        .setIcon(
          L.divIcon({
            className: 'popup-hint',
            html: `<div id="${this.prop.id}" style="pointer-events: none; width: ${this.prop.width}px; height: ${this.prop.height}px; background-color: rgb(0,0,0); border: 2px solid ${this.options.borderColor};"></div>`,
            iconSize: [this.prop.width, this.prop.height],
            iconAnchor: [this.prop.width / 2, this.prop.height / 2]
          })
        )
        // .on('dblclick', () => {
        //   this.sendCommand('videoZoomById', [this.prop.id]);
        // })
        .on('contextmenu', () => {
          this.sendCommand('objectPackupByPid', [this.prop.id]);
        })
        .on('mousedown', () => {
          this.map.dragging._draggable._enabled = false;

          this.map
            .on('mousemove', e => {
              const scale = this.getScale();

              const latlng = this.map.containerPointToLatLng({ x: e.containerPoint.x / scale, y: e.containerPoint.y / scale });
              this.m.setLatLng(latlng);
              this.l.setLatLngs([latlng, target]);

            }).on('mouseup', () => {
              this.map.off('mousemove').off('mouseup');
              this.map.dragging._draggable._enabled = true;
              this.sendCommand('videoRelocate', [this.prop.id, this.m.getLatLng().lng, this.m.getLatLng().lat]);
            })

        })


      this.l = L.polyline([latLng, target]);
    }

    getScale() {
      return 1;
    }

    sendCommand(event, params) { }

    setLatLng(lng, lat) {
      this.l.setLatLngs([{ lng: lng, lat: lat }, this.target]);
      this.m.setLatLng({ lng: lng, lat: lat });
    }

    popup(map) {
      this.map = map;

      this.m.addTo(map);
      this.l.addTo(map);
      this.on = true;

      this.addVideoComponent();

    }

    packup(map) {
      this.on = false;

      this.comp && this.comp.cleanup();
      this.comp = null;

      $(`#${this.prop.id}`).empty();

      this.m.removeFrom(map);
      this.l.removeFrom(map);
    }

    zoomIn(container, options) {
      const style = Lmapd.formatCss({
        position: 'absolute',
        left: '50%',
        top: '50%',
        zIndex: '1000 !important',
        width: `${options.width}px`,
        height: `${options.height}px`,
        marginLeft: `-${options.width / 2}px`,
        marginTop: `-${options.height / 2}px`,
        backgroundColor: 'black'
      });

      this.$zoomVideoContainer =
        $(`<div style="${style}">
          <div id="zoom-${this.prop.id}" ></div>
        </div>`)
          .on('contextmenu', () => {
            this.sendCommand('videoZoomOutById', [this.prop.id]);
          });
      $(container).append(this.$zoomVideoContainer);

      this.compZoom = this.newVideoPlayer(`zoom-${this.prop.id}`, this.prop.property.url, options.width, options.height);

    }

    zoomOut() {
      if (this.$zoomVideoContainer) {
        this.$zoomVideoContainer.remove();
      }

      if (this.compZoom) {
        this.compZoom.cleanup();
      }
    }

    addVideoComponent() {
      if (!this.comp) {
        this.comp = this.newVideoPlayer(this.prop.id, this.prop.property.url, this.prop.width, this.prop.height);
      }

    }

    newVideoPlayer(id, url, width, height) {
      let playerType = '';
      if (url.startsWith('rtsp') || url.startsWith('file')) {
        playerType = 'rtsp';
      } else if (url.startsWith('webrtc')) {
        playerType = 'webrtc';
      } else if (url.endsWith('m3u8')) {
        playerType = 'hls';
      } else if (url.startsWith('wisPlayer')) {
        playerType = 'wisPlayer'
      } else if (url.endsWith('.mp4')) {
        playerType = 'file'
      }

      const v = new VideoPlayer(`VideoPlayer-${id}`, `VideoPlayer-code-${id}`, document.getElementById(id), 1, {
        property: {
          basic: {
            frame: [0, 0, width, height]
          },
          videoOptions: {
            src: url,
            playerType: playerType,
            wisAjaxParam: playerType == 'wisPlayer' ? url.substring(12, url.length) : null
          },
          playerOptions: {
            isLoop: true
          }
        }
      });

      v.passiveLoad();

      return v;
    }

    removeVideoComponent() {
      if (!this.comp) {
        return;
      }

      //$(`#${this.prop.id}`).css('backgroundColor', 'black');

      this.comp.cleanup();
      this.comp = null;
    }

    cleanup() {
      if (this.comp) {
        this.comp.cleanup();
      }

      if (this.$zoomVideoContainer) {
        this.$zoomVideoContainer.remove();
      }

      if (this.compZoom) {
        this.compZoom.cleanup();
      }
    }
  }

  class PopupImage {
    constructor(data, target) {
      this.prop = data;
      this.target = target;

      let latLng = wktToLatLng(this.prop.popupGps, 'point');

      this.m = L.marker(latLng, { zIndexOffset: 998, draggable: false })
        .setIcon(
          L.divIcon({
            className: '',
            html: `<div id="${this.prop.id}" style="width: ${this.prop.width}px; height: ${this.prop.height}px; background-image: url(${this.prop.property.url}); background-size: 100% 100%;"></div>`,
            iconSize: [this.prop.width, this.prop.height],
            iconAnchor: [this.prop.width / 2, this.prop.height / 2]
          })
        )
        .on('contextmenu', () => {
          this.sendCommand('objectPackupByPid', [this.prop.id]);
        })
        .on('mousedown', () => {
          this.map.dragging._draggable._enabled = false;

          this.map
            .on('mousemove', e => {
              const scale = this.getScale();

              const latlng = this.map.containerPointToLatLng({ x: e.containerPoint.x / scale, y: e.containerPoint.y / scale });
              this.m.setLatLng(latlng);
              this.l.setLatLngs([latlng, target]);

            }).on('mouseup', () => {
              this.map.off('mousemove').off('mouseup');
              this.map.dragging._draggable._enabled = true;
              this.sendCommand('videoRelocate', [this.prop.id, this.m.getLatLng().lng, this.m.getLatLng().lat]);
            })

        })

      this.l = L.polyline([latLng, target]);
    }

    sendCommand(event, params) { }

    setLatLng(lng, lat) {
      this.l.setLatLngs([{ lng: lng, lat: lat }, this.target]);
      this.m.setLatLng({ lng: lng, lat: lat });
    }

    setImageUrl(url) {
      this.prop.property.url = url;

      const icon = L.divIcon({
        className: '',
        html: `<div id="${this.prop.id}" style="width: ${this.prop.width}px; height: ${this.prop.height}px; background-image: url(${url}); background-size: 100% 100%;"></div>`,
        iconSize: [this.prop.width, this.prop.height],
        iconAnchor: [this.prop.width / 2, this.prop.height / 2]
      })

      this.m.setIcon(icon);
    }

    popup(map) {
      this.map = map;

      this.m.addTo(map);
      this.l.addTo(map);

    }

    packup(map) {
      this.m.removeFrom(map);
      this.l.removeFrom(map);
    }

    zoomOut() {

    }

    zoomIn() {

    }

    cleanup() {

    }
  }

  /** 
   * 图层容器对象，包括添加，条件搜索，转换聚类，统计等
   * add by matianyu 2021/3/3
   */
  class LayerGroup {
    constructor(map) {
      this._map = map;
      // 是否开启聚类
      this._clusterMode = false;

      this._markerGroup = {};
      this._lineGroup = {};
      this._polygonGroup = {};

      this._drawLayer = [];  // 需要渲染的图层code，未在该集合内的图层不渲染

      this._markerClusterGroup = L.markerClusterGroup({ spiderfyOnMaxZoom: true, removeOutsideVisibleBounds: true });
    }

    push(layerCode, object, isDraw = true) {
      const prop = object.options.prop;
      switch (prop.type) {
        case 1: {
          if (!this._markerGroup[layerCode]) {
            this._markerGroup[layerCode] = {};
          }

          if (!this._markerGroup[layerCode][prop.id]) {
            this._markerGroup[layerCode][prop.id] = object;
          }

          break;
        }
        case 2: {
          if (!this._lineGroup[layerCode]) {
            this._lineGroup[layerCode] = {};
          }

          if (!this._lineGroup[layerCode][prop.id]) {
            this._lineGroup[layerCode][prop.id] = object;
          }

          break;
        }
        case 3: {
          if (!this._polygonGroup[layerCode]) {
            this._polygonGroup[layerCode] = {};
          }

          if (!this._polygonGroup[layerCode][prop.id]) {
            this._polygonGroup[layerCode][prop.id] = object;
          }

          break;
        }
      }

      if (isDraw) {
        this._drawLayer.push(layerCode);
      }

    }

    addToMap() {
      if (!this._clusterMode) {
        // 未开启聚类
        $.each(this._markerGroup, (i) => {
          if ($.inArray(i, this._drawLayer) >= 0) {
            $.each(this._markerGroup[i], (j) => {
              this._markerGroup[i][j].addTo(this._map);
            });
          }
        });

      } else {
        // 开启聚类  
        this._markerClusterGroup.clearLayers();

        $.each(this._markerGroup, (i) => {
          if ($.inArray(i, this._drawLayer) >= 0) {
            $.each(this._markerGroup[i], (j) => {
              this._markerGroup[i][j].packupAll(this._map);
              this._markerGroup[i][j].removeFrom(this._map);
              this._markerClusterGroup.addLayer(this._markerGroup[i][j]);
            });
          }
        });
      }

      $.each(this._polygonGroup, (i) => {
        if ($.inArray(i, this._drawLayer) >= 0) {
          $.each(this._polygonGroup[i], (j) => {
            this._polygonGroup[i][j].addTo(this._map);
          });
        }
      });

      $.each(this._lineGroup, (i) => {
        if ($.inArray(i, this._drawLayer) >= 0) {
          $.each(this._lineGroup[i], (j) => {
            this._lineGroup[i][j].addTo(this._map);
          });
        }
      });

    }

    clusterMode(bool) {
      if (this._clusterMode === bool) {
        return;
      }

      this._clusterMode = bool;
      if (bool) {
        this._markerClusterGroup.addTo(this._map);

        this._markerClusterGroup.clearLayers();

        $.each(this._markerGroup, (i) => {
          if ($.inArray(i, this._drawLayer) >= 0) {
            $.each(this._markerGroup[i], (j) => {
              this._markerGroup[i][j].packupAll(this._map);
              this._markerGroup[i][j].removeFrom(this._map);
              this._markerClusterGroup.addLayer(this._markerGroup[i][j]);
            });
          }
        });
      } else {
        this._markerClusterGroup.removeFrom(this._map);
      }

    }

    removeLayer(layerCodes) {
      for (let i = 0; i < layerCodes.length; i++) {
        const code = layerCodes[i];

        if (this._markerGroup[code]) {
          $.each(this._markerGroup[code], (j) => {
            this._markerGroup[code][j].packupAll(this._map);
            this._markerGroup[code][j].cleanVideo();
            this._markerGroup[code][j].removeFrom(this._map);
            delete this._markerGroup[code][j];
          });
          delete this._markerGroup[code];
        }

        if (this._lineGroup[code]) {
          $.each(this._lineGroup[code], (j) => {
            this._lineGroup[code][j].removeFrom(this._map);
            delete this._lineGroup[code][j];
          });
          delete this._lineGroup[code];
        }

        if (this._polygonGroup[code]) {
          $.each(this._polygonGroup[code], (j) => {
            this._polygonGroup[code][j].removeFrom(this._map);
            delete this._polygonGroup[code][j];
          });
          delete this._polygonGroup[code];
        }
      }

    }

    clearAll() {
      this._markerClusterGroup.clearLayers();

      $.each(this._markerGroup, (i) => {
        $.each(this._markerGroup[i], (j) => {
          this._markerGroup[i][j].packupAll(this._map);
          this._markerGroup[i][j].cleanVideo();
          this._markerGroup[i][j].removeFrom(this._map);
        });
      });
      this._markerGroup = {};

      $.each(this._lineGroup, (i) => {
        $.each(this._lineGroup[i], (j) => {
          this._lineGroup[i][j].removeFrom(this._map);
        });
      });
      this._lineGroup = {};

      $.each(this._polygonGroup, (i) => {
        $.each(this._polygonGroup[i], (j) => {
          this._polygonGroup[i][j].removeFrom(this._map);
        });
      });
      this._polygonGroup = {};
    }

    /* 根据id查找某一对象*/
    findById(id) {
      for (let i in this._markerGroup) {
        for (let j in this._markerGroup[i]) {
          if (j === id) {
            return this._markerGroup[i][j];
          }
        }
      }

      for (let i in this._lineGroup) {
        for (let j in this._lineGroup[i]) {
          if (j === id) {
            return this._lineGroup[i][j];
          }
        }
      }

      for (let i in this._polygonGroup) {
        for (let j in this._polygonGroup[i]) {
          if (j === id) {
            return this._polygonGroup[i][j];
          }
        }
      }

      return false;

    }

    /* 根据name查找某一对象*/
    findByName(name) {
      for (let i in this._markerGroup) {
        if ($.inArray(i, this._drawLayer) >= 0) {
          for (let j in this._markerGroup[i]) {
            if (this._markerGroup[i][j].options.prop.name === name) {
              return this._markerGroup[i][j];
            }
          }
        }
      }

      for (let i in this._lineGroup) {
        if ($.inArray(i, this._drawLayer) >= 0) {
          for (let j in this._lineGroup[i]) {
            if (this._lineGroup[i][j].options.prop.name === name) {
              return this._lineGroup[i][j];
            }
          }
        }
      }

      for (let i in this._polygonGroup) {
        if ($.inArray(i, this._drawLayer) >= 0) {
          for (let j in this._polygonGroup[i]) {
            if (this._polygonGroup[i][j].options.prop.name === name) {
              return this._polygonGroup[i][j];
            }
          }
        }
      }

      return false;

    }

    /* 根据弹窗id找到某一弹窗*/
    findPopupById(id) {
      for (let i in this._markerGroup) {
        for (let j in this._markerGroup[i]) {
          if (this._markerGroup[i][j].popupGroup) {
            const m = this._markerGroup[i][j];
            for (let k in m.popupGroup) {
              if (id === m.popupGroup[k].prop.id) {
                return m.popupGroup[k];
              }
            }
          }
        }
      }

      for (let i in this._lineGroup) {
        for (let j in this._lineGroup[i]) {
          if (this._lineGroup[i][j].popupGroup) {
            let l = this._lineGroup[i][j];
            for (let k in l.popupGroup) {
              if (id === l.popupGroup[k].prop.id) {
                return l.popupGroup[k];
              }
            }
          }

        }
      }

      for (let i in this._polygonGroup) {
        for (let j in this._polygonGroup[i]) {
          if (this._polygonGroup[i][j].popupGroup) {
            let p = this._polygonGroup[i][j];
            for (let k in p.popupGroup) {
              if (id === p.popupGroup[k].prop.id) {
                return p.popupGroup[k];
              }
            }
          }

        }
      }

    }

    getMarkerSum() {
      let count = 0;
      for (let i in this._markerGroup) {
        if ($.inArray(i, this._drawLayer) >= 0) {
          this._markerGroup[i].each(function () {
            count++;
          });
        }

      }

      return count;
    }

    getLineSum() {
      let count = 0;
      for (let i in this._lineGroup) {
        if ($.inArray(i, this._drawLayer) >= 0) {
          this._lineGroup[i].each(function () {
            count++;
          });
        }
      }

      return count;
    }

    getPolygonSum() {
      let count = 0;
      for (let i in this._polygonGroup) {
        if ($.inArray(i, this._drawLayer) >= 0) {
          this._polygonGroup[i].each(function () {
            count++;
          });
        }
      }

      return count;
    }

    /* 对象统计*/
    getObjectSum() {
      return this.getMarkerSum() + this.getLineSum() + this.getPolygonSum();
    }

    /* 遍历某一图层所有点*/
    eachMarker(layerId = null, callback) {
      if (layerId) {
        $.each(this._markerGroup[layerId], (i) => {
          callback(this._markerGroup[layerId][i]);
        });
      } else {
        $.each(this._markerGroup, (i) => {
          if ($.inArray(i, this._drawLayer) >= 0) {
            $.each(this._markerGroup[i], (j) => {
              callback(this._markerGroup[i][j]);
            });
          }
        });
      }
    }

    /* 遍历某一图层所有线*/
    eachLine(layerId = null, callback) {
      if (layerId) {
        $.each(this._lineGroup[layerId], (i) => {
          callback(this._lineGroup[layerId][i]);
        });
      } else {
        $.each(this._lineGroup, (i) => {
          $.each(this._lineGroup[i], (j) => {
            callback(this._lineGroup[i][j]);
          });
        });
      }
    }

    /* 遍历某一图层所有面*/
    eachPolygon(layerId = null, callback) {
      if (layerId) {
        $.each(this._polygonGroup[layerId], (i) => {
          callback(this._polygonGroup[layerId][i]);
        });
      } else {
        $.each(this._polygonGroup, (i) => {
          if ($.inArray(i, this._drawLayer) >= 0) {
            $.each(this._polygonGroup[i], (j) => {
              callback(this._polygonGroup[i][j]);
            });
          }
        });
      }
    }

    /* 获取已加载的图层*/
    getLayers() {
      const layers = [];

      if (Object.keys(this._markerGroup).length > 0) {
        layers.push({
          codes: Object.keys(this._markerGroup),
          type: 1
        });
      }

      if (Object.keys(this._lineGroup).length > 0) {
        layers.push({
          codes: Object.keys(this._lineGroup),
          type: 2
        });
      }

      if (Object.keys(this._polygonGroup).length > 0) {
        layers.push({
          codes: Object.keys(this._polygonGroup),
          type: 3
        });
      }

      return layers;

    }

    /* 获取所弹出的窗，用于快照存储*/
    getAllPopup() {
      const set = {};

      $.each(this._markerGroup, (i) => {
        $.each(this._markerGroup[i], (j, object) => {
          if (object.popupOn) {
            $.each(object.popupGroup, (k, popup) => {
              set[k] = popup.m.getLatLng();
            })
          }
        });
      });

      $.each(this._lineGroup, (i) => {
        $.each(this._lineGroup[i], (j, object) => {
          if (object.popupOn) {
            $.each(object.popupGroup, (k, popup) => {
              set[k] = popup.m.getLatLng();
            })
          }
        });
      });

      $.each(this._polygonGroup, (i) => {
        $.each(this._polygonGroup[i], (j, object) => {
          if (object.popupOn) {
            $.each(object.popupGroup, (k, popup) => {
              set[k] = popup.m.getLatLng();
            })
          }
        });
      });

      return set;

    }

    /* 遍历所有视频弹窗*/
    eachVideo(layerId = null, callback) {
      if (layerId) {
        $.each(this._markerGroup[layerId], (i, object) => {
          $.each(object.popupGroup, (j, popup) => {
            callback(popup);
          })
        });

        $.each(this._lineGroup[layerId], (i, object) => {
          $.each(object.popupGroup, (j, popup) => {
            callback(popup);
          })
        });

        $.each(this._polygonGroup[layerId], (i, object) => {
          $.each(object.popupGroup, (j, popup) => {
            callback(popup);
          })
        });
      } else {
        $.each(this._markerGroup, (i) => {
          if ($.inArray(i, this._drawLayer) >= 0) {
            $.each(this._markerGroup[i], (j, object) => {
              $.each(object.popupGroup, (k, popup) => {
                callback(popup);
              })
            });
          }
        });

        $.each(this._lineGroup, (i) => {
          if ($.inArray(i, this._drawLayer) >= 0) {
            $.each(this._lineGroup[i], (j, object) => {
              $.each(object.popupGroup, (k, popup) => {
                callback(popup);
              })
            });
          }
        });

        $.each(this._polygonGroup, (i) => {
          if ($.inArray(i, this._drawLayer) >= 0) {
            $.each(this._polygonGroup[i], (j, object) => {
              $.each(object.popupGroup, (k, popup) => {
                callback(popup);
              })
            });
          }
        });
      }

    }

    /* 所有对象标识为已读状态*/
    readAll() {
      for (let i in this._markerGroup) {
        for (let j in this._markerGroup[i]) {
          this._markerGroup[i][j].isNew = false;
        }

      }

      for (let i in this._lineGroup) {
        for (let j in this._lineGroup[i]) {
          this._lineGroup[i][j].isNew = false;
        }
      }

      for (let i in this._polygonGroup) {
        for (let j in this._polygonGroup[i]) {
          this._polygonGroup[i][j].isNew = false;
        }
      }

      return false;

    }

    /* 根据对象id的集合进行筛选*/
    filter(set, autoPopup) {
      if (!set || set.length === 0) {
        return;
      }

      for (let i in this._markerGroup) {
        for (let j in this._markerGroup[i]) {
          if ($.inArray(this._markerGroup[i][j].options.prop.id, set) >= 0) {
            this._markerGroup[i][j].addTo(this._map);
            autoPopup && this._markerGroup[i][j].popupAll(this._map);
          } else {
            this._markerGroup[i][j].removeFrom(this._map);
            this._markerGroup[i][j].packupAll(this._map);
          }

        }

      }


      for (let i in this._lineGroup) {
        for (let j in this._lineGroup[i]) {
          if ($.inArray(this._lineGroup[i][j].options.prop.id, set) >= 0) {
            this._lineGroup[i][j].addTo(this._map);
          } else {
            this._lineGroup[i][j].removeFrom(this._map);
          }
        }
      }

      for (let i in this._polygonGroup) {
        for (let j in this._polygonGroup[i]) {
          if ($.inArray(this._polygonGroup[i][j].options.prop.id, set) >= 0) {
            this._polygonGroup[i][j].addTo(this._map);
          } else {
            this._polygonGroup[i][j].removeFrom(this._map);
          }
        }
      }

    }

    /* 根据圆心和半径进行过滤，仅用于点集合*/
    filterByRadius(center, radius, autoPopup) {
      for (let i in this._markerGroup) {
        for (let j in this._markerGroup[i]) {
          if (this._map.distance(center, this._markerGroup[i][j].getLatLng()) <= radius) {
            this._markerGroup[i][j].addTo(this._map);
            autoPopup && this._markerGroup[i][j].popupAll(this._map);
          } else {
            this._markerGroup[i][j].removeFrom(this._map);
            this._markerGroup[i][j].packupAll(this._map);
          }

        }

      }
    }

    cleanup() {
      $.each(this._markerGroup, (i) => {
        $.each(this._markerGroup[i], (j) => {
          this._markerGroup[i][j].cleanVideo();
        });
      });
    }
  }

  /**
   * 
   * @param {*} map 
   */
  function layerGroup(map) {
    return new LayerGroup(map);
  }

  /* 地图白板*/
  class MapWhiteBoard {
    constructor(options) {
      this.$container = $(options.container);
      this.map = options.map;
      this.foldPath = options.foldPath;

      this.featureGroup = L.featureGroup().addTo(this.map);

      this.actions = [
        {
          action: 'marker',
          icon: 'marker.png'
        },
        {
          action: 'polyline',
          icon: 'polyline.png'
        },
        {
          action: 'polygon',
          icon: 'polygon.png'
        },
        // {
        //   action: 'del',
        //   icon: 'del.png'
        // },
        {
          action: 'cleanup',
          icon: 'clear.png'
        }
      ]

      this._init()
    }

    _init() {
      this.$div =
        $(`<div style="position: absolute; right: 50px; top: 50px; padding: 10px; z-index: 1000; background-color: rgba(0,0,0,0.7);" >
        ${this.actions.map((v, i) => `<img id="mapWhiteBoard-img-${v.action}" data-index=${i} src="${this.foldPath}/images/${v.icon}" style="width: 80px; height: 80px; margin-right: 20px;" />`).join('')}        
      </div>`);

      this.$container.append(this.$div);

      this._initEvent();

    }

    _initEvent() {
      this.$div.on('click', (e) => {
        if (e.target.tagName == "IMG") {
          const i = $(e.target).attr('data-index');

          this.map.off('click').off('mousemove').off('contextmenu');

          switch (this.actions[i].action) {
            case 'marker': {
              this._initMarker();
              break;
            }
            case 'polyline': {
              this._initPolyline();
              break;
            }
            case 'polygon': {
              this._initPolygon();
              break;
            }
            case 'cleanup': {
              // this._initCleanup();

              this.sendCommand("mapWhiteBoardClear", []);
              break;
            }
          }
        }
      })
    }

    _initMarker() {
      const click = (e) => {
        this.map.off('click');

        const scale = this.getScale();
        const latlng = this.map.containerPointToLatLng({ x: e.containerPoint.x / scale, y: e.containerPoint.y / scale });

        this.sendCommand("mapWhiteBoardAddMarker", [Lmapd.latLngToWKT(latlng, 'point')]);
      }


      this.map.on('click', click);

    }

    _initPolyline() {
      const latlngs = [];

      const l = L.polyline(latlngs, { weight: 5 }).addTo(this.map);

      const click = (e) => {
        const scale = this.getScale();
        const latlng = this.map.containerPointToLatLng({ x: e.containerPoint.x / scale, y: e.containerPoint.y / scale });

        latlngs.push(latlng);

        // latlngs.push(e.latlng);

        l.setLatLngs(latlngs);
      }

      const mousemove = (e) => {
        const scale = this.getScale();
        const latlng = this.map.containerPointToLatLng({ x: e.containerPoint.x / scale, y: e.containerPoint.y / scale });

        latlngs.push(latlng);

        l.setLatLngs(latlngs);

        latlngs.length = latlngs.length - 1;
      }

      const contextmenu = () => {
        this.map.off('click').off('mousemove', mousemove).off('contextmenu');

        l.removeFrom(this.map);

        this.sendCommand("mapWhiteBoardAddPolyline", [Lmapd.latLngToWKT(l.getLatLngs(), 'line')]);

      }

      this.map.on('click', click).on('mousemove', mousemove).on('contextmenu', contextmenu);

    }

    _initPolygon() {
      const latlngs = [];

      const p = L.polygon(latlngs, { weight: 5 }).addTo(this.map);

      const click = (e) => {
        const scale = this.getScale();
        const latlng = this.map.containerPointToLatLng({ x: e.containerPoint.x / scale, y: e.containerPoint.y / scale });

        latlngs.push(latlng);
        // latlngs.push(e.latlng);

        p.setLatLngs(latlngs);
      }

      const mousemove = (e) => {
        const scale = this.getScale();
        const latlng = this.map.containerPointToLatLng({ x: e.containerPoint.x / scale, y: e.containerPoint.y / scale });

        latlngs.push(latlng);
        // latlngs.push(e.latlng);

        p.setLatLngs(latlngs);

        latlngs.length = latlngs.length - 1;
      }

      const contextmenu = () => {
        this.map.off('click').off('mousemove', mousemove).off('contextmenu');

        p.removeFrom(this.map);

        this.sendCommand("mapWhiteBoardAddPolygon", [Lmapd.latLngToWKT(p.getLatLngs()[0], 'polygon')]);
      }

      this.map.on('click', click).on('mousemove', mousemove).on('contextmenu', contextmenu);
    }

    _initCleanup() {
      for (let each of this.featureGroup) {
        each.removeFrom(this.map);
      }

      this.featureGroup.length = 0;
    }

    add(object) {
      this.featureGroup.addLayer(object);
    }

    getAll() {
      const latlngs = [[], [], []];
      this.featureGroup.eachLayer(layer => {
        if (layer.options.type == 'point') {
          latlngs[0].push(latLngToWKT(layer.getLatLng(), 'point'));
        }

        if (layer.options.type == 'line') {
          latlngs[1].push(latLngToWKT(layer.getLatLngs(), 'line'));
        }

        if (layer.options.type == 'polygon') {
          latlngs[2].push(latLngToWKT(layer.getLatLngs()[0], 'polygon'));
        }
      })

      return JSON.stringify(latlngs);
    }

    load(data) {
      data = JSON.parse(data);

      for (let each of data[0]) {
        const m = L.marker(Lmapd.wktToLatLng(each, 'point'), { icon: L.icon({ iconUrl: `${this.foldPath}/images/marker-icon-2x.png`, iconSize: [80, 80], type: 'point' }) });

        this.add(m);
      }

      for (let each of data[1]) {
        const l = L.polyline(Lmapd.wktToLatLng(each, 'line'), { type: 'line' });

        this.add(l);
      }

      for (let each of data[2]) {
        const p = L.polygon(Lmapd.wktToLatLng(each, 'polygon'), { type: 'polygon' });

        this.add(p);
      }

    }

    getScale() {
      return 1;
    }

    clear() {
      this.featureGroup.clearLayers();
    }

    sendCommand() {

    }


  }

  function mapWhiteBoard(options) {
    return new MapWhiteBoard(options);
  }

  class ColorSequence {
    constructor() {
      this.colors =
        [
          '#ea5514'
          , '#66ff00'
          , '#33ffff'
          , '#6699ff'
          , '#c9a063'
          , '#e60012'
          , '#727171'
          , '#e61673'
          , '#f39800'
        ];

      this.seq = this.colors.length;
    }

    getColor() {
      this.seq++;

      if (this.seq >= this.colors.length) {
        this.seq = 0;
      }

      return this.colors[this.seq];
    }
  }
  const colorSequence = new ColorSequence();

  /**
   * 
   */
  function getColor() {
    return colorSequence.getColor();
  }

  /**
   * 
   * 
   */
  function uuid() {
    const s = [];
    const hexDigits = "0123456789abcdef";
    for (let i = 0; i < 36; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
    }
    // bits 12-15 of the time_hi_and_version field to 0010
    s[14] = "4";
    // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
    s[8] = s[13] = s[18] = s[23] = "-";

    const uuid = s.join("");
    return uuid;
  }

  /**
   * 坐标系转换
   * @param {*} method 
   * @param {*} lng 
   * @param {*} lat 
   */
  function coordinateTransfrom(method, lng, lat) {
    const xPI = 3.14159265358979324 * 3000.0 / 180.0;
    const PI = 3.1415926535897932384626;
    const a = 6378245.0;
    const ee = 0.00669342162296594323;

    const transformlat = function (lng, lat) {
      let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
      ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
      ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0;
      ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0;
      return ret;
    };

    const transformlng = function (lng, lat) {
      let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
      ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
      ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0;
      ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0;
      return ret;
    };

    // 判断是否在国内，不在国内则不做偏移
    const outOfChina = function (lng, lat) {
      return (lng < 72.004 || lng > 137.8347) || ((lat < 0.8293 || lat > 55.8271) || false);
    };

    switch (method) {
      // BD-09转GCJ-02
      case "bd09togcj02": {
        const xPI = 3.14159265358979324 * 3000.0 / 180.0;
        const x = lng - 0.0065;
        const y = lat - 0.006;
        const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * xPI);
        const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * xPI);
        const ggLng = z * Math.cos(theta);
        const ggLat = z * Math.sin(theta);

        return { lng: ggLng, lat: ggLat };
      }
      // GCJ-02转BD-09
      case "gcj02tobd09": {
        const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * xPI);
        const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * xPI);
        const bdLng = z * Math.cos(theta) + 0.0065;
        const bdLat = z * Math.sin(theta) + 0.006;
        return { lng: bdLng, lat: bdLat };
      }
      // WGS84转GCj02
      case "wgs84togcj02": {
        if (outOfChina(lng, lat)) {
          return { lng: lng, lat: lat };
        }

        let dLat = transformlat(lng - 105.0, lat - 35.0);
        let dLng = transformlng(lng - 105.0, lat - 35.0);
        const radLat = lat / 180.0 * PI;
        let magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        let sqrtmagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);
        dLng = (dLng * 180.0) / (a / sqrtmagic * Math.cos(radLat) * PI);
        const mgLat = lat + dLat;
        const mglng = lng + dLng;
        return { lng: mglng, lat: mgLat };

      }
      // GCJ02转WGS84
      case "gcj02towgs84": {
        if (outOfChina(lng, lat)) {
          return { lng: lng, lat: lat };
        }

        let dLat = transformlat(lng - 105.0, lat - 35.0);
        let dLng = transformlng(lng - 105.0, lat - 35.0);
        const radLat = lat / 180.0 * PI;
        let magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        const sqrtmagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);
        dLng = (dLng * 180.0) / (a / sqrtmagic * Math.cos(radLat) * PI);
        const mgLat = lat + dLat;
        const mglng = lng + dLng;
        return { lng: lng * 2 - mglng, lat: lat * 2 - mgLat };

      }
    }
  }

  /**
   * 
   * @param {*} jsonData 
   */
  function formatCss(jsonData) {
    let style = "";
    $.each(jsonData, (key, value) => {
      const chars = key.split('');

      let cssKey = "";
      for (let i in chars) {
        if (isUpperCase(chars[i])) {
          cssKey += `-${chars[i].toLowerCase()}`;
        } else {
          cssKey += chars[i];
        }
      }

      style += `${cssKey}: ${value}; `;

    });

    return style;
  }

  /**
   * 判断是否是大写字母
   * add by matianyu 2021/12/8
   * @param {*} px 
   * @param {*} py 
   * @param {*} mx 
   * @param {*} my 
   */
  function isUpperCase(ch) {
    return ch >= 'A' && ch <= 'Z'
  }

  /**
   * 根据两点计算角度，与线方向相关，获得结果未0点为起点的顺时针角度
   * add by matianyu 2021/3/3
   * @param {*} px 
   * @param {*} py 
   * @param {*} mx 
   * @param {*} my 
   */
  function getAngle(px, py, mx, my) {
    if (px === mx && py === my) {
      return 0;
    }

    // 在X轴上
    if (py === my && mx > px) {
      return 90;
    } else if (py === my && mx < px) {
      return 270;
    }

    // 在Y轴上
    if (px === mx && my > py) {
      return 0;
    } else if (px === mx && my < py) {
      return 180;
    }

    // 斜边长
    let len3 = Math.sqrt(Math.pow((mx - px), 2) + Math.pow((my - py), 2));

    // 第一象限
    if (mx > px && my > py) {
      return Math.asin(Math.abs(mx - px) / len3) / (Math.PI / 180);
    }

    // 第二象限
    if (mx > px && my < py) {
      return 90 + Math.acos(Math.abs(mx - px) / len3) / (Math.PI / 180);
    }

    // 第三象限
    if (mx < px && my < py) {
      return 180 + Math.asin(Math.abs(mx - px) / len3) / (Math.PI / 180);
    }

    // 第四象限
    if (mx < px && my > py) {
      return 270 + Math.acos(Math.abs(mx - px) / len3) / (Math.PI / 180);
    }

    return 0;

  }

  /**
   * 
   * @param {*} wkt 
   * @param {*} type 
   */
  function wktToLatLng(wkt, type) {
    switch (type) {
      case 'point': {
        // 从属性中解析经纬度
        let latlngstr = wkt.slice(6, wkt.length - 1).split(' ');
        return { lng: latlngstr[0], lat: latlngstr[1] };
      }
      case 'line': {
        return wkt.slice(11, wkt.length - 1).split(',').map(function (str) {
          return { lng: str.split(' ')[0], lat: str.split(' ')[1] };
        });
      }
      case 'polygon': {
        return wkt.slice(9, wkt.length - 2).split(',').map(function (str) {
          return { lng: str.split(' ')[0], lat: str.split(' ')[1] };
        });
      }
    }
  }

  /**
   * 
   * @param {*} latlng 
   * @param {*} type 
   */
  function latLngToWKT(latlng, type) {
    switch (type) {
      case 'point': {
        return `POINT(${latlng.lng} ${latlng.lat})`;
      }
      case 'line': {
        return "LINESTRING(" + latlng.map(function (each) { return `${each.lng} ${each.lat}`; }) + ')';
      }
      case 'polygon': {
        return "POLYGON((" + latlng.map(function (each) { return `${each.lng} ${each.lat}`; }) + `,${latlng[0].lng} ${latlng[0].lat}))`;
      }
    }
  }

  class MapWebSocket {
    constructor(ws) {
      if (!ws) { return; }

      this.rWebSock = new ReconnectingWebSocket(ws, null, {
          debug: false,
          automaticOpen: true,
          reconnectInterval: 10000,
          maxReconnectInterval: 60000,
          reconnectDecay: 1.5,
            timeoutInterval: 50000
      });
      this.rWebSock.onopen = function () {
        console.log("map data WS opened!");
      };

      this.stompClient = Stomp.over(this.rWebSock);

      this.topics = {};
    }

    sendMsg(destination, { }, data) {
      this.stompClient.send(destination, {}, data);
    }

    send(message) {
      this.stompClient.send(message);
    }

    /* 多订阅，带回调连接，回调内依次订阅*/
    connect(callback) {
      this.stompClient.connect({}, success => {
        console.log("map data service connection succeeded", success);

        callback && callback();
      }, error => {
        console.warn("map data service connection failed", error);
      }
      );
    }

    /* 连接成功后进行订阅 */
    subscribeTopic(topic, callback) {
      this.topics[topic] = this.stompClient.subscribe(topic, callback);
    }

    /* 取消topic订阅*/
    unsubscribeData(topic) {
      try {
        this.topics[topic].unsubscribe(function () {
          console.log("map data ws stomp unsubscribe success");
        });
      } catch (e) {
        console.warn('map data ws stomp unsubscribe error');
      } finally {
        return;
      }
    }

    /* 清理所有订阅*/
    clear() {
      for (let topic in this.topics) {
        // if (this.topics.hasOwnProperty(topic)) {
        this.unsubscribeData(topic);
        // }
      }

      this.stompClient.connected && this.stompClient.disconnect();
      this.rWebSock && this.rWebSock.disconnect();
    }
  }

  /**
   * 
   * @param {*} ws 
   */
  function mapWebSocket(ws) {
    return new MapWebSocket(ws);
  }

  /**
   * 
   * @param {*} container 
   * @param {*} content 
   * @param {*} options 
   */
  function popupWin(container, content, options) {
    const cssStr = formatCss({
      width: options.width,
      height: options.height,
      position: 'absolute',
      top: '50%',
      left: '50%',
      marginLeft: options.width / 2,
      marginTop: options.height / 2,
      zIndex: 9999,
      backgroundColor: 'white'
    });

    let $popupWin = null;
    if ($('#popup-window-on-map').length === 0) {
      $popupWin =
        $(`<div id="popup-window-on-map" style="${cssStr}">${content}</div>`)
          .on('contextmenu', function () {
            $(this).remove();
          });
    } else {
      $popupWin = $('#popup-window-on-map').empty().append(content);
    }

    $(container).append($popupWin);
  }

  function packupWin() {
    if ($('#popup-window-on-map').length !== 0) {
      $('#popup-window-on-map').remove();
    }
  }

  function rectRelation(rect1, rect2) {
    if ((rect2[0] + rect2[2]) <= rect1[0] || rect2[0] >= (rect1[0] + rect1[2]) || (rect2[1] + rect2[3]) < rect1[1] || rect2[1] >= (rect1[1] + rect1[3])) {
      return false;
    }

    return true;
  }

  /* 根据距离起点的两段距离，计算距离间的经纬度*/
  function clipLineByDistance(latlngs, start, end, map) {
    if (start >= end || start < 0) {
      return;
    }

    let length = 0, rlatlngs = [], i = 0;
    for (i = 0; i < latlngs.length - 1; i++) {
      const nextStep = map.distance(latlngs[i], latlngs[i + 1])

      if (start >= length && start <= nextStep) {
        rlatlngs.push(Lmapd.getLatLngByDistance(latlngs[i], latlngs[i + 1], start - length, map));
      }

      if ((length + nextStep) >= start && (length + nextStep) <= end) {
        rlatlngs.push(latlngs[i + 1])
      } else if ((length + nextStep) > end) {
        rlatlngs.push(Lmapd.getLatLngByDistance(latlngs[i], latlngs[i + 1], end - length, map));
        break;
      }

      length += nextStep;

    }

    return rlatlngs;
  }

  /* 获取距离起点的指定长度的经纬度*/
  function getLatLngByDistance(latlng1, latlng2, distance, map) {
    latlng1 = { lng: parseFloat(latlng1.lng), lat: parseFloat(latlng1.lat) };
    latlng2 = { lng: parseFloat(latlng2.lng), lat: parseFloat(latlng2.lat) };

    const longLength = map.distance(latlng1, latlng2),
      yoffset = (latlng2.lat - latlng1.lat) / longLength * distance,
      xoffset = (latlng2.lng - latlng1.lng) / longLength * distance;

    return { lng: latlng1.lng + xoffset, lat: latlng1.lat + yoffset };

  }

  function deepCopy(obj) {
    let objClone = Array.isArray(obj) ? [] : {};

    if (obj && typeof obj === "object") {
      for (var key in obj) {
        if (obj.hasOwnProperty(key)) {
          //判断ojb子元素是否为对象，如果是，递归复制
          if (obj[key] && typeof obj[key] === "object") {
            objClone[key] = deepCopy(obj[key]);
          } else {
            //如果不是，简单复制
            objClone[key] = obj[key];
          }
        }
      }
    }

    return objClone;
  }


  window.Lmapd = exports;

  exports.version = version;

  exports.mapWhiteBoard = mapWhiteBoard;
  exports.layerGroup = layerGroup;
  exports.wktToLatLng = wktToLatLng;
  exports.latLngToWKT = latLngToWKT;
  exports.getAngle = getAngle;
  exports.formatCss = formatCss;
  exports.coordinateTransfrom = coordinateTransfrom;
  exports.uuid = uuid;
  exports.getColor = getColor;
  exports.mapWebSocket = mapWebSocket;

  exports.markerFactory = markerFactory;
  exports.lineFactory = lineFactory;
  exports.polygonFactory = polygonFactory;
  exports.movingMarkerFactory = movingMarkerFactory;

  exports.popupWin = popupWin;
  exports.packupWin = packupWin;

  exports.mapWhiteBoard = mapWhiteBoard;

  exports.clipLineByDistance = clipLineByDistance;
  exports.getLatLngByDistance = getLatLngByDistance;

  exports.rectRelation = rectRelation;
  exports.deepCopy = deepCopy;


})));
