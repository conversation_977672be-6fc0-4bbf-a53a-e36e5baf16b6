<!--
 * @Author: 陆晓夫
 * @Date: 2022-03-01 10:22:52
 * @LastEditors: 陆晓夫
 * @LastEditTime: 2022-05-26 17:26:25
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <link rel="stylesheet" type="text/css" href="v1.0/nariTopoView.css" />
    <link
      rel="stylesheet"
      type="text/css"
      href="../htmlViewer/v1.0/htmlViewer.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="../baseComponents/css/fonts.css"
    />
  </head>
  <body style="background-color: #000000">
    <div id="con" style="width: 10200px; height: 3150px"></div>
    <script src="../../WisVisual/libs/d3.v5.min.js"></script>
    <script src="../../WisVisual/libs/anime.min.js"></script>
    <script src="../../WisVisual/libs/jquery.min.js"></script>
    <script src="../../WisVisual/libs/lodash.min.js"></script>
    <script src="../../WisVisual/Util/CompUtil.js"></script>
    <script src="../../WisVisual/Util/Util.js"></script>
    <!-- <script src="../../WisVisual/libs/reconnecting-websocket.min.js"></script> -->
    <!-- <script src="../../WisVisual/libs/stomp.js"></script> -->
    <!-- <script src="../../WisVisual/API/API.js"></script> -->
    <script src="../base/optionType.js"></script>
    <script src="../base/componentBase.js"></script>
    <!-- <script src="../imageView/v1.0/imageView.js"></script> -->
    <!-- <script src="../powerPlantStateBoard/v1.0/powerPlantStateBoard.js"></script> -->
    <!-- <script src="../cylinderBoard/v1.0/cylinderBoard.js"></script> -->
    <!-- <script src="../digitGearBoard/v1.0/digitGearBoard.js"></script> -->
    <script src="../htmlViewer/v1.0/htmlViewer.js"></script>
    <script src="v1.0/nariTopoView.js"></script>
    <script>
      let mapId = getUrlParam("mapId");
      let w = getUrlParam("w");
      let h = getUrlParam("h");

      // window.initAPIPromise.then(() => {
      draw();
      // });

      function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
      }
      function draw() {
        window.currentSceneId = "225";
        console.log(mapId);
        if (mapId === null) {
          mapId = "X32lyZsUrR";
        }
        w = 10860;
        h = 4000;
        window.comp = new NariTopoView(
          "858",
          "123",
          document.getElementById("con"),
          0,
          {
            property: {
              basic: {
                frame: [0, 0, w, h],
              },
              viewSetting: {
                loadMapId: mapId,
                w: w,
                h: h,
                isShow500KV: false,
              },
              isTest: true,
            },
          }
        );
      }
    </script>
  </body>
</html>
