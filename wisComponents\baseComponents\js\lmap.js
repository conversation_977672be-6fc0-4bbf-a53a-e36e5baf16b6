/*
 * @Descriptoin: 地图基类组件，实现了地图通用的基本功能
 * @Version: 2.0.18
 * @Author: mat<PERSON><PERSON>
 * @Date: 2021-06-08 09:24:47
 * @LastEditors: matianyu
 * @LastEditTime: 2025-07-25 11:57:54
 */

/* global OptionType Lmapd L MapWidget turf*/
/* exported Lmap */
class Lmap extends MapWidget {
  constructor(id, code, container, workMode, option = {}, useDefaultOpt = true) {
    super(id, code, container, workMode, option, useDefaultOpt);

    this.version = {
      jsonModelVer: '1.5.2'
    };

    this.containerScale = 1;
    this._self = Lmapd.uuid();
    this._config = this.property.config;
    this._foldPath = WisUtil.scriptPath('lmap');  // 组件绝对路径
    this._viewBox = null;

    if (window.commandType !== 'hs' && window.commandType !== "" && window.commandType !== "dd" && window.commandType !== "dp") {
      this._draw();
    }

  }

  _draw() {
    super._draw();

    if (this.workMode === 2) {
      $(this.container).empty().append(`<div id="map-placeHolder" style="width: ${this.property.basic.frame[2]}px; height: ${this.property.basic.frame[3]}px;"></div>`);

      return;
    }

    // 待容器创建完成
    const waitContainer = setInterval(() => {
      if (document.getElementById(`lmap-${this.id}`) !== null) {
        clearInterval(waitContainer);

        this._drawMap();
      }
    }, 200);

  }

  _drawMap() {
    this._minZoom = 1;
    this._maxZoom = 18;

    if (this._config.boundaryControl) {  // 开启边界控制
      this._minZoom = this.property.center.zoom;
      this._maxZoom = this._config.maxZoom;
    }

    if(this.property.config.tileOrigin) {
      this._tile = {
        tileLayer: L.tileLayer(`${window.origin}/bigemap.dc-satellite/tiles/{z}/{x}/{y}.jpg?access_token=pk.eyJhIjoiMjl3N2FhcjQ0bDhmZ3Q5OXpqNHU1dmE3cyIsImV4cCI6MTc1MjkwOTAxMCwidSI6IjE5OTAwMDAwMDAwMCJ9.CdJ2rpSyTDzRZ1STPwMwwg`, {
          minZoom: this._minZoom,
          maxZoom: this._maxZoom,
          zIndex: 0
        }),
        name: this._config.tile
      };
    } else {
      this._tile = {
        tileLayer: L.tileLayer(`${window.origin}/tiles/${this._config.tile}/{z}/{x}/{y}.png`, {
          minZoom: this._minZoom,
          maxZoom: this._maxZoom,
          zIndex: 0
        }),
        name: this._config.tile
      };
    }     

    // 初始化地图
    this.map = L.map(`lmap-${this.id}`, {
      center: [this.property.center.lat, this.property.center.lng],
      zoom: this.property.center.zoom,
      layers: [this._tile.tileLayer],
      zoomControl: false,
      doubleClickZoom: false,
      //dragging: false,   // 分屏模式下要关闭改项
      // scrollWheelZoom: false,  // 关闭原本的滚轮缩放功能
      attributionControl: false
    });

    if(this._config.terminatorLayer) {
      this._terminatorLayer = L.terminator({className: "terminator-style"}).addTo(this.map);
    }

    // 记录当前层级
    this._zoom = this.property.center.zoom;

    // 初始化边界控制, 边界放大一点，防止地图初始抖动
    const leftTop = this.map.containerPointToLatLng({ x: -100, y: -100 }), lightBottom = this.map.containerPointToLatLng({ x: this.property.basic.frame[2] + 100, y: this.property.basic.frame[3] + 100 });

    if (this._config.boundaryControl) {  // 边界限值开启
      this.map.setMaxBounds(L.latLngBounds(leftTop, lightBottom));
    }

    // 地图对象集合
    this._objectLayer = Lmapd.layerGroup(this.map);

    this._gsLayer = L.layerGroup().addTo(this.map);

    if (this.property.tool.whiteBoard) {
      this.mapWhiteBoard = Lmapd.mapWhiteBoard({
        map: this.map,
        foldPath: this._foldPath,
        container: this.container
      });

      this.mapWhiteBoard.sendCommand = this._sendCommand.bind(this);
      this.mapWhiteBoard.getScale = this.getScale.bind(this);

      if (this.snapShotWhiteBoard && this.snapShotWhiteBoard.length > 0) {
        this.mapWhiteBoard.load(this.snapShotWhiteBoard);
      }
    }

    // 创建缩放追踪、指南针
    this._mapTool();

    // 格式化图层配置
    this._layerConfigFormat();

    if(this._config.snapShot) {
      this.loadSnapshot();
    } else {
      this._initData();
    }
    

  }

  /* 生成地图容器，添加分辨率控制*/
  _generateBasicDIV() {
    const style = Lmapd.formatCss({
      width: `${this.property.basic.frame[2]}px`,
      height: `${this.property.basic.frame[3]}px`
    });

    this.$container = $(this.container).attr('class', 'lmap-div-container').css("pointer-events", "auto").html(`<div id="lmap-${this.id}" style="${style}"></div>`);
  }

  /* 初始化数据订阅*/
  _initData(layers) {
    this.map.on('movestart', this.mapMoveStart, this).on('moveend', this.mapMoveEnd, this);

    // 判断是否有为进行完的动画
    if (!window.sceneHasEnterAnimation) {
      // 数据订阅
      (async () => {
        await this._subscribePropare();
        this._initWsData(layers);
      })();
    }

    // 开启多屏同步
    if (this._config.multiStation) {
      this._multiScreenSynchronization();
    }

    setTimeout(() => {
      this._updateGsLayer();
    }, 10000);


  }

  _updateGsLayer() {
    this._gsLayer.clearLayers();

    if (this._gslayerLoad && this._gslayerLoad[this.map.getZoom()] && this._gslayerLoad[this.map.getZoom()].length > 0) {
      for (let each of this._gslayerLoad[this.map.getZoom()]) {
        L.tileLayer.wms("/geoserver/wiscom/wms", {
          layers: each,
          format: 'image/png',
          crs: L.CRS.EPSG4326,
          transparent: true,
          zIndex: 5
        }).addTo(this._gsLayer);
      }

    }
  }

  /**
   * 地图移动开始前的执行的函数，
   * 子类中绑定地图移动后事件需重写此方法，
   * 请勿再次再map对象中绑定事件
   * add by matianyu 2021/3/3
   */
  mapMoveStart() {
    this._zoom = this.map.getZoom();  // 移动前刷新当前层级
  }

  /**
   * 地图移动结束后的执行的函数，
   * 子类中绑定地图移动后事件需重写此方法，
   * 请勿再次再map对象中绑定事件
   * add by matianyu 2021/3/3
   */
  mapMoveEnd() {
    if (this._zoom == this.map.getZoom()) {  // 如果层级未发生变化，进行增量加载
      this._newRequest();
    } else {   // 如果移动后层级发生变化，则清空对象，重新加载
      this._objectLayer.clearAll();

      this._newRequest();

      setTimeout(() => {
        this._updateGsLayer();
      }, 7000);

    }
   

  }

  _subscribePropare() {
    return new Promise(function (resolve, reject) {
      resolve();
    })
  }

  /**
   * 分批加载图层订阅服务
   * add by matianyu 2021/3/3
   */
  _initWsData(layers) {
    if (!this._config.layerSubscribe) {
      return;
    }

    const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
    this._ws = Lmapd.mapWebSocket(`${protocol}://${window.location.host}/gisSocket`);

    this._ws.connect(() => {
      this._ws.subscribeTopic(`/topic/${window.currentSceneId}${this.property.basic.code}`, (data) => {
        data = JSON.parse(data.body);

        // 请求辨别
        // if (data.flag !== this._request.flag) { return; }

        // 首批收包
        if (this._request.status === 0) {
          // 记录包总数
          this._request.page = data.page;

          if (this._request.page <=1) {
            this._request.done = true;
          }

          // 推包入组
          this._request.dataGroup.push(data);
          // 修改收包记录
          this._request.record[data.index] = 1;

          // 是否收到最后一个包
          if (data.index === (data.page - 1)) {
            // 查找请求缺失的包
            for (let i = 0; i < this._request.page; i++) {
              if (this._request.record[i] !== 1) {
                // 发送补包请求
                this._requestData(i);
                return;
              }
            }

            // 没有缺失，收包结束
            this._request.done = true;
          }
        } else {
          // 补包
          // 判断是否是已经收到的包
          if (this._request.record[data.index] !== 1) {
            this._request.dataGroup.push(data);
            this._request.record[data.index] = 1;
          }

          // 查找请求缺失的包
          for (let i = 0; i < this._request.page; i++) {
            if (this._request.record[i] !== 1) {
              // 发送补包请求
              this._requestData(i);
              return;
            }
          }
          this._request.done = true;
        }

      });

      this._newRequest(layers);

    });

  }

  /**
   * 发送图层加载请求消息，并启动绘制
   * add by matianyu 2021/3/3
   */
  _newRequest(layers) {
    if (!this._config.layerSubscribe) {
      return;
    }

    // 请求数据记录，用于缓存数据，记录收发包状态等
    this._request = {
      flag: Lmapd.uuid(),  // 单次请求标志      
      page: 0,        // 总索引      
      status: -1,     // 收包状态，-1未发消息，0首批收包，1~补包      
      record: [],     // 收包索引记录      
      dataGroup: [],  // 收包数组      
      done: false     // 完成标志
    };

    // 发送数据请求
    if (!this._requestData(-1, layers)) {
      return false;
    }

    // 启动绘制
    // 若前次请求定时器还存在，则清除并置空
    if (this._drawInterval) {
      // 清除计时器
      clearInterval(this._drawInterval);
      // 计时器变量置空
      this._drawInterval = null;
    }

    let _index = 0, _markerSum = 0, _clusterMode = false;
    this._drawInterval = setInterval(() => {
      // 接收到完成消息，并且绘制完最后一组，或者返回没有数据，page为0
      if (this._request.done && (this._request.page <= 1 || _index === (this._request.page - 1))) {
        clearInterval(this._drawInterval);
        this._drawInterval = null;
      }

      if (this._request.dataGroup[_index]) {
        // 增量绘制对象
        $.each(this._request.dataGroup[_index].data, (key, value) => {
          if (!this._objectLayer.findById(value.id)) {  // 若对象不存在则创建
            let object = null;
            switch (value.type) {
              case 1: {
                object = Lmapd.markerFactory(value);
                object.sendCommand = this._sendCommand.bind(this);

                this._updateMarkerIcon(object);

                _markerSum++;
                break;
              }
              case 2: {
                object = Lmapd.lineFactory(value);
                break;
              }
              case 3: {
                object = Lmapd.polygonFactory(value);
                break;
              }
              default: {
                console.log("非法类型");
                return;
              }
            }

            $.inArray(value.layerCode, this._layerLoadDraw)


            this._objectLayer.push(value.layerCode, object, $.inArray(value.layerCode, this._layerLoadDraw) >= 0);
          }


        });

        // 如果聚类开启，达到聚类层级或者达到聚类数量，则切换到聚类模式
        if (this.property.cluster.on && this.map.getZoom() < this.property.cluster.zoom && _markerSum >= this.property.cluster.number) {
          this._objectLayer.clusterMode(true);
          _clusterMode = true;
        }
        this._objectLayer.addToMap();

        // 绘制弹窗
        $.each(this._request.dataGroup[_index].dataPopup, (key, value) => {
          const object = this._objectLayer.findById(value.objectId);
          /**
           * 1.如果当前对象为增量需要判断弹窗是否需要弹出;
           * 2.如果存在快照数据说明是初始化数据加载，是否弹出弹窗来源于快照；
           * 3.如果没有快照数据说明为使用中的对象初次被加载，是否弹出弹窗来源于配置工具
           * 4.如果当前对象为非增量对象则无需再次绑定弹窗
           * */
          if (object.isNew) {
            const popup = object.bindPopup(value);
            popup.getScale = this.getScale.bind(this);

            if (this.snapShotObjectPopup && this.snapShotObjectPopup[value.id]) {
              popup.setLatLng(this.snapShotObjectPopup[value.id].lng, this.snapShotObjectPopup[value.id].lat);

              object.popupAll(this.map);
            } else if (value.visible && !_clusterMode) {
              object.popupAll(this.map);
            }

          }

        });

        this.snapShotObjectPopup = null;

        _index++;
      }

      if (this._request.done && (_index === (this._request.page) || this._request.page == 0)) {
        this._objectLayer.readAll();

        if (this._featureFilterSet) {
          this._objectLayer.filter(this._featureFilterSet);
        }

        if (this._featureFilterCenter && this._featureFilterRadius) {
          this._objectLayer.filterByRadius(this._featureFilterCenter, this._featureFilterRadius);
        }

        this.saveSnapshot();

        this._loadDataCallback();
      }

      // 绘制间隔，尽量保证每组绘制完成再绘制下一组
    }, 1000);

  }

  /**
   * 组装参数，发包，若传入图层参数则加载对应图层，未传入图层参数则根据图层配置加载
   * add by matianyu 2021/3/3
   */
  _requestData(offset, layers) {
    // 如果没有请求图层，则按照配置加载
    if (!layers) {
      layers = [];

      const zoom = this.map.getZoom();

      if (this._layerLoad[zoom]) {
        if (this._layerLoad[zoom].point) {
          layers.push({
            codes: this._layerLoad[zoom].point,
            type: 1
          });
        }

        if (this._layerLoad[zoom].polygon) {
          layers.push({
            codes: this._layerLoad[zoom].polygon,
            type: 3
          });
        }

        if (this._layerLoad[zoom].line) {
          layers.push({
            codes: this._layerLoad[zoom].line,
            type: 2
          });
        }

      }

      // 无图层需要加载时返回
      if (layers.length === 0) {
        return false;
      }
    }

    this._request.status++;
    try {
      this._ws.sendMsg('/app/wisGis/loadLayer', {},
        JSON.stringify({
          flag: this._request.flag,
          topic: `/topic/${window.currentSceneId}${this.property.basic.code}`,
          layers: layers,
          zoom: this._config.dynamicZoom ? this.map.getZoom() : 18 ,
          bounds: this._getBounds(),
          offset: offset,
          size: 500
        }));
    } catch (e) {
      console.warn("发送图层请求异常！");
      return false;
    }

    return true;
  }

  // 订阅数据加载完成回调
  _loadDataCallback() {
    if (this._viewBox) {
      this._objectLayer.eachVideo(null, (v) => {
        if (!v.on) {
          return;
        }

        const pos = v.map.latLngToContainerPoint(v.m.getLatLng()), width = parseFloat(v.prop.width), height = parseFloat(v.prop.height);

        if (!Lmapd.rectRelation(this._viewBox, [pos.x - width / 2, pos.y - height / 2, width, height])) { // 不相交
          v.removeVideoComponent();
        } else {  // 相交
          v.addVideoComponent();
        }

      });

    }

  }

  _updateMarkerIcon(object) { }

  _featureFilter(set, autoPopup) {
    this._featureFilterCenter = null;
    this._featureFilterRadius = null;

    this._featureFilterSet = set.split(',');

    this._objectLayer.filter(this._featureFilterSet, autoPopup);

    this.saveSnapshot();
  }

  _featureFilterByRadius(lng, lat, radius, autoPopup) {
    this._featureFilterSet = null;

    this._featureFilterCenter = { lng: lng, lat: lat };
    this._featureFilterRadius = radius;

    this._objectLayer.filterByRadius(this._featureFilterCenter, this._featureFilterRadius, autoPopup);

    this.saveSnapshot();
  }

  _featureFilterOff() {
    this._featureFilterCenter = null;
    this._featureFilterRadius = null;
    this._featureFilterSet = null;

    this._objectLayer.addToMap();

    this.saveSnapshot();
  }

  // 组件属性
  _initProperty() {
    super._initProperty();

    const _layerConfig = {
      layerCode: {
        displayName: "图层Code",
        type: OptionType.string,
        value: "",
        options: "",
        isVisible: true
      },
      layerType: {
        displayName: "图层类型",
        type: OptionType.enum,
        value: "point",
        options: [
          { value: 'point', name: "点" },
          { value: 'line', name: "线" },
          { value: 'polygon', name: "面" }
        ],
        isVisible: true
      },
      zoom: {
        displayName: "加载层级",
        type: OptionType.multipleEnum,
        value: 1,
        options: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
        isVisible: true
      },
      platForm: {
        displayName: "发布平台",
        type: OptionType.enum,
        value: 'wisGis',
        options: [
          { value: 'wisGis', name: "wisGis" },
          { value: 'geoserver', name: "geoserver" }
        ],
        isVisible: true
      },
      isDraw: {
        displayName: "是否渲染",
        type: OptionType.boolean,
        value: true,
        options: "",
        isVisible: true
      }
    };

    const options = {
      basic: {
        className: 'Lmap'
      },
      center: {
        lng: 118.776969,
        lat: 32.043004,
        zoom: 12
      },
      config: {
        tileOrigin: false,
        tile: 'terrain',
        containerScale: 1,
        boundaryControl: true,
        maxZoom: 18,
        layerConfig: [],
        layerSubscribe: true,
        multiStation: true,
        dynamicZoom: false,
        snapShot: false,
        terminatorLayer: false
      },
      cluster: {
        on: false,
        zoom: 17,
        number: 500
      },
      video: {
        zoomWidth: 1920,
        zoomHeight: 1080
      },
      tool: {
        whiteBoard: false,
        compass: false,
        compassScale: 1
      }
    };

    const optionDic = [
      {
        name: 'center',
        displayName: '初始位置',
        description: '初始位置',
        children: [
          {
            name: 'lng',
            displayName: '经度',
            description: '初始经度',
            type: OptionType.double,
            show: true,
            editable: true
          }, {
            name: 'lat',
            displayName: '纬度',
            description: '初始纬度',
            type: OptionType.double,
            show: true,
            editable: true
          }, {
            name: 'zoom',
            displayName: '层级',
            description: '初始层级',
            type: OptionType.int,
            show: true,
            editable: true
          }
        ]
      },
      {
        name: 'config',
        displayName: '地图配置',
        description: '地图配置',
        children: [
          {
            name: 'tileOrigin',
            displayName: 'bigemap瓦片源',
            description: 'bigemap瓦片源',
            type: OptionType.boolean,
            show: true,
            editable: true
          },
          {
            name: 'tile',
            displayName: '默认瓦片',
            description: '默认瓦片文件夹名',
            type: OptionType.string,
            show: true,
            editable: true
          },
          {
            name: 'containerScale',
            displayName: '比例',
            description: '地图放大比例',
            type: OptionType.double,
            show: false,
            editable: false
          },
          {
            name: 'boundaryControl',
            displayName: '范围限制',
            description: '范围限制',
            type: OptionType.boolean,
            show: true,
            editable: true
          },
          {
            name: 'maxZoom',
            displayName: '最大层级',
            description: '最大层级',
            type: OptionType.int,
            show: true,
            editable: true
          }, {
            name: 'layerConfig',
            displayName: '图层配置',
            description: '图层配置',
            type: OptionType.jsonModel,
            options: _layerConfig,
            show: true,
            editable: true
          }, {
            name: 'layerSubscribe',
            displayName: '图层订阅',
            description: '图层订阅',
            type: OptionType.boolean,
            show: true,
            editable: true
          }, {
            name: 'multiStation',
            displayName: '跨屏',
            description: '跨屏',
            type: OptionType.boolean,
            show: false,
            editable: false
          }, {
            name: 'dynamicZoom',
            displayName: '动态层级',
            description: '动态层级',
            type: OptionType.boolean,
            show: true,
            editable: true
          }, {
            name: 'snapShot',
            displayName: '地图缓存开关',
            description: '地图缓存开关',
            type: OptionType.boolean,
            show: true,
            editable: true
          }, {
            name: 'terminatorLayer',
            displayName: '晨昏图',
            description: '晨昏图',
            type: OptionType.boolean,
            show: true,
            editable: true
          }
        ]
      },
      {
        name: 'cluster',
        displayName: '聚类',
        description: '聚类',
        children: [
          {
            name: 'on',
            displayName: '开启聚类',
            description: '开启聚类',
            type: OptionType.boolean,
            show: true,
            editable: true
          },
          {
            name: 'zoom',
            displayName: '不聚类分界',
            description: '不聚类分界',
            type: OptionType.int,
            show: true,
            editable: true
          },
          {
            name: 'number',
            displayName: '聚类数量',
            description: '聚类数量',
            type: OptionType.int,
            show: true,
            editable: true
          }
        ]
      },
      {
        name: 'video',
        displayName: '视频弹窗',
        description: '视频弹窗',
        children: [
          {
            name: 'zoomWidth',
            displayName: '放大视频宽',
            description: '放大视频宽',
            type: OptionType.int,
            show: true,
            editable: true
          },
          {
            name: 'zoomHeight',
            displayName: '放大视频高',
            description: '放大视频高',
            type: OptionType.int,
            show: true,
            editable: true
          }
        ]
      },
      {
        name: 'tool',
        displayName: '工具',
        description: '工具',
        children: [
          {
            name: 'whiteBoard',
            displayName: '白板',
            description: '白板',
            type: OptionType.boolean,
            show: true,
            editable: true
          },
          {
            name: 'compass',
            displayName: '指南针',
            description: '指南针',
            type: OptionType.boolean,
            show: true,
            editable: true
          },
          {
            name: 'compassScale',
            displayName: '指南针比例',
            description: '指南针比例',
            type: OptionType.double,
            show: true,
            editable: true
          }
        ]
      }
    ];

    this._addProperty(options, optionDic);

  }

  _initEvents() {
    super._initEvents();

    const funcs = [
      {
        name: 'moveLeft',
        displayName: '左移',
        params: []
      }, {
        name: 'moveRight',
        displayName: '右移',
        params: []
      }, {
        name: 'moveTop',
        displayName: '上移',
        params: []
      }, {
        name: 'moveBottom',
        displayName: '下移',
        params: []
      }, {
        name: 'zoomIn',
        displayName: '放大',
        params: []
      }, {
        name: 'zoomOut',
        displayName: '缩小',
        params: []
      }, {
        name: 'flyTo',
        displayName: '飞行定位',
        params: [
          {
            name: 'lng',
            displayName: '经度',
            type: OptionType.double
          }, {
            name: 'lat',
            displayName: '纬度',
            type: OptionType.double
          }, {
            name: 'zoom',
            displayName: '层级',
            type: OptionType.int
          }, {
            name: 'tile',
            displayName: '瓦片',
            type: OptionType.string
          }
        ]
      }, {
        name: 'setView',
        displayName: '快速定位',
        params: [
          {
            name: 'lng',
            displayName: '经度',
            type: OptionType.double
          }, {
            name: 'lat',
            displayName: '纬度',
            type: OptionType.double
          }, {
            name: 'zoom',
            displayName: '层级',
            type: OptionType.int
          }, {
            name: 'tile',
            displayName: '瓦片',
            type: OptionType.string
          }
        ]
      }, {
        name: 'jumpTo',
        displayName: '跳跃定位',
        params: [
          {
            name: 'lng',
            displayName: '经度',
            type: OptionType.double
          }, {
            name: 'lat',
            displayName: '纬度',
            type: OptionType.double
          }, {
            name: 'zoom',
            displayName: '层级',
            type: OptionType.int
          }, {
            name: 'tile',
            displayName: '瓦片',
            type: OptionType.string
          }
        ]
      }, {
        name: 'setViewFromStr',
        displayName: '字符串定位',
        params: [
          {
            name: 'str',
            displayName: '参数',
            type: OptionType.string
          }
        ]
      }, {
        name: 'recover',
        displayName: '复位',
        params: []
      }, {
        name: 'addLayer',
        displayName: '添加图层',
        params: [{
          name: '图层code',
          displayName: '图层code',
          type: OptionType.string
        }, {
          name: '图层类型',
          displayName: '图层类型',
          type: OptionType.string
        }, {
          name: 'lng',
          displayName: '经度',
          type: OptionType.double
        }, {
          name: 'lat',
          displayName: '纬度',
          type: OptionType.double
        }, {
          name: 'zoom',
          displayName: '层级',
          type: OptionType.int
        }]
      }, {
        name: 'removeLayer',
        displayName: '删除图层',
        params: [{
          name: '图层code',
          displayName: '图层code',
          type: OptionType.string
        }]
      }, {
        name: 'addLayers',
        displayName: '批量添加图层',
        params: [{
          name: '图层数据',
          displayName: '图层数据',
          type: OptionType.string
        }]
      }, {
        name: 'removeLayers',
        displayName: '批量删除图层',
        params: [{
          name: '图层数据',
          displayName: '图层数据',
          type: OptionType.string
        }]
      }, {
        name: 'modifyTileLayer',
        displayName: '切换瓦片',
        params: [{
          name: '瓦片名称',
          displayName: '瓦片名称',
          type: OptionType.string
        }]
      }, {
        name: 'addAllLayers',
        displayName: '加载所有图层',
        params: []
      }, {
        name: 'removeAllLayers',
        displayName: '删除所有图层',
        params: []
      }, {
        name: 'removeTileLayer',
        displayName: '删除瓦片',
        params: []
      }, {
        name: 'layerPopup',
        displayName: '打开图层视频',
        params: [{
          name: '图层',
          displayName: '图层',
          type: OptionType.string
        }]
      }, {
        name: 'layerPackup',
        displayName: '关闭图层视频',
        params: [{
          name: '图层',
          displayName: '图层',
          type: OptionType.string
        }]
      }, {
        name: 'enlightLineLayer',
        displayName: '高亮线路图层',
        params: [{
          name: '图层',
          displayName: '图层',
          type: OptionType.string
        }]
      }, {
        name: 'openTerminatorLayer',
        displayName: '打开晨昏图',
        params: []
      }, {
        name: 'closeTerminatorLayer',
        displayName: '关闭晨昏图',
        params: []
      }, {
        name: 'setTerminator',
        displayName: '设置晨昏图',
        params: []
      }, {
        name: 'initTerminator',
        displayName: '初始化晨昏图',
        params: []
      }   
    ];    

    this.invokeFunc = this.invokeFunc.concat(funcs);

  }

  /**
   * 绑定地图交互事件
   * add by matianyu 2021/3/3
   */
  _multiScreenSynchronization() {
    // 绑定滚轮缩放事件，保证视觉效果尽量同步
    // document.getElementById(`lmap-${this.id}`).addEventListener('mousewheel', (e) => {
    //   // if (!this.map.dragging._draggable._enabled) {
    //   //   return;
    //   // }

    //   // let zoom = this.map.getZoom();
    //   // if (e.wheelDelta > 0 && zoom + 1 <= this._maxZoom) {
    //   //   zoom++;
    //   // } else if (e.wheelDelta < 0 && zoom - 1 >= this._minZoom) {
    //   //   zoom--;
    //   // } else {
    //   //   return;
    //   // }

    //   // const latlng = this.map.containerPointToLatLng([e.offsetX, e.offsetY]);

    //   // this._sendCommand('setZoomAround', [latlng.lng, latlng.lat, zoom]);

    //   this._sendCommand('setView', [this.map.getCenter().lng, this.map.getCenter().lat, this.map.getZoom(), null, this._self]);
    // });

    this.map.on('zoomend', ()=> {
      this._sendCommand('setView', [this.map.getCenter().lng, this.map.getCenter().lat, this.map.getZoom(), null, this._self]);
    })

    this.map.on('drag', () => {
      this._sendCommand('setView', [this.map.getCenter().lng, this.map.getCenter().lat, this.map.getZoom(), null, this._self]);
    })

  }

  /**
   * 传递容器缩放比例，地图初始化时需要已经取到scale，所以将地图的绘制放在该函数中，如果已经创建则仅存储值，而无需再次初始化地图
   * @param {*} scale 
   */
  setScale(scale) {
    window.containerScale = scale;
    this.containerScale = scale;

    // 首次赋值scale后进行初始化地图
    if (!this.map) {
      this._draw();
    }

  }

  setClipRect(left, top, width, height) {
    this._viewBox = [left, top, width, height];
  }

  getScale() {
    return this.containerScale;
  }

  /**
   * 显示当前缩放级别
   * add by matianyu 2021/3/3
   */
  _mapTool() {
    const style = Lmapd.formatCss({
      position: `absolute`,
      zIndex: 999,
      left: `20px`,
      bottom: `20px`,
      background: `white`,
      width: `50px`,
      height: `50px`,
      lineHeight: `50px`,
      borderRadius: `25px`
    }),
      style2 = Lmapd.formatCss({
        margin: 0,
        fontSize: `30px`,
        textAlign: `center`,
        lineHeight: `50px`
      });

    this.$container.append(`<div style="${style}"><p id="map-p-zoom" style="${style2}">${parseInt(this.map.getZoom())}</p></div>`);

    // 比例尺
    const scaleArray = ["3000km", "1000km", "500km", "200km", "100km", "50km", "30km", "10km", "5km", "3km", "2km", "1km", "500m", "200m", "100m", "50m", "30m", "10m"],
      style3 = Lmapd.formatCss({
        position: 'absolute',
        zIndex: 999,
        right: `20px`,
        bottom: `20px`,
        textAlign: `right`
      }),
      style4 = Lmapd.formatCss({
        backgroundColor: `rgba(255, 255, 255, 0.5)`,
        border: `2px solid #777`,
        borderTop: `none`,
        padding: `2px 5px 1px`,
        fontSize: `20px`
      });

    this.$container.append(`<div style="${style3}"><div id="map-div-scale" style="${style4}">${scaleArray[this.map.getZoom()]}</div></div>`);

    // 刷新比例尺
    this.map.on('zoomend', () => {
      $("#map-p-zoom").html(this.map.getZoom());
      $("#map-div-scale").html(scaleArray[this.map.getZoom() + 1]);
    });

    if(this.property.tool.compass) {
      const style5 = Lmapd.formatCss({
        position: 'absolute',
        zIndex: 999,
        right: `20px`,
        bottom: `50px`,
        background: `url(../../../wisVisual/images/compass.png)`,
        backgroundSize: '100% 100%',
        width: `${200 * parseFloat(this.property.tool.compassScale)}px`,
        height: `${200 * parseFloat(this.property.tool.compassScale)}px`,
      })
      this.$container.append(`<div style="${style5}"></div>`);
    }
    
    
  }

  /**
   * 将图层配置格式化为加载可用的数据格式
   * add by matianyu 2021/3/3
   */
  _layerConfigFormat() {
    this._layerLoad = {}, this._layerLoadDraw = [], this._gslayerLoad = {};

    this._config.layerConfig &&
      this._config.layerConfig.forEach((each) => {
        if (!each.platForm || each.platForm.value === 'wisGis') {
          for (let each2 of each.zoom.value) {
            if (!this._layerLoad[each2]) {
              this._layerLoad[each2] = [];
            }

            if (!this._layerLoad[each2][each.layerType.value]) {
              this._layerLoad[each2][each.layerType.value] = [];
            }

            this._layerLoad[each2][each.layerType.value].push(each.layerCode.value);
          }

          if (!each.isDraw || each.isDraw.value) {
            this._layerLoadDraw.push(each.layerCode.value);
          }
        } else if (each.platForm.value === 'geoserver') {
          for (let each2 of each.zoom.value) {
            if (!this._gslayerLoad[each2]) {
              this._gslayerLoad[each2] = [];
            }

            this._gslayerLoad[each2].push(each.layerCode.value);
          }
        }


      });

    this._layerLoadBackups = Lmapd.deepCopy(this._layerLoad);
  }

  /**
   * 回复初始位置
   * add by matianyu 2021/3/3
   */
  recover() {
    this._layerLoad= Lmapd.deepCopy(this._layerLoadBackups);

    this.map.setView({ lng: this.property.center.lng, lat: this.property.center.lat }, this.property.center.zoom);
  }

  /**
   * 左移
   * add by matianyu 2021/3/3
   */
  moveLeft() {
    const center = this.map.getCenter(), step = this._moveStep();

    this.map.setView({ lng: center.lng - step, lat: center.lat }, this.map.getZoom());
  }

  /**
   * 右移
   * add by matianyu 2021/3/3
   */
  moveRight() {
    const center = this.map.getCenter(), step = this._moveStep();

    this.map.setView({ lng: center.lng + step, lat: center.lat }, this.map.getZoom());
  }

  /**
   * 上移
   * add by matianyu 2021/3/3
   */
  moveTop() {
    const center = this.map.getCenter(), step = this._moveStep();

    this.map.setView({ lng: center.lng, lat: center.lat - step }, this.map.getZoom());
  }

  /**
   * 下移
   * add by matianyu 2021/3/3
   */
  moveBottom() {
    const center = this.map.getCenter(), step = this._moveStep();

    this.map.setView({ lng: center.lng, lat: center.lat + step }, this.map.getZoom());
  }

  /**
   * 根据层级匹配移动步阶
   * add by matianyu 2021/3/3
   */
  _moveStep() {
    const d = {
      1: 0.036,
      2: 0.034,
      3: 0.032,
      4: 0.030,
      5: 0.028,
      6: 0.026,
      7: 0.024,
      8: 0.022,
      9: 0.020,
      10: 0.018,
      11: 0.016,
      12: 0.014,
      13: 0.012,
      14: 0.01,
      15: 0.008,
      16: 0.006,
      17: 0.004,
      18: 0.002
    };

    return d[parseInt(this.map.getZoom())];
  }

  /**
   * 镜头拉近
   * add by matianyu 2021/3/3
   */
  zoomIn() {
    if (this.map.getZoom() + 1 > this._config.maxZoom) { return; }

    this.map.setView(this.map.getCenter(), this.map.getZoom() + 1);
  }

  /**
   * 镜头推远
   * add by matianyu 2021/3/3
   */
  zoomOut() {
    if (this.map.getZoom() - 1 < this.property.center.zoom) { return; }

    this.map.setView(this.map.getCenter(), this.map.getZoom() - 1);
  }

  /**
  * 接近定位
  * add by matianyu 2021/11/10
  */
  setZoomAround(lng, lat, zoom, self) {
    if (self && self == this._self) {
      return;
    }

    this.map.setZoomAround({ lng: lng, lat: lat }, zoom);
  }

  /**
   * 飞行定位
   * add by matianyu 2021/3/3
   */
  flyTo(lng, lat, zoom, tile) {
    if (tile !== undefined && tile.length > 0) {
      const moveennd = () => {
        this.map.off('moveend', moveennd);

        this.modifyTileLayer(tile);
      };

      this.map.on('moveend', moveennd);
    }
    this.map.flyTo({ lng: lng, lat: lat }, zoom);
  }

  /**
  * 快速定位
  * add by matianyu 2021/3/3
  */
  setView(lng, lat, zoom, tile, self) {
    if (self && self == this._self) {
      return;
    }

    if (tile && tile.length > 0) {
      const moveennd = () => {
        this.map.off('moveend', moveennd);

        this.modifyTileLayer(tile);
      };

      this.map.on('moveend', moveennd);
    }

    this.map.setView({ lng: lng, lat: lat }, zoom);
  }

  /**
   * 跳跃定位
  */
  jumpTo(lng, lat, zoom, tile) {
    const zoomend1 = e => {
      this.map.off('zoomend', zoomend1);

      if (tile && tile.length > 0) {
        const zoomend2 = e => {
          this.map.off('zoomend', zoomend2);

          this.modifyTileLayer(tile);
        }
        this.map.on('zoomend', zoomend2);
      }

      setTimeout(() => {
        this.map.flyTo({ lng: lng, lat: lat }, zoom);
      }, 1000);

    }

    let z = this.property.center.zoom;
    if (this.map.getZoom() - 5 > parseInt(this.property.center.zoom)) {
      z = this.map.getZoom() - 5;
    }

    this.map.on('zoomend', zoomend1);
    this.map.flyTo({ lng: lng, lat: lat }, 15);
  }

  /**
   * 字符串快速定位
   * @param {*} str 
   */
  setViewFromStr(str) {
    const latlng = str.split(' ');

    this.map.setView({ lng: latlng[0], lat: latlng[1] }, latlng[2]);
  }

  /**
   * 加载图层
   * add by matianyu 2021/3/3
   */
  addLayer(layer, type, lng = null, lat = null, zoom = null) {
    // 将添加的图层追加到特层加载结构中
    for (let i in this._layerLoad) {
      // 没有当前类型的图层则创建对应类型
      if (!this._layerLoad[i][type]) {
        this._layerLoad[i][type] = [];
      }

      // 如果图层已经加载，直接退出
      if ($.inArray(layer, this._layerLoad[i][type]) >= 0) {
        return;
      }

      // 将添加的图层加入结构中
      this._layerLoad[i][type].push(layer);

    }

    lng = lng && lng.length > 0 ? lng : null;
    lat = lat && lat.length > 0 ? lat : null;
    zoom = zoom ? parseInt(zoom) : null;

    if (lng && lat && zoom) {
      const moveend = () => {
        this.map.off('moveend', moveend);

        this._newRequest([{
          codes: [layer],
          type: type === 'point' ? 1 : (type === 'line' ? 2 : 3)
        }]);
      }
      this.map.on('moveend', moveend);

      this.map.setView({ lng: lng, lat: lat }, zoom);
    } else {
      this._newRequest([{
        codes: [],
        type: type === 'point' ? 1 : (type === 'line' ? 2 : 3)
      }]);
    }

  }

  addLayers(layers) {
    layers = layers.split('|').map(v => {
      v = v.split(':');

      return {
        type: v[0],
        codes: v[1].split('/')
      }
    });

    for (let i = this.property.center.zoom; i <= this.property.config.maxZoom; i++) {
      if (!this._layerLoad[i]) {
        this._layerLoad[i] = {
          point: [],
          line: [],
          polygon: []
        }
      }
    }

    for (let layer of layers) {
      // 遍历层级
      for (let i in this._layerLoad) {
        // 没有当前类型的图层则创建对应类型
        if (!this._layerLoad[i][layer.type]) {
          this._layerLoad[i][layer.type] = [];
        }

        for (let code of layer.codes) {
          // 如果图层已经加载
          if ($.inArray(code, this._layerLoad[i][layer.type]) < 0) {
            this._layerLoad[i][layer.type].push(code);  // 将添加的图层加入结构中
          } else {  // 图层未加载
            layer.codes.splice(layer.codes.indexOf(code), 1);
          }

          if ($.inArray(code, this._layerLoadDraw) < 0) {
            this._layerLoadDraw.push(code);
          }

        }
      }

      layer["type"] = layer.type === 'point' ? 1 : (layer.type === 'line' ? 2 : 3);

    }

    this._newRequest(layers);

  }

  clearLayers() {
    this._objectLayer.clearAll();
  }

  /**
   * 移除图层
   * add by matianyu 2021/3/3
   */
  removeLayer(layer) {
    // 将要删除的图层总结构删除
    for (let i in this._layerLoad) {
      const each = this._layerLoad[i];

      if ($.inArray(layer, each.point) >= 0) {
        each.point.splice($.inArray(layer, each.point), 1);
      }

      if ($.inArray(layer, each.line) >= 0) {
        each.line.splice($.inArray(layer, each.point), 1);
      }

      if ($.inArray(layer, each.polygon) >= 0) {
        each.polygon.splice($.inArray(layer, each.polygon), 1);
      }
    }

    this._objectLayer.removeLayer([layer]);

    this.saveSnapshot();
  }

  removeLayers(layers) {
    layers = layers.split('/');

    for (let i in this._layerLoad) {
      const each = this._layerLoad[i];

      for (let code of layers) {
        if ($.inArray(code, each.point) >= 0) {
          each.point.splice(each.point.indexOf(code), 1);
        }

        if ($.inArray(code, each.line) >= 0) {
          each.line.splice(each.line.indexOf(code), 1);
        }

        if ($.inArray(code, each.polygon) >= 0) {
          each.polygon.splice(each.polygon.indexOf(code), 1);
        }
      }
    }

    this._objectLayer.removeLayer(layers);
  }

  async addAllLayers() {
    const snapShot = await this._getSnapShotData();
    this.snapShotObjectPopup = JSON.parse(snapShot.data).objectPopup;

    this._newRequest();

  }

  removeAllLayers() {
    this._objectLayer.clearAll();
  }

  /**
   * 切换瓦片
   * add by matianyu 2021/3/3
   */
  modifyTileLayer(tile) {
    if (this._tile.name === tile) {
      return;
    }

    this.map.removeLayer(this._tile.tileLayer);

    this._tile = {
      tileLayer: L.tileLayer(`${window.origin}/tiles/${tile}/{z}/{x}/{y}.png`, {
        minZoom: this._minZoom,
        maxZoom: this._maxZoom,
        zIndex: 0
      }),
      name: tile
    };

    this.map.addLayer(this._tile.tileLayer);

    this.saveSnapshot();
  }

  /**
 * 删除瓦片
 * add by matianyu 2021/3/3
 */
  removeTileLayer() {
    this.map.removeLayer(this._tile.tileLayer);
    this._tile.name = null;
  }

  /**
  * 获取当前地图边界
  * add by matianyu 2021/3/3
  */
  _getBounds(b = true) {
    let bounds = "POLYGON((-180 90,180 90,180 -90,-180 -90,-180 90))";
    if (b) {
      const leftTop = this.map.containerPointToLatLng({ x: 0, y: 0 });
      const lightBottom = this.map.containerPointToLatLng({ x: this.property.basic.frame[2], y: this.property.basic.frame[3] });
      bounds = `POLYGON((${leftTop.lng} ${leftTop.lat}, ${lightBottom.lng} ${leftTop.lat}, ${lightBottom.lng} ${lightBottom.lat}, ${leftTop.lng} ${lightBottom.lat}, ${leftTop.lng} ${leftTop.lat}))`;
    }

    return bounds;
  }

  // 设置线图层透明度
  layerOpacity(opacity, layer) {
    this._objectLayer.eachLine(layer, l=> {
      l.setStyle({opacity: opacity, fillOpacity: opacity})
    })

    this._objectLayer.eachPolygon(layer, l=> {
      l.setStyle({opacity: opacity, fillOpacity: opacity})
    })


  }

  // 弹出某一对象所有视频弹窗
  objectAllPopup(id) {
    const o = this._objectLayer.findById(id);
    if (o) {
      o.popupAll(this.map);
    }

    this.saveSnapshot();
  }

  // 关闭某一对象所有视频弹窗
  objectAllPackup(id) {
    const o = this._objectLayer.findById(id);
    if (o) {
      o.packupAll(this.map);
    }

    this.saveSnapshot();
  }

  // 弹出某一视频弹窗
  objectPopupByPid(id) {
    const o = this._objectLayer.findPopupById(id);
    if (o) {
      o.popup(this.map);
    }

    this.saveSnapshot();

  }

  // 关闭某一视频弹窗
  objectPackupByPid(id) {
    const o = this._objectLayer.findPopupById(id);
    if (o) {
      o.packup(this.map);
    }

    this.saveSnapshot();

  }

  // 放大某一视频弹窗
  videoZoomById(id) {
    const o = this._objectLayer.findPopupById(id);
    if (o) {
      o.packup(this.map);
      o.zoomIn(this.container, { width: this.property.video.zoomWidth, height: this.property.video.zoomHeight });
    }
  }

  // 恢复某一视频弹窗
  videoZoomOutById(id) {
    const o = this._objectLayer.findPopupById(id);
    if (o) {
      o.zoomOut();
      o.popup(this.map);
    }
  }

  // 视频移动同步
  videoRelocate(id, lng, lat) {
    const o = this._objectLayer.findPopupById(id);
    if (o) {
      o.setLatLng(lng, lat);
    }

    this.saveSnapshot();
  }

  // 将某一图层的视频全部打开
  layerPopup(layer) {
    this._objectLayer.eachMarker(layer, m => {
      m.popupAll(this.map);
    })

    this._objectLayer.eachLine(layer, l => {
      l.popupAll(this.map);
    })

    this._objectLayer.eachPolygon(layer, p => {
      p.popupAll(this.map);
    })

  }

  // 将某一图层的视频全部关闭
  layerPackup(layer) {
    this._objectLayer.eachMarker(layer, m => {
      m.packupAll(this.map);
    })

    this._objectLayer.eachLine(layer, l => {
      l.packupAll(this.map);
    })

    this._objectLayer.eachPolygon(layer, p => {
      p.packupAll(this.map);
    })

  }

  // 强化指定线路图层
  enlightLineLayer(layers) {
    if (!layers || layers.length == 0) {
      layers = [];
    } else {
      layers = layers.split(',');
    }

    this._objectLayer.eachLine(null, l => {
      const lineStyle = l.options.prop.lineStyle;

      if (layers.length == 0 || $.inArray(l.options.prop.layerCode, layers) >= 0) {
        l.setStyle({ color: lineStyle.color, weight: lineStyle.weight, dashArray: lineStyle.dashArray, opacity: 1 });
      } else {
        l.setStyle({ color: lineStyle.color, weight: lineStyle.weight, dashArray: lineStyle.dashArray, opacity: 0.3 });
      }
    })
  }

  // 保存快照
  async saveSnapshot(json = {}) {
    const snapshot = {
      lng: this.map.getCenter().lng,
      lat: this.map.getCenter().lat,
      zoom: this.map.getZoom(),
      tile: this._tile.name,
      cluster: this.property.cluster.on,   // 该值为配置工具配置初始值，组件不可修改
      layers: this._objectLayer.getLayers(),
      objectPopup: this._objectLayer.getAllPopup(),
      whiteBoard: this.mapWhiteBoard ? this.mapWhiteBoard.getAll() : null,
      filterSet: this._featureFilterSet,
      filterCenter: this._featureFilterCenter,
      filterRadius: this._featureFilterRadius
    }

    const microServer = new Boolean(localStorage.getItem('microServer'));

    await WisUtil.ajaxPost(`${microServer.valueOf() ? '/action/config':'/action'}/meetingLayoutManager/cacheLayoutResourceComponent`, {
      layoutId: window.layoutId,
      componentId: this.id,
      data: JSON.stringify({ ...snapshot, ...json })
    });


  }

  // 加载快照
  async loadSnapshot() {
    const data = await this._getSnapShotData();
    if (!data) {
      this._initData();
    } else if (data.data && data.data.length > 0) {
      const snapShot = JSON.parse(data.data);
      
      this.snapShotObjectPopup = snapShot.objectPopup;
      this.snapShotWhiteBoard = snapShot.whiteBoard;

      this._featureFilterSet = snapShot.filterSet;
      this._featureFilterCenter = snapShot.filterCenter;
      this._featureFilterRadius = snapShot.filterRadius;

      if (snapShot.whiteBoard && snapShot.whiteBoard.length > 0) {
        this.property.tool.whiteBoard = true;
      }
      

      const moveEnd = () => {
        this.map.off('moveend', moveEnd);

        this._initData(snapShot.layers);
      }
      this.map.off('moveend').on('moveend', moveEnd);

      this.map.setView({ lng: snapShot.lng, lat: snapShot.lat }, snapShot.zoom);
      this.modifyTileLayer(snapShot.tile);
    } else {
      this._initData();
    }
  }

  _getSnapShotData() {
    const microServer = new Boolean(localStorage.getItem('microServer'));
    return WisUtil.ajaxGet(`${microServer.valueOf() ? '/action/config':'/action'}/meetingLayoutManager/getLayoutResourceComponentCache?layoutId=${window.layoutId}&componentId=${this.id}`, null);
  }

  mapWhiteBoardAddMarker(latlng) {
    const m = L.marker(Lmapd.wktToLatLng(latlng, 'point'), { icon: L.icon({ iconUrl: `${this._foldPath}/v2.2/images/marker-icon-2x.png`, iconSize: [80, 80] }), type: 'point' });
    this.mapWhiteBoard.add(m);

    this.saveSnapshot();
  }

  mapWhiteBoardAddPolyline(latlngs) {
    const l = L.polyline(Lmapd.wktToLatLng(latlngs, 'line'), { type: 'line' });
    this.mapWhiteBoard.add(l);

    this.saveSnapshot();
  }

  mapWhiteBoardAddPolygon(latlngs) {
    const p = L.polygon(Lmapd.wktToLatLng(latlngs, 'polygon'), { type: 'polygon' });
    this.mapWhiteBoard.add(p);

    this.saveSnapshot();
  }

  mapWhiteBoardClear() {
    this.mapWhiteBoard.clear();

    this.saveSnapshot();
  }

  mapDraggable(bool) {
    this.map.dragging._draggable._enabled = bool;
  }

  openTerminatorLayer() {
    this._terminatorLayer && this._terminatorLayer.addTo(this.map);
  }

  closeTerminatorLayer() {
    this._terminatorLayer && this._terminatorLayer.removeFrom(this.map);
  }

  setTerminator(date) {
    this._terminatorLayer && this._terminatorLayer.setTime(new Date(date).getTime());
  }

  initTerminator() {
    this._terminatorLayer && this._terminatorLayer.initialize();    
  }

  cleanup() {
    super.cleanup();

    this._objectLayer && this._objectLayer.cleanup();
  }




}
