class NariTopoView extends SVGComponentBase {
  /**
   * @constructor 组件基类构造函数
   * @param id {string} 组件id
   * @param code {string} 组件编码
   * @param container {HTMLElement} 组件容器
   * @param workMode {number} 组件工作模式 0为测试 1为大屏 2为配置工具 3为控制中心 4为单一组件调试
   * @param option {Object} 组件属性
   * @param useDefaultOpt {boolean} 是否启用默认值
   */
  constructor(
    id,
    code,
    container,
    workMode,
    option = {},
    useDefaultOpt = true
  ) {
    super(id, code, container, workMode, option, useDefaultOpt);
    this._setupDefaultValues();
    this._draw();
    this._keyboardDownHandler();
    if (this.property.basic.needSync && workMode !== 2 && workMode !== 0) {
      this._initEventSync();
    }
    if (workMode !== 2) {
      // this._getWSData();
    }
  }

  /**
   * @description 初始化组件所需要的数据
   */
  _setupDefaultValues() {
    super._setupDefaultValues();

    //************** */
    //topo图的节点和链路数据
    this.topoData = {};
    this.mapList = [];
    this.ip = "http://**************";
    this.port = "8080";
    this.mapIdList = [];
    //子图层列表
    this.subLayerList = [];
    //热点区域列表
    this.hotspotsList = [];
    // 绘制变压站所需的svg数据
    this.svgData = {};
    //图层位移缩放
    this.transform = {
      k: 1,
      x: 0,
      y: 0,
    };
    //图层动画时长
    this.animeDur = 0;
    //图层动画对象
    this.anime = null;
    //电厂组件列表
    this.powerPlantMap = {};
    //变电站组件列表
    this.transStationMap = {};
    //换流站组件列表
    this.exchStationMap = {};
    //分区板组件列表
    this.areaBoardMap = {};
    //图层数据
    this.mapData = {};

    this.currentArmInfo = {};

    this.isHotMap = false;
    this.tw = 0;
    this.th = 0;

    this.armData = {};

    //测试数据
    // this.armData = {
    //   eventList: [
    //     {
    //       faultDev: "131132000000000129",
    //       eventId: "100",
    //       seqFac01: "01123200000022",
    //       objId: "LuUgvQn3jN#525705_113997366104687051",
    //       id: "22205032000000000100",
    //       faultSec: "2021-11-13 19:43:28.000000",
    //       objType: "node",
    //     },
    //     {
    //       faultDev: "131132000000000128",
    //       eventId: "95",
    //       seqFac01: "01123200000022",
    //       objId: "LuUgvQn3jN#525705_113997366104687051",
    //       id: "22205032000000000095",
    //       faultSec: "2021-11-13 18:41:52.000000",
    //       objType: "node",
    //     },
    //   ],
    //   zhsgzRecvList: [
    //     {
    //       recvSec: "2023-01-31 23:20:45.000000",
    //       occurSec: "2023-01-31 23:20:46.000000",
    //       objId: "ecQNj11s4j#10805_113997366104686638",
    //       sigId: "10201932000000130953",
    //       stId: "1123209000001",
    //       id: "22250032000000000100",
    //       objType: "node",
    //     },
    //     {
    //       recvSec: "2023-01-15 04:58:40.000000",
    //       occurSec: "2023-01-15 04:58:40.000000",
    //       objId: "zl0Ow9ksDG#411354_113997366104687051",
    //       sigId: "10201932000000130834",
    //       stId: "1123205000060",
    //       id: "22250032000000000099",
    //       objType: "node",
    //     },
    //   ],
    // };
    //节点样式 s小/l大/m变电站带母线/p打印
    this.nodeType = "l";
    //展示子图层           500kv链路      500kv节点      500kv标注    500kv链路标注
    // this.showSublayer = [
    //   "KS5tPfwEKN",
    //   "FS5tPfwEKT",
    //   "KS5TXwwEDE",
    //   "Zr124XA8fb",
    // ];
    // this.showSublayer = ["SDAfqbRLOiv", "ShSbMrmGpXX"];
    this.showSublayer = [
      "SpRJVSc0FZZ",
      "Szk0WcEFtA4",
      "SLIoHipUc60",
      "ScOQrXyyLdO",
      "Smf7A7j2Whm",
      "SCitPziVn9C",
      "SwiY9R7Sgsd",
      "SYim7PjiOxx",
      "SFErC2WTtIS",
      "ShiUL9gdhZL",
    ];
    // this.showSublayer = [
    // "KS5tPfwEKN",
    // "FS5tPfwEKT",
    // "FS5wMfwEKT",
    // "KS3tPfwEKM",
    // "FS3tPfwEKF",
    // "Fw5TXwwEDo",
    // "M4WacTIW5E",
    // "W4WacTIW5M",]
    this.isDivide = false;
    this.showCross = false; //默认不显示分区着色
    this.isTest = !!this.property.isTest;
    // -1 灰 0 蓝 1银色 2 金 3 玫红 4 橙 5 黄 6 虚
    this.lineColorDic = {
      "-1": "#e800ff",
      0: "#2cebff",
      1: "#8D8D8D",
      2: "#9B7238",
      3: "#e71864",
      4: "#ff7800",
      5: "#e4ff00",
      6: "#00b4ff",
    };
    // -1 灰 0 蓝 1黑色 2 黑 3 玫红 4 橙 5 黄 6 虚
    this.lineColorDic2 = {
      "-1": "#e800ff",
      0: "#2cebff",
      1: "#000000",
      2: "#000000",
      3: "#e71864",
      4: "#ff7800",
      5: "#e4ff00",
      6: "#00b4ff",
    };

    // TODO:记录每个线路的箭头数量的map

    this.arrowMap = {}; //箭头数量
    this.arrowDir = {}; //箭头方向

    // 性能优化：添加缓存对象
    this._linkNodeCache = {}; // 链路DOM节点缓存
    this._arrowGroupCache = {}; // 箭头组缓存
    this._linkLengthCache = {}; // 链路长度缓存
    this._lastNodeData = {}; // 节点历史数据缓存
    this._lastLinkData = {}; // 链路历史数据缓存
    this._updateInProgress = false; // 更新进行中标志

    this.df = true;
  }

  /**
   * @description 初始化组件配置项
   */
  _initProperty() {
    super._initProperty();
    let options = {
      basic: {
        className: "NariTopoView",
        needSync: true,
      },
      viewSetting: {
        url500KV: "http://*************:6818/ftp/dwyzt/dwyztdp.json", //500kv
        url220KV: "http://*************:6818/ftp/dwyzt/dwyzt.json",
        isShow500KV: false,
        // loadMapId: 'yO3u76xwhH',  //220kv
        ip: "http://*************",
        port: "8899/topoEdit",
        ftpIp: "http://*************",
        ftpPort: "6818",
        armUrl: "http://************:8800/data/getNewAlarmInfo?mapId=xxx",
        dataUrl: "http://************:8800/getDataByMapIdDynamic?mapId=xxx",
        w: 9800,
        h: 7033,
        iw: 3000,
        ih: 2400,
        mapW: 4000,
      },
    };

    let optionDic = [
      {
        name: "viewSetting",
        displayName: "显示设置",
        show: true,
        editable: true,
        children: [
          {
            name: "url500KV",
            displayName: "500KV地址",
            description: "500KV文件地址",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "url220KV",
            displayName: "220KV地址",
            description: "220KV文件地址",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "isShow500KV",
            displayName: "是否500KV",
            description: "是否显示500KV",
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: "ip",
            displayName: "ip地址",
            description: "ip地址",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "port",
            displayName: "端口",
            description: "端口",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "ftpIp",
            displayName: "ftp的ip地址",
            description: "ftp的ip地址",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "ftpPort",
            displayName: "ftp端口",
            description: "ftp端口",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "armUrl",
            displayName: "告警接口",
            description: "告警接口",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "dataUrl",
            displayName: "数据接口",
            description: "数据接口",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "w",
            displayName: "显示宽度",
            description: "显示宽度",
            type: OptionType.int,
            show: true,
            editable: true,
          },
          {
            name: "h",
            displayName: "显示高度",
            description: "显示高度",
            type: OptionType.int,
            show: true,
            editable: true,
          },
          {
            name: "iw",
            displayName: "告警显示宽度",
            description: "告警显示宽度",
            type: OptionType.int,
            show: true,
            editable: true,
          },
          {
            name: "ih",
            displayName: "告警显示高度",
            description: "告警显示高度",
            type: OptionType.int,
            show: true,
            editable: true,
          },
          {
            name: "mapW",
            displayName: "地图显示宽度",
            description: "地图显示宽度",
            type: OptionType.int,
            show: true,
            editable: true,
          },
        ],
      },
    ];

    this._addProperty(options, optionDic);
  }

  /**
   * @description 初始化组件事件
   */
  _initEvents() {
    super._initEvents();
    this.invokeFunc = this.invokeFunc.concat([
      {
        name: "switchTo220",
        displayName: "200KV全景图",
        params: [],
      },
      // {
      //   name: "close500Link",
      //   displayName: "关闭500连线",
      //   params: [],
      // },
      // {
      //   name: "open500Link",
      //   displayName: "打开500连线",
      //   params: [],
      // },
      {
        name: "switchTo500",
        displayName: "500KV全景图",
        params: [],
      },
      {
        name: "flyToHotspots",
        displayName: "跳转热点",
        params: [
          {
            name: "hotspotsId",
            displayName: "热点Id",
            type: OptionType.enum,
            options: [],
          },
        ],
      },
      {
        name: "switchLinkBolckMode",
        displayName: "线框模式",
        params: [],
      },
      {
        name: "getTotalData",
        displayName: "获取数据",
        params: [],
      },
      {
        name: "switchDivideLink",
        displayName: "切换显示分区联络线",
        params: [
          {
            name: "status",
            displayName: "是否显示",
            type: OptionType.boolean,
          },
        ],
      },
      {
        name: "switchDivideFill",
        displayName: "切换显示分区填充",
        params: [
          {
            name: "status",
            displayName: "是否显示",
            type: OptionType.boolean,
          },
        ],
      },
      // {
      //   name: "equipmentTwinkle",
      //   displayName: "设备闪烁",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "设备ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      // {
      //   name: "closeEquipmentTwinkle",
      //   displayName: "关闭设备闪烁",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "设备ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      // {
      //   name: "crossingChannel",
      //   displayName: "过江通道",
      //   params: [
      //     {
      //       name: "status",
      //       displayName: "是否显示",
      //       type: OptionType.boolean,
      //     },
      //   ],
      // },
      {
        name: "amplify",
        displayName: "放大",
        params: [
          {
            name: "scale",
            displayName: "放大比例",
            type: OptionType.int,
          },
        ],
      },
      {
        name: "reduce",
        displayName: "缩小",
        params: [
          {
            name: "scale",
            displayName: "缩小比例",
            type: OptionType.int,
          },
        ],
      },
      {
        name: "toUp",
        displayName: "上移",
        params: [
          {
            name: "dis",
            displayName: "移动距离",
            type: OptionType.int,
          },
        ],
      },
      {
        name: "toDown",
        displayName: "下移",
        params: [
          {
            name: "dis",
            displayName: "移动距离",
            type: OptionType.int,
          },
        ],
      },
      {
        name: "toLeft",
        displayName: "左移",
        params: [
          {
            name: "dis",
            displayName: "移动距离",
            type: OptionType.int,
          },
        ],
      },
      {
        name: "toRight",
        displayName: "右移",
        params: [
          {
            name: "dis",
            displayName: "移动距离",
            type: OptionType.int,
          },
        ],
      },
      // {
      //   name: "lineInfo",
      //   displayName: "线路OMS",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "线路ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      // {
      //   name: "lineTagan",
      //   displayName: "塔杆线路",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "线路ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      // {
      //   name: "lineTonggan",
      //   displayName: "同杆线路",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "线路ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      // {
      //   name: "lineCheck",
      //   displayName: "线路检修",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "线路ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      // {
      //   name: "lineWarning",
      //   displayName: "线路告警",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "线路ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      // {
      //   name: "stationInfo",
      //   displayName: "站内设备信息",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "站点ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      // {
      //   name: "stationOMS",
      //   displayName: "站点OMS",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "站点ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      // {
      //   name: "stationWiring1",
      //   displayName: "站内接线图",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "站点ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      // {
      //   name: "stationDefect",
      //   displayName: "设备缺陷信息",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "站点ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      // {
      //   name: "stationCheck",
      //   displayName: "设备检修",
      //   params: [
      //     {
      //       name: "id",
      //       displayName: "站点ID",
      //       type: OptionType.string,
      //     },
      //   ],
      // },
      {
        name: "closePopMenu",
        displayName: "关闭弹窗",
        params: [],
      },
    ]);
  }

  /**
   * 切换子图层显示隐藏状态
   * @param {string} name 子图层名称
   * @param {boolean} isShow 是否显示
   */
  switchSublayer(name, isShow) {
    let sublayerId = this.subLayerList.filter((d) => d.sublayerName === name)[0]
      .sublayerId;
    this.mainSVG
      .selectAll(`.${sublayerId}`)
      .style("display", isShow ? "block" : "none");
  }

  /**
   * @description 绘制入口
   */
  _draw() {
    return new Promise((resolve, reject) => {
      super._draw();
      this._drawLoading();
      this._drawPopMenu();
      this.mainSVG
        .style("fill-rule", "evenodd")
        .style("clip-rule", "evenodd")
        .style("stroke-linejoin", "round")
        .style("stroke-miterlimit", 2);
      // if(this.isHotMap){
      // this.mainSVG
      // .attr('width',7348.75)
      //             .attr('height',7033)
      // .attr('viewBox',`0 0 ${this.property.basic.frame[2]} ${this.property.basic.frame[3]}`)
      // }
      this._generateDefines();
      this.containerPosition = {
        x: 0,
        y: 0,
        width: this.property.basic.frame[2],
        height: this.property.basic.frame[3],
      };
      // 初始的时候，清空状态
      this.arrowMap = {};
      this.arrowDir = {};
      this.mainSVG.select(".nari_current_container").remove();
      this.mainSVG.append("g").attr("class", "nari_current_container");
      this._getTopo();
      resolve();
    });
  }

  _drawLoading() {
    this.loadingCon = d3
      .select(this.container)
      .append("div")
      .attr("id", "naritopo-loading-con");
    this.loading = this.loadingCon.append("div").attr("id", "naritopo-loading");

    $(this.loading.node()).append(`<div class="spinner-box">
    <div class="pulse-container">  
      <div class="pulse-bubble pulse-bubble-1"></div>
      <div class="pulse-bubble pulse-bubble-2"></div>
      <div class="pulse-bubble pulse-bubble-3"></div>
    </div>
  </div>`);
  }

  _drawPopMenu() {
    this.popCon = d3
      .select(this.container)
      .append("div")
      .attr("id", "naritopo-pop-con")
      .style("transform", "scale(0)")
      .style("transition", "transform 0.5s ease-in-out");
    this.popTiTle = this.popCon
      .append("div")
      .attr("id", "naritopo-pop-title")
      .style("font-size", "128px")
      .style("text-align", "center");
    this.popImage = this.popCon
      .append("img")
      .attr("id", "naritopo-pop-image")
      .style("width", "100%");

    this.popTiTle.text("9999999");
    this.popImage.attr(
      "src",
      `${this._foldPath}/images/popImages/lineCheck.png`
    );
  }

  async switchTo220() {
    this.property.viewSetting.isShow500KV = false;
    this.loadingCon.remove();
    this.popCon.remove();
    await this._draw();
    this.limitCon.style("display", "none");
    this.transform = {
      x: 0,
      y: 0,
      k: 1,
    };
    this.nodeType = "s";
    this.animeDur = 0;
    this._transformMain();
  }

  close500Link() {
    this._removeShowSublayer(["KS5tPfwEKN", "s5iL6EUIpN", "Zr124XA8fb"]);
    this.linkCon.selectAll(".KS5tPfwEKN").style("display", "none");
    this.nodeCon.selectAll(".s5iL6EUIpN").style("display", "none");
    this.nodeCon.selectAll(".Zr124XA8fb").style("display", "none");
    this.arrowCon.style("display", "none");
  }

  open500Link() {
    this._addShowSublayer(["KS5tPfwEKN", "s5iL6EUIpN"]);
    this.linkCon.selectAll(".KS5tPfwEKN").style("display", "block");
    this.nodeCon.selectAll(".s5iL6EUIpN").style("display", "block");
    this.arrowCon.style("display", "block");
  }

  async switchTo500() {
    this.property.viewSetting.isShow500KV = true;
    this.loadingCon.remove();
    this.popCon.remove();
    await this._draw();
    this.limitCon.style("display", "block");

    this.transform = {
      x: 0,
      y: 0,
      k: 1,
    };
    this.nodeType = "l";
    this.animeDur = 0;
    setTimeout(() => {
      this._transformMain();
    }, 500);
  }

  async flyToHotspots(id) {
    if (!this.isHotMap) {
      this.isHotMap = true;
      this.loadingCon.remove();
      this.popCon.remove();
      await this._draw();
    }

    setTimeout(() => {
      //  this._getHotspotsList(this.mapId)

      let hotspot = this.hotspotsList.filter((d) => d.hotspotId === id)[0];
      let [x, y] = hotspot.position.split(",");
      this.transform = {
        x: x * (9800 / 7348),
        y: y * (9800 / 7348),
        k: hotspot.scale * (9800 / 7348),
      };
      this.showSublayer = [
        "KS5tPfwEKN",
        "FS5tPfwEKT",
        "FS5wMfwEKT",
        "KS3tPfwEKM",
        "FS3tPfwEKF",
        "Fw5TXwwEDo",
        "M4WacTIW5E",
        "W4WacTIW5M",
      ];
      this.nodeType = "m";
      this.animeDur = 5000;
      this._transformMain();
    }, 100);
  }

  /**
   * 显示/隐藏跨区线
   * @param {boolean} status 状态 true开启 false关闭
   */
  switchDivideLink(status) {
    this.linkCon
      .selectAll("path")
      .style("filter", `brightness(${status ? 0.3 : 1})`);
    this.nodeCon
      .selectAll("svg")
      .style("filter", `brightness(${status ? 0.3 : 1})`);
    this.linkCon.selectAll(".topo-isDivide").style("filter", "brightness(1)");
    this.nodeCon.selectAll(".topo-isDivide").style("filter", "brightness(1)");
    status ? this.close500Link() : this.open500Link();
  }

  /**
   * 显示/隐藏分区填充
   * @param {boolean} status 状态 true开启 false关闭
   */
  switchDivideFill(status) {
    this.isDivide = status;
    this.blockCon.selectAll("path").style("fill-opacity", status ? 1 : 0);
  }

  switchLinkBolckMode() {
    d3.select(this.container).style("background-color", "#fff");
    this.linkCon.selectAll(".KS3tPfwEKM").style("stroke", "#000");
    this.blockCon
      .selectAll("path")
      .style("filter", "")
      .style("fill", "none")
      .style("stroke", "#000")
      .style("stroke-width", 2.5);
    for (const key in this.powerPlantMap) {
      this.powerPlantMap[key].setType("p");
    }
    for (const key in this.exchStationMap) {
      this.exchStationMap[key].setType("p");
    }
    for (const key in this.transStationMap) {
      let comp = this.transStationMap[key];
      comp.con
        .attr("x", comp.option.x)
        .attr("y", comp.option.y)
        .attr("width", comp.option.w)
        .attr("height", comp.option.h);
      comp.setType("p");
    }
  }

  getTotalData() {
    this._getData("b0stYcRNkK");
  }

  /**
   * 设备闪烁
   * @param {string} id 设备id
   */
  equipmentTwinkle(id) {
    setTimeout(() => {
      this.transStationMap[id]._setSize(50);
      this.nodeCon
        .select(`#node_${id.replace("#", "_")}`)
        .classed("topo-flash-node", true);
    }, 8000);
  }

  /**
   * 取消设备闪烁
   * @param {string} id 设备id
   */
  closeEquipmentTwinkle(id) {
    setTimeout(() => {
      this.transStationMap[id]._setSize(this.nodeType === "m" ? 60 : 31);
      this.nodeCon
        .select(`#node_${id.replace("#", "_")}`)
        .classed("topo-flash-node", false);
    }, 8000);
  }

  /**
   * 切换到过江子图层
   * @param {boolean} status 状态 true开启 false关闭
   */
  crossingChannel(status) {
    this.showCross = status;
    // this.transform = {
    //   x: 0,
    //   y: 0,
    //   k: 1,
    // };
    // this.showSublayer = ['SuVpsLPSY1l'];
    // //   this.nodeType = 'l';
    // this.animeDur = 0;
    // this._transformMain();
    this.linkCon.selectAll("path").style("opacity", status ? 0.3 : 1);
    this.nodeCon.selectAll("svg").style("opacity", status ? 0.3 : 1);
    this.nodeCon.selectAll("text").style("opacity", status ? 0.3 : 1);
    this.arrowCon.selectAll("polygon").style("opacity", status ? 0.3 : 1);

    this.linkCon.selectAll(".SuVpsLPSY1l").style("opacity", 1);
    this.nodeCon.selectAll(".SuVpsLPSY1l").style("opacity", 1);
    this.arrowCon.selectAll(".SuVpsLPSY1l").style("opacity", 1);
    this.nodeCon.selectAll(".SuVpsLPSY1l").select("text").style("opacity", 1);
  }

  _transformMain() {
    this.mainSVG
      .transition()
      .duration(this.workMode == 0 ? this.animeDur : 100)
      .call(
        this.zoom.transform,
        d3.zoomIdentity
          .translate(this.transform.x, this.transform.y)
          .scale(this.transform.k)
      );
  }

  amplify(scale) {
    this.transform.k += Number(scale);
    this.animeDur = 0;
    // this. _transformMain()

    // this.mainSVG.transition().call(this.zoom.scaleBy, scale);
    this.mainSVG
      .select(".nari_current_container")
      .style(
        "transform",
        `translateX(${this.transform.x}px) translateY(${this.transform.y}px) translateZ(0) scale(${this.transform.k})`
      );
  }

  reduce(scale) {
    this.transform.k -= Number(scale);

    this.animeDur = 0;
    // this. _transformMain()

    this.animeDur = 0;
    // this.mainSVG.transition().call(this.zoom.scaleBy, scale);
    this.mainSVG
      .select(".nari_current_container")
      .style(
        "transform",
        `translateX(${this.transform.x}px) translateY(${this.transform.y}px) translateZ(0) scale(${this.transform.k})`
      );
  }

  toUp(dis) {
    this.transform.y -= Number(dis);
    this.animeDur = 0;
    // this._transformMain();
    this.mainSVG
      .select(".nari_current_container")
      .style(
        "transform",
        `translateX(${this.transform.x}px) translateY(${this.transform.y}px) translateZ(0) scale(${this.transform.k})`
      );
  }

  toDown(dis) {
    this.transform.y += Number(dis);
    this.animeDur = 0;
    // this._transformMain();
    this.mainSVG
      .select(".nari_current_container")
      .style(
        "transform",
        `translateX(${this.transform.x}px) translateY(${this.transform.y}px) translateZ(0) scale(${this.transform.k})`
      );
  }

  toLeft(dis) {
    this.transform.x -= Number(dis);
    this.animeDur = 0;
    // this._transformMain();
    this.mainSVG
      .select(".nari_current_container")
      .style(
        "transform",
        `translateX(${this.transform.x}px) translateY(${this.transform.y}px) translateZ(0) scale(${this.transform.k})`
      );
  }

  toRight(dis) {
    this.transform.x += Number(dis);
    this.animeDur = 0;
    // this._transformMain();
    this.mainSVG
      .select(".nari_current_container")
      .style(
        "transform",
        `translateX(${this.transform.x}px) translateY(${this.transform.y}px) translateZ(0) scale(${this.transform.k})`
      );
  }

  closePopMenu() {
    this.popCon.style("transform", "scale(0)");
  }

  lineInfo(ID) {
    this.popCon.style("transform", "scale(1)");
    this.popTiTle.text("线路OMS");
    this.popImage.attr(
      "src",
      `${this._foldPath}/images/popImages/lineInfo.png`
    );
  }
  lineTagan() {
    this.popCon.style("transform", "scale(1)");
    this.popTiTle.text("塔杆线路");
    this.popImage.attr(
      "src",
      `${this._foldPath}/images/popImages/lineTagan.png`
    );
  }
  lineTonggan() {
    this.popCon.style("transform", "scale(1)");
    this.popTiTle.text("同杆线路");
    this.popImage.attr(
      "src",
      `${this._foldPath}/images/popImages/lineTonggan.png`
    );
  }
  lineCheck() {
    this.popCon.style("transform", "scale(1)");
    this.popTiTle.text("线路检修");
    this.popImage.attr(
      "src",
      `${this._foldPath}/images/popImages/lineCheck.png`
    );
  }
  lineWarning() {
    this.popCon.style("transform", "scale(1)");
    this.popTiTle.text("线路告警");
    this.popImage.attr(
      "src",
      `${this._foldPath}/images/popImages/lineWarning.png`
    );
  }

  stationInfo(ID) {
    this.popCon.style("transform", "scale(1)");
    this.popTiTle.text("站内设备信息");
    this.popImage.attr(
      "src",
      `${this._foldPath}/images/popImages/stationInfo.png`
    );
  }
  stationOMS() {
    this.popCon.style("transform", "scale(1)");
    this.popTiTle.text("站点OMS");
    this.popImage.attr(
      "src",
      `${this._foldPath}/images/popImages/stationOMS.png`
    );
  }
  stationWiring1() {
    this.popCon.style("transform", "scale(1)");
    this.popTiTle.text("站内接线图");
    this.popImage.attr(
      "src",
      `${this._foldPath}/images/popImages/stationWiring1.png`
    );
  }
  stationDefect() {
    this.popCon.style("transform", "scale(1)");
    this.popTiTle.text("设备缺陷信息");
    this.popImage.attr(
      "src",
      `${this._foldPath}/images/popImages/stationDefect.png`
    );
  }
  stationCheck() {
    this.popCon.style("transform", "scale(1)");
    this.popTiTle.text("设备检修");
    this.popImage.attr(
      "src",
      `${this._foldPath}/images/popImages/stationCheck.png`
    );
  }

  /**
   * 监听键盘按键
   */
  _keyboardDownHandler() {
    document.onkeydown = (e) => {
      // console.log(e.key + e.keyCode);
      switch (e.keyCode) {
        // 小键盘+
        case 107:
          this.transform.k += 0.01;
          break;
        // 小键盘-
        case 109:
          this.transform.k -= 0.01;
          break;
        // 方向键左
        case 37:
          this.transform.x -= 100;
          break;
        // 方向键上
        case 38:
          this.transform.y -= 100;
          break;
        // 方向键右
        case 39:
          this.transform.x += 100;
          break;
        // 方向键下
        case 40:
          this.transform.y += 100;
          break;
        // 功能键Home
        case 36:
          this.transform = {
            k: 1,
            x: 0,
            y: 0,
          };
          break;
        //数字键1  全部显示
        case 49:
          this.mainSVG.selectAll(".KS3tPfwEKM").style("display", "block"); //20w线路
          this.mainSVG.selectAll(".KS5tPfwEKN").style("display", "block"); //50w链路
          this.mainSVG.selectAll(".FS3tPfwEKF").style("display", "block"); //22w节点
          this.mainSVG.selectAll(".FS5tPfwEKT").style("display", "block"); //50w节点
          break;
        //数字键2  显示500
        case 50:
          this.mainSVG.selectAll(".KS3tPfwEKM").style("display", "none");
          this.mainSVG.selectAll(".KS5tPfwEKN").style("display", "block");
          this.mainSVG.selectAll(".FS3tPfwEKF").style("display", "none");
          this.mainSVG.selectAll(".FS5tPfwEKT").style("display", "block");
          break;
        //数字键3 显示220
        case 51:
          this.mainSVG.selectAll(".KS3tPfwEKM").style("display", "block");
          this.mainSVG.selectAll(".KS5tPfwEKN").style("display", "none");
          this.mainSVG.selectAll(".FS3tPfwEKF").style("display", "block");
          this.mainSVG.selectAll(".FS5tPfwEKT").style("display", "block");
          break;
        //数字键4 全部隐藏
        case 52:
          this.mainSVG.selectAll(".KS3tPfwEKM").style("display", "none");
          this.mainSVG.selectAll(".KS5tPfwEKN").style("display", "none");
          this.mainSVG.selectAll(".FS3tPfwEKF").style("display", "none");
          this.mainSVG.selectAll(".FS5tPfwEKT").style("display", "none");
          break;
      }
      this.mainSVG
        .select(".nari_current_container")
        .style(
          "transform",
          `translateX(${this.transform.x}px) translateY(${this.transform.y}px) translateZ(0) scale(${this.transform.k})`
        );
    };
  }

  /**
   * 绑定缩放拖拽事件
   */
  _bindZoom() {
    let vb = this.mainSVG
      .select(".nari_current_container")
      .node()
      .dataset.vb.split(" ");
    this.zoom = d3
      .zoom()
      // .extent([
      //   [0, 0],
      //   [Number(vb[2]), Number(vb[3])],
      // ])
      // .translateExtent([
      //   [0, 0],
      //   [Number(vb[2]), Number(vb[3])],
      // ])
      // .scaleExtent([0.1, 8])
      .on("start", () => {
        // 使用优化的缩放开始处理
        this._onZoomStart();
      })
      .on("zoom", () => {
        let tf = d3.event.transform;
        if (this.workMode !== 1) {
          this.mainSVG
            .select(".nari_current_container")
            .style(
              "transform",
              `translateX(${tf.x}px) translateY(${tf.y}px) translateZ(0) scale(${tf.k})`
            );
        }
        this.transform = JSON.parse(JSON.stringify(tf));
      })
      .on("end", () => {
        let tf = d3.event.transform;
        this.transform = JSON.parse(JSON.stringify(tf));
        if (this.workMode != 0) {
          this._sendMessageByWS(
            JSON.stringify({
              functionName: "_setTransform",
              params: tf,
            })
          );
        } else {
          //  this._setTransform(tf);

          this.mainSVG
            .select(".nari_current_container")
            .style(
              "transform",
              `translateX(${tf.x}px) translateY(${tf.y}px) translateZ(0) scale(${tf.k})`
            );

          this._drawLinks(this.topoData.links);
          requestAnimationFrame(() => {
            this._drawNodes(this.topoData.nodes).then(() => {
              // if (JSON.stringify(this.currentArmInfo) !== "{}") {
              //   this._armPop(this.currentArmInfo);
              // }

              if (JSON.stringify(this.mapData) !== "{}") {
                requestAnimationFrame(() => {
                  // this.switchDivideFill(this.isDivide);
                  this._update(this.mapData, true);
                });
              }
            });
          });
        }
      });
    this.mainSVG.call(this.zoom);
  }

  /**
   * 设置动画位移
   * @param {Object} transform 动画位移
   * @param {number} duration 动画持续时间
   */
  _setTransform(transform) {
    if (this.animeDur === 0) {
      this.mainSVG
        .select(".nari_current_container")
        .style(
          "transform",
          `translateX(${transform.x}px) translateY(${transform.y}px) translateZ(0) scale(${transform.k})`
        );
      this._drawLinks(this.topoData.links);
      requestAnimationFrame(() => {
        this._drawNodes(this.topoData.nodes).then(() => {
          if (JSON.stringify(this.currentArmInfo) !== "{}") {
            this._armPop(this.currentArmInfo);
          }
          if (JSON.stringify(this.mapData) !== "{}") {
            requestAnimationFrame(() => {
              this.switchDivideFill(this.isDivide);
              this._update(this.mapData, true);
            });
          }
        });
      });

      return;
    }
    this.anime = new anime({
      targets: this.mainSVG.select(".nari_current_container").node(),
      translateX: transform.x,
      translateY: transform.y,
      scale: transform.k,
      autoplay: false,
      duration: this.animeDur,
      easing: "easeInOutQuad",
      loop: false,
    });
    this._setAnimateSyncParam(this.animeDur);
    this._animateSyncCallback((data) => {
      if (data.body === "OK") {
        this.maxIndex = 0;
        return;
      }
      let recvData = JSON.parse(data.body);
      if (recvData.index > this.maxIndex) {
        this.anime.seek(recvData.time);
        this.maxIndex = recvData.index;
      }
      if (recvData.finish) {
        this.maxIndex = 0;
        this._stopAnimate();
        this._drawLinks(this.topoData.links);
        requestAnimationFrame(() => {
          this._drawNodes(this.topoData.nodes).then(() => {
            if (JSON.stringify(this.currentArmInfo) !== "{}") {
              this._armPop(this.currentArmInfo);
            }
            if (JSON.stringify(this.mapData) !== "{}") {
              this._update(this.mapData, true);
            }
          });
        });
      }
    });
  }

  /**
   * 定义svg的defs
   */
  _generateDefines() {
    let defs = this.mainSVG.append("defs");
    d3.text(`${this._foldPath}/defs.html`).then((data) => {
      defs.html(data);
    });
  }

  /**
   * 事件同步入口
   * @param {json} data 同步jsonString
   */
  _eventSyncProcess(data = {}) {
    let msg = JSON.parse(data);
    switch (msg.functionName) {
      case "_setTransform":
        this._setTransform(msg.params);
        break;
      default:
        break;
    }
  }

  async _prevMap() {
    this.mapIdList.pop();
    let mapId = this.mapIdList.reduceRight(
      (prev, cur) => [...prev, cur],
      []
    )[0];
    this.mapIdList.pop();
    await this._drawTopoByMapId(mapId);
  }

  /**
   * @description 获取拓扑图所有节点数据入口
   */
  async _getTopo() {
    // await this._getMapList();

    await this._getAllData();
    let mapId = this.mapObj.mapId;

    this.mapId = this.mapObj.mapId;
    // this.mapExternalBind = this.mapList.filter((d) => d.mapId === mapId)[0].externalBind;
    // this.mapInternalBind = this.mapList.filter((d) => d.mapId === mapId)[0].internalBind;
    this.mapExternalBind = this.mapObj.externalBind;
    this.mapInternalBind = this.mapObj.internalBind;
    await this._drawTopoByMapId(mapId);
  }

  /**
   * 根据图层id绘制图层
   * @param {string} mapId 图层id
   */
  async _drawTopoByMapId(mapId) {
    if (mapId === "") return;
    await this._getSubLayerList(mapId);
    // await this._getNodeLinkList(mapId).then((data) => (this.topoData = data));
    // await this._getNodeLinkList(mapId, 'M4WacTIW5E').then((data) => (this.area220NameNode = data));
    // await this._getHotspotsList(mapId);
    await this._loadTransformerSubstationSvgData();
    await this._drawTopo(mapId);

    this._bindZoom();
    this.switchDivideFill(false); //默认不显示区域填充

    // setTimeout(() => {
    //   this._getArmInfo();
    // }, 3000);

    setTimeout(() => {
      this._getData(mapId);
      this._getLimitDevice();
    }, 3000);

    // setTimeout(() => {
    //   _this._getArmInfo();
    // }, 30000);

    // 性能优化：改进数据更新循环
    let lastTime = 0;
    let lastCacheCleanTime = 0;
    let _this = this;

    function loop(currentTime) {
      if (currentTime - lastTime >= 60000) {
        // 间隔1分钟
        lastTime = currentTime;

        // 使用防抖机制避免频繁更新
        if (!_this._updateInProgress) {
          // 执行需要间隔一分钟执行的操作
          _this._getData();
          _this._getLimitDevice();

          // 延迟执行样式更新，避免与数据更新冲突
          setTimeout(() => {
            _this.crossingChannel(_this.showCross); //防止更新数据导致箭头颜色明显
          }, 500);
        }
      }

      // 每5分钟清理一次缓存，防止内存泄漏
      if (currentTime - lastCacheCleanTime >= 300000) {
        lastCacheCleanTime = currentTime;
        _this._clearCaches();
      }

      requestAnimationFrame(loop);
    }
    requestAnimationFrame(loop);
  }
  _getAllData() {
    let url = this.property.viewSetting.isShow500KV
      ? this.property.viewSetting.url500KV
      : this.property.viewSetting.url220KV;
    return new Promise((resolve, reject) => {
      if (this.isTest) {
        let jsonName = this.property.viewSetting.isShow500KV
          ? "dwyzt_500"
          : "dwyzt_220";
        d3.json(`${this._foldPath}/json/${jsonName}.json`).then((res) => {
          if (res.map) {
            this.mapObj = res.map;
            this.topoData = res;
          } else {
            this.mapObj = res.mapInfo;
            this.topoData = res.mapContent;
          }

          //   this._update(data);
          resolve();
        });
      } else {
        $.ajax(url, {
          type: "get",
          contentType: "application/json",
          success: (res) => {
            this.mapObj = res.map;
            this.topoData = res;
            resolve();
          },
          error: () => {
            reject();
          },
        });
      }
    });
  }

  /**
   * 获取所有图层列表
   * @returns Promise
   */
  _getMapList() {
    return new Promise((resolve, reject) => {
      $.ajax(
        `${this.property.viewSetting.ip}:${this.property.viewSetting.port}/getMapList`,
        {
          type: "get",
          contentType: "application/json",
          success: ({ data }) => {
            this.mapList = data;
            resolve();
          },
          error: () => {
            reject();
          },
        }
      );
    });
  }

  /**
   * 获取拓扑图的子图层列表
   * @param {string} mapId 图ID
   * @returns Promise
   */
  _getSubLayerList(mapId) {
    return new Promise((resolve, rejcet) => {
      // if (this.isTest) {
      const fileName = this.property.viewSetting.isShow500KV
        ? "sublayerList_500.json"
        : "sublayerList_220.json";
      d3.json(`${this._foldPath}/json/${fileName}`).then(({ data }) => {
        this.subLayerList = data;
        this.subLayerList.sort((a, b) => {
          return b.listOrder - a.listOrder;
        });
        //   console.log(this.subLayerList)
        this.subLayerOrderMap = {};
        this.subLayerList.forEach((sub) => {
          this.subLayerOrderMap[sub.sublayerId] = sub.listOrder;
        });
        // console.log(this.subLayerOrderMap)
        resolve();
      });
      // } else {
      //   $.ajax(
      //     `${this.property.viewSetting.ip}:${this.property.viewSetting.port}/getExistSublayerList`,
      //     {
      //       type: "get",
      //       contentType: "application/json",
      //       data: {
      //         mapId: mapId,
      //       },
      //       success: ({ data }) => {
      //         this.subLayerList = data;
      //         this.subLayerList.sort((a, b) => {
      //           return b.listOrder - a.listOrder;
      //         });
      //         //   console.log(this.subLayerList)
      //         this.subLayerOrderMap = {};
      //         this.subLayerList.forEach((sub) => {
      //           this.subLayerOrderMap[sub.sublayerId] = sub.listOrder;
      //         });
      //         // console.log(this.subLayerOrderMap)
      //         resolve();
      //       },
      //       error: () => {
      //         reject();
      //       },
      //     }
      //   );
      // }
    });
  }

  /**
   * @description 获取拓扑图所有节点数据
   */
  _getNodeLinkList(mapId, sublayerId = null) {
    let param =
      sublayerId === null
        ? {
            mapId: mapId,
          }
        : {
            mapId: mapId,
            sublayerId: sublayerId,
          };
    return new Promise((resolve, reject) => {
      $.ajax(
        `${this.property.viewSetting.ip}:${this.property.viewSetting.port}/getNodeLinkListByMapId`,
        {
          type: "get",
          contentType: "application/json",
          data: param,
          success: ({ data }) => {
            resolve(data);
          },
          error: () => {
            reject();
          },
        }
      );
    });
  }

  /**
   * 获取图层热点区域
   */
  _getHotspotsList(mapId) {
    return new Promise((resolve, reject) => {
      if (this.isTest) {
        d3.json(`${this._foldPath}/json/hotSpotList.json`).then(({ data }) => {
          this.hotspotsList = data;
          this.invokeFunc.filter(
            (d) => d.name === "flyToHotspots"
          )[0].params[0].options = this.hotspotsList.map(function (d) {
            return {
              name: d.hotspotName,
              value: d.hotspotId,
            };
          });
          resolve();
        });
      } else {
        $.ajax(
          `${this.property.viewSetting.ip}:${this.property.viewSetting.port}/getHotspotsList`,
          {
            type: "get",
            contentType: "application/json",
            data: {
              mapId: mapId,
            },
            success: ({ data }) => {
              this.hotspotsList = data;
              this.invokeFunc.filter(
                (d) => d.name === "flyToHotspots"
              )[0].params[0].options = this.hotspotsList.map(function (d) {
                return {
                  name: d.hotspotName,
                  value: d.hotspotId,
                };
              });
              resolve();
            },
            error: () => {
              reject();
            },
          }
        );
      }
    });
  }

  /**
   * 绘制拓扑图
   * @returns
   */
  _drawTopo(mapId) {
    return new Promise((resolve, reject) => {
      this.mapIdList.push(mapId);
      let topoContainer = this.mainSVG
        .select(".nari_current_container")
        .attr("id", `topo_${mapId}`);
      if ($(topoContainer.node()).children().length !== 0) {
        this.mainSVG.select(".topo_last_container").remove();
        this.mainSVG
          .select(".nari_current_container")
          .attr("class", "topo_temp_container")
          .transition()
          .duration(1000)
          .style("opacity", 0)
          .style("transform", "scale(0)")
          .on("end", () => {
            this.mainSVG.select(".topo_temp_container").remove();
          });
        topoContainer = this.mainSVG
          .append("g")
          .attr("class", "nari_current_container")
          .attr("id", `topo_${mapId}`)
          .style("opacity", 0)
          .style("transform", "scale(0)");
        topoContainer
          .transition()
          .duration(1000)
          .style("opacity", 1)
          .style("transform", "scale(1)");
      }
      //   let tw = this.mapList.filter((d) => d.mapId === mapId)[0].mapSize.split('*')[0];
      //   let th = this.mapList.filter((d) => d.mapId === mapId)[0].mapSize.split('*')[1];
      //   let tw = this.mapObj.mapSize.split("*")[0];
      //   let th = this.mapObj.mapSize.split("*")[1];
      let tw = this.property.viewSetting.w;
      let th = this.property.viewSetting.h;
      this.tw = tw;
      this.th = th;
      //   this.mainSVG.attr('viewBox', [0, 0, tw, th]);
      this.mainSVG.attr(
        "viewBox",
        `0 0 ${this.property.viewSetting.w} ${this.property.viewSetting.h}`
      );
      // this.mainSVG.attr('viewBox', `0 0 19600 12600`);
      topoContainer.attr("data-vb", `0 0 ${tw} ${th}`);

      this.blockCon = topoContainer.append("g").attr("id", "block-container");
      this.linkCon = topoContainer.append("g").attr("id", "link-container");
      this.arrowCon = topoContainer.append("g").attr("id", "arrow-container");
      this.nodeCon = topoContainer.append("g").attr("id", "node-container");
      this.limitCon = topoContainer.append("g").attr("id", "limit-container");

      //   this.popCon = topoContainer.append('g').attr('id', 'pop-container')

      //对数据按照图层进行排序TODO:暂时注释

      this.topoData.links.sort((a, b) => {
        return (
          this.subLayerOrderMap[
            b?.sublayerList?.sort((m, n) => {
              return (
                this.subLayerOrderMap[m.sublayerId] -
                this.subLayerOrderMap[n.sublayerId]
              );
            })[0]?.sublayerId
          ] -
          this.subLayerOrderMap[
            a?.sublayerList?.sort((m, n) => {
              return (
                this.subLayerOrderMap[m.sublayerId] -
                this.subLayerOrderMap[n.sublayerId]
              );
            })[0]?.sublayerId
          ]
        );
      });

      this.topoData.nodes.sort((a, b) => {
        return (
          this.subLayerOrderMap[
            b?.sublayerList?.sort((m, n) => {
              return (
                this.subLayerOrderMap[m.sublayerId] -
                this.subLayerOrderMap[n.sublayerId]
              );
            })[0]?.sublayerId
          ] -
          this.subLayerOrderMap[
            a?.sublayerList?.sort((m, n) => {
              return (
                this.subLayerOrderMap[m.sublayerId] -
                this.subLayerOrderMap[n.sublayerId]
              );
            })[0]?.sublayerId
          ]
        );
      });

      this._drawLinks(this.topoData.links);
      requestAnimationFrame(() => {
        this._drawNodes(this.topoData.nodes);
      });

      resolve();
    });
  }

  /**
   * 通过子图层绘制拓扑图
   */
  _drawTopoBySublayer() {}

  /**
   * 通过子图层移除拓扑图
   */
  _removeTopoBySublayer() {}

  /**
   * 检查节点是否全部在容器外，在返回true,否则返回false
   */
  _checkNodeOutContainer(p, s) {
    p = p.split(",").map((d) => Number(d));
    s = s.split("*").map((d) => Number(d));
    let cp = this.containerPosition;
    let ct = this.transform;
    // let cr = this.mainSVG.select('.nari_current_container').node().getBoundingClientRect();
    let x1 = cp.x;
    let x2 = cp.x + cp.width;
    let y1 = cp.y;
    let y2 = cp.y + cp.height;
    //生成节点的4个端点坐标
    let points = [
      [ct.x + ct.k * p[0], ct.y + ct.k * p[1]],
      [ct.x + ct.k * p[0] + ct.k * s[0], ct.y + ct.k * p[1]],
      [ct.x + ct.k * p[0], ct.y + ct.k * p[1] + ct.k * s[1]],
      [ct.x + ct.k * p[0] + ct.k * s[0], ct.y + ct.k * p[1] + ct.k * s[1]],
    ];
    //如果至少一个端点在视图范围内则判断节点在容器内  返回false
    for (let i = 0; i < 4; i++) {
      if (
        points[i][0] >= x1 &&
        points[i][0] <= x2 &&
        points[i][1] >= y1 &&
        points[i][1] <= y2
      ) {
        return false;
      }
    }
    return true;
  }

  /**
   * 检查链路是否完全在容器外，超出返回true,否则返回false
   */
  _checkLinkOutContainer(d) {
    let cp = this.containerPosition;
    let ct = this.transform;
    // let cr = this.mainSVG.select('.nari_current_container').node().getBoundingClientRect();
    let x1 = cp.x;
    let x2 = cp.x + cp.width;
    let y1 = cp.y;
    let y2 = cp.y + cp.height;
    let points = d
      .split("M")[1]
      .split("L")
      .map((d) => d.trim().split(" "));
    for (let i = 0; i < points.length; i++) {
      if (
        points[i][0] * ct.k + ct.x >= x1 &&
        points[i][0] * ct.k + ct.x <= x2 &&
        points[i][1] * ct.k + ct.y >= y1 &&
        points[i][1] * ct.k + ct.y <= y2
      ) {
        return false;
      }
    }
    return true;
  }

  _getLinkStrokeColor(link) {
    const metaData = link.metaData || {};
    if (metaData.volt === "_800KV") {
      return "#c87afe";
    } else if (metaData.volt === "_500KV") {
      if (["ACLine", "AcLine"].includes(metaData.type)) {
        return "#8D8D8D";
      }
      return "#08fe8d";
    } else if (metaData.volt === "_220KV") {
      return "#2cebff";
    } else if (metaData.volt === "_110KV") {
      return "#800080";
    } else {
      return "#9B7238";
    }
  }

  /**
   * 绘制图层中所有链路
   * @param {Array} links 图层中链路数据
   */
  _drawLinks(links) {
    links.forEach((link) => {
      let style = JSON.parse(link.linkStyles);
      let linkDom = null;

      if (style.hasOwnProperty("isBlock") && style.isBlock) {
        //绘制分区
        // if (style.isBlock) {
        if (this.blockCon.select(`#topoLink_${link.linkId}`).node() !== null)
          return;
        linkDom = this.blockCon
          .append("path")
          .attr("id", "topoLink_" + link.linkId)
          .attr(
            "class",
            link.sublayerList
              ? link.sublayerList.map((d) => d.sublayerId).join(" ")
              : ""
          )
          .attr("d", link.linkPath)
          .style("stroke", "#414141")
          .style("stroke-width", "4pt")
          .style("fill", style.fill)
          .style("fill-opacity", 0);
        // }

        if (link.bindMap.mapId) {
          this._bindLinkMap(linkDom, link);
        }
      } else {
        //绘制链路
        // let linkNode = this.linkCon.select(`#topoLink_${link.linkId.replace('#', '_')}`);
        //如果链路所在子图层不存在子图层内且链路存在则删除链路
        if (
          !link.sublayerList ||
          (link.sublayerList && !link.sublayerList.length)
        ) {
          if (
            this.linkCon
              .select(`#topoLink_${link.linkId.replace("#", "_")}`)
              .node() !== null
          ) {
            this.linkCon
              .select(`#topoLink_${link.linkId.replace("#", "_")}`)
              .remove();
            this.arrowCon
              .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
              .remove();
          }
          return;
        }
        //如果链路超出可视区域则判断链路是否存在，存在则删除
        if (this._checkLinkOutContainer(link.linkPath)) {
          if (
            this.linkCon
              .select(`#topoLink_${link.linkId.replace("#", "_")}`)
              .node() !== null
          ) {
            this.linkCon
              .select(`#topoLink_${link.linkId.replace("#", "_")}`)
              .remove();
            this.arrowCon
              .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
              .remove();
          }
          return;
        }
        //如果链路已存在则不重新绘制
        if (
          this.linkCon
            .select(`#topoLink_${link.linkId.replace("#", "_")}`)
            .node() !== null
        ) {
          return;
        }
        //默认链路宽度为6  特高压子图层链路宽度为8, 220KV链路子图层链路宽度为1.5
        // let linkWidth = 4;
        // if (link.sublayerList) {
        //   if (
        //     link.sublayerList.map((d) => d.sublayerId).includes("KoBiqOUwMo")
        //   ) {
        //     linkWidth = 5;
        //   } else if (
        //     link.sublayerList.map((d) => d.sublayerId).includes("KS3tPfwEKM")
        //   ) {
        //     linkWidth = 1;
        //   }
        // }
        // debugger;
        let linkNode = this.linkCon
          .append("path")
          .attr("id", "topoLink_" + link.linkId.replace("#", "_"))
          .attr(
            "class",
            +link.sublayerList
              ? link.sublayerList.map((d) => d.sublayerId).join(" ")
              : "" + this._isLinkCrossArea(link.metaData)
              ? "topo_crossLink"
              : ""
          )
          .attr("d", link.linkPath)
          .style("stroke-width", link.linkWidth)
          .style(
            "stroke-dasharray",
            link.linkType === "虚线" ? link.dashedLink : "none"
          )

          //   .style(
          //     "stroke",
          //     link.metaData.volt === "_500KV"
          //       ? "#a5a9b6"
          //       : link.metaData.volt === "_220KV"
          //       ? "#2cebff"
          //       : "#dac4a0"
          //   )
          .style("stroke", this._getLinkStrokeColor(link))
          // .style('stroke', style.volt === '500' ? 'url(#inset-500)' : style.volt === '220' ? '#2cebff' : '#dac4a0')
          // .style('stroke','blue')
          // .style('filter','url(#inset-500)')
          // .style('filter', style.volt === '500' ? 'url(#inset-500)' : style.volt === '220' ? 'none' : 'url(#inset-500)')
          .style("fill", "none");
        this._isLinkCrossArea(link.metaData);
        // if (linkNode.node() !== null) {
        //如果子图层包含500kv链路且不包含220kv的链路时表示在500kv全景图状态，此时展示箭头
        if (link.sublayerList) {
          if (
            this.arrowCon
              .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
              .node() === null
          ) {
            // 箭头容器
            this.arrowCon
              .append("g")
              .attr("id", `topoArrow_${link.linkId.replace("#", "_")}`);
            // this._drawExternalCompOnLink(link, linkNode);
          }
        } else {
          if (
            this.arrowCon
              .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
              .node() !== null
          ) {
            this.arrowCon
              .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
              .remove();
          }
        }
        // return;
        // }
      }
    });
  }

  /**
   * 添加显示子图层
   * @param {Array} arr 子图层id数组
   */
  _addShowSublayer(arr) {
    arr.forEach((item) => {
      if (this.showSublayer.filter((d) => d === item).length === 0) {
        this.showSublayer.push(item);
      }
    });
  }

  /**
   * 删除显示子图层
   * @param {Array} arr 子图层id数组
   */
  _removeShowSublayer(arr) {
    arr.forEach((item) => {
      this.showSublayer = this.showSublayer.filter((d) => d !== item);
    });
  }

  /**
   * 判断链路是否跨分区
   * @param {Object} metaData 链路MetaData对象
   * @returns 是否跨分区
   */
  _isLinkCrossArea(metaData) {
    if (!metaData) return false;
    if (!metaData.hasOwnProperty("areaKeyId")) return false;
    let areaList = metaData.areaKeyId.split(",");
    if (
      areaList.length >= 2 &&
      areaList.filter((d) => d === "0").length > 0 &&
      areaList[0] !== areaList[1]
    ) {
      return true;
    }
    return false;
  }

  /**
   * 绘制链路的外部对象
   * @param {Object} link 链路
   */
  _drawExternalCompOnLink(link, linkDom, data, num, inx) {
    if (link.compClass && link.compClass !== "") {
      let externalComp = this.mapExternalBind[link.compClass];
      let arrowGroup = this.arrowCon.select(
        `#topoArrow_${link.linkId.replace("#", "_")}`
      );
      // if(arrowGroup.node() === null){
      eval(
        `new ${externalComp.className}(link.linkId, 'test', arrowGroup, 2, externalComp.options, link, linkDom, data,num,inx )`
      );
      // }else{
      // arrowGroup.remove();
      // }
    }
  }

  /**
   * 绘制图层中所有节点
   * @param {Array} nodes 图层中节点数据
   */
  _drawNodes(nodes) {
    return new Promise((resolve, reject) => {
      //   this._setLoadingAttr();
      this.loadingCon.style("display", "block");
      let drawNodeList = [];
      for (let node of nodes) {
        if (
          this._needDrawNode(node) &&
          drawNodeList.filter((d) => d.nodeId === node.nodeId).length === 0
        ) {
          drawNodeList.push(node);
        }
      }
      const _this = this;

      function addNodes(resolve) {
        // let loadNum = 2000;
        for (let i = 0; i <= drawNodeList.length; i++) {
          if (i >= drawNodeList.length) {
            // console.log("finish draw nodes");
            _this.loadingCon.style("display", "none");
            _this.drawEnd && _this.drawEnd();
            resolve();
            return;
          }

          let node = drawNodeList[i];
          requestAnimationFrame(() => {
            _this._drawNode(node);
          });
        }
      }
      requestAnimationFrame(() => {
        addNodes(resolve);
      });
    });
  }

  /**
   * 判断是否需要绘制节点
   * @param {Object} node node对象
   * @returns true为绘制 false为不绘制
   */
  _needDrawNode(node) {
    //当节点的子图层不存在  不展示节点
    if (
      !node.sublayerList ||
      (node.sublayerList && !node.sublayerList.length)
    ) {
      //如果节点存在则删除节点
      if (
        this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).node() !==
        null
      ) {
        this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).remove();
        delete this.powerPlantMap[node.nodeId];
        delete this.transStationMap[node.nodeId];
        delete this.exchStationMap[node.nodeId];
        delete this.areaBoardMap[node.nodeId];
      }
      return false;
    }
    //如果节点在容器外 判断是否存在节点 如果存在则删除节点后绘制下一个节点
    if (this._checkNodeOutContainer(node.nodePosition, node.nodeSize)) {
      if (
        node.sublayerList &&
        node.sublayerList.map((d) => d.sublayerId).includes("M4WacTIW5E")
      ) {
        return (
          this.nodeCon
            .select(`#node_${node.nodeId.replace("#", "_")}`)
            .node() === null
        );
      }
      if (
        this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).node() !==
        null
      ) {
        this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).remove();
        delete this.powerPlantMap[node.nodeId];
        delete this.transStationMap[node.nodeId];
        delete this.exchStationMap[node.nodeId];
        delete this.areaBoardMap[node.nodeId];
      }
      return false;
    }
    //如果需要绘制节点且节点已存在 则不创建新节点但是同步状态
    if (
      this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).node() !==
      null
    ) {
      if (this.powerPlantMap[node.nodeId])
        this.powerPlantMap[node.nodeId].setType(this.nodeType);
      if (this.transStationMap[node.nodeId])
        this.transStationMap[node.nodeId].setType(this.nodeType);
      if (this.exchStationMap[node.nodeId])
        this.exchStationMap[node.nodeId].setType(this.nodeType);
      return false;
    }
    return true;
  }

  _drawNode(node) {
    let nodeDom = null;
    switch (node.nodeType) {
      case "circle":
        nodeDom = this._drawCircleNode(this.nodeCon, node);
        break;
      case "rect":
        nodeDom = this._drawRectNode(this.nodeCon, node);
        break;
      case "text":
        nodeDom = this._drawTextNode(this.nodeCon, node);
        break;
      case "image":
        if (
          node.metaData &&
          (node.metaData.type === "TransStation" ||
            node.metaData.type === "PowerStation" ||
            node.metaData.type === "ExchStation")
        ) {
          nodeDom = this._drawTransformerSubstation(this.nodeCon, node);
        } else {
          nodeDom = this._drawImageNode(this.nodeCon, node);
        }
        break;
      default:
        if (
          node.metaData &&
          (node.metaData.type === "TransStation" ||
            node.metaData.type === "PowerStation" ||
            node.metaData.type === "ExchStation")
        ) {
          nodeDom = this._drawTransformerSubstation(this.nodeCon, node);
        } else {
          nodeDom = this._drawComponentNode(this.nodeCon, node);
        }
        break;
    }
    if (nodeDom !== null) {
      this._bindLinkMap(nodeDom, node);
      if (node.nodeText !== "" && node.nodeType !== "text") {
        // this._drawNodeText(this.nodeCon, node);
      }
    }
  }

  /**
   * 通过node对象获取节点位置以及宽高
   * @param {Object} node 节点对象
   * @returns {x,y,w,h} 节点位置以及宽高
   */
  _getPositionAndSize(node) {
    return {
      x: Number(node.nodePosition.split(",")[0]),
      y: Number(node.nodePosition.split(",")[1]),
      w: Number(node.nodeSize.split("*")[0]),
      h: Number(node.nodeSize.split("*")[1]),
    };
  }

  /**
   * 绘制矩形节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawRectNode(con, node) {
    let { x, y, w, h } = this._getPositionAndSize(node);
    let style = JSON.parse(node.nodeStyles);
    let nodeDom = con
      .append("rect")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("x", x)
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .attr("fill", style.fill);
    return nodeDom;
  }

  /**
   * 绘制椭圆节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawCircleNode(con, node) {
    let { x, y, w, h } = this._getPositionAndSize(node);
    let style = JSON.parse(node.nodeStyles);
    let nodeDom = con
      .append("ellipse")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("cx", x + w / 2)
      .attr("cy", y + h / 2)
      .attr("rx", w / 2)
      .attr("ry", h / 2)
      .attr("stroke", style.stroke)
      .attr("stroke-width", style.strokeWidth)
      .attr("fill", style.fill);
    return nodeDom;
  }

  /**
   * 绘制节点文字
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawNodeText(con, node) {
    let { x, y } = this._getPositionAndSize(node);
    let textPosition = node.textPosition.split(",").map((d) => (d = Number(d)));
    let textStyle = JSON.parse(node.textStyles);
    con
      .append("text")
      .attr("x", textPosition[0] + x)
      .attr("y", textPosition[1] + y)
      .attr("rotate", textStyle.duration === "竖向" ? -90 : 0)
      .attr(
        "transform",
        textStyle.duration === "竖向"
          ? `rotate(90 ${x + node.fontSize / 2} ${y + node.fontSize / 2})`
          : ""
      )
      .style("font-size", node.fontSize)
      .style("fill", node.fontColor)
      .style("letter-space", "2px")
      .style("user-select", "none")
      .text(node.nodeText);
  }

  /**
   * 绘制文字节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawTextNode(con, node) {
    let { x, y, w, h } = this._getPositionAndSize(node);
    let style = JSON.parse(node.nodeStyles);
    let textStyle = JSON.parse(node.textStyles);
    let fontColor = node.fontColor;
    if (
      node.sublayerList &&
      node.sublayerList.map((d) => d.sublayerId).includes("KS5TXwwEDE")
    ) {
      fontColor = "url(#text_500KV_trans_name)";
    }
    const pattern = new RegExp("[\u4E00-\u9FA5]+");
    const letterSpacing = pattern.test(node.nodeText) ? "-3px" : "0";
    let nodeDom = con
      .append("text")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr(
        "class",
        node.sublayerList
          ? node.sublayerList.map((d) => d.sublayerId).join(" ")
          : ""
      )
      .attr(
        "x",
        textStyle.align === "left"
          ? x
          : textStyle.align === "center"
          ? x + w / 2
          : x + w
      )
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .style("fill", fontColor)
      .style("user-select", "none")
      .style("font-size", node.fontSize)
      //   .style('letter-spacing', letterSpacing)
      .style("dominant-baseline", "text-before-edge")
      .style(
        "text-anchor",
        textStyle.align === "left"
          ? "start"
          : textStyle.align === "center"
          ? "middle"
          : "end"
      )
      .attr(
        "transform",
        node.rotate === 0
          ? ""
          : `rotate(${node.rotate} ${x + w / 2} ${y + h / 2})`
      );
    //   .text(node.nodeText === '0000' ? '0' : node.nodeText);
    const list = node.nodeText ? node.nodeText.split(/[\r\n|↵]+/) : [];
    if (list.length > 1) {
      list.forEach((d, i) => {
        nodeDom
          .append("tspan")
          .attr("dy", i === 0 ? 0 : node.fontSize)
          .attr("x", x)
          .style("letter-spacing", letterSpacing)
          .style("font-family", "AR PL UKai CN,楷体,楷体_GB2312")
          .text(d);
      });
    } else {
      nodeDom
        .text(node.nodeText === "0000" ? "0" : node.nodeText)
        .style("font-family", "AR PL UKai CN,楷体,楷体_GB2312");
    }
    if (textStyle.duration === "竖向") {
      nodeDom
        .style("text-anchor", "start")
        .attr("rotate", "-90")
        .attr("transform", `rotate(90 ${x} ${y})`)
        .style("dominant-baseline", "");
    }
    return nodeDom;
  }

  /**
   * 绘制图片节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawImageNode(con, node) {
    let x = Number(node.nodePosition.split(",")[0]);
    let y = Number(node.nodePosition.split(",")[1]);
    let w = Number(node.nodeSize.split("*")[0]);
    let h = Number(node.nodeSize.split("*")[1]);
    let style = JSON.parse(node.nodeStyles);
    let nodeDom = con
      .append("image")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("x", x)
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .attr(
        "xlink:href",
        this.property.viewSetting.ftpIp +
          ":" +
          this.property.viewSetting.ftpPort +
          style.image
      );
    return nodeDom;
  }

  /**
   * 绘制组件节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawComponentNode(con, node) {
    if (node.compClass === "" || node.compClass === undefined) return;
    let x = Number(node.nodePosition.split(",")[0]);
    let y = Number(node.nodePosition.split(",")[1]);
    let w = Number(node.nodeSize.split("*")[0]);
    let h = Number(node.nodeSize.split("*")[1]);
    let internalComp = this.mapInternalBind[node.compClass];
    if (internalComp) {
      let container = con
        .append("foreignObject")
        .attr("id", `node_${node.nodeId.replace("#", "_")}`)
        .attr(
          "class",
          node.sublayerList
            ? node.sublayerList.map((d) => d.sublayerId).join(" ")
            : ""
        )
        .attr("x", x)
        .attr("y", y)
        .attr("width", w)
        .attr("height", h)
        .append("xhtml:div")
        .node();
      internalComp.options["basic"] = {
        frame: [0, 0, w, h],
      };
      let opt = $.extend(
        true,
        internalComp.options,
        JSON.parse(node.nodeStyles).hasOwnProperty("customStyles")
          ? JSON.parse(node.nodeStyles).customStyles
          : {}
      );
      let comp = eval(
        `new ${internalComp.className}(node.nodeId, 'test', container, 2, {'property': opt, 'metaData': node.metaData})`
      );
      if (internalComp.className === "AreaBoard") {
        this.areaBoardMap[node.nodeId] = comp;
      }

      if (node.nodeId == "vwqIy36bqM") {
        d3.select(container).on("click", () => {
          this.flyToHotspots("gk4PLnjleN");
        });
      }
      return comp.mainSVG;
    }
  }

  /**
   * 加载电厂svg数据
   */
  _loadTransformerSubstationSvgData() {
    return new Promise((resolve, reject) => {
      d3.json(`${this._foldPath}/svgData.json`).then((data) => {
        this.svgData = data;
        resolve();
      });
    });
  }

  /**
   * 绘制电厂节点
   * @param {Ibject} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawTransformerSubstation(con, node) {
    let x = Number(node.nodePosition.split(",")[0]);
    let y = Number(node.nodePosition.split(",")[1]);
    let w = Number(node.nodeSize.split("*")[0]);
    let h = Number(node.nodeSize.split("*")[1]);
    let container = con
      .append("svg")
      .attr(
        "class",
        node.sublayerList
          ? node.sublayerList.map((d) => d.sublayerId).join(" ")
          : ""
      )
      .attr("x", x)
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .style("transform", "translateZ(0)")
      .style("transform-origin", "center center");
    if (
      node.nodeId == "P9M5KwTkta#840157_113997366104686638" ||
      node.nodeId == "ftQ9I9Lja9#10902_113997366104686638"
    ) {
      //   container.classed("topo-flash-node", true);
    }
    if (node.metaData.type === "TransStation") {
      if (
        (node.metaData.volt == "_500KV" || node.metaData.volt == "_1000KV") &&
        (this.nodeType == "l" || this.nodeType == "m")
      ) {
        container.attr("viewBox", "0 0 595.3 841.9");
      } else {
        container.attr("viewBox", "0 0 246 246");
      }

      let pp = new TransformerSubstation(node.nodeId, "test", container, 2, {
        x: x,
        y: y,
        w: w,
        h: h,
        type: this.nodeType,
        data: node.metaData,
        svgData: this.svgData,
      });
      this.transStationMap[node.nodeId] = pp;
    } else if (node.metaData.type === "PowerStation") {
      //   container.attr("viewBox", "0 0 209 209");
      // if (
      //   (node.metaData.volt == "_500KV" || node.metaData.volt == "_1000KV") &&
      //   (this.nodeType == "l" || this.nodeType == "m")
      // ) {
      //   container.attr("viewBox", "0 0 595.3 841.9");
      // } else {
      container.attr("viewBox", "0 0 209 209");
      // }
      let pp = new PowerPlant(node.nodeId, "test", container, 2, {
        x: x,
        y: y,
        w: w,
        h: h,
        type: this.nodeType,
        data: node.metaData,
      });
      this.powerPlantMap[node.nodeId] = pp;
    } else {
      if (
        (node.metaData.volt == "_500KV" || node.metaData.volt == "_1000KV") &&
        (this.nodeType == "l" || this.nodeType == "m")
      ) {
        container.attr("viewBox", "0 0 595.3 841.9");
      } else {
        container.attr("viewBox", "0 0 246 246");
      }
      let pp = new ExchStation(node.nodeId, "test", container, 2, {
        x: x,
        y: y,
        w: w,
        h: h,
        type: this.nodeType,
        data: node.metaData,
        svgData: this.svgData,
      });
      this.exchStationMap[node.nodeId] = pp;
    }
  }

  /**
   *
   * @param {Object} nodeDom 节点容器
   * @param {Object} node 节点数据
   */
  _bindLinkMap(nodeDom, node) {
    if (nodeDom === undefined) return;
    nodeDom.on("click", () => {
      if (node.bindMap.mapId) {
        // this._sendMessageByWS(
        //   JSON.stringify({
        //     functionName: 'drawTopoByMapId',
        //     params: node.bindMap.mapId[0],
        //   })
        // );
        this._drawTopoByMapId(node.bindMap.mapId[0]);
      }
    });
  }

  /**
   * 更新拓扑图数据
   */
  _update(data, flag = false) {
    // 性能优化：使用 requestIdleCallback 进行分批处理
    if (this._updateInProgress) {
      return; // 防止重复更新
    }

    this._updateInProgress = true;

    try {
      // 优化：预先过滤需要更新的数据
      const updateQueue = this._prepareUpdateQueue(data);

      if (updateQueue.nodes.length === 0 && updateQueue.links.length === 0) {
        this._updateInProgress = false;
        return;
      }

      // 分批处理节点更新
      this._batchUpdateNodes(updateQueue.nodes, data);

      // 分批处理链路更新（重点优化）
      this._batchUpdateLinks(updateQueue.links, data, flag);
    } finally {
      this._updateInProgress = false;
    }
  }

  /**
   * 准备更新队列，预先过滤需要更新的元素
   */
  _prepareUpdateQueue(data) {
    const updateQueue = { nodes: [], links: [] };

    // 优化节点过滤
    if (this.nodeType === "m" || this.nodeType === "l") {
      for (const node of this.topoData.nodes) {
        if (node.metaData && data[node.nodeId]) {
          const nodeMeta = node.metaData;

          // 预先检查是否需要更新
          if (this._shouldUpdateNode(node, nodeMeta, data[node.nodeId])) {
            updateQueue.nodes.push({ node, data: data[node.nodeId] });
          }
        }
      }
    }

    // 优化链路过滤
    for (const link of this.topoData.links) {
      if (link.metaData && data[link.linkId]) {
        const linkMeta = link.metaData;
        if (
          ["AcLine", "ACLine", "DcLine", "DCLine", "DC"].includes(linkMeta.type)
        ) {
          // 检查数据是否真的发生了变化
          if (this._shouldUpdateLink(link, data[link.linkId])) {
            updateQueue.links.push({ link, data: data[link.linkId] });
          }
        }
      }
    }

    return updateQueue;
  }

  /**
   * 检查节点是否需要更新
   */
  _shouldUpdateNode(node, nodeMeta, newData) {
    const nodeId = node.nodeId;
    const lastData = this._lastNodeData?.[nodeId];

    // 如果没有历史数据，需要更新
    if (!lastData) return true;

    // 比较关键字段是否发生变化
    const keyFields = ["value", "flash", "status"];
    return keyFields.some((field) => lastData[field] !== newData[field]);
  }

  /**
   * 检查链路是否需要更新
   */
  _shouldUpdateLink(link, newData) {
    const linkId = link.linkId;
    const lastData = this._lastLinkData?.[linkId];

    // 如果没有历史数据，需要更新
    if (!lastData) return true;

    // 比较关键字段是否发生变化
    const keyFields = ["direction", "status", "flash", "rate", "isDivide"];
    return keyFields.some((field) => lastData[field] !== newData[field]);
  }

  /**
   * 分批处理节点更新
   */
  _batchUpdateNodes(nodeQueue, allData) {
    if (nodeQueue.length === 0) return;

    // 初始化历史数据存储
    if (!this._lastNodeData) this._lastNodeData = {};

    const BATCH_SIZE = 50; // 每批处理50个节点
    let currentIndex = 0;

    const processBatch = () => {
      const endIndex = Math.min(currentIndex + BATCH_SIZE, nodeQueue.length);

      for (let i = currentIndex; i < endIndex; i++) {
        const { node, data } = nodeQueue[i];
        const nodeMeta = node.metaData;

        try {
          switch (nodeMeta.type) {
            case "DText":
              this._updateText(node.nodeId, data);
              break;
            case "TransStation":
              if (this.transStationMap.hasOwnProperty(node.nodeId)) {
                this.transStationMap[node.nodeId].update(data);
              }
              break;
            case "PowerStation":
              if (
                this.powerPlantMap.hasOwnProperty(node.nodeId) &&
                nodeMeta.num !== 0 &&
                ["THERMAL", "PUMP", "NUCLEAR", "WIND"].includes(
                  nodeMeta.powerType
                )
              ) {
                this.powerPlantMap[node.nodeId].update(data);
              }
              break;
            case "ExchStation":
              if (this.exchStationMap[node.nodeId]) {
                this.exchStationMap[node.nodeId].update(data);
              }
              break;
            case "AreaBoard":
              if (
                this.showSublayer.includes("SDAfqbRLOiv") &&
                this.areaBoardMap[node.nodeId]
              ) {
                this.areaBoardMap[node.nodeId].update(data);
              }
              break;
          }

          // 保存历史数据
          this._lastNodeData[node.nodeId] = { ...data };
        } catch (error) {
          console.warn(`Error updating node ${node.nodeId}:`, error);
        }
      }

      currentIndex = endIndex;

      // 如果还有更多节点需要处理，使用 requestAnimationFrame 继续
      if (currentIndex < nodeQueue.length) {
        requestAnimationFrame(processBatch);
      }
    };

    // 开始处理第一批
    processBatch();
  }

  /**
   * 分批处理链路更新（重点优化）
   */
  _batchUpdateLinks(linkQueue, allData, flag) {
    if (linkQueue.length === 0) return;

    // 初始化历史数据存储
    if (!this._lastLinkData) this._lastLinkData = {};

    const BATCH_SIZE = 30; // 每批处理30个链路（链路更新更耗时）
    let currentIndex = 0;

    const processBatch = () => {
      const endIndex = Math.min(currentIndex + BATCH_SIZE, linkQueue.length);

      for (let i = currentIndex; i < endIndex; i++) {
        const { link, data } = linkQueue[i];

        try {
          this._updateAcLine(link, data, flag);

          // 保存历史数据
          this._lastLinkData[link.linkId] = { ...data };
        } catch (error) {
          console.warn(`Error updating link ${link.linkId}:`, error);
        }
      }

      currentIndex = endIndex;

      // 如果还有更多链路需要处理，使用 requestAnimationFrame 继续
      if (currentIndex < linkQueue.length) {
        requestAnimationFrame(processBatch);
      }
    };

    // 开始处理第一批
    processBatch();
  }

  /**
   * 更新文字组件
   */
  _updateText(id, data) {
    this.nodeCon
      .select(`#node_${id.replace("#", "_")}`)
      .classed("redTextFilter", data && data.flash === "1")
      //   .classed("topo-flash", data && data.flash === "1")
      .text(data.value);
  }

  /**
   * 更新链路运动方向
   */
  _updateAcLine(link, data, flag) {
    // if (link.linkId == "9IsOgpHld1") debugger;
    //data:{ "isDivide": "0","direction": "1","flash": "0","status": "0"}
    //direction字典: -1 反向 1 正向 0无箭头
    //status字典: -1 灰 0 蓝 1银色 2 紫 3 玫红 4 橙 5 黄 6 虚
    // let lineColorDic = {
    //   "-1": "#e800ff",
    //   0: "#2cebff",
    //   1: "#5f5f5f",
    //   2: "#604718",
    //   3: "#e71864",
    //   4: "#ff7800",
    //   5: "#e4ff00",
    //   6: "#00b4ff",
    // };

    // 性能优化：缓存DOM选择器结果
    const linkIdSafe = link.linkId.replace("#", "_");
    const linkSelector = `#topoLink_${linkIdSafe}`;
    const arrowSelector = `#topoArrow_${linkIdSafe}`;

    // 缓存DOM元素
    let linkNode = this._linkNodeCache?.[linkIdSafe];
    if (!linkNode || linkNode.empty()) {
      linkNode = this.linkCon.select(linkSelector);
      if (!this._linkNodeCache) this._linkNodeCache = {};
      this._linkNodeCache[linkIdSafe] = linkNode;
    }

    // 如果链路不存在，直接返回
    if (linkNode.empty()) return;

    let arrowGroup = this._arrowGroupCache?.[linkIdSafe];
    if (!arrowGroup || arrowGroup.empty()) {
      arrowGroup = this.arrowCon.select(arrowSelector);
      if (!this._arrowGroupCache) this._arrowGroupCache = {};
      this._arrowGroupCache[linkIdSafe] = arrowGroup;
    }

    // 性能优化：缓存链路长度
    let linkLen = this._linkLengthCache?.[linkIdSafe];
    if (linkLen === undefined) {
      const linkElement = linkNode.node();
      linkLen = linkElement?.getTotalLength() || 100;
      if (!this._linkLengthCache) this._linkLengthCache = {};
      this._linkLengthCache[linkIdSafe] = linkLen;
    }

    // if (link.linkId == "FxEdigDsCQ") {
    //   arrowGroup.selectAll("polygon").remove();
    // } else {
    //   if (!arrowGroup.selectAll("polygon").empty()) {
    //     return;
    //   }
    // }

    // if (!arrowGroup.selectAll("polygon").empty()) {
    //   return;
    // }
    // console.log(arrowGroup.selectAll("polygon").empty());

    // 清除之前边框样式
    // this.linkCon
    //   .select(`#topoLink_${link.linkId.replace("#", "_")}`)
    //   .classed("whiteBorderFilter", false);
    // this.linkCon
    //   .select(`#topoLink_${link.linkId.replace("#", "_")}`)
    //   .classed("yellowBorderFilter", false);

    // this.linkCon
    //   .select(`#topoLink_${link.linkId.replace("#", "_")}`)
    //   .classed("greenBorderFilter", false);

    // this.linkCon
    //   .select(`#topoLink_${link.linkId.replace("#", "_")}`)
    //   .classed("purpleBorderFilter", false);

    const selection = this.linkCon.select(
      `#topoLink_${link.linkId.replace("#", "_")}`
    );

    // 先移除所有可能的状态class（避免多个状态同时存在）
    selection.classed(
      "whiteBorderFilter greenBorderFilter yellowBorderFilter purpleBorderFilter",
      false
    );

    this.linkCon
      .select(`#topoLink_${link.linkId.replace("#", "_")}`)
      .classed("topo-isDivide", data.isDivide === "1")
      .classed("redTextFilter", data && data.flash === "1")
      //   .classed("topo-flash", data.flash === "1")
      .attr("stroke-dasharray", data.status === "6" ? "20 20" : "none");

    const { volt, type } = link.metaData;
    if (data.status != "6") {
      if (!["_220KV", "_110KV"].includes(volt)) {
        this.linkCon
          .select(`#topoLink_${link.linkId.replace("#", "_")}`)
          .style("stroke", this.lineColorDic2[data.status]);
      }

      if (data && data.flash === "0") {
        // this.linkCon
        //   .select(`#topoLink_${link.linkId.replace("#", "_")}`)
        //   .attr("class", () => {
        //     if (volt == "_500KV") {
        //       if (["ACLine", "AcLine"].includes(type)) {
        //         return "whiteBorderFilter";
        //       } else {
        //         return "greenBorderFilter";
        //       }
        //     } else if (volt == "_1000KV") {
        //       return "yellowBorderFilter";
        //     } else if (volt == "_800KV") {
        //       return "purpleBorderFilter";
        //     }
        //   });

        // 然后根据条件添加需要的class
        if (volt == "_500KV") {
          if (["ACLine", "AcLine"].includes(type)) {
            selection.classed("whiteBorderFilter", true);
          } else {
            selection.classed("greenBorderFilter", true);
          }
        } else if (volt == "_1000KV") {
          selection.classed("yellowBorderFilter", true);
        } else if (volt == "_800KV") {
          selection.classed("purpleBorderFilter", true);
        }
      }
    } else {
      this.linkCon
        .select(`#topoLink_${link.linkId.replace("#", "_")}`)
        // .classed(link.metaData.volt == '_500kv' ? 'whiteBorderFilter': '')
        .style("stroke", "#8d8d8d");
    }

    if (data && data.flash === "1") {
      this.linkCon
        .select(`#topoLink_${link.linkId.replace("#", "_")}`)
        .style("stroke", "#9B2738");
    }

    if (data.isDivide === "1") {
      data.nodes &&
        data.nodes.forEach((id) => {
          this.nodeCon
            .select(`#node_${id.replace("#", "_")}`)
            .classed("topo-isDivide", true);
        });
    }

    // 根据线路状态绘制箭头 status :5 :无箭头, 其他：多箭头;  flash:1: 多箭头,
    let num = 0;
    //   data.flash === "1"
    //     ? Math.ceil((linkLen / 80) * data.rate)
    //     : Math.ceil((linkLen / 80) * data.rate);
    if (data.rate >= 1) {
      num = Math.ceil(linkLen / 35);
    } else if (data.rate >= 0.9 && data.rate < 1) {
      num = Math.ceil(linkLen / 70);
    } else if (data.rate >= 0.8 && data.rate < 0.9) {
      num = Math.ceil(linkLen / 115);
    } else {
      num = Math.ceil(linkLen / 140);
    }
    if (data.status === "6") {
      num = 1;
    }
    if (["_220KV", "_110KV"].includes(link.metaData.volt)) {
      num = 1;
    }
    if (data.status === "5") {
      num = 0;
    }
    if (data.direction == "0") {
      num = 0;
    }

    // 性能优化：智能箭头更新策略
    const linkId = link.linkId;
    const lastNum = this.arrowMap[linkId];
    const lastDir = this.arrowDir[linkId];

    // 检查是否需要重新绘制箭头
    const needRedraw =
      flag ||
      lastNum === undefined ||
      lastNum !== num ||
      lastDir !== data.direction;

    if (!needRedraw) {
      return; // 无需更新，直接返回
    }

    // 批量移除现有箭头（性能优化）
    if (!arrowGroup.empty()) {
      const polygons = arrowGroup.selectAll("polygon");
      if (!polygons.empty()) {
        polygons.remove();
      }
    }

    // 更新缓存
    this.arrowMap[linkId] = num;
    this.arrowDir[linkId] = data.direction;

    // 性能优化：如果箭头数量为0，直接返回
    if (num === 0) return;

    // 分批绘制箭头以避免阻塞UI
    if (num > 10) {
      this._batchDrawArrows(link, linkNode, data, num);
    } else {
      // 少量箭头直接绘制
      for (let i = 0; i < num; i++) {
        this._drawExternalCompOnLink(link, linkNode, data, num, i);
      }
    }
  }

  /**
   * 分批绘制箭头以避免UI阻塞
   */
  _batchDrawArrows(link, linkNode, data, totalNum) {
    const BATCH_SIZE = 5; // 每批绘制5个箭头
    let currentIndex = 0;

    const drawBatch = () => {
      const endIndex = Math.min(currentIndex + BATCH_SIZE, totalNum);

      for (let i = currentIndex; i < endIndex; i++) {
        try {
          this._drawExternalCompOnLink(link, linkNode, data, totalNum, i);
        } catch (error) {
          console.warn(
            `Error drawing arrow ${i} for link ${link.linkId}:`,
            error
          );
        }
      }

      currentIndex = endIndex;

      // 如果还有更多箭头需要绘制，使用 requestAnimationFrame 继续
      if (currentIndex < totalNum) {
        requestAnimationFrame(drawBatch);
      }
    };

    // 开始绘制第一批
    drawBatch();
  }

  /**
   * 清理缓存以防止内存泄漏
   */
  _clearCaches() {
    // 清理DOM缓存
    this._linkNodeCache = {};
    this._arrowGroupCache = {};

    // 保留长度缓存，因为链路长度不会改变
    // this._linkLengthCache = {};

    // 清理历史数据缓存（保留最近的数据）
    const maxCacheSize = 1000;
    if (Object.keys(this._lastNodeData).length > maxCacheSize) {
      const keys = Object.keys(this._lastNodeData);
      const keysToDelete = keys.slice(0, keys.length - maxCacheSize);
      keysToDelete.forEach((key) => delete this._lastNodeData[key]);
    }

    if (Object.keys(this._lastLinkData).length > maxCacheSize) {
      const keys = Object.keys(this._lastLinkData);
      const keysToDelete = keys.slice(0, keys.length - maxCacheSize);
      keysToDelete.forEach((key) => delete this._lastLinkData[key]);
    }
  }

  /**
   * 优化的缩放事件处理
   */
  _onZoomStart() {
    // 清理DOM缓存，因为缩放可能导致DOM重新创建
    this._linkNodeCache = {};
    this._arrowGroupCache = {};

    // 清理现有的弹窗
    d3.selectAll(".allPoUp").remove();

    // 清理组件映射
    this.transStationMap = {};
    this.powerPlantMap = {};
    this.areaBoardMap = {};
    this.exchStationMap = {};
  }

  _filterArmInfo(oldVal, newVal) {
    let armObj = {
      eventList: [],
      zhsgzRecvList: [],
    };
    return new Promise((resolve, reject) => {
      for (let i = 0; i < newVal.eventList.length; i++) {
        if (newVal.eventList[i].faultSec > oldVal.eventList[0].faultSec) {
          armObj.eventList.push(newVal.eventList[i]);
        }
      }

      for (let i = 0; i < newVal.zhsgzRecvList.length; i++) {
        if (newVal.zhsgzRecvList[i].recvSec > oldVal.zhsgzRecvList[0].recvSec) {
          armObj.zhsgzRecvList.push(newVal.zhsgzRecvList[i]);
        }
      }

      //   armObj.eventList.push(newVal.eventList[0]);
      //   armObj.zhsgzRecvList.push(newVal.zhsgzRecvList[0]);

      resolve(armObj);
    });
  }

  _drawPop(posX, posY, obj, type, sessionData) {
    let iw = this.property.viewSetting.iw;
    let ih = this.property.viewSetting.ih;
    let mapW = this.property.viewSetting.mapW;

    let id = obj.objId;
    let frameId = type == "event" ? obj.eventId : obj.id;
    let container = d3
      .select(this.container)
      .append("div")
      .attr("id", `pop_${id.replace("#", "_")}`)
      .attr("class", "allPoUp")
      .style("display", "flex")
      .style("left", `${posX}px`)
      .style("top", `${posY}px`)
      .style("position", "absolute")
      .style("width", `${iw + mapW}px`)
      .style("height", `${ih}px`);
    // .append('xhtml:div');

    let armFrame = container
      .append("div")
      .attr("class", "armPop")
      .style("width", `${iw}px`)
      .style("height", `${ih}px`);

    let url = `http://28.46.2.22:7080/osp/GraphPub/Navigator.html?graph=${
      type == "event"
        ? "ifa_fault_simple.sys.pic.g;m_event_id=" + frameId
        : "ifa_zhsgz_info.sys.pic.g;m_event_id=" + frameId
    };menubarshow=0;toolbarshow=0&nuspLoginSurfix=${
      sessionData.nuspLoginSurfix
    }&nuspLoginName=${sessionData.nuspLoginName}&userRealName=${
      sessionData.userRealName
    }`;

    // url = "http://127.0.0.1:5500/mine.html";

    // console.info("创建告警弹窗");
    let comp = new HtmlViewer(`_${id}`, "123", armFrame.node(), 0, {
      property: {
        basic: {
          frame: [0, 0, iw, ih],
        },
        properties: {
          src: url,
          adaption: true,
          pageWidth: iw,
          pageHeight: ih,
          scrolling: true,
        },
      },
    });

    let mapComp = null;
    if (window.commandType != "") {
      let mapFrame = container
        .append("div")
        .attr("class", "mapPop")
        .style("width", `${mapW}px`)
        .style("height", `${ih}px`);

      let dkyId =
        type != "event"
          ? obj.stId
          : obj.faultDev.startsWith("1201")
          ? obj.faultDev
          : obj.seqFac01;

      let param =
        type == "event"
          ? obj.faultDev.startsWith("1201")
            ? "dkyId"
            : "factoryId"
          : "factoryId";

      let mapURL = `http://20.47.122.83/fxgk-ui/#/apimap?${param}=${dkyId}`; //正式

      // let mapURL = 'http://172.19.42.21:5504/WisComponents/hdTopoView/test.html'

      mapComp = new HtmlViewer(`_map_${id}`, "123", mapFrame.node(), 0, {
        property: {
          basic: {
            frame: [0, 0, mapW, ih],
          },
          properties: {
            src: mapURL,
            adaption: true,
            pageWidth: mapW,
            pageHeight: ih,
            scrolling: true,
          },
        },
      });
    }

    //定时删除弹窗
    setTimeout(() => {
      d3.select(`#pop_${id.replace("#", "_")}`).remove();
      mapComp = null;
      comp = null;
    }, 30 * 1000);
  }

  async _armPop(armInfo) {
    let { eventList, zhsgzRecvList } = armInfo;
    // console.log(eventList, zhsgzRecvList);
    let width = this.tw;
    let height = this.th;
    let iw = this.property.viewSetting.iw;
    let ih = this.property.viewSetting.ih;
    let mapW = this.property.viewSetting.mapW;
    let ct = this.transform;

    let sessionData = await this._getNariSession();
    // console.log("session", sessionData);
    let posX = width / 2 - (iw / 2 + mapW / 2);
    let posY = height / 2 - ih / 2;

    for (let i = 0; i < eventList.length; i++) {
      //   if (eventList[i].objType == "node") {
      //     let dom = this.nodeCon.select(
      //       `#node_${eventList[i].objId.replace("#", "_")}`
      //     );
      // console.log(dom.empty());
      // if (dom.empty()) {
      //显示在屏幕中间

      // posX = width / 2 - iw / 2;
      // posY = height / 2 - ih / 2;
      // this._drawPop(posX,posY,eventList[i].objId,'event')

      //   alert('666')
      // } else {
      //   let x = Number(dom.attr("x"));
      //   let y = Number(dom.attr("y"));
      //   let w = Number(dom.attr("width"));
      //   let h = Number(dom.attr("height"));

      //   posX = (x + w) * ct.k + ct.x;
      //   posY = y * ct.k + ct.y;

      //   if (posX + iw > width) {
      //     posX = posX - iw - ct.k * w;
      //   }
      //   if (posX < 0) {
      //     posX = 0;
      //   }
      //   if (posY + ih > height) {
      //     posY = posY - ih;
      //   }
      //   if (posY < 0) {
      //     posY = 0;
      //   }

      //   //   this._drawPop(posX,posY,eventList[i].objId)
      // }
      //   } else {
      //     let dom = this.linkCon.select(
      //       `#topoLink_${eventList[i].objId.replace("#", "_")}`
      //     );

      // let dom = this.linkCon.select( `#topoLink_dT1m6UicZk_538054_113997366104687051`)

      // if (dom.empty()) {
      //显示在屏幕中间

      // posX = width / 2 - iw / 2;
      // posY = height / 2 - ih / 2;
      // this._drawPop(posX,posY,eventList[i].objId)

      //   alert('666')
      // } else {
      //   let d = dom.attr("d");

      //   let points = d
      //     .split("M")[1]
      //     .split("L")
      //     .map((d) => d.trim().split(" "));
      //   console.log(points);
      //   let arrX = points.map((d) => {
      //     return d[0];
      //   });

      //   let arrY = points.map((d) => {
      //     return d[1];
      //   });

      //   let minX = d3.min(arrX);
      //   let minY = d3.min(arrY);
      //   let maxY = d3.max(arrY);

      //   console.log(minX, minY, maxY);

      //   posX = minX * ct.k + ct.x;
      //   posY = maxY * ct.k + ct.y;

      //   if (posX + iw > width) {
      //     posX = posX - iw;
      //   }

      //   if (posY + ih > height) {
      //     posY = minY * ct.k + ct.y - ih;
      //   }

      //   // this._drawPop(posX,posY,eventList[i].objId)
      // }
      //   }

      this._drawPop(posX, posY, eventList[i], "event", sessionData);
    }

    for (let i = 0; i < zhsgzRecvList.length; i++) {
      //   posX = 0;
      //   posY = 0;
      //   if (zhsgzRecvList[i].objType == "node") {
      //     let dom = this.nodeCon.select(
      //       `#node_${zhsgzRecvList[i].objId.replace("#", "_")}`
      //     );
      // console.log(dom.empty());
      // if (dom.empty()) {
      //显示在屏幕中间

      // posX = width / 2 - iw / 2;
      // posY = height / 2 - ih / 2;
      //   this._drawPop(posX,posY,zhsgzRecvList[i].objId)

      //   alert('666')
      // } else {
      //   let x = Number(dom.attr("x"));
      //   let y = Number(dom.attr("y"));
      //   let w = Number(dom.attr("width"));
      //   let h = Number(dom.attr("height"));

      //   posX = (x + w) * ct.k + ct.x;
      //   posY = y * ct.k + ct.y;

      //   if (posX + iw > width) {
      //     posX = posX - iw - ct.k * w;
      //   }
      //   if (posX < 0) {
      //     posX = 0;
      //   }
      //   if (posY + ih > height) {
      //     posY = posY - ih;
      //   }
      //   if (posY < 0) {
      //     posY = 0;
      //   }

      //   // this._drawPop(posX,posY,zhsgzRecvList[i].objId)
      // }
      //   } else {
      // let dom = this.linkCon.select(
      //   `#topoLink_${zhsgzRecvList[i].objId.replace("#", "_")}`
      // );

      // let dom = this.linkCon.select( `#topoLink_dT1m6UicZk_538054_113997366104687051`)

      // if (dom.empty()) {
      //显示在屏幕中间

      // posX = width / 2 - iw / 2;
      // posY = height / 2 - ih / 2;
      //   this._drawPop(posX,posY,zhsgzRecvList[i].objId)

      //   alert('666')
      // } else {
      //   let d = dom.attr("d");

      //   let points = d
      //     .split("M")[1]
      //     .split("L")
      //     .map((d) => d.trim().split(" "));
      //   console.log(points);
      //   let arrX = points.map((d) => {
      //     return d[0];
      //   });

      //   let arrY = points.map((d) => {
      //     return d[1];
      //   });

      //   let minX = d3.min(arrX);
      //   let minY = d3.min(arrY);
      //   let maxY = d3.max(arrY);

      //   console.log(minX, minY, maxY);

      //   posX = minX * ct.k + ct.x;
      //   posY = maxY * ct.k + ct.y;

      //   if (posX + iw > width) {
      //     posX = posX - iw;
      //   }

      //   if (posY + ih > height) {
      //     posY = minY * ct.k + ct.y - ih;
      //   }

      //   //   this._drawPop(posX,posY,zhsgzRecvList[i].objId)
      // }
      //   }

      this._drawPop(posX, posY, zhsgzRecvList[i], "zhsgz", sessionData);
    }
  }

  _handleData(data) {
    Object.keys(this.armData).length == 0 &&
      (this.armData = JSON.parse(JSON.stringify(data)));

    //   console.log(this.armData)
    //   筛选出新的告警信息
    this._filterArmInfo(this.armData, data).then((armInfo) => {
      //告警弹窗
      this.currentArmInfo = armInfo;
      // console.info("告警信息", armInfo);
      this._armPop(armInfo);

      //   将新数据赋值保存；
      this.armData = JSON.parse(JSON.stringify(data));
    });
  }

  _getArmInfo(mapId) {
    new Promise((resolve, reject) => {
      if (this.isTest) {
        d3.json(`${this._foldPath}/armInfo.json`).then(({ data }) => {
          this._handleData(data);
          resolve();
        });
      } else {
        $.ajax(`${this.property.viewSetting.armUrl}`, {
          type: "get",
          contentType: "application/json",
          //   data: {
          //     mapId: mapId,
          //   },
          success: ({ data }) => {
            // this.mapData = data;
            this._handleData(data);
            resolve();
          },
          error: () => {
            reject();
          },
        });
      }
    });
  }

  _getNariSession() {
    return new Promise((resolve, reject) => {
      if (this.isTest) {
        $.ajax(`http://172.19.139.41:8087/mock/189/dwyzt/getNariSession`, {
          type: "get",
          contentType: "application/json",
          // data: {
          //   mapId: mapId,
          // },
          success: ({ data }) => {
            resolve(data);
          },
          error: () => {
            reject();
          },
        });
      } else {
        $.ajax(`http://26.47.29.52/dwyzt/getNariSession`, {
          type: "get",
          contentType: "application/json",
          // data: {
          //   mapId: mapId,
          // },
          success: ({ data }) => {
            resolve(data);
          },
          error: () => {
            reject();
          },
        });
      }
    });
  }

  _handleLimitData(data) {
    // console.log(data);
    let allDevice = data
      .map((item) => {
        return item.sectionDeviceInfoPOS;
      })
      .flat();
    // console.log(allDevice);

    // 转化cloudDeviceId为rtKeyId{num}
    allDevice.forEach((d) => {
      if (d.cloudDeviceId[0] == 0) {
        // console.log('11')
        d.rtKeyId =
          d.cloudDeviceId.slice(1, 4) + ":" + d.cloudDeviceId.slice(4) + "0000";
      } else {
        d.rtKeyId =
          d.cloudDeviceId.slice(0, 4) + ":" + d.cloudDeviceId.slice(4) + "0000";
      }
    });

    // console.log("rtkeyId", allDevice);
    // 根据rtKeyId筛选出页面线路/厂站，对应metaDta>rtKeyId/rtKeyId{num}
    // console.log(allDevice.map((v) => v.rtKeyId));

    let links = this.topoData.links.filter((d) => {
      return allDevice
        .map((v) => v.rtKeyId)
        .includes(d.metaData && d.metaData.rtKeyId0);
    });
    // console.log("links", links);

    let nodes = this.topoData.nodes.filter((d) => {
      return this._judgeNodeDevice(
        allDevice.map((v) => v.rtKeyId),
        d
      );
    });

    // console.log("nodes", nodes);

    // 将nodeId和linkId都筛选出来
    let nodeLinkIdArr = links.concat(nodes).map((v) => {
      return v.nodeId || v.linkId;
    });
    // console.log("nodeLinkIdArr", nodeLinkIdArr);
    // [ 'tLFLACi8UA']  未绑定节点

    // 找到关联（bindLink）了筛选出的节点和线路的文字节点，
    let dTextNodes = this.topoData.nodes.filter((d) => {
      return d.nodeType == "text" && d.bindLink && d.nodeText != "000";
    });

    // console.log("dTextNodes", dTextNodes);

    // 筛选出绑定了nodes和links的DText
    //

    let bindText = dTextNodes.filter((d) => {
      return nodeLinkIdArr.includes(d.bindLink);
    });

    // console.log("bindText", bindText);
    // 在绑定的文字节点旁边添加图片
    this._drawLimitImage(bindText);
  }

  _judgeNodeDevice(keyIdArr, node) {
    if (!node.metaData) return false;
    // 需要找出node.metaData中rtkeyId(num)字段，只要有一个字段的值在keyIdArr中，返回true，否则返回false
    let lab = "";
    for (let key in node.metaData) {
      if (key.startsWith("rtKeyId") && !key.endsWith("Desc")) {
        lab = lab + "," + node.metaData[key];
      }
    }

    let nodeTemp = keyIdArr.filter((id) => {
      return lab.indexOf(id) != "-1";
    });

    if (nodeTemp.length > 0) {
      return true;
    }
    return false;
  }

  _drawLimitImage(textNodes) {
    $(this.limitCon.node()).empty();
    textNodes.forEach((node) => {
      let x = Number(node.nodePosition.split(",")[0]);
      let y = Number(node.nodePosition.split(",")[1]);
      let w = Number(node.nodeSize.split("*")[0]);
      let h = Number(node.nodeSize.split("*")[1]);
      this.limitCon
        .append("image")
        .attr("id", `limit_${node.nodeId.replace("#", "_")}`)
        .attr("x", x - w)
        .attr("y", y)
        .attr("width", w)
        .attr("height", h)
        .attr(
          "transform",
          node.rotate === 0
            ? ""
            : `rotate(${node.rotate} ${x + w / 2} ${y + h / 2})`
        )
        .attr("xlink:href", `${this._foldPath}/images/limit.png`);
    });
  }

  //   获取越限设备
  _getLimitDevice() {
    return new Promise((resolve, reject) => {
      if (this.isTest) {
        $.ajax(
          `http://172.19.139.41:8087/mock/189/dispatchscreen-cloud/balance/section/loadSectionAndDeviceInfo`,
          {
            type: "get",
            contentType: "application/json",
            // data: {
            //   mapId: mapId,
            // },
            success: ({ data }) => {
              this._handleLimitData(data);
              resolve();
            },
            error: () => {
              reject();
            },
          }
        );
      } else {
        $.ajax(
          `http://26.47.29.11:8080/dispatchscreen-cloud/balance/section/loadSectionAndDeviceInfo`,
          {
            type: "get",
            contentType: "application/json",
            // data: {
            //   mapId: mapId,
            // },
            success: ({ data }) => {
              this._handleLimitData(data);
              resolve();
            },
            error: () => {
              reject();
            },
          }
        );
      }
    });
  }

  _getUrlParameter(url, paramName) {
    try {
      const urlObj = new URL(url);
      return urlObj.searchParams.get(paramName);
    } catch (error) {
      console.error("Invalid URL:", error);
      return null;
    }
  }

  /**
   * 接收订阅数据
   */
  _getData(mapId) {
    new Promise((resolve, reject) => {
      if (this.isTest) {
        // this.df = !this.df;
        // if (this.df) {
        d3.json(`${this._foldPath}/json/data.json`).then(({ data }) => {
          this.mapData = data;
          this._update(data);
          resolve();
        });
        // } else {
        //   d3.json(`${this._foldPath}/data1.json`).then(({ data }) => {
        //     this.mapData = data;
        //     this._update(data);
        //     resolve();
        //   });
        // }
      } else {
        // 如果dataURL中的mapId = xxx 或者 空，则不进行网络请求
        if (!this.property.viewSetting.dataUrl) return;

        // const mapId = this._getUrlParameter(
        //   this.property.viewSetting.dataUrl,
        //   "mapId"
        // );
        // if (!mapId || mapId === "xxx") {
        //   return;
        // }
        $.ajax(`${this.property.viewSetting.dataUrl}`, {
          type: "get",
          contentType: "application/json",
          // data: {
          //   mapId: mapId,
          // },
          success: ({ data }) => {
            this.mapData = data;
            this._update(data);
            resolve();
          },
          error: () => {
            reject();
          },
        });
      }
    });
  }
}

/**
 * 链路箭头
 */
class NariTopoArrow {
  constructor(id, code, con, workMode, option, link, linkDom, data, num, inx) {
    this.id = id;
    this.con = con;
    // this.option = option;
    this.link = link;
    this.linkDom = linkDom;
    this.data = data; //接口数据
    this.num = num;
    this.inx = inx;
    this._foldPath = WisUtil.scriptPath("NariTopoView");

    this.style = JSON.parse(link.linkStyles);
    this._draw();
  }

  _getArrowColor() {
    const { flash } = this.data;
    const { volt, type } = this.link.metaData;
    if (flash == "1") {
      return "#FFE5E5";
    } else if (volt === "_500KV") {
      if (["ACLine", "AcLine"].includes(type)) {
        return "#ffffff";
      }
      return "#08fe8d";
    } else if (volt === "_1000KV") {
      return "#FFDD2E";
    } else if (volt === "_800KV") {
      return "#c87afe";
    } else if (volt === "_220KV") {
      return "#2cebff";
    } else if (volt === "_110KV") {
      return "#800080";
    } else {
      return "#2cebff";
    }
  }

  // 箭头宽高30*30
  _draw() {
    const { volt } = this.link.metaData;
    let w = volt == "_500KV" ? 15 : volt == "_1000KV" ? 10 : 15;
    let h = volt == "_500KV" ? 15 : volt == "_1000KV" ? 10 : 15;
    let arrowArr = [
      [15, 0],
      [5, -4],
      [5, -2],
      [7, 0],
      // [0, 2],
      [5, 2],
      [5, 4],
    ];

    // let arrowArr = [
    //     [10, 0],
    //     [7, 7],
    //     [5, 5],
    //     [7, 0],
    //     [-5, -5],
    //     [-7, -7],
    //     [10, 0]
    // ];
    arrowArr.forEach((d) => {
      d[0] = (d[0] * w) / 10;
      d[1] = (d[1] * h) / 10;
      d = d.join(",");
    });
    let pathLength = this.linkDom.node()?.getTotalLength();
    this.con
      .append("polygon")
      .attr("points", arrowArr.join(" "))
      .style("display", "block")
      .attr(
        "class",
        this.link.sublayerList
          ? this.link.sublayerList.map((d) => d.sublayerId).join(" ")
          : ""
      )
      .style("transform", `rotate(${this.data.direction === "1" ? 0 : 180}deg)`)
      .style("fill", this._getArrowColor())
      .style("offset-path", `path("${this.link.linkPath}")`)
      .style(
        "animation",
        `arrowMove ${(pathLength / 35) * 1}s linear ${
          -this.inx * (((pathLength / 35) * 1000) / this.num)
        }ms infinite`
      )

      .style(
        "animation-direction",
        this.data.status === "6"
          ? "normal"
          : this.data.direction === "1"
          ? "normal"
          : "reverse"
      );

    if (this.data.status === "6" || ["_220KV", "_110KV"].includes(volt)) {
      //   console.log(arrPos?.x)
      this.con
        .selectAll("polygon")

        .style("animation", "none")
        .style("transform-origin", "15px 0px")
        // .style("animation", `halfArrowMove 0s linear 0s 1 normal forwards`)
        .style("offset-distance", "50%");
    }
  }
}

/**
 * 变电站节点
 */
class TransformerSubstation {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this._draw();
  }

  _draw() {
    // if (this.option.data.volt === '_500KV') {
    //   this.con.attr('x', this.option.x);
    //   this.con.attr('y', this.option.y);
    //   this.con.attr('width', this.option.w * 2);
    //   this.con.attr('height', this.option.h * 2);
    // }

    this._getSwitchIndex();
    this._setSize(1.22);

    // this._setSize(this.option.w);
    if (
      (this.option.type === "p" || this.option.type === "s") &&
      this.option.data.volt === "_500KV"
    ) {
      this.con
        .append("image")
        .attr("width", 246)
        .attr("height", 246)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/transformerSub_${this.option.type}_500_${
            Number(this.option.data["xfmr_num"]) >= 3
              ? 3
              : this.option.data["xfmr_num"]
          }.svg`
        );
      return;
    }
    if (
      (this.option.type === "p" || this.option.type === "s") &&
      this.option.data.volt === "_1000KV"
    ) {
      this.con
        .append("image")
        .attr("width", 246)
        .attr("height", 246)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/transformerSub_${this.option.type}_1000_${
            Number(this.option.data["xfmr_num"]) >= 3
              ? 3
              : this.option.data["xfmr_num"]
          }.svg`
        );
      return;
    }
    if (
      (this.option.type === "p" || this.option.type === "s") &&
      this.option.data.volt === "_220KV"
    ) {
      this.con
        .append("image")
        .attr("width", 246)
        .attr("height", 246)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/transformerSub_${this.option.type}_220.svg`
        );
      return;
    }
    this.defs = this.con.append("defs");
    this.bgImage = this.con
      .append("image")
      .attr("width", 595.3)
      .attr("height", 841.9)
      .attr("transform", "scale(1.5)")
      .attr("transform-origin", "center");
    if (
      (this.option.type === "l" || this.option.type === "m") &&
      this.option.data.volt === "_220KV"
    ) {
      this.con
        .append("circle")
        .attr("class", "bg_cir")
        .attr("cx", 123)
        .attr("cy", 123)
        .attr("r", this.option.data["highbus_num"] === "0" ? 105 : 123)
        .style("fill", "#231815");
    } else {
      this.con
        .append("circle")
        .attr("class", "bg_cir")
        .attr("cx", 297.2)
        .attr("cy", 421.9)
        .attr("r", this.option.data["highbus_num"] === "0" ? 210 : 298)
        .style("fill", "#231815");
    }

    if (
      Number(this.option.data["switch_num"]) >
      Number(this.option.data["lowbus_num"])
    ) {
      this.option.data["switch_num"] = this.option.data["lowbus_num"];
    }
    this._drawHighBus(this.option.data["highbus_num"]);
    if (this.option.data["xfmr_num"] !== "0") {
      this._drawLowBus(
        this.option.data["lowbus_num"],
        this.option.data["switch_num"]
      );
    }

    if (this.option.data["switch_num"] !== "0") {
      this._drawSwitch(
        this.option.data["lowbus_num"],
        this.option.data["switch_num"]
      );
    }
    if (this.option.data["xfmr_num"] !== "0") {
      this._drawXfmr(this.option.data["xfmr_num"]);
    }
  }

  _setSize(scale = 1) {
    this.con
      .attr("x", this.option.x - (this.option.w * scale - this.option.w) / 2)
      .attr("y", this.option.y - (this.option.h * scale - this.option.h) / 2)
      .attr("width", this.option.w * scale)
      .attr("height", this.option.h * scale);
  }

  setType(type) {
    if (this.option.type !== type) {
      this.option.type = type;
      $(this.con.node()).empty();
      this._draw();
    }
  }

  _getSwitchIndex() {
    if (this.option.data["switchNo"] === undefined) return;
    let switchNo = this.option.data["switchNo"].split(",");
    let switchArray = switchNo.map((d) => d.split("_"));
    this.switchIndex = [];
    switch (this.option.data["switch_num"]) {
      case "4":
        this.switchIndex = switchArray.map((d) => {
          if (d[1] === "3" && d[2] === "4") {
            return 1;
          } else if (d[1] === "1" && d[2] === "3") {
            return 2;
          } else if (d[1] === "2" && d[2] === "4") {
            return 3;
          } else if (d[1] === "1" && d[2] === "2") {
            return 4;
          }
        });
    }
  }

  _drawXfmr(num) {
    if (!this.option.svgData.xfmr.hasOwnProperty(num)) {
      // console.warn(`无主变数量为${num}的svg数据`);
      return;
    }
    let xfmrList = this.option.data["xfmrNo"].split(",") || [];
    let g = this.con.append("g").attr("class", "xfmr");
    if (this.option.data.volt === "_220KV") {
      this.option.svgData.xfmr[num].forEach((d) => {
        let con = g
          .append("g")
          .attr("transform", "scale(0.8)")
          .attr("transform-origin", "center");
        // con
        //   .append("path")
        //   .attr("class", "topo_xfmr")
        //   .attr("d", d)
        //   .style("filter", "url(#shadow-xfmr)")
        //   .style("fill", "#0088ff");
        con
          .append("path")
          .attr("class", "topo_xfmr_inset")
          .attr("d", d)
          .style("filter", "url(#inset-shadow-blue)")
          .style("fill", "#0f4372")
          .style("stroke-width", 1)
          .style("stroke", "#2cebff");
      });
    } else {
      // g.append('path').attr('d', 'M 33 123 a 90 90 0 1 0 180 0 a 90 90 0 1 0 -180 0 z').style('filter', 'url(#center-bg-inset)');
      // g.append('path').attr('class', 'xfmr-rate').attr('d', this._genXfmrRate(0)).style('fill', '#00fe42').style('filter', 'url(#inset-water-0)');
      // //绘制文字
      // this.text = g.append('text').attr('class', 'topo-centerText').text('0000');

      // 更新后的图元
      this.option.svgData.xfmr550[num] &&
        this.option.svgData.xfmr550[num].forEach((d, i) => {
          // 主变容量
          let xfMrMvarate = this.option.data[`xfmrMvarate${xfmrList[i]}`] || "";
          let con = g.append("g");
          // con
          //   .append("path")
          //   .attr("class", "topo_xfmr")
          //   .attr("d", d)
          //   .style("filter", "url(#shadow-xfmr)")
          //   .style("fill", "#0088ff");
          con.append("path").attr("class", "topo_xfmr_inset").attr("d", d);
          if (xfMrMvarate == "750.0000") {
            con.style("fill", "url(#blue_xfmr_Gradient)");
          } else if (xfMrMvarate == "3000.0000") {
            con.style("fill", "url(#purple_xfmr_Gradient)");
          } else {
            con.style("fill", "url(#green_xfmr_Gradient)");
          }
          //   .style("filter", "url(#inset-shadow-blue)")
          //   .style("fill", "#0f4372")
          //   .style("stroke-width", 1)
          //   .style("stroke", "#2cebff");
        });
    }
  }

  _genXfmrRate(k) {
    if (k >= 1) return "M 33 123 a 90 90 0 1 0 180 0 a 90 90 0 1 0 -180 0 z";
    let cx = 123;
    let cy = 123;
    let r = 90;
    let x0 = cx - 2 * r * Math.sqrt(k - k * k);
    let y0 = cy + (r - 2 * k * r);
    let x1 = cx + 2 * r * Math.sqrt(k - k * k);
    let y1 = cy + (r - 2 * k * r);
    return `M ${x0} ${y0} A ${r} ${r} 0 ${k < 0.5 ? 0 : 1} 0 ${x1} ${y1} Z`;
  }

  _drawLowBus(lowNum, switchNum) {
    // if (!this.option.svgData.lowbus.hasOwnProperty(`${lowNum}-${switchNum}`)) {
    //   console.warn(`无低压数量为${lowNum}且开关数量为${switchNum}的svg数据`);
    //   return;
    // }
    let g = this.con.append("g").attr("class", "lowbus");
    if (this.option.data.volt === "_220KV") {
      this.option.svgData.lowbus[`${lowNum}-${switchNum}`] &&
        this.option.svgData.lowbus[`${lowNum}-${switchNum}`].forEach((d) => {
          let con = g.append("g");
          con
            .append("path")
            .attr("class", "TransformerSubstation")
            .attr("d", d)
            .style("filter", "url(#shadow-xfmr)")
            .style("fill", "#0088ff");
          con
            .append("path")
            .attr("class", "TransformerSubstation-inset")
            .attr("d", d)
            .style("filter", "url(#inset-shadow-blue)")
            .style("fill", "#2d99fc")
            .style("stroke-width", 1)
            .style("stroke", "#00e7ff");
        });
    } else {
      this.option.svgData.lowbus550[`${lowNum}`] &&
        this.option.svgData.lowbus550[`${lowNum}`].forEach((d) => {
          let con = g.append("g");
          // con
          //   .append("path")
          //   .attr("class", "TransformerSubstation")
          //   .attr("d", d)
          //   .style("filter", "url(#shadow-xfmr)")
          //   .style("fill", "#0088ff");
          con
            .append("path")
            .attr("class", "TransformerSubstation-inset")
            .attr("d", d)
            //   .style("filter", "url(#inset-shadow-blue)")
            //   .style("fill", "#2d99fc")
            .style("fill", "url(#silver_Gradient)");
          //   .style("stroke-width", 1)
          //   .style("stroke", "#00e7ff");
        });
    }
  }

  _drawSwitch(lowNum, switchNum) {
    // if (!this.option.svgData.switch.hasOwnProperty(`${lowNum}-${switchNum}`)) {
    //   console.warn(`无低压数量为${lowNum}且开关数量为${switchNum}的svg数据`);
    //   return;
    // }
    let g = this.con.append("g").attr("class", "switch");
    if (this.option.data.volt === "_220KV") {
      this.option.svgData.switch[`${lowNum}-${switchNum}`] &&
        this.option.svgData.switch[`${lowNum}-${switchNum}`].forEach((d) => {
          let [x, y, w, h, rx, tf] = d.split(",");
          let con = g.append("g");
          con
            .append("rect")
            .attr("class", "TransformerSwitch")
            .attr("x", x)
            .attr("y", y)
            .attr("width", w)
            .attr("height", h)
            .attr("rx", rx)
            .attr("transform", tf === "null" ? "" : tf)
            .style("fill", "#0088ff");
        });
    } else {
      //用一个圆遮挡住开关
      this.con
        .append("circle")
        .attr("cx", 297.2)
        .attr("cy", 421.9)
        .attr("r", 150)
        .style("fill", "#231815");

      //筛选出低压母线对应的开关map
      let switchMap = this.option.svgData.switchObj[lowNum];
      //   获得开关位置列表
      let switchList = [];
      //   if (lowNum == "2") {
      if (this.option.data["switchNo"] === undefined) return;
      switchList = this.option.data["switchNo"].split(",").map((ele, i) => {
        return lowNum == "2"
          ? ele.split("_")[1] + "_" + ele.split("_")[2] + "_" + i
          : ele.split("_")[1] + "_" + ele.split("_")[2];
      });

      switchList.forEach((d, index) => {
        // const rtKeyId = this.option.data[`rtKeyId${switchNos[index]}`];

        let con = g.append("g");
        //   .attr("keyid", rtKeyId)
        //   .attr("class", "device-detail")
        //   .attr("data-type", "Switch");
        con
          .append("path")
          .attr("class", "TransformerSwitch")
          .attr("d", switchMap && switchMap[d])
          //   .style("filter", "url(#inset-shadow-blue)")
          .style("stroke-width", 2)
          .style("stroke", "black")
          .style("fill", "#daa520");
      });

      //   this.option.svgData.switch550[`${lowNum}`].forEach((d) => {
      //     // let [x, y, w, h, rx, tf] = d.split(",");
      //     let con = g.append("g");
      //     con
      //       .append("path")
      //       .attr("class", "TransformerSwitch")
      //       .attr("d", d)
      //       //   .style("filter", "url(#inset-shadow-blue)")
      //       .style("stroke-width", 2)
      //       .style("stroke", "black")
      //       .style("fill", "#daa520");
      //   });
    }
  }

  _drawHighBus(num) {
    if (num === "0") return;
    if (!this.option.svgData.highbus.hasOwnProperty(num)) {
      // console.warn(`无高压数量为${num}的svg数据`);
      return;
    }
    let g = this.con.append("g").attr("class", "highbus");
    if (this.option.data.volt === "_220KV") {
      this.option.svgData.highbus[num].forEach((d) => {
        let con = g.append("g");
        con
          .append("path")
          .attr("class", "TransformerSubstation")
          .attr("d", d)
          .style("filter", "url(#shadow-xfmr)")
          .style("fill", "#ff0083");
        con
          .append("path")
          .attr("class", "TransformerSubstation-inset")
          .attr("d", d)
          .style("filter", "url(#inset-shadow-red)")
          .style("fill", "#fc0046")
          .style("stroke-width", 1)
          .style("stroke", "#e1b5bd");
      });
    } else {
      this.option.svgData.highbus550[num].forEach((d) => {
        let con = g.append("g");
        con
          .append("path")
          .attr("class", "TransformerSubstation-inset")
          .attr("d", d)
          .style(
            "fill",
            `url(#${
              this.option.data.volt === "_1000KV"
                ? "golden_Gradient"
                : "silver_Gradient"
            })`
          );
      });
    }
  }

  update(data) {
    let nodeMeta = this.option.data;
    let busColorDic = {
      0: "blue", //蓝色
      1: "silver",
      2: "golden",
      3: "roseo",
      4: "orange",
      5: "yellow",
    };
    let xfmrColorDic = {
      "-1": { color: "gary", filter: "gary", insetColor: "", stroke: "" },
      0: {
        color: "#0088ff",
        filter: "blue",
        insetColor: "#0f4372",
        stroke: "#2cebff",
      },
      1: {
        color: "#ff2600",
        filter: "red",
        insetColor: "#72180f",
        stroke: "#ff3f2c",
      },
      2: {
        color: "#ff009c",
        filter: "purple",
        insetColor: "#720f4e",
        stroke: "#ff2cb5",
      },
      3: { color: "gary", filter: "roseo", insetColor: "", stroke: "" },
      4: { color: "gary", filter: "orange", insetColor: "", stroke: "" },
      5: { color: "#e4ff00", filter: "yellow", insetColor: "", stroke: "" },
    };
    let waterColorDic = {
      0: "#226d00", //绿
      1: "#a2451f", //黄
      2: "#7f1b2c", //红
    };
    // let xfmr500 = {

    // };
    //变电站闪烁
    // this.con.select(".xfmr").classed("topo-flash", data.rate >= 1);
    //越限外发光
    const hasFlash = data.xfmr.some((item) => item.flash === "1");

    this.con.classed("redTransFilter", hasFlash);
    if (hasFlash) {
      this.bgImage.attr("xlink:href", `${this._foldPath}/images/redBorder.png`);
    } else {
      this.bgImage.attr("xlink:href", null);
    }
    //更新主变数据
    if (nodeMeta.xfmr_num !== "0" && nodeMeta.volt !== "_220KV") {
      // this.con.select('.xfmr').select('.xfmr-rate').style('fill', waterColorDic[data.status]).style('filter', `url(#inset-water-${data.status})`).attr('d', this._genXfmrRate(data.rate));
      // this.text.text(data.value);

      [...this.con.select(".xfmr").selectAll("g").nodes()].forEach((p, i) => {
        if (data.xfmr[i]?.status == "5") {
          d3.select(p)
            .selectAll(".topo_xfmr_inset")
            .style(
              "fill",
              `url(#${busColorDic[data.xfmr[i]?.status]}_xfmr_Gradient)`
            );
        } else {
          d3.select(p).selectAll(".topo_xfmr_inset").style("fill", null);
        }
        // if (
        //   data.xfmr[i]?.status == "3" ||
        //   data.xfmr[i]?.status == "4" ||
        //   data.xfmr[i]?.status == "5"
        // ) {
        //   d3.select(p)
        //     .selectAll(".topo_xfmr_inset")
        //     .style(
        //       "fill",
        //       `url(#${busColorDic[data.xfmr[i]?.status]}_Gradient)`
        //     );
        // }
        // let color = xfmrColorDic[data.xfmr[i]?.status];
        // d3.select(p).selectAll('.topo_xfmr').style('fill', color?.color);
        // d3.select(p).selectAll('.topo_xfmr_inset').style('filter', `url(#inset-shadow-${xfmrColorDic[data.xfmr[i]?.status]?.filter})`).style('fill', color?.insetColor).style('stroke', color?.stroke);
      });
    } else {
      [...this.con.select(".xfmr").selectAll("g").nodes()].forEach((p, i) => {
        let color = xfmrColorDic[data.xfmr[i]?.status];
        d3.select(p).selectAll(".topo_xfmr").style("fill", color?.color);
        d3.select(p)
          .selectAll(".topo_xfmr_inset")
          .style(
            "filter",
            `url(#inset-shadow-${xfmrColorDic[data.xfmr[i]?.status]?.filter})`
          )
          .style("fill", color?.insetColor)
          .style("stroke", color?.stroke);
      });
    }
    //更新低压母线数据
    if (nodeMeta.lowBusNo !== "") {
      [...this.con.select(".lowbus").selectAll("g").nodes()].forEach((p, i) => {
        if (nodeMeta.volt === "_220KV") {
          d3.select(p)
            .selectAll(".TransformerSubstation-inset")
            .style(
              "filter",
              `url(#inset-shadow-${busColorDic[data.lowBus[i]?.status]})`
            );
        } else {
          if (!data.lowBus[i]?.status) {
            d3.select(p)
              .selectAll(".TransformerSubstation-inset")
              .style("fill", `url(#silver_Gradient)`);
          } else {
            d3.select(p)
              .selectAll(".TransformerSubstation-inset")
              .style(
                "fill",
                `url(#${busColorDic[data.lowBus[i]?.status]}_Gradient)`
              );
          }
        }
      });
    }
    //更新高压母线数据
    if (nodeMeta.highBusNo !== "") {
      [...this.con.select(".highbus").selectAll("g").nodes()].forEach(
        (p, i) => {
          if (nodeMeta.volt === "_220KV") {
            d3.select(p)
              .selectAll(".TransformerSubstation-inset")
              .style(
                "filter",
                `url(#inset-shadow-${busColorDic[data.highBus[i]?.status]})`
              );
          } else {
            // 只要不是5
            if (data.highBus[i]?.status !== "5") {
              d3.select(p)
                .selectAll(".TransformerSubstation-inset")
                .style(
                  "fill",
                  `url(#${
                    nodeMeta.volt === "_1000KV"
                      ? "golden_Gradient"
                      : "silver_Gradient"
                  })`
                );
            } else {
              d3.select(p)
                .selectAll(".TransformerSubstation-inset")
                .style(
                  "fill",
                  `url(#${busColorDic[data.highBus[i]?.status]}_Gradient)`
                );
            }
          }
        }
      );
    }
    //更新开关数据
    if (nodeMeta.switchNo !== "") {
      let switchArray = [...this.con.select(".switch").selectAll("g").nodes()];

      switchArray.forEach((p, i) => {
        d3.select(p)
          .selectAll(".TransformerSwitch")
          .style("stroke", data.switch[i]?.status === "0" ? "black" : "#FF0000")
          .style("fill", data.switch[i]?.status === "0" ? "#daa520" : null);

        if (this.option.data.volt === "_500KV") {
          d3.select(p)
            .selectAll(".TransformerSwitch")

            // .style('fill', data.switch[i]?.status === '0' ? "url(#blue_switch_Gradient)" : '#19eb42');
            .style("stroke-width", data.switch[i]?.status === "0" ? 2 : 10)
            .style(
              "stroke",
              data.switch[i]?.status === "0" ? "black" : "#daa520"
            );
        }
      });

      //   this.switchIndex.forEach((d, i) => {
      //     d3.select(switchArray[d - 1])
      //       .selectAll(".TransformerSwitch")

      //       // .style('fill', data.switch[i]?.status === '0' ? "url(#blue_switch_Gradient)" : '#19eb42');
      //       //   .style("stroke-width", data.switch[i]?.status === "0"  ? 2 : 10)
      //       .style("stroke", data.switch[i]?.status === "0" ? "black" : "#FF0000")
      //       .style("fill", data.switch[i]?.status === "0" ? "#daa520" : null);

      //     if (this.option.data.volt === "_500KV") {
      //       d3.select(switchArray[d - 1])
      //         .selectAll(".TransformerSwitch")

      //         // .style('fill', data.switch[i]?.status === '0' ? "url(#blue_switch_Gradient)" : '#19eb42');
      //         .style("stroke-width", data.switch[i]?.status === "0" ? 2 : 10)
      //         .style(
      //           "stroke",
      //           data.switch[i]?.status === "0" ? "black" : "#daa520"
      //         );
      //     }
      //   });
    }
  }
}

/**
 * 电厂节点
 */
class PowerPlant {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this.data = [];
    this.option.data.no &&
      this.option.data.no.split(",").forEach((d) => {
        this.data.push({
          mvarate: 100,
          value: 0,
          name: this.option.data[`keyDesc${d}`],
          status: 0,
          // total: 0,
          percent: 0,
        });
      });
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this.fontSizeDic = [55, 50, 45, 40, 35, 30, 25, 20];
    this.colorMap = {
      0: { fillColor: "blue", endColor: "#77f9ff" },
      1: { fillColor: "green", endColor: "#00ff00" },
    };
    this.imageDom = null;
    this._draw();
  }

  _draw() {
    this._setSize();
    const PowerPlantMap = {
      WPV: "WIND_PV_STORAGE",
      Gas: "THERMAL",
      PUMP: "THERMAL",
    };

    const volt = this.option.data.volt || "_220KV";
    const name =
      PowerPlantMap[this.option.data.powerType] || this.option.data.powerType;

    // 220KV和110KV 的火电和燃机 需要动态生成
    if (
      ["_220KV", "_110KV"].includes(volt) &&
      ["THERMAL", "Gas"].includes(name)
    ) {
      this.drawRect = {
        x: 15,
        y: 17,
        width: 176,
        height: 176,
      };

      this.con
        .append("image")
        .attr("width", 209)
        .attr("height", 209)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/PowerStation/${volt}/powerPlant_THERMAL_bg.svg`
        );

      this.mainG = this.con
        .append("g")
        .attr("class", "container")
        .attr("transform", `translate(${this.drawRect.x}, ${this.drawRect.y})`);
      this.barNum = Number(this.option.data.num);

      this._genRange220();
      this._drawBar220();
      this._drawName();
      this._drawValue();
    } else {
      this.con
        .append("image")
        .attr("width", 209)
        .attr("height", 209)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/PowerStation/${volt}/powerPlant_${name}.svg`
        );
    }
  }

  _setSize(l) {
    this.con
      .attr("x", this.option.x)
      .attr("y", this.option.y)
      .attr("width", this.option.w)
      .attr("height", this.option.h);
  }

  setType(type) {
    if (this.option.type !== type) {
      this.option.type = type;
      $(this.con.node()).empty();
      this._draw();
    }
  }

  _genRange220() {
    this.xRange = [];
    this.data.forEach((d) => {
      this.xRange.push(
        d3
          .scaleLinear()
          .domain([0, Number(d.mvarate)])
          .range([this.drawRect.x, this.drawRect.width])
      );
    });
    this.yRange = d3
      .scaleBand()
      .domain(this.data.map((d, i) => i))
      .range([this.drawRect.y, this.drawRect.y + this.drawRect.height]);
  }

  _drawBar220() {
    let barG = this.mainG.append("g").attr("class", "powerplant_bar");

    if (this.option.data.volt == "_500KV") {
      barG
        .selectAll(".bgRect")
        .data(this.data)
        .enter()
        .append("rect")
        .attr("class", "bgRect")
        .attr("x", 0)
        .attr("y", (d, i) => this.yRange.bandwidth() * i)
        .attr("width", this.drawRect.width)
        .attr("height", this.yRange.bandwidth())
        .style("fill", `url(#powerplant-bar-bg${this.option.data.volt})`);
    }

    barG
      .selectAll(".rect")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("rect")
            .attr("class", "rect")
            .attr("x", 0)
            .attr("y", (d, i) => this.yRange.bandwidth() * i)
            .attr("width", (d, i) => this.xRange[i](d.value))
            .attr("height", this.yRange.bandwidth())
            // .style("fill", `url(#powerplant-bar${this.option.data.volt})`),
            .style("fill", (d, i) => {
              if (d.status != "0") {
                return `url(#powerplant-bar_${
                  this.colorMap[d.status].fillColor
                })`;
              } else {
                return `url(#powerplant-bar${this.option.data.volt})`;
              }
            }),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .attr("y", (d, i) => this.yRange.bandwidth() * i)
            .attr("height", this.yRange.bandwidth())
            .attr("width", (d, i) => this.xRange[i](d.value))
            .style("fill", (d, i) => {
              if (d.status != "0") {
                return `url(#powerplant-bar_${
                  this.colorMap[d.status].fillColor
                })`;
              } else {
                return `url(#powerplant-bar${this.option.data.volt})`;
              }
            }),
        (exit) => exit.remove()
      );

    if (this.option.data.volt == "_500KV") {
      barG
        .selectAll(".endRect")
        .data(this.data)
        .join(
          (enter) =>
            enter
              .append("rect")
              .attr("class", "endRect")
              .attr("x", (d, i) => this.xRange[i](d.value) - 2.5)
              .attr("y", (d, i) => this.yRange.bandwidth() * i)
              .attr("width", 5)
              .attr("height", this.yRange.bandwidth())
              .style("fill", (d, i) => this.colorMap[d.status].endColor)
              .style("opacity", 0.8),
          (update) =>
            update
              .transition(d3.transition().duration(1000))
              .attr("x", (d, i) => this.xRange[i](d.value) - 2.5)
              .attr("y", (d, i) => this.yRange.bandwidth() * i)
              .attr("height", this.yRange.bandwidth())
              .style("fill", (d, i) => this.colorMap[d.status].endColor),

          (exit) => exit.remove()
        );
    }
  }

  _genRange() {
    this.yRange = [];
    this.data.forEach((d) => {
      this.yRange.push(
        d3
          .scaleLinear()
          .domain([0, Number(d.mvarate)])
          .range([0, this.drawRect.height])
      );
    });
    this.xRange = d3
      .scaleBand()
      .domain(this.data.map((d, i) => i))
      .range([0, this.drawRect.width]);
  }

  _drawBar() {
    let barG = this.mainG.append("g").attr("class", "powerplant_bar");

    if (this.option.data.volt == "_500KV") {
      barG
        .selectAll(".bgRect")
        .data(this.data)
        .enter()
        .append("rect")
        .attr("class", "bgRect")
        .attr("x", (d, i) => this.xRange.bandwidth() * i)
        .attr("y", 0)
        .attr("width", this.xRange.bandwidth())
        .attr("height", this.drawRect.height)
        .style("fill", `url(#powerplant-bar-bg${this.option.data.volt})`);
    }

    barG
      .selectAll(".rect")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("path")
            .attr("class", "rect")
            .attr("d", (d, i) => {
              let offsetX =
                this.xRange.bandwidth() * i + this.xRange.bandwidth() / 12;
              let barW = this.xRange.bandwidth() / 1.2;
              let offsetY = this.drawRect.height - this.yRange[i](d.value);
              // if(offsetY < 0){
              //     offsetY = 0
              // }

              return `M${offsetX} ${offsetY} A${barW} ${barW / 4} 0 0 0  ${
                offsetX + barW
              } ${offsetY} L${offsetX + barW} ${this.drawRect.height} A${
                barW / 2
              } ${barW / 4} 0 0 1 ${offsetX} ${
                this.drawRect.height
              } L${offsetX} ${offsetY}`;
              // return `M${this.xRange.bandwidth() * i} ${this.drawRect.height - this.yRange[i](d.value)} A20 10 0 0 0 ${this.xRange.bandwidth()} 0 L${this.xRange.bandwidth()} ${this.yRange[i](d.value)} A20 10 0 0 1 0 ${this.yRange[i](d.value)} L${this.xRange.bandwidth() * i} ${this.drawRect.height - this.yRange[i](d.value)}`
            })
            //   .attr("x", (d, i) => this.xRange.bandwidth() * i)
            //   .attr("y", (d, i) => this.drawRect.height - this.yRange[i](d.value))
            //   .attr("width", this.xRange.bandwidth())
            //   .attr("height", (d, i) => this.yRange[i](d.value))
            .style("fill", (d, i) => {
              if (d.status == "0") {
                if (d.percent < 0.99) {
                  return "url(#powerBar_blue)";
                } else {
                  return "url(#powerBar_red)";
                }
                // return `url(#powerplant-bar_${
                //   this.colorMap[d.status].fillColor
                // })`;
              } else {
                // return `url(#powerplant-bar${this.option.data.volt})`;
                return `url(#powerBar_green)`;
              }
            }),
        // .style("fill", `url(#powerplant-bar${this.option.data.volt})`),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .attr("d", (d, i) => {
              let offsetX =
                this.xRange.bandwidth() * i + this.xRange.bandwidth() / 12;
              let barW = this.xRange.bandwidth() / 1.2;
              let offsetY = this.drawRect.height - this.yRange[i](d.value);

              return `M${offsetX} ${offsetY} A${barW} ${barW / 4} 0 0 0  ${
                offsetX + barW
              } ${offsetY} L${offsetX + barW} ${
                this.drawRect.height - barW / 2
              } A${barW / 2} ${barW / 4} 0 0 1 ${offsetX} ${
                this.drawRect.height - barW / 2
              } L${offsetX} ${offsetY}`;
              // return `M${this.xRange.bandwidth() * i} ${this.drawRect.height - this.yRange[i](d.value)} A20 10 0 0 0 ${this.xRange.bandwidth()} 0 L${this.xRange.bandwidth()} ${this.yRange[i](d.value)} A20 10 0 0 1 0 ${this.yRange[i](d.value)} L${this.xRange.bandwidth() * i} ${this.drawRect.height - this.yRange[i](d.value)}`
            })
            //   .attr("x", (d, i) => this.xRange.bandwidth() * i)
            //   .attr("y", (d, i) => this.drawRect.height - this.yRange[i](d.value))
            //   .attr("width", this.xRange.bandwidth())
            //   .attr("height", (d, i) => this.yRange[i](d.value))

            .style("fill", (d, i) => {
              if (d.status == "0") {
                if (d.percent < 0.99) {
                  return "url(#powerBar_blue)";
                } else {
                  return "url(#powerBar_red)";
                }
                // return `url(#powerplant-bar_${
                //   this.colorMap[d.status].fillColor
                // })`;
              } else {
                // return `url(#powerplant-bar${this.option.data.volt})`;
                return `url(#powerBar_green)`;
              }
            }),

        // (enter) =>
        //   enter
        //     .append("rect")
        //     .attr("class", "rect")
        //     .attr("x", (d, i) => this.xRange.bandwidth() * i)
        //     .attr("y", (d, i) => this.drawRect.height - this.yRange[i](d.value))
        //     .attr("width", this.xRange.bandwidth())
        //     .attr("height", (d, i) => this.yRange[i](d.value))
        //     .style("fill", (d, i) => {
        //       if (d.status != "0") {
        //         return `url(#powerplant-bar_${
        //           this.colorMap[d.status].fillColor
        //         })`;
        //       } else {
        //         return `url(#powerplant-bar${this.option.data.volt})`;
        //       }
        //     }),
        // // .style("fill", `url(#powerplant-bar${this.option.data.volt})`),
        // (update) =>
        //   update
        //     .transition(d3.transition().duration(1000))
        //     .attr("x", (d, i) => this.xRange.bandwidth() * i)
        //     .attr("y", (d, i) => this.drawRect.height - this.yRange[i](d.value))
        //     .attr("width", this.xRange.bandwidth())
        //     .attr("height", (d, i) => this.yRange[i](d.value))
        //     .style("fill", (d, i) => {
        //       if (d.status != "0") {
        //         return `url(#powerplant-bar_${
        //           this.colorMap[d.status].fillColor
        //         })`;
        //       } else {
        //         return `url(#powerplant-bar${this.option.data.volt})`;
        //       }
        //     }),
        (exit) => exit.remove()
      );

    barG
      .selectAll(".top")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("ellipse")
            .attr("class", "top")
            .attr(
              "cx",
              (d, i) =>
                this.xRange.bandwidth() * i + this.xRange.bandwidth() / 2
            )
            .attr(
              "cy",
              (d, i) => this.drawRect.height - this.yRange[i](d.value)
            )
            .attr("rx", this.xRange.bandwidth() / 2.4)
            .attr("ry", this.xRange.bandwidth() / 10)

            .style("fill", (d, i) => {
              if (d.status == "0") {
                if (d.percent < 0.99) {
                  return "url(#powerBarTop_blue)";
                } else {
                  return "url(#powerBarTop_red)";
                }
                // return `url(#powerplant-bar_${
                //   this.colorMap[d.status].fillColor
                // })`;
              } else {
                // return `url(#powerplant-bar${this.option.data.volt})`;
                return `url(#powerBarTop_green)`;
              }
            }),

        // .style("fill", `url(#powerplant-bar${this.option.data.volt})`),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .attr(
              "cx",
              (d, i) =>
                this.xRange.bandwidth() * i + this.xRange.bandwidth() / 2
            )
            .attr(
              "cy",
              (d, i) => this.drawRect.height - this.yRange[i](d.value)
            )
            .attr("rx", this.xRange.bandwidth() / 4)
            .attr("ry", this.xRange.bandwidth() / 8)

            .style("fill", (d, i) => {
              if (d.status == "0") {
                if (d.percent < 0.99) {
                  return "url(#powerBarTop_blue)";
                } else {
                  return "url(#powerBarTop_red)";
                }
                // return `url(#powerplant-bar_${
                //   this.colorMap[d.status].fillColor
                // })`;
              } else {
                // return `url(#powerplant-bar${this.option.data.volt})`;
                return `url(#powerBarTop_green)`;
              }
            }),

        (exit) => exit.remove()
      );

    // if (this.option.data.volt == "_500KV") {
    //   barG
    //     .selectAll(".endRect")
    //     .data(this.data)
    //     .join(
    //       (enter) =>
    //         enter
    //           .append("rect")
    //           .attr("class", "endRect")
    //           .attr("x", (d, i) => this.xRange[i](d.value) - 2.5)
    //           .attr("y", (d, i) => this.yRange.bandwidth() * i)
    //           .attr("width", 5)
    //           .attr("height", this.yRange.bandwidth())
    //           .style("fill", (d, i) => this.colorMap[d.status].endColor)
    //           .style("opacity", 0.8),
    //       (update) =>
    //         update
    //           .transition(d3.transition().duration(1000))
    //           .attr("x", (d, i) => this.xRange[i](d.value) - 2.5)
    //           .attr("y", (d, i) => this.yRange.bandwidth() * i)
    //           .attr("height", this.yRange.bandwidth())
    //           .style("fill", (d, i) => this.colorMap[d.status].endColor),

    //       (exit) => exit.remove()
    //     );
    // }
  }

  _drawName() {
    // let pattern = /#([0-9]{1,2})/;
    // let pattern2 = /([0-9]{1,2})号/;
    let pattern = /(#\d+|(?<!\d)\d+号)/;
    let nameG = this.mainG.append("g").attr("class", "powerplant_name");
    nameG
      .selectAll(".name")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("text")
            .attr("class", "name")
            .attr("x", 10)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .style("fill", "#fff")
            .style("font-size", `${50 - this.data.length * 5}px`)
            .style("dominant-baseline", "central")
            .text((d, i) => {
              if (!d.name) return;
              let n = d.name.match(pattern);
              if (n[0].indexOf("号") != "-1") {
                return `#${n[0].slice(0, -1)}`;
              } else {
                return n[0];
              }
            }),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .style("font-size", `${50 - this.data.length * 5}px`)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .attr("width", (d, i) => this.xRange[i](d.value))
            .text((d, i) => {
              if (!d.name) return;
              let n = d.name.match(pattern);
              if (n[0].indexOf("号") != "-1") {
                return `#${n[0].slice(0, -1)}`;
              } else {
                return n[0];
              }
            }),
        (exit) => exit.remove()
      );
  }

  _drawValue() {
    let valueG = this.mainG.append("g").attr("class", "powerplant_value");
    valueG
      .selectAll(".value")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("text")
            .attr("class", "value")
            .attr("x", this.drawRect.width)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .style("fill", "#fff")
            .style("font-size", `${50 - this.data.length * 5}px`)
            .style("dominant-baseline", "central")
            .style("text-anchor", "end")
            .text((d) => `${Math.round(d.value)}`),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .style("font-size", `${50 - this.data.length * 5}px`)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .text((d) => `${Math.round(d.value)}`),
        (exit) => exit.remove()
      );
  }

  _drawTotal(data) {
    let totalG = this.mainG.append("g").attr("class", "powerplant_total");
    totalG
      .append("text")
      .style("fill", "url(#area_board_value")
      .style("font-size", "160px")
      .style("font-weight", "bold")
      .attr(
        "x",
        this.option.data.powerType == "NUCLEAR" ||
          this.option.data.powerType == "WIND"
          ? 190
          : 100
      )
      .attr("y", 460)
      .text(data?.value || "0");
  }

  update(data) {
    if (data === undefined) return;
    // let total = 0;
    // if (this.option.data.powerType == "WIND") {
    //   total = data.value || "100";
    // } else {
    //   data.machineList.forEach((item) => {
    //     total += Number(item.value);
    //   });
    // }

    this.data = data.machineList.map((i, index) => {
      return {
        mvarate: i.mvarate,
        value: i.value,
        name: this.data[index]?.name,
        status: i?.status || "0",
        // total: total,
        percent: Number(i.value) / Number(i.mvarate),
      };
    });
    // if(data.machineList[0].status == '1') debugger
    if (this.mainG === undefined) return;
    $(this.mainG.node()).empty();
    if (["_220KV", "_110KV"].includes(this.option.data.volt)) {
      this._genRange220();
      this._drawBar220();
      this._drawName();
      this._drawValue();
    }
    // else {
    //   this._drawTotal(data);
    //   if (this.option.data.powerType === "WIND") {
    //     this.imageDom.classed("rotate-animation", data.value != 0);
    //   } else {
    //     this._genRange();
    //     this._drawBar();
    //     // this._drawName();
    //     // this._drawValue();
    //   }
    // }
  }
}

/**
 * 换流站组件
 */
class ExchStation {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this._draw();
  }

  _draw() {
    if (this.option.data.volt == "_500KV") {
      this._setSize(this.option.type === "m" ? 92 : 156);
    } else {
      this._setSize(this.option.type === "m" ? 72 : 138);
    }
    // this._setSize(this.option.w);
    if (this.option.type === "p" || this.option.type === "s") {
      this.con
        .append("image")
        .attr("width", 246)
        .attr("height", 246)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/exchStation_${this.option.type}${this.option.data.volt}.svg`
        );
    } else {
      this.con
        .append("circle")
        .attr("cx", 297.2)
        .attr("cy", 421.9)
        .attr("r", this.option.data["highbus_num"] === "0" ? 210 : 298)
        .style("fill", "#231815");
      this.con
        .append("image")
        .attr("width", 450)
        .attr("height", 450)
        .attr("x", 72)
        .attr("y", 197)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/exchStation_${this.option.type}.png`
        );
      this._drawHighBus(this.option.data["highbus_num"]);
    }
  }

  _drawHighBus(num) {
    if (num === "0") return;
    if (!this.option.svgData.highbus550.hasOwnProperty(num)) {
      console.warn(`无高压数量为${num}的svg数据`);
      return;
    }
    let g = this.con.append("g").attr("class", "highbus");

    this.option.svgData.highbus550[num].forEach((d) => {
      let con = g.append("g");

      con
        .append("path")
        .attr("class", "TransformerSubstation-inset")
        .attr("d", d)
        .style(
          "fill",
          `url(#${
            this.option.data.volt === "_1000KV"
              ? "golden_Gradient"
              : "silver_Gradient"
          })`
        );
    });
    // }
  }

  update(data) {
    let nodeMeta = this.option.data;
    let busColorDic = {
      0: "blue", //蓝色
      1: "silver",
      2: "golden",
      3: "roseo",
      4: "orange",
      5: "yellow",
    };

    requestAnimationFrame(() => {
      //更新高压母线数据
      if (nodeMeta.highBusNo !== "") {
        [...this.con.select(".highbus").selectAll("g").nodes()].forEach(
          (p, i) => {
            if (data.highBus[i]?.status !== "5") {
              d3.select(p)
                .selectAll(".TransformerSubstation-inset")
                .style(
                  "fill",
                  `url(#${
                    nodeMeta.volt === "_1000KV"
                      ? "golden_Gradient"
                      : "silver_Gradient"
                  })`
                );
            } else {
              d3.select(p)
                .selectAll(".TransformerSubstation-inset")
                .style(
                  "fill",
                  `url(#${
                    data.highBus[i] && busColorDic[data.highBus[i].status]
                  }_Gradient)`
                );
            }
          }
        );
      }
    });
  }

  _setSize(l) {
    this.con
      .attr("x", this.option.x)
      .attr("y", this.option.y)
      .attr("width", this.option.w)
      .attr("height", this.option.h);
  }

  setType(type) {
    if (this.option.type !== type) {
      this.option.type = type;
      $(this.con.node()).empty();
      this._draw();
    }
  }
}

/**
 * 分区板组件
 */
class AreaBoard {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this._draw();
    this.colorDic = {
      0: "green", //绿
      1: "blue", //蓝色
      2: "purple", //紫色
      3: "red", //红
    };
    // console.log(this.colorDic[0])
  }

  _draw() {
    this.nameStr = this.option.property.name.length;
    let svg = d3
      .select(this.con)
      .append("svg")
      .attr("id", `area_board_${this.id}`)
      .attr("width", this.option.property.basic.frame[2])
      .attr("height", this.option.property.basic.frame[3])
      .attr("viewBox", `0 0 468 200`);
    this.image = svg
      .append("image")
      .attr("width", 468)
      .attr("height", 200)
      .attr("xlink:href", `${this._foldPath}/images/areaBoard.png`);

    svg
      .append("text")
      .attr("class", "area-board-name")
      .style("transform", `translate(115px, 88px)`)
      .text(this.option.property.name);
    this.text = svg
      .append("text")
      .attr("class", "area-board-value")
      .style("transform", `translate(434px, 42px)`)
      .text(this.option.property.value);

    this.remain = svg
      .append("text")
      .attr("class", "area-board-value")
      .style("transform", `translate( 434px, 134px)`)
      //   .style("fill", this.colorDic[0])
      .text(this.option.property.remaining || "0000");

    // let id = this.id + "_digit_gear";

    // let container = svg
    //   .append("g")
    //   .style("transform", `translate(${this.nameStr === 3 ? 211 : 145}px, 6px)`)
    //   //   .style("transform", `translate(0px, 0px)`)
    //   .append("g");
    // this.text = new DigitGearBoard(id, "123", container.node(), 0, {
    //   property: {
    //     basic: {
    //       frame: [0, 0, 280, 90],
    //     },
    //     setting: {
    //       rotateTime: 3000,
    //     },
    //   },
    // });

    // this.text.update("1111");
  }

  /**
   * 更新分区板数据
   * @param {Object} data 分区数据
   */
  update(data) {
    this.text.text(data.value);
    this.remain.text(data.remaining);
    this.remain.style(
      "fill",
      () => `url(#remain_${this.colorDic[data?.bstatus]})`
    );
    // this.text.update(data.value);
    this.image.attr("xlink:href", `${this._foldPath}/images/areaBoard.png`);
  }
}
