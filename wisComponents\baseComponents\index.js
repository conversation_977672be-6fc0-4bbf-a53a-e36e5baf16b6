/**
 * <AUTHOR>
 * @description 组件依赖加载入口
 * @date 2020/7/1
 */
class InitBaseComp {
    constructor(path, isUglify) {
        this.headEl = document.getElementsByTagName("head")[0];
        this.isUglify = isUglify;
        this.path = path || "http://**************/dp/components";
        this.jsFileCount = 0;
        this.cssFileCount = 0;
        this.cssList = [
            /**
             * @description 地图组件依赖
             * <AUTHOR>
             * @date 2020-07-01
             */
            "baseComponents/css/lmap",
            /**
             * @description 数码数字字体
             * <AUTHOR>
             * @date 2020-07-06
             */
            "baseComponents/css/fonts",
            /**
             * @description 魔方地图第三方插件样式
             * <AUTHOR>
             * @date 2020-07-06
             */
            "baseComponents/css/hexaflip",
            /**
             * @description 轻量化地图基类样式
             * <AUTHOR>
             * @date 2024-04-18
             */
            "lightGISBase/v4.0/lightGISBase",
        ];

        this.jsList = [
            /**
             * @description 组件基类/工具类
             * <AUTHOR>
             * @date 2020-07-16
             */
            "base/componentBase",
            "base/optionType",
            "componentFactory",
            "baseComponents/utils/index",
            "baseComponents/utils/SvgElement",
            /**
             * @description 图表组件依赖
             * <AUTHOR>
             * @date 2020-07-01
             */
            "baseComponents/js/area",
            "baseComponents/js/axis",
            "baseComponents/js/bar",
            "baseComponents/js/graph",
            "baseComponents/js/line",
            "baseComponents/js/markLine",
            "baseComponents/js/markPoint",
            "baseComponents/js/river",

            /**
             * @description 地图组件依赖
             * <AUTHOR>
             * @date 2020-07-01
             */
            "baseComponents/js/lmapd",
            "baseComponents/js/lmap",
            "baseComponents/js/logicGraph",
			"baseComponents/js/logicGraph/LogicCustomComponent",
            /**
             * @description 3D地图组件依赖
             * <AUTHOR>
             * @date 2021-03-23
             */
            "baseComponents/js/cmapd",
            "baseComponents/js/cmap",
            /**
             * @description pdf组件依赖
             * <AUTHOR>
             * @date 2020-09-8
             */
            "baseComponents/js/pdf",
            "baseComponents/js/pdf.worker",
            /**
             * @description eCharts组件依赖
             * <AUTHOR>
             * @date 2020-10-30
             */
            "baseComponents/js/echarts.min",
            /**
             * @description 词云组件依赖
             * <AUTHOR>
             * @date 2020-11-23
             */
            "baseComponents/js/echarts-wordcloud.min",
            /**
             * @description 3d饼图组件依赖
             * <AUTHOR>
             * @date 2020-11-23
             */
            "baseComponents/js/echarts-gl.min",
            //统计插件
            "baseComponents/js/ecStat.min",
            /**
             * @description 视频组件依赖
             * <AUTHOR>
             * @date 2020-12-22
             */
            "baseComponents/js/jswebrtc.min",
            /**
             * @description wps组件依赖
             * <AUTHOR>
             * @date 2021-07-05
             */
            "baseComponents/js/web-office-sdk-v1.1.14.umd",
            /**
             * @description wps组件依赖
             * <AUTHOR>
             * @date 2021-07-05
             */
            "baseComponents/js/preview-jssdk-v1.1.0.min",
            /**
             * @description wps组件依赖
             * <AUTHOR>
             * @date 2021-07-05
             */
            "baseComponents/js/base64.min",
            /**
             * @description ae动画
             * <AUTHOR>
             * @date 2021-07-05
             */
            "baseComponents/js/lottie",
            "baseComponents/js/svga.min",
            /**
             * @description 粒子动画
             * <AUTHOR>
             * @date 2024-09-03
             */
             "baseComponents/js/particles.min",
             /**
             * @description gsap动画插件
             * <AUTHOR>
             * @date 2024-09-03
             */
            //   "baseComponents/js/gsap.min",
            //   "baseComponents/js/MotionPathPlugin.min",
            /**
             * @description three3D组件依赖
             * <AUTHOR>
             * @date 2022-06-08
             */
            'baseComponents/js/three',
            'baseComponents/js/OrbitControls',
            /**
             * @description videoPlayer组件依赖
             * <AUTHOR>
             * @date 2021-11-19
             */
            "baseComponents/js/video.min",
            "baseComponents/js/hls",
            /**
             * @description 魔方地图第三方插件
             * <AUTHOR>
             * @date 2020-07-06
             */
            "baseComponents/js/hexaflip",
            /**
             * @description 桑基图插件
             * <AUTHOR>
             * @date 2022-06-13
             */
            "baseComponents/js/d3-sanKey",
            /**
             * @description 视频组件插件
             * <AUTHOR>
             * @date 2022-06-27
             */
            // 'baseComponents/js/adapter',
            "../../wisVisual/libs/adapter-7.4.0",
            /**
             * @description 视频组件插件
             * <AUTHOR>
             * @date 2022-06-27
             */
            "../../wisVisual/libs/srs.sdk",
            // 'baseComponents/js/srs.sdk',
            /**
             * @description 3D散点图
             * <AUTHOR>
             * @date 2022-07-27
             */
            "baseComponents/js/OBJLoader",
            /**
             * @description 3D散点图
             * <AUTHOR>
             * @date 2022-07-27
             */
            "baseComponents/js/hammer.min",
            /**
             * @description 视频组件h5模式
             * <AUTHOR>
             * @date 2022-09-19
             */
            "baseComponents/js/h5player.min",
            "baseComponents/js/h5Video.min",
            /**
             * @description videoPlayer组件依赖
             * <AUTHOR>
             * @date 2023-1-14
             */
            "baseComponents/js/flv",
            "baseComponents/js/crypto-js.min",
            "baseComponents/js/hikPlayer",
            /**
             * @description geoTopo组件依赖
             * <AUTHOR>
             * @date 2023-6-12
             */
            "baseComponents/js/baseExchStation/v1.0/baseExchStation",
            "baseComponents/js/basePowerPlant/v1.0/basePowerPlant",
            "baseComponents/js/baseTransformerSubstation/v1.0/baseTransformerSubstation",
            /**
             * @description venn依赖文件
             * <AUTHOR>
             * @date 2023-8-29
             */
            "baseComponents/js/venn.min",
            /**
             * @description 农历公历转换
             * <AUTHOR>
             * @date 2023-8-30
             */
            "baseComponents/js/lunar",
            /**
             * @description highcharts组件依赖文件
             * <AUTHOR>
             * @date 2023-09-08
             */
            "baseComponents/js/highcharts",
            "baseComponents/js/highcharts-3d",
            "baseComponents/js/highcharts-cylinder",
            "baseComponents/js/highcharts-funnel3d",
            /**
             * @description 轻量化地图基类
             * <AUTHOR>
             * @date 2024-04-18
             */
            "lightGISBase/v4.0/lightGISBase",
            /**
             * @description 日期选择插件
             * <AUTHOR>
             * @date 2024-07-02
             */
            "baseComponents/js/jedate.min",
        ];
    }

    addCSSFiles() {
        this.cssList.forEach((cssFile) => {
            let el = document.createElement("link");
            el.setAttribute("rel", "stylesheet");
            el.setAttribute("type", "text/css");
            el.setAttribute("href", `${this.path}/${cssFile}${this.isUglify ? ".min" : ""}.css`);
            this.headEl.appendChild(el);
            el.onload = () => {
                this.cssFileCount++;
            };
            el.onerror = () => {
                console.warn(`组件css依赖${jsFile}未找到!`);
                this.cssFileCount++;
            };
        });
    }

    // addJSFiles() {
    //   this.jsList.forEach((jsFile) => {
    //     let el = document.createElement("script");
    //     el.setAttribute(
    //       "src",
    //       `${this.path}/${jsFile}${this.isUglify ? ".min" : ""}.js`
    //     );
    //     this.headEl.appendChild(el);
    //     el.onload = () => {
    //       this.jsFileCount++;
    //     };
    //     el.onerror = () => {
    //       console.warn(`组件js依赖${jsFile}未找到!`);
    //       this.jsFileCount++;
    //     };
    //   });
    // }

    loadScript(src) {
        return new Promise(function (resolve, reject) {
            let script = document.createElement("script");
            script.src = src;
            script.async = false;
            script.onload = () => resolve(script);
            script.onerror = () => reject(new Error(`Script load error for ${src}`));
            document.head.append(script);
        });
    }

    addJSFiles() {
        this.jsList.forEach(async (jsFile) => {
            await this.loadScript(`${this.path}/${jsFile}${this.isUglify ? ".min" : ""}.js`);
            this.jsFileCount++;
        });
    }

    initBaseComponents(isDp) {
        //如果是大屏加载，则前2个js架包无需动态加载
        if (isDp) this.jsList = this.jsList.splice(2);
        return new Promise((resolve) => {
            this.addCSSFiles();
            this.addJSFiles();
            let interval = setInterval(() => {
                if (
                    this.jsFileCount === this.jsList.length &&
                    this.cssFileCount === this.cssList.length
                ) {
                    clearInterval(interval);
                    resolve();
                }
            }, 500);
        });
    }
}
