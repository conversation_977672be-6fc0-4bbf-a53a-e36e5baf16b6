/**
 * Created by lixin on 2021/3/16.
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
    typeof define === 'function' && define.amd ? define(['exports'], factory) : (factory((global.Cmapd = global.Cmapd || {})));
}(this, (function (exports) {
  'use strict';

  let version = '1.0a';
  let _dataServerIP = null;

  /** 
   * 将服务地址传入，在封装过的ajax请求中使用
   * add by matianyu 2021/3/3
   */
  function setDataServerIP(ip) {
    _dataServerIP = ip;
  }

  class ColorSequence {
    constructor() {
      this.colors =
        [
          '#ea5514'
          , '#66ff00'
          , '#33ffff'
          , '#6699ff'
          , '#c9a063'
          , '#e60012'
          , '#727171'
          , '#e61673'
          , '#f39800'
        ];

      this.seq = this.colors.length;
    }

    getColor() {
      this.seq++;

      if (this.seq >= this.colors.length) {
        this.seq = 0;
      }

      return this.colors[this.seq];
    }
  }

  const colorSequence = new ColorSequence();
  function getColor() {
    colorSequence.getColor();
  }

  function uuid() {
    var s = [];
    var hexDigits = "0123456789abcdef";
    for (var i = 0; i < 36; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
    }
    s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23] = "-";

    var uuid = s.join("");
    return uuid;
  }

  /* 坐标系转换*/
  function coordinateTransfrom(method, lng, lat) {
    const x_PI = 3.14159265358979324 * 3000.0 / 180.0;
    const PI = 3.1415926535897932384626;
    const a = 6378245.0;
    const ee = 0.00669342162296594323;

    const transformlat = function (lng, lat) {
      let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
      ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
      ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0;
      ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0;
      return ret
    }

    const transformlng = function (lng, lat) {
      let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
      ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
      ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0;
      ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0;
      return ret
    }

    const out_of_china = function (lng, lat) { // 判断是否在国内，不在国内则不做偏移
      return (lng < 72.004 || lng > 137.8347) || ((lat < 0.8293 || lat > 55.8271) || false);
    }

    switch (method) {
      case "bd09togcj02": { // BD-09转GCJ-02
        var x_pi = 3.14159265358979324 * 3000.0 / 180.0;
        var x = lng - 0.0065;
        var y = lat - 0.006;
        var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
        var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
        var gg_lng = z * Math.cos(theta);
        var gg_lat = z * Math.sin(theta);

        return { lng: gg_lng, lat: gg_lat };
      }
      case "gcj02tobd09": { // GCJ-02转BD-09
        var z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI);
        var theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI);
        var bd_lng = z * Math.cos(theta) + 0.0065;
        var bd_lat = z * Math.sin(theta) + 0.006;
        return { lng: bd_lng, lat: bd_lat };
      }
      case "wgs84togcj02": { // WGS84转GCj02
        if (out_of_china(lng, lat)) {
          return { lng: lng, lat: lat }
        }
        else {
          var dlat = transformlat(lng - 105.0, lat - 35.0);
          var dlng = transformlng(lng - 105.0, lat - 35.0);
          var radlat = lat / 180.0 * PI;
          var magic = Math.sin(radlat);
          magic = 1 - ee * magic * magic;
          var sqrtmagic = Math.sqrt(magic);
          dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);
          dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI);
          var mglat = lat + dlat;
          var mglng = lng + dlng;
          return { lng: mglng, lat: mglat };
        }
      }
      case "gcj02towgs84": { // GCJ02转WGS84
        if (out_of_china(lng, lat)) {
          return { lng: lng, lat: lat }

        }
        else {
          var dlat = transformlat(lng - 105.0, lat - 35.0);
          var dlng = transformlng(lng - 105.0, lat - 35.0);
          var radlat = lat / 180.0 * PI;
          var magic = Math.sin(radlat);
          magic = 1 - ee * magic * magic;
          var sqrtmagic = Math.sqrt(magic);
          dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);
          dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI);
          mglat = lat + dlat;
          mglng = lng + dlng;
          return { lng: lng * 2 - mglng, lat: lat * 2 - mglat }
        }
      }
    }
  }

  function formatCss(jsonData) {
    let style = "";
    $.each(jsonData, (key, value) => {
      switch (key) {
        case 'transformOrigin': {
          style += `transform-origin: ${value}; `;
          break;
        }
        case 'fontSize': {
          style += `font-size: ${value}; `;
          break;
        }
        case 'lineHeight': {
          style += `line-height: ${value}; `;
          break;
        }
        case 'borderRadius': {
          style += `border-radius: ${value}; `;
          break;
        }
        case 'zIndex': {
          style += `z-index: ${value}; `;
          break;
        }
        case 'textAlign': {
          style += `text-align: ${value}; `;
          break;
        }
        case 'backgroundColor': {
          style += `background-color: ${value}; `;
          break;
        }
        case 'backgroundSize': {
          style += `background-size: ${value}; `;
          break;
        }
        case 'borderTop': {
          style += `border-top: ${value}; `;
          break;
        }
        case 'marginLeft': {
          style += `margin-left: ${value}; `;
          break;
        }
        case 'marginTop': {
          style += `margin-top: ${value}; `;
          break;
        }
        case 'textAlign': {
          style += `text-align: ${value}; `;
          break;
        }
        case 'lineHeight': {
          style += `line-height: ${value}; `;
          break;
        }
        default: {
          style += `${key}: ${value}; `;
        }

      }

    })

    return style;
  }

  /* 
  * 根据两点计算角度，与线方向相关，获得结果未0点为起点的顺时针角度
  * add by matianyu 2021/3/3
  * */
  function getAngle(px, py, mx, my) {
    if (px == mx && py == my) {
      return 0;
    }

    if (py == my && mx > px) { // 在X轴上
      return 90;
    } else if (py == my && mx < px) {
      return 270;
    }

    if (px == mx && my > py) { // 在Y轴上
      return 0;
    } else if (px == mx && my < py) {
      return 180;
    }

    let len3 = Math.sqrt(Math.pow((mx - px), 2) + Math.pow((my - py), 2)); // 斜边长

    if (mx > px && my > py) {  // 第一象限
      return Math.asin(Math.abs(mx - px) / len3) / (Math.PI / 180);
    }

    if (mx > px && my < py) {  // 第二象限
      return 90 + Math.acos(Math.abs(mx - px) / len3) / (Math.PI / 180);
    }

    if (mx < px && my < py) {  // 第三象限
      return 180 + Math.asin(Math.abs(mx - px) / len3) / (Math.PI / 180);
    }

    if (mx < px && my > py) {  // 第四象限
      return 270 + Math.acos(Math.abs(mx - px) / len3) / (Math.PI / 180);
    }

    return 0;

  }

  function wktToLatLng(wkt, type) {
    switch (type) {
      case 'point': {
        let latlngstr = wkt.slice(6, wkt.length - 1).split(' ');  // 从属性中解析经纬度
        return { lng: latlngstr[0], lat: latlngstr[1] };
      }
      case 'line': {
        return wkt.slice(11, wkt.length - 1).split(',').map(function (str) {
          return { lng: str.split(' ')[0], lat: str.split(' ')[1] };
        });
      }
      case 'polygon': {
        return wkt.slice(9, wkt.length - 2).split(',').map(function (str) {
          return { lng: str.split(' ')[0], lat: str.split(' ')[1] };
        });
      }
    }
  }

  function latLngToWKT(latlng, type) {
    switch (type) {
      case 'point': {
        return `POINT(${latlng.lng} ${latlng.lat})`;
        break;
      }
      case 'line': {
        return "LINESTRING(" + latlng.map(function (each) { return `${each.lng} ${each.lat}`; }) + ')'
        break;
      }
      case 'polygon': {
        return "POLYGON((" + latlng.map(function (each) { return `${each.lng} ${each.lat}`; }) + `,${latlng[0].lng} ${latlng[0].lat}` + '))';
        break;
      }
    }
  }

  class MapWebSocket {
    constructor(ws) {
      if (!ws) return;

      this.rWebSock = new ReconnectingWebSocket(ws);
      this.rWebSock.onopen = function () {
        console.info("map data WS opened!");
      }

      this.stompClient = Stomp.over(this.rWebSock);

      this.topics = {};
    }

    sendMsg(destination, { }, data) {
      this.stompClient.send(destination, {}, data);
    }

    /* 多订阅，带回调连接，回调内依次订阅*/
    connect(callback) {
      this.stompClient.connect({}, success => {
        console.info("map data ws stomp connect succeed", success);

        callback && callback();
      }, error => {
        console.warn("map data ws stomp connect failed", error);
      }
      );
    }

    /* 连接成功后进行订阅 */
    subscribeTopic(topic, callback) {
      this.topics[topic] = this.stompClient.subscribe(topic, callback)
    }

    /* 取消topic订阅*/
    unsubscribeData(topic) {
      try {
        this.topics[topic].unsubscribe(function () {
          console.info("map data ws stomp unsubscribe success");
        });
      } catch (e) {
        console.warn('map data ws stomp unsubscribe error');
      } finally {
        return;
      }
    }

    /* 清理所有订阅*/
    clear() {
      for (let topic in this.topics) {
        this.unsubscribeData(topic);
      }

      this.stompClient.connected && this.stompClient.disconnect();
      this.rWebSock && this.rWebSock.disconnect();
    }
  }

  function mapWebSocket(ws) {
    return new MapWebSocket(ws);
  }

  function ajaxPost(url, para, success, fail) {
    const ajaxHandler = $.ajax({
      url: `http://${_dataServerIP}${url}`,
      type: 'post',
      headers: {
        "Content-Type": "application/json",
        "Authorization": sessionId
      },
      data: JSON.stringify(para),
      async: true,
      dataType: 'json',
      timeout: 10000,
      success: (data) => {
        if (data.code == '0000') {
          success && success(data.data);
        } else {

        }

      },
      error: (e) => {
        fail && fail();
      },
      complete: (XMLHttpRequest, status) => {
        if (status == 'timeout') {
          ajaxHandler.abort();
          fail && fail();
        }
      }
    })
  }

  function ajaxGet(url, para, success, fail) {
    const ajaxHandler = $.ajax({
      url: `http://${_dataServerIP}${url}`,
      type: 'get',
      headers: {
        "Content-Type": "application/json",
        // "Authorization": sessionId
      },
      data: para,
      async: true,
      dataType: 'json',
      timeout: 10000,
      success: (data) => {
        if (data.code == '0000') {
          success(data.data);
        } else {

        }
      },
      error: (e) => {
        fail && fail();
      },
      complete: (XMLHttpRequest, status) => {
        if (status == 'timeout') {
          ajaxHandler.abort();
          fail && fail();
        }
      }
    })
  }

  window.Cmapd = exports;

  exports.version = version;
  exports.setDataServerIP = setDataServerIP;
  exports.wktToLatLng = wktToLatLng;
  exports.latLngToWKT = latLngToWKT;
  exports.getAngle = getAngle;
  exports.formatCss = formatCss;
  exports.coordinateTransfrom = coordinateTransfrom;
  exports.uuid = uuid;
  exports.getColor = getColor;
  exports.mapWebSocket = mapWebSocket;
  exports.ajaxPost = ajaxPost;
  exports.ajaxGet = ajaxGet;

})));










