!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.PreviewSDK=t():e.PreviewSDK=t()}(window,function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=6)}([function(e,t,n){e.exports=!n(5)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t){var n=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=n)},function(e,t,n){var o=n(16),r=n(17),i=n(19),u=Object.defineProperty;t.f=n(0)?Object.defineProperty:function(e,t,n){if(o(e),t=i(t,!0),o(n),r)try{return u(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.config=void 0;var o=u(n(7)),r=u(n(8)),i=n(22);function u(e){return e&&e.__esModule?e:{default:e}}var f=function(){function e(t){var n=this;return(0,o.default)(this,e),this.initEventQueen=[],this.lastToken=null,this.lastTokenTime=null,this.firstSetToken=!0,t?t.url?t.mount?(this.targetLocation=(0,i.parseUrl)(t.url),this.sourceLocation=window.location,this.previewMount=t.mount,this.config=t,this.previewMount.src=t.url,void window.addEventListener("message",function(e){if("wpsPreviewDidMount"===e.data&&e.origin===n.targetLocation.origin){for(var t=0;t<n.initEventQueen.length;t++){var o=n.initEventQueen[t];try{o(),n.initEventQueen[t]=void 0}catch(e){continue}}n.initEventQueen=n.initEventQueen.filter(function(e){return!!e}),n.setConfig()}})):console.error("璇疯缃寕杞介瑙堥〉闈㈢殑iframe"):console.error("璇疯缃畬鏁撮瑙坲rl"):console.error("鍒濆鍖杝dk澶辫触锛岀己澶眂onfig鍙傛暟")}return(0,r.default)(e,[{key:"destroy",value:function(){this.previewMount.remove()}},{key:"setConfig",value:function(){}},{key:"postMsg",value:function(e){(this.previewMount.contentWindow||this.previewMount.window).postMessage(e,this.targetLocation.origin)}},{key:"postToken",value:function(e){this.postMsg({eventName:this.firstSetToken?"setToken":"setTokenRefresh",data:e}),this.firstSetToken=!1,this.lastToken=e,this.lastTokenTime=(new Date).getTime()}},{key:"setToken",value:function(e){var t=this;if(!e||!e.token||!e.timeout)return console.error("璇锋寜鐓ф枃妗ｈ鑼冭缃畉oken鏍煎紡");this.initEventQueen.push(function(){t.postToken(e),"function"==typeof t.config.refreshToken&&t.refreshToken(e.timeout)})}},{key:"refreshToken",value:function(e){var t=this;window.document.addEventListener("visibilitychange",function(){if("hidden"!==document.visibilityState){var e=(new Date).getTime();if(t.lastToken&&e-t.lastTokenTime>t.lastToken.timeout){var n=t.config.refreshToken();"[object Promise]"===n.toString()?n.then(function(e){t.postToken(e)}):t.postToken(n)}}});var n=function(e){t.postToken(e),e.timeout&&o(e.timeout)},o=function(e){var o=void 0,r=e-3e5;setTimeout(function(){var i=(new Date).getTime(),u=t.config.refreshToken();if("[object Promise]"===u.toString())u.then(function(t){o=t;var u=(new Date).getTime();setTimeout(function(){n(o)},r>0?3e5-(u-i):e-(u-i))});else{o=u;var f=(new Date).getTime();setTimeout(function(){n(o)},r>0?3e5-(f-i):e-(f-i))}},r)};o(e)}},{key:"setOfficePreviewCallback",value:function(e,t){var n=this;if(t||"function"!=typeof e||(t=e,e=""),e&&!/^https?/.test(e))return console.error("鍥炶皟鍦板潃涓嶇鍚堣鑼�");this.initEventQueen.push(function(){e=encodeURIComponent(e),n.postMsg({eventName:"setOfficePreviewCallback",data:{url:e,officePreviewCallbackResult:!!t}}),t&&"function"==typeof t&&window.addEventListener("message",function(e){e.data&&e.origin===n.targetLocation.origin&&("setOfficePreviewCallbackSuccess"===e.data.eventName?t(e.data.data):"setOfficePreviewCallbackError"===e.data.eventName&&t("",e.data.data))})})}}]),e}();t.config=function(e){return new f(e)}},function(e,t,n){"use strict";t.__esModule=!0,t.default=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},function(e,t,n){"use strict";t.__esModule=!0;var o=function(e){return e&&e.__esModule?e:{default:e}}(n(9));t.default=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),(0,o.default)(e,r.key,r)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}()},function(e,t,n){e.exports={default:n(10),__esModule:!0}},function(e,t,n){n(11);var o=n(3).Object;e.exports=function(e,t,n){return o.defineProperty(e,t,n)}},function(e,t,n){var o=n(12);o(o.S+o.F*!n(0),"Object",{defineProperty:n(4).f})},function(e,t,n){var o=n(2),r=n(3),i=n(13),u=n(15),f=n(21),c=function(e,t,n){var a,s,l,p=e&c.F,v=e&c.G,d=e&c.S,h=e&c.P,y=e&c.B,w=e&c.W,m=v?r:r[t]||(r[t]={}),g=m.prototype,b=v?o:d?o[t]:(o[t]||{}).prototype;for(a in v&&(n=t),n)(s=!p&&b&&void 0!==b[a])&&f(m,a)||(l=s?b[a]:n[a],m[a]=v&&"function"!=typeof b[a]?n[a]:y&&s?i(l,o):w&&b[a]==l?function(e){var t=function(t,n,o){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,n)}return new e(t,n,o)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(l):h&&"function"==typeof l?i(Function.call,l):l,h&&((m.virtual||(m.virtual={}))[a]=l,e&c.R&&g&&!g[a]&&u(g,a,l)))};c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,e.exports=c},function(e,t,n){var o=n(14);e.exports=function(e,t,n){if(o(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,o){return e.call(t,n,o)};case 3:return function(n,o,r){return e.call(t,n,o,r)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t,n){var o=n(4),r=n(20);e.exports=n(0)?function(e,t,n){return o.f(e,t,r(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t,n){var o=n(1);e.exports=function(e){if(!o(e))throw TypeError(e+" is not an object!");return e}},function(e,t,n){e.exports=!n(0)&&!n(5)(function(){return 7!=Object.defineProperty(n(18)("div"),"a",{get:function(){return 7}}).a})},function(e,t,n){var o=n(1),r=n(2).document,i=o(r)&&o(r.createElement);e.exports=function(e){return i?r.createElement(e):{}}},function(e,t,n){var o=n(1);e.exports=function(e,t){if(!o(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!o(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!o(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!o(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseUrl=function(e){var t={};t.href=e;var n=e.indexOf("https");t.protocol=n>=0?"https:":"http:";var o=e.match(/[^http|\/][0-9a-zA-Z][^\:|\/]+/);t.hostname=o&&o[0]||"";var r=e.indexOf("#")>=0?e.split("#")[1]:"";t.hash=r;var i=e.match(/\:[0-9]+/);return t.port=i&&i[0].replace(/\:/,"")||"",t.host=t.hostname+(t.port?":"+t.port:""),t.origin=t.protocol+"//"+t.host,t.search=e.indexOf("?")>-1?e.slice(e.indexOf("?")):"",t.path=e.replace(t.origin,"").replace(t.search,""),t}}])});