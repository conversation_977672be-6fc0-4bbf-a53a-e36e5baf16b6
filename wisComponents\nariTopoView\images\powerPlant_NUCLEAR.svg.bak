<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 135 135" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1,0,0,1,-686,-1493)">
        <g transform="matrix(1.37125,0,0,2.04701,-125.113,0)">
            <g>
                <g transform="matrix(0.241655,0,0,0.266912,-3216.26,1297.88)">
                    <g transform="matrix(0.676259,0,0,0.412755,11587.2,-1818.93)">
                        <g>
                            <g transform="matrix(2.96991,0,0,3.12522,6219.65,-727.274)">
                                <path d="M0,182.925C-9.494,182.925 -17.218,175.201 -17.218,165.708L-17.79,9.033C-17.79,-0.462 -10.066,-8.185 -0.572,-8.185L166.804,-8.185C176.299,-8.185 184.022,-0.462 184.022,9.033L184.594,165.708C184.594,175.201 176.871,182.925 167.376,182.925L0,182.925Z" style="fill:url(#_Linear1);fill-rule:nonzero;"/>
                            </g>
                            <g transform="matrix(2.8136,0,0,2.96073,6232.49,-713.004)">
                                <path d="M0,182.994C-9.494,182.994 -17.218,175.27 -17.218,165.777L-17.79,9.033C-17.79,-0.462 -10.066,-8.185 -0.572,-8.185L166.947,-8.185C176.442,-8.185 184.165,-0.462 184.165,9.033L184.737,165.777C184.737,175.27 177.014,182.994 167.519,182.994L0,182.994Z" style="fill:rgb(0,19,35);fill-rule:nonzero;"/>
                            </g>
                            <g transform="matrix(0,-509.769,-484.435,0,6464.33,-199.594)">
                                <path d="M1.048,0.482C1.048,0.533 1.006,0.575 0.955,0.575L0.044,0.571C-0.007,0.571 -0.049,0.53 -0.049,0.479L-0.049,-0.494C-0.049,-0.545 -0.007,-0.587 0.044,-0.587L0.955,-0.584C1.006,-0.584 1.048,-0.542 1.048,-0.491L1.048,0.482Z" style="fill:url(#_Linear2);fill-rule:nonzero;"/>
                            </g>
                            <g transform="matrix(4.46245,-0,-0,4.43425,6166.04,-753.667)">
                                <use xlink:href="#_Image3" x="6" y="6" width="123px" height="123px"/>
                            </g>
                            <g transform="matrix(2.81511,0,0,3.12317,6696.18,-654.859)">
                                <path d="M2.142,-21.713L-165.369,-21.713C-172.151,-21.713 -177.668,-16.196 -177.668,-9.415L-177.668,-8.185C-177.668,-14.966 -172.151,-20.483 -165.369,-20.483L2.142,-20.483C8.923,-20.483 14.441,-14.966 14.441,-8.185L14.441,-9.415C14.441,-16.196 8.923,-21.713 2.142,-21.713" style="fill:rgb(44,235,255);fill-rule:nonzero;"/>
                            </g>
                        </g>
                    </g>
                    <g transform="matrix(2.59857,0,0,1.57602,-16382.9,-3804.98)">
                        <path d="M12470,1063L12380,1063C12380,1063 12370.1,1064.97 12369,1076.79C12367.9,1088.61 12369,1207 12369,1207C12369,1207 12375.1,1223.15 12394,1218.62L12470,1063Z" style="fill:url(#_Linear4);"/>
                    </g>
                </g>
                <g transform="matrix(0.729259,-0,-0,0.488519,591.511,729.358)">
                    <use xlink:href="#_Image5" x="10" y="13" width="115px" height="110px"/>
                </g>
            </g>
        </g>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(4.74784e-12,-181.453,192.157,4.48338e-12,83.4019,173.268)"><stop offset="0" style="stop-color:rgb(155,155,155);stop-opacity:1"/><stop offset="0.52" style="stop-color:rgb(141,141,141);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(163,163,163);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear2" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.02611,-6.65374e-17,-5.93313e-17,-1.02611,0,1.58442e-06)"><stop offset="0" style="stop-color:rgb(0,69,38);stop-opacity:1"/><stop offset="0.54" style="stop-color:rgb(0,64,82);stop-opacity:1"/><stop offset="0.97" style="stop-color:rgb(15,67,114);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,244,255);stop-opacity:1"/></linearGradient>
        <image id="_Image3" width="123px" height="123px" xlink:href="data:image/png;base64,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"/>
        <linearGradient id="_Linear4" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(44.0826,83.6986,-83.6986,44.0826,12395.9,1072.3)"><stop offset="0" style="stop-color:white;stop-opacity:0.46"/><stop offset="1" style="stop-color:white;stop-opacity:0"/></linearGradient>
        <image id="_Image5" width="115px" height="110px" xlink:href="data:image/png;base64,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"/>
    </defs>
</svg>
