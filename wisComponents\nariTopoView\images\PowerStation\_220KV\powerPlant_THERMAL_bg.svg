<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 208 208" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1,0,0,1,-1,0)">
        <g transform="matrix(1,0,0,1,1,0)">
            <g transform="matrix(1.06667,0,0,1,-13.8667,0)">
                <path d="M208,20.102C208,9.007 199.556,0 189.155,0L31.845,0C21.444,0 13,9.007 13,20.102L13,187.898C13,198.993 21.444,208 31.845,208L189.155,208C199.556,208 208,198.993 208,187.898L208,20.102Z" style="fill:url(#_Linear1);"/>
            </g>
            <g transform="matrix(1.01538,0,0,0.951923,-8.2,5)">
                <path d="M208,20.102C208,9.007 199.556,0 189.155,0L31.845,0C21.444,0 13,9.007 13,20.102L13,187.898C13,198.993 21.444,208 31.845,208L189.155,208C199.556,208 208,198.993 208,187.898L208,20.102Z"/>
            </g>
            <g transform="matrix(1.00513,0,0,0.942308,-7.06667,6)">
                <path d="M208,20.102C208,9.007 199.556,0 189.155,0L31.845,0C21.444,0 13,9.007 13,20.102L13,187.898C13,198.993 21.444,208 31.845,208L189.155,208C199.556,208 208,198.993 208,187.898L208,20.102Z" style="fill:rgb(0,35,255);"/>
            </g>
            <g transform="matrix(0.974359,0,0,0.913462,-3.66667,9)">
                <path d="M208,20.102C208,9.007 199.556,0 189.155,0L31.845,0C21.444,0 13,9.007 13,20.102L13,187.898C13,198.993 21.444,208 31.845,208L189.155,208C199.556,208 208,198.993 208,187.898L208,20.102Z" style="fill:none;"/>
            </g>
            <g transform="matrix(0.974359,0,0,0.913462,-3.66667,9)">
                <clipPath id="_clip2">
                    <rect x="13" y="195.958" width="195" height="12.042"/>
                </clipPath>
                <g clip-path="url(#_clip2)">
                    <path d="M208,20.102C208,9.007 199.556,0 189.155,0L31.845,0C21.444,0 13,9.007 13,20.102L13,187.898C13,198.993 21.444,208 31.845,208L189.155,208C199.556,208 208,198.993 208,187.898L208,20.102Z" style="fill:url(#_Linear3);"/>
                </g>
            </g>
            <g transform="matrix(5.96623e-17,-0.974359,0.913462,5.59334e-17,9,211.667)">
                <clipPath id="_clip4">
                    <rect x="13" y="195.958" width="195" height="12.042"/>
                </clipPath>
                <g clip-path="url(#_clip4)">
                    <path d="M208,20.102C208,9.007 199.556,0 189.155,0L31.845,0C21.444,0 13,9.007 13,20.102L13,187.898C13,198.993 21.444,208 31.845,208L189.155,208C199.556,208 208,198.993 208,187.898L208,20.102Z" style="fill:url(#_Linear5);"/>
                </g>
            </g>
            <g transform="matrix(-0.974359,-1.19325e-16,1.11867e-16,-0.913462,211.667,199)">
                <clipPath id="_clip6">
                    <rect x="13" y="195.958" width="195" height="12.042"/>
                </clipPath>
                <g clip-path="url(#_clip6)">
                    <path d="M208,20.102C208,9.007 199.556,0 189.155,0L31.845,0C21.444,0 13,9.007 13,20.102L13,187.898C13,198.993 21.444,208 31.845,208L189.155,208C199.556,208 208,198.993 208,187.898L208,20.102Z" style="fill:url(#_Linear7);"/>
                </g>
            </g>
            <g transform="matrix(-1.78987e-16,0.974359,-0.913462,-1.678e-16,199,-3.66667)">
                <clipPath id="_clip8">
                    <rect x="13" y="195.958" width="195" height="12.042"/>
                </clipPath>
                <g clip-path="url(#_clip8)">
                    <path d="M208,20.102C208,9.007 199.556,0 189.155,0L31.845,0C21.444,0 13,9.007 13,20.102L13,187.898C13,198.993 21.444,208 31.845,208L189.155,208C199.556,208 208,198.993 208,187.898L208,20.102Z" style="fill:url(#_Linear9);"/>
                </g>
            </g>
            <path d="M9,30.362L9,27.362C9,17.228 17.228,9 27.362,9L180.638,9C190.772,9 199,17.228 199,27.362L199,30.362C199,20.228 190.772,12 180.638,12L27.362,12C17.228,12 9,20.228 9,30.362Z" style="fill:white;"/>
        </g>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.11895e-14,194.92,-182.738,1.19354e-14,98.3125,6.94533)"><stop offset="0" style="stop-color:rgb(162,162,162);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(57,57,57);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear3" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(8.79812e-16,-15.3263,14.3684,9.38466e-16,107.421,211.284)"><stop offset="0" style="stop-color:rgb(0,252,247);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,252,247);stop-opacity:0"/></linearGradient>
        <linearGradient id="_Linear5" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(8.79812e-16,-15.3263,14.3684,9.38466e-16,107.421,211.284)"><stop offset="0" style="stop-color:rgb(0,252,247);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,252,247);stop-opacity:0"/></linearGradient>
        <linearGradient id="_Linear7" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(8.79812e-16,-15.3263,14.3684,9.38466e-16,107.421,211.284)"><stop offset="0" style="stop-color:rgb(0,252,247);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,252,247);stop-opacity:0"/></linearGradient>
        <linearGradient id="_Linear9" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(8.79812e-16,-15.3263,14.3684,9.38466e-16,107.421,211.284)"><stop offset="0" style="stop-color:rgb(0,252,247);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(0,252,247);stop-opacity:0"/></linearGradient>
    </defs>
</svg>
