/**
 * @description 拓扑图查看器
 * <AUTHOR>
 * @date 2020-12-29
 * @class TopoView
 * @extends {SVGComponentBase}
 */
class NariTopoView extends SVGComponentBase {
  /**
   * @constructor 组件基类构造函数
   * @param id {string} 组件id
   * @param code {string} 组件编码
   * @param container {HTMLElement} 组件容器
   * @param workMode {number} 组件工作模式 0为测试 1为大屏 2为配置工具 3为控制中心 4为单一组件调试
   * @param option {Object} 组件属性
   * @param useDefaultOpt {boolean} 是否启用默认值
   */
  constructor(
    id,
    code,
    container,
    workMode,
    option = {},
    useDefaultOpt = true
  ) {
    super(id, code, container, workMode, option, useDefaultOpt);
    this._setupDefaultValues();
    this._draw();
    this._keyboardDownHandler();
    if (this.property.basic.needSync && workMode !== 2) {
      this._initEventSync();
    }
    if (workMode !== 2) {
      // this._getWSData();
    }
  }

  /**
   * @description 初始化组件所需要的数据
   */
  _setupDefaultValues() {
    super._setupDefaultValues();
    //topo图的节点和链路数据
    this.topoData = {};
    this.mapList = [];
    this.ip = "http://**************";
    this.port = "8899";
    this.mapIdList = [];
    //子图层列表
    this.subLayerList = [];
    //热点区域列表
    this.hotspotsList = [];
    // 绘制变压站所需的svg数据
    this.svgData = {};
    //图层位移缩放
    this.transform = {
      k: 1,
      x: 0,
      y: 0,
    };
    //图层动画时长
    this.animeDur = 0;
    //图层动画对象
    this.anime = null;
    //电厂组件列表
    this.powerPlantMap = {};
    //变电站组件列表
    this.transStationMap = {};
    //换流站组件列表
    this.exchStationMap = {};
    //分区板组件列表
    this.areaBoardMap = {};
    //图层数据
    this.mapData = {};
    //节点样式 s小/l大/m变电站带母线/p打印
    this.nodeType = "l";
    //展示子图层           500kv链路      500kv节点      500kv标注    500kv链路标注
    this.showSublayer = [
      "KS5tPfwEKN",
      "FS5tPfwEKT",
      "KS5TXwwEDE",
      "Zr124XA8fb",
    ];
  }

  /**
   * @description 初始化组件配置项
   */
  _initProperty() {
    super._initProperty();
    let options = {
      basic: {
        className: "NariTopoView",
        needSync: true,
      },
      viewSetting: {
        loadMapId: "O0InrXDzGj", //500kv
        // loadMapId: 'yO3u76xwhH',  //220kv
        ip: "http://**************",
        port: "8899",
        ftpIp: "http://**************",
        ftpPort: "6818",
        dataUrl: "http://************:8800/getDataByMapIdDynamic?mapId=xxx",
        w: 9800,
        h: 3150,
      },
    };

    let optionDic = [
      {
        name: "viewSetting",
        displayName: "显示设置",
        show: true,
        editable: true,
        children: [
          {
            name: "loadMapId",
            displayName: "图层ID",
            description: "图层ID",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "ip",
            displayName: "ip地址",
            description: "ip地址",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "port",
            displayName: "端口",
            description: "端口",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "ftpIp",
            displayName: "ftp的ip地址",
            description: "ftp的ip地址",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "ftpPort",
            displayName: "ftp端口",
            description: "ftp端口",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "dataUrl",
            displayName: "数据接口",
            description: "数据接口",
            type: OptionType.string,
            show: true,
            editable: true,
          },
          {
            name: "w",
            displayName: "显示宽度",
            description: "显示宽度",
            type: OptionType.int,
            show: true,
            editable: true,
          },
          {
            name: "h",
            displayName: "显示高度",
            description: "显示高度",
            type: OptionType.int,
            show: true,
            editable: true,
          },
        ],
      },
    ];

    this._addProperty(options, optionDic);
  }

  /**
   * @description 初始化组件事件
   */
  _initEvents() {
    super._initEvents();
    this.invokeFunc = this.invokeFunc.concat([
      {
        name: "switchTo220",
        displayName: "200KV全景图",
        params: [],
      },
      {
        name: "close500Link",
        displayName: "关闭500连线",
        params: [],
      },
      {
        name: "open500Link",
        displayName: "打开500连线",
        params: [],
      },
      {
        name: "switchTo500",
        displayName: "500KV全景图",
        params: [],
      },
      {
        name: "flyToHotspots",
        displayName: "跳转热点",
        params: [
          {
            name: "hotspotsId",
            displayName: "热点Id",
            type: OptionType.enum,
            options: [],
          },
        ],
      },
      {
        name: "switchLinkBolckMode",
        displayName: "线框模式",
        params: [],
      },
      {
        name: "getTotalData",
        displayName: "获取数据",
        params: [],
      },
      {
        name: "switchDivideLink",
        displayName: "切换显示分区联络线",
        params: [
          {
            name: "status",
            displayName: "是否显示",
            type: OptionType.boolean,
          },
        ],
      },
      {
        name: "switchDivideFill",
        displayName: "切换显示分区填充",
        params: [
          {
            name: "status",
            displayName: "是否显示",
            type: OptionType.boolean,
          },
        ],
      },
    ]);
  }

  /**
   * 切换子图层显示隐藏状态
   * @param {string} name 子图层名称
   * @param {boolean} isShow 是否显示
   */
  switchSublayer(name, isShow) {
    let sublayerId = this.subLayerList.filter((d) => d.sublayerName === name)[0]
      .sublayerId;
    this.mainSVG
      .selectAll(`.${sublayerId}`)
      .style("display", isShow ? "block" : "none");
  }

  /**
   * @description 绘制入口
   */
  _draw() {
    super._draw();
    this._drawLoading();
    this.mainSVG
      .style("fill-rule", "evenodd")
      .style("clip-rule", "evenodd")
      .style("stroke-linejoin", "round")
      .style("stroke-miterlimit", 2);
    this._generateDefines();
    this.containerPosition = {
      x: 0,
      y: 0,
      width: this.property.basic.frame[2],
      height: this.property.basic.frame[3],
    };
    this.mainSVG.select(".nari_current_container").remove();
    this.mainSVG.append("g").attr("class", "nari_current_container");
    this._getTopo();
  }
  _drawLoading() {
    this.loadingCon = d3
      .select(this.container)
      .append("div")
      .attr("id", "naritopo-loading-con");
    this.loading = this.loadingCon.append("div").attr("id", "naritopo-loading");

    $(this.loading.node()).append(`<div class="spinner-box">
    <div class="pulse-container">  
      <div class="pulse-bubble pulse-bubble-1"></div>
      <div class="pulse-bubble pulse-bubble-2"></div>
      <div class="pulse-bubble pulse-bubble-3"></div>
    </div>
  </div>`);
  }

  switchTo220() {
    this.transform = {
      x: 0,
      y: 0,
      k: 1,
    };
    //展示子图层 500链路 500节点 500红字 220链路 220节点 220分区名称
    this.showSublayer = [
      "KS5tPfwEKN",
      "FS5tPfwEKT",
      "FS5wMfwEKT",
      "KS3tPfwEKM",
      "FS3tPfwEKF",
      "M4WacTIW5E",
    ];
    this.nodeType = "s";
    this.animeDur = 0;
    this._transformMain();
  }

  close500Link() {
    this._removeShowSublayer(["KS5tPfwEKN", "s5iL6EUIpN", "Zr124XA8fb"]);
    this.linkCon.selectAll(".KS5tPfwEKN").style("display", "none");
    this.nodeCon.selectAll(".s5iL6EUIpN").style("display", "none");
    this.nodeCon.selectAll(".Zr124XA8fb").style("display", "none");
    this.arrowCon.style("display", "none");
  }

  open500Link() {
    this._addShowSublayer(["KS5tPfwEKN", "s5iL6EUIpN"]);
    this.linkCon.selectAll(".KS5tPfwEKN").style("display", "block");
    this.nodeCon.selectAll(".s5iL6EUIpN").style("display", "block");
    this.arrowCon.style("display", "block");
  }

  switchTo500() {
    this.transform = {
      x: 0,
      y: 0,
      k: 1,
    };
    this.showSublayer = [
      "KS5tPfwEKN",
      "FS5tPfwEKT",
      "KS5TXwwEDE",
      "Zr124XA8fb",
    ];
    this.nodeType = "l";
    this.animeDur = 0;
    this._transformMain();
  }

  flyToHotspots(id) {
    let hotspot = this.hotspotsList.filter((d) => d.hotspotId === id)[0];
    let [x, y] = hotspot.position.split(",");
    this.transform = {
      x: x,
      y: y,
      k: hotspot.scale,
    };
    this.showSublayer = [
      "KS5tPfwEKN",
      "FS5tPfwEKT",
      "FS5wMfwEKT",
      "KS3tPfwEKM",
      "FS3tPfwEKF",
      "Fw5TXwwEDo",
      "M4WacTIW5E",
      "W4WacTIW5M",
    ];
    this.nodeType = "m";
    this.animeDur = 5000;
    this._transformMain();
  }

  /**
   * 显示/隐藏跨区线
   * @param {boolean} status 状态 true开启 false关闭
   */
  switchDivideLink(status) {
    this.linkCon
      .selectAll("path")
      .style("filter", `brightness(${status ? 0.3 : 1})`);
    this.linkCon.selectAll(".topo-isDivide").style("filter", "brightness(1)");
    status ? this.close500Link() : this.open500Link();
  }

  /**
   * 显示/隐藏分区填充
   * @param {boolean} status 状态 true开启 false关闭
   */
  switchDivideFill(status) {
    this.blockCon.selectAll("path").style("fill-opacity", status ? 1 : 0);
  }

  switchLinkBolckMode() {
    d3.select(this.container).style("background-color", "#fff");
    this.linkCon.selectAll(".KS3tPfwEKM").style("stroke", "#000");
    this.blockCon
      .selectAll("path")
      .style("filter", "")
      .style("fill", "none")
      .style("stroke", "#000")
      .style("stroke-width", 2.5);
    for (const key in this.powerPlantMap) {
      this.powerPlantMap[key].setType("p");
    }
    for (const key in this.exchStationMap) {
      this.exchStationMap[key].setType("p");
    }
    for (const key in this.transStationMap) {
      let comp = this.transStationMap[key];
      comp.con
        .attr("x", comp.option.x)
        .attr("y", comp.option.y)
        .attr("width", comp.option.w)
        .attr("height", comp.option.h);
      comp.setType("p");
    }
  }

  getTotalData() {
    this._getData("9ABaG87T8t");
  }

  _transformMain() {
    this.mainSVG
      .select(".nari_current_container")
      .transition()
      .duration(this.workMode == 0 ? this.animeDur : 100)
      .call(
        this.zoom.transform,
        d3.zoomIdentity
          .translate(this.transform.x, this.transform.y)
          .scale(this.transform.k)
      );
  }

  /**
   * 监听键盘按键
   */
  _keyboardDownHandler() {
    document.onkeydown = (e) => {
      console.log(e.key + e.keyCode);
      switch (e.keyCode) {
        // 小键盘+
        case 107:
          this.transform.k += 0.01;
          break;
        // 小键盘-
        case 109:
          this.transform.k -= 0.01;
          break;
        // 方向键左
        case 37:
          this.transform.x -= 100;
          break;
        // 方向键上
        case 38:
          this.transform.y -= 100;
          break;
        // 方向键右
        case 39:
          this.transform.x += 100;
          break;
        // 方向键下
        case 40:
          this.transform.y += 100;
          break;
        // 功能键Home
        case 36:
          this.transform = {
            k: 1,
            x: 0,
            y: 0,
          };
          break;
        //数字键1  全部显示
        case 49:
          this.mainSVG.selectAll(".KS3tPfwEKM").style("display", "block"); //20w线路
          this.mainSVG.selectAll(".KS5tPfwEKN").style("display", "block"); //50w链路
          this.mainSVG.selectAll(".FS3tPfwEKF").style("display", "block"); //22w节点
          this.mainSVG.selectAll(".FS5tPfwEKT").style("display", "block"); //50w节点
          break;
        //数字键2  显示500
        case 50:
          this.mainSVG.selectAll(".KS3tPfwEKM").style("display", "none");
          this.mainSVG.selectAll(".KS5tPfwEKN").style("display", "block");
          this.mainSVG.selectAll(".FS3tPfwEKF").style("display", "none");
          this.mainSVG.selectAll(".FS5tPfwEKT").style("display", "block");
          break;
        //数字键3 显示220
        case 51:
          this.mainSVG.selectAll(".KS3tPfwEKM").style("display", "block");
          this.mainSVG.selectAll(".KS5tPfwEKN").style("display", "none");
          this.mainSVG.selectAll(".FS3tPfwEKF").style("display", "block");
          this.mainSVG.selectAll(".FS5tPfwEKT").style("display", "block");
          break;
        //数字键4 全部隐藏
        case 52:
          this.mainSVG.selectAll(".KS3tPfwEKM").style("display", "none");
          this.mainSVG.selectAll(".KS5tPfwEKN").style("display", "none");
          this.mainSVG.selectAll(".FS3tPfwEKF").style("display", "none");
          this.mainSVG.selectAll(".FS5tPfwEKT").style("display", "none");
          break;
      }
      this.mainSVG
        .select(".nari_current_container")
        .style(
          "transform",
          `translateX(${this.transform.x}px) translateY(${this.transform.y}px) translateZ(0) scale(${this.transform.k})`
        );
    };
  }

  /**
   * 绑定缩放拖拽事件
   */
  _bindZoom() {
    let vb = this.mainSVG
      .select(".nari_current_container")
      .node()
      .dataset.vb.split(" ");
    this.zoom = d3
      .zoom()
      .extent([
        [0, 0],
        [Number(vb[2]), Number(vb[3])],
      ])
      .translateExtent([
        [0, 0],
        [Number(vb[2]), Number(vb[3])],
      ])
      .scaleExtent([1, 8])
      .on("start", () => {
        $("#node-container").children().not(".M4WacTIW5E").remove();
        $(this.linkCon.node()).empty();
        $(this.arrowCon.node()).empty();
        this.transStationMap = {};
        this.powerPlantMap = {};
        this.areaBoardMap = {};
        this.exchStationMap = {};
        this._drawNodes(this.area220NameNode.nodes);
      })
      .on("zoom", () => {
        let tf = d3.event.transform;
        if (this.workMode !== 1) {
          this.mainSVG
            .select(".nari_current_container")
            .style(
              "transform",
              `translateX(${tf.x}px) translateY(${tf.y}px) translateZ(0) scale(${tf.k})`
            );
        }
        this.transform = tf;
      })
      .on("end", () => {
        let tf = d3.event.transform;
        this.transform = tf;
        this._setTransform(tf);
      });
    this.mainSVG.select(".nari_current_container").call(this.zoom);
  }

  /**
   * 设置动画位移
   * @param {Object} transform 动画位移
   * @param {number} duration 动画持续时间
   */
  _setTransform(transform) {
    if (this.animeDur === 0) {
      this.mainSVG
        .select(".nari_current_container")
        .style(
          "transform",
          `translateX(${transform.x}px) translateY(${transform.y}px) translateZ(0) scale(${transform.k})`
        );
      this._drawLinks(this.topoData.links);
      this._drawNodes(this.topoData.nodes).then(() => {
        if (JSON.stringify(this.mapData) !== "{}") {
          this._update(this.mapData);
        }
      });
      return;
    }
    this.anime = new anime({
      targets: this.mainSVG.select(".nari_current_container").node(),
      translateX: transform.x,
      translateY: transform.y,
      scale: transform.k,
      autoplay: false,
      duration: this.animeDur,
      easing: "easeInOutQuad",
      loop: false,
    });
    this._setAnimateSyncParam(this.animeDur);
    this._animateSyncCallback((data) => {
      if (data.body === "OK") {
        this.maxIndex = 0;
        return;
      }
      let recvData = JSON.parse(data.body);
      if (recvData.index > this.maxIndex) {
        this.anime.seek(recvData.time);
        this.maxIndex = recvData.index;
      }
      if (recvData.finish) {
        this.maxIndex = 0;
        this._stopAnimate();
        this._drawLinks(this.topoData.links);
        this._drawNodes(this.topoData.nodes).then(() => {
          if (JSON.stringify(this.mapData) !== "{}") {
            this._update(this.mapData);
          }
        });
      }
    });
  }

  /**
   * 定义svg的defs
   */
  _generateDefines() {
    let defs = this.mainSVG.append("defs");
    d3.text(`${this._foldPath}/defs.html`).then((data) => {
      defs.html(data);
    });
  }

  /**
   * 事件同步入口
   * @param {json} data 同步jsonString
   */
  _eventSyncProcess(data = {}) {
    // let msg = JSON.parse(data);
    // switch (msg.functionName) {
    //   case 'prevMap':
    //     this._prevMap();
    //     break;
    //   case 'drawTopoByMapId':
    //     this._drawTopoByMapId(msg.params);
    //     break;
    //   default:
    //     break;
    // }
  }

  async _prevMap() {
    this.mapIdList.pop();
    let mapId = this.mapIdList.reduceRight(
      (prev, cur) => [...prev, cur],
      []
    )[0];
    this.mapIdList.pop();
    await this._drawTopoByMapId(mapId);
  }

  /**
   * @description 获取拓扑图所有节点数据入口
   */
  async _getTopo() {
    await this._getMapList();
    let mapId = this.property.viewSetting.loadMapId;
    this.mapExternalBind = this.mapList.filter(
      (d) => d.mapId === mapId
    )[0].externalBind;
    this.mapInternalBind = this.mapList.filter(
      (d) => d.mapId === mapId
    )[0].internalBind;
    await this._drawTopoByMapId(mapId);
  }

  /**
   * 根据图层id绘制图层
   * @param {string} mapId 图层id
   */
  async _drawTopoByMapId(mapId) {
    if (mapId === "") return;
    await this._getSubLayerList(mapId);
    await this._getNodeLinkList(mapId).then((data) => (this.topoData = data));
    await this._getNodeLinkList(mapId, "M4WacTIW5E").then(
      (data) => (this.area220NameNode = data)
    );
    await this._getHotspotsList(mapId);
    await this._loadTransformerSubstationSvgData();
    await this._drawTopo(mapId);

    this._bindZoom();
    // this._getData(mapId);
  }

  /**
   * 获取所有图层列表
   * @returns Promise
   */
  _getMapList() {
    return new Promise((resolve, reject) => {
      $.ajax(
        `${this.property.viewSetting.ip}:${this.property.viewSetting.port}/topoEdit/getMapList`,
        {
          type: "get",
          contentType: "application/json",
          success: ({ data }) => {
            this.mapList = data;
            resolve();
          },
          error: () => {
            reject();
          },
        }
      );
    });
  }

  /**
   * 获取拓扑图的子图层列表
   * @param {string} mapId 图ID
   * @returns Promise
   */
  _getSubLayerList(mapId) {
    return new Promise((resolve, rejcet) => {
      $.ajax(
        `${this.property.viewSetting.ip}:${this.property.viewSetting.port}/topoEdit/getExistSublayerList`,
        {
          type: "get",
          contentType: "application/json",
          data: {
            mapId: mapId,
          },
          success: ({ data }) => {
            this.subLayerList = data;
            resolve();
          },
          error: () => {
            reject();
          },
        }
      );
    });
  }

  /**
   * @description 获取拓扑图所有节点数据
   */
  _getNodeLinkList(mapId, sublayerId = null) {
    let param =
      sublayerId === null
        ? {
            mapId: mapId,
          }
        : {
            mapId: mapId,
            sublayerId: sublayerId,
          };
    return new Promise((resolve, reject) => {
      $.ajax(
        `${this.property.viewSetting.ip}:${this.property.viewSetting.port}/topoEdit/getNodeLinkListByMapId`,
        {
          type: "get",
          contentType: "application/json",
          data: param,
          success: ({ data }) => {
            resolve(data);
          },
          error: () => {
            reject();
          },
        }
      );
    });
  }

  /**
   * 获取图层热点区域
   */
  _getHotspotsList(mapId) {
    return new Promise((resolve, reject) => {
      $.ajax(
        `${this.property.viewSetting.ip}:${this.property.viewSetting.port}/topoEdit/getHotspotsList`,
        {
          type: "get",
          contentType: "application/json",
          data: {
            mapId: mapId,
          },
          success: ({ data }) => {
            this.hotspotsList = data;
            this.invokeFunc.filter(
              (d) => d.name === "flyToHotspots"
            )[0].params[0].options = this.hotspotsList.map(function (d) {
              return {
                name: d.hotspotName,
                value: d.hotspotId,
              };
            });
            resolve();
          },
          error: () => {
            reject();
          },
        }
      );
    });
  }

  /**
   * 绘制拓扑图
   * @returns
   */
  _drawTopo(mapId) {
    return new Promise((resolve, reject) => {
      if (this.topoData === {}) reject();
      this.mapIdList.push(mapId);
      let topoContainer = this.mainSVG
        .select(".nari_current_container")
        .attr("id", `topo_${mapId}`);
      if ($(topoContainer.node()).children().length !== 0) {
        this.mainSVG.select(".topo_last_container").remove();
        this.mainSVG
          .select(".nari_current_container")
          .attr("class", "topo_temp_container")
          .transition()
          .duration(1000)
          .style("opacity", 0)
          .style("transform", "scale(0)")
          .on("end", () => {
            this.mainSVG.select(".topo_temp_container").remove();
          });
        topoContainer = this.mainSVG
          .append("g")
          .attr("class", "nari_current_container")
          .attr("id", `topo_${mapId}`)
          .style("opacity", 0)
          .style("transform", "scale(0)");
        topoContainer
          .transition()
          .duration(1000)
          .style("opacity", 1)
          .style("transform", "scale(1)");
      }
      let tw = this.mapList
        .filter((d) => d.mapId === mapId)[0]
        .mapSize.split("*")[0];
      let th = this.mapList
        .filter((d) => d.mapId === mapId)[0]
        .mapSize.split("*")[1];
      this.mainSVG.attr("viewBox", [0, 0, tw, th]);
      this.mainSVG.attr(
        "viewBox",
        `0 0 ${this.property.viewSetting.w} ${this.property.viewSetting.h}`
      );
      // this.mainSVG.attr('viewBox', `0 0 19600 12600`);
      topoContainer.attr("data-vb", `0 0 ${tw} ${th}`);

      this.blockCon = topoContainer.append("g").attr("id", "block-container");
      this.linkCon = topoContainer.append("g").attr("id", "link-container");
      this.arrowCon = topoContainer.append("g").attr("id", "arrow-container");
      this.nodeCon = topoContainer.append("g").attr("id", "node-container");
      this._drawLinks(this.topoData.links);
      this._drawNodes(this.topoData.nodes);
      resolve();
    });
  }

  /**
   * 通过子图层绘制拓扑图
   */
  _drawTopoBySublayer() {}

  /**
   * 通过子图层移除拓扑图
   */
  _removeTopoBySublayer() {}

  /**
   * 检查节点是否全部在容器外，在返回true,否则返回false
   */
  _checkNodeOutContainer(p, s) {
    p = p.split(",").map((d) => Number(d));
    s = s.split("*").map((d) => Number(d));
    let cp = this.containerPosition;
    let ct = this.transform;
    let cr = this.mainSVG
      .select(".nari_current_container")
      .node()
      .getBoundingClientRect();
    let x1 = cp.x;
    let x2 = cp.x + cp.width;
    let y1 = cp.y;
    let y2 = cp.y + cp.height;
    //生成节点的4个端点坐标
    let points = [
      [ct.x + ct.k * p[0], ct.y + ct.k * p[1]],
      [ct.x + ct.k * p[0] + ct.k * s[0], ct.y + ct.k * p[1]],
      [ct.x + ct.k * p[0], ct.y + ct.k * p[1] + ct.k * s[1]],
      [ct.x + ct.k * p[0] + ct.k * s[0], ct.y + ct.k * p[1] + ct.k * s[1]],
    ];
    //如果至少一个端点在视图范围内则判断节点在容器内  返回false
    for (let i = 0; i < 4; i++) {
      if (
        points[i][0] >= x1 &&
        points[i][0] <= x2 &&
        points[i][1] >= y1 &&
        points[i][1] <= y2
      ) {
        return false;
      }
    }
    return true;
  }

  /**
   * 检查链路是否完全在容器外，超出返回true,否则返回false
   */
  _checkLinkOutContainer(d) {
    let cp = this.containerPosition;
    let ct = this.transform;
    let cr = this.mainSVG
      .select(".nari_current_container")
      .node()
      .getBoundingClientRect();
    let x1 = cp.x;
    let x2 = cp.x + cp.width;
    let y1 = cp.y;
    let y2 = cp.y + cp.height;
    let points = d
      .split("M")[1]
      .split("L")
      .map((d) => d.trim().split(" "));
    for (let i = 0; i < points.length; i++) {
      if (
        points[i][0] * ct.k + ct.x >= x1 &&
        points[i][0] * ct.k + ct.x <= x2 &&
        points[i][1] * ct.k + ct.y >= y1 &&
        points[i][1] * ct.k + ct.y <= y2
      ) {
        return false;
      }
    }
    return true;
  }

  /**
   * 绘制图层中所有链路
   * @param {Array} links 图层中链路数据
   */
  _drawLinks(links) {
    links.forEach((link) => {
      let style = JSON.parse(link.linkStyles);
      let linkDom = null;
      if (style.hasOwnProperty("isBlock")) {
        //绘制分区
        if (this.blockCon.select(`#topoLink_${link.linkId}`).node() !== null)
          return;
        linkDom = this.blockCon
          .append("path")
          .attr("id", "topoLink_" + link.linkId)
          .attr(
            "class",
            link.sublayerList
              ? link.sublayerList.map((d) => d.sublayerId).join(" ")
              : ""
          )
          .attr("d", link.linkPath)
          .style("stroke", "#414141")
          .style("stroke-width", "4pt")
          .style("fill", style.fill);
        if (link.bindMap.mapId) {
          this._bindLinkMap(linkDom, link);
        }
      } else {
        //绘制链路
        // let linkNode = this.linkCon.select(`#topoLink_${link.linkId.replace('#', '_')}`);
        //如果链路所在子图层不在展示的子图层内且链路存在则删除链路
        if (
          link.sublayerList &&
          link.sublayerList
            .map((d) => d.sublayerId)
            .filter((d) => this.showSublayer.includes(d)).length === 0
        ) {
          if (
            this.linkCon
              .select(`#topoLink_${link.linkId.replace("#", "_")}`)
              .node() !== null
          ) {
            this.linkCon
              .select(`#topoLink_${link.linkId.replace("#", "_")}`)
              .remove();
            this.arrowCon
              .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
              .remove();
          }
          return;
        }
        //如果链路超出可视区域则判断链路是否存在，存在则删除
        if (this._checkLinkOutContainer(link.linkPath)) {
          if (
            this.linkCon
              .select(`#topoLink_${link.linkId.replace("#", "_")}`)
              .node() !== null
          ) {
            this.linkCon
              .select(`#topoLink_${link.linkId.replace("#", "_")}`)
              .remove();
            this.arrowCon
              .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
              .remove();
          }
          return;
        }
        //如果链路已存在则不重新绘制
        if (
          this.linkCon
            .select(`#topoLink_${link.linkId.replace("#", "_")}`)
            .node() !== null
        ) {
          return;
        }
        //默认链路宽度为6  特高压子图层链路宽度为8, 220KV链路子图层链路宽度为1.5
        let linkWidth = 6;
        if (link.sublayerList) {
          if (
            link.sublayerList.map((d) => d.sublayerId).includes("KoBiqOUwMo")
          ) {
            linkWidth = 8;
          } else if (
            link.sublayerList.map((d) => d.sublayerId).includes("KS3tPfwEKM")
          ) {
            linkWidth = 1.5;
          }
        }
        let linkNode = this.linkCon
          .append("path")
          .attr("id", "topoLink_" + link.linkId.replace("#", "_"))
          .attr(
            "class",
            link.sublayerList
              ? link.sublayerList.map((d) => d.sublayerId).join(" ")
              : "" + this._isLinkCrossArea(link.metaData)
              ? "topo_crossLink"
              : ""
          )
          .attr("d", link.linkPath)
          .style("stroke-width", linkWidth)
          .style(
            "stroke",
            style.volt === "500"
              ? "#e70742"
              : style.volt === "220"
              ? "#2cebff"
              : "#e800ff"
          )
          .style("fill", "none");
        this._isLinkCrossArea(link.metaData);
        // if (linkNode.node() !== null) {
        //如果子图层包含500kv链路且不包含220kv的链路时表示在500kv全景图状态，此时展示箭头
        if (
          link.sublayerList &&
          link.sublayerList.map((d) => d.sublayerId).includes("KS5tPfwEKN") &&
          !link.sublayerList.map((d) => d.sublayerId).includes("KS3tPfwEKM")
        ) {
          if (
            this.arrowCon
              .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
              .node() === null
          ) {
            this._drawExternalCompOnLink(link, linkNode);
          }
        } else {
          if (
            this.arrowCon
              .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
              .node() !== null
          ) {
            this.arrowCon
              .select(`#topoArrow_${link.linkId.replace("#", "_")}`)
              .remove();
          }
        }
        // return;
        // }
      }
    });
  }

  /**
   * 添加显示子图层
   * @param {Array} arr 子图层id数组
   */
  _addShowSublayer(arr) {
    arr.forEach((item) => {
      if (this.showSublayer.filter((d) => d === item).length === 0) {
        this.showSublayer.push(item);
      }
    });
  }

  /**
   * 删除显示子图层
   * @param {Array} arr 子图层id数组
   */
  _removeShowSublayer(arr) {
    arr.forEach((item) => {
      this.showSublayer = this.showSublayer.filter((d) => d !== item);
    });
  }

  /**
   * 判断链路是否跨分区
   * @param {Object} metaData 链路MetaData对象
   * @returns 是否跨分区
   */
  _isLinkCrossArea(metaData) {
    if (!metaData.hasOwnProperty("areaKeyId")) return false;
    let areaList = metaData.areaKeyId.split(",");
    if (
      areaList.length >= 2 &&
      areaList.filter((d) => d === "0").length > 0 &&
      areaList[0] !== areaList[1]
    ) {
      return true;
    }
    return false;
  }

  /**
   * 绘制链路的外部对象
   * @param {Object} link 链路
   */
  _drawExternalCompOnLink(link, linkDom) {
    if (link.compClass && link.compClass !== "") {
      let externalComp = this.mapExternalBind[link.compClass];
      eval(
        `new ${externalComp.className}(link.linkId, 'test', this.arrowCon, 2, externalComp.options, link, linkDom )`
      );
    }
  }

  /**
   * 绘制图层中所有节点
   * @param {Array} nodes 图层中节点数据
   */
  _drawNodes(nodes) {
    return new Promise((resolve, reject) => {
      this.loadingCon.style("display", "block");
      let nodeIndex = 0;
      let drawNodeList = [];
      let drawedNode = [];
      for (let node of nodes) {
        if (
          this._needDrawNode(node) &&
          drawNodeList.filter((d) => d.nodeId === node.nodeId).length === 0
        ) {
          drawNodeList.push(node);
        }
      }
      console.log("start draw nodes");
      let inter = setInterval(() => {
        let loadNum = 10;
        if (nodeIndex >= drawNodeList.length) {
          clearInterval(inter);
          this.loadingCon.style("display", "none");
          console.log("finish draw nodes");
          resolve();
        }
        for (let i = 0; i < loadNum; i++) {
          if (nodeIndex + i >= drawNodeList.length) {
            clearInterval(inter);
            this.loadingCon.style("display", "none");
            console.log("finish draw nodes");
            resolve();
            break;
          }
          this._drawNode(drawNodeList[nodeIndex + i]);
          drawedNode.push(drawNodeList[nodeIndex + i].nodeId);
        }
        nodeIndex += loadNum;
      }, 0.01);
    });
  }

  /**
   * 判断是否需要绘制节点
   * @param {Object} node node对象
   * @returns true为绘制 false为不绘制
   */
  _needDrawNode(node) {
    //当节点的子图层列表与现在展示的子图层列表不匹配时  不展示节点
    if (
      node.sublayerList &&
      node.sublayerList
        .map((d) => d.sublayerId)
        .filter((d) => this.showSublayer.includes(d)).length === 0
    ) {
      //如果节点存在则删除节点
      if (
        this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).node() !==
        null
      ) {
        this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).remove();
        delete this.powerPlantMap[node.nodeId];
        delete this.transStationMap[node.nodeId];
        delete this.exchStationMap[node.nodeId];
        delete this.areaBoardMap[node.nodeId];
      }
      return false;
    }
    //如果节点在容器外 判断是否存在节点 如果存在则删除节点后绘制下一个节点
    if (this._checkNodeOutContainer(node.nodePosition, node.nodeSize)) {
      if (
        node.sublayerList &&
        node.sublayerList.map((d) => d.sublayerId).includes("M4WacTIW5E")
      ) {
        return (
          this.nodeCon
            .select(`#node_${node.nodeId.replace("#", "_")}`)
            .node() === null
        );
      }
      if (
        this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).node() !==
        null
      ) {
        this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).remove();
        delete this.powerPlantMap[node.nodeId];
        delete this.transStationMap[node.nodeId];
        delete this.exchStationMap[node.nodeId];
        delete this.areaBoardMap[node.nodeId];
      }
      return false;
    }
    //如果需要绘制节点且节点已存在 则不创建新节点但是同步状态
    if (
      this.nodeCon.select(`#node_${node.nodeId.replace("#", "_")}`).node() !==
      null
    ) {
      if (this.powerPlantMap[node.nodeId])
        this.powerPlantMap[node.nodeId].setType(this.nodeType);
      if (this.transStationMap[node.nodeId])
        this.transStationMap[node.nodeId].setType(this.nodeType);
      if (this.exchStationMap[node.nodeId])
        this.exchStationMap[node.nodeId].setType(this.nodeType);
      return false;
    }
    return true;
  }

  _drawNode(node) {
    let nodeDom = null;
    switch (node.nodeType) {
      case "circle":
        nodeDom = this._drawCircleNode(this.nodeCon, node);
        break;
      case "rect":
        nodeDom = this._drawRectNode(this.nodeCon, node);
        break;
      case "text":
        nodeDom = this._drawTextNode(this.nodeCon, node);
        break;
      case "image":
        nodeDom = this._drawImageNode(this.nodeCon, node);
        break;
      default:
        if (
          node.metaData &&
          (node.metaData.type === "TransStation" ||
            node.metaData.type === "PowerStation" ||
            node.metaData.type === "ExchStation")
        ) {
          nodeDom = this._drawTransformerSubstation(this.nodeCon, node);
        } else {
          nodeDom = this._drawComponentNode(this.nodeCon, node);
        }
        break;
    }
    if (nodeDom !== null) {
      this._bindLinkMap(nodeDom, node);
      if (node.nodeText !== "" && node.nodeType !== "text") {
        // this._drawNodeText(this.nodeCon, node);
      }
    }
  }

  /**
   * 通过node对象获取节点位置以及宽高
   * @param {Object} node 节点对象
   * @returns {x,y,w,h} 节点位置以及宽高
   */
  _getPositionAndSize(node) {
    return {
      x: Number(node.nodePosition.split(",")[0]),
      y: Number(node.nodePosition.split(",")[1]),
      w: Number(node.nodeSize.split("*")[0]),
      h: Number(node.nodeSize.split("*")[1]),
    };
  }

  /**
   * 绘制矩形节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawRectNode(con, node) {
    let { x, y, w, h } = this._getPositionAndSize(node);
    let style = JSON.parse(node.nodeStyles);
    let nodeDom = con
      .append("rect")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("x", x)
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .attr("fill", style.fill);
    return nodeDom;
  }

  /**
   * 绘制椭圆节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawCircleNode(con, node) {
    let { x, y, w, h } = this._getPositionAndSize(node);
    let style = JSON.parse(node.nodeStyles);
    let nodeDom = con
      .append("ellipse")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("cx", x + w / 2)
      .attr("cy", y + h / 2)
      .attr("rx", w / 2)
      .attr("ry", h / 2)
      .attr("stroke", style.stroke)
      .attr("stroke-width", style.strokeWidth)
      .attr("fill", style.fill);
    return nodeDom;
  }

  /**
   * 绘制节点文字
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawNodeText(con, node) {
    let { x, y } = this._getPositionAndSize(node);
    let textPosition = node.textPosition.split(",").map((d) => (d = Number(d)));
    let textStyle = JSON.parse(node.textStyles);
    con
      .append("text")
      .attr("x", textPosition[0] + x)
      .attr("y", textPosition[1] + y)
      .attr("rotate", textStyle.duration === "竖向" ? -90 : 0)
      .attr(
        "transform",
        textStyle.duration === "竖向"
          ? `rotate(90 ${x + node.fontSize / 2} ${y + node.fontSize / 2})`
          : ""
      )
      .style("font-size", node.fontSize)
      .style("fill", node.fontColor)
      .style("letter-space", "2px")
      .style("user-select", "none")
      .text(node.nodeText);
  }

  /**
   * 绘制文字节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawTextNode(con, node) {
    let { x, y, w, h } = this._getPositionAndSize(node);
    let style = JSON.parse(node.nodeStyles);
    let textStyle = JSON.parse(node.textStyles);
    let fontColor = node.fontColor;
    if (
      node.sublayerList &&
      node.sublayerList.map((d) => d.sublayerId).includes("KS5TXwwEDE")
    ) {
      fontColor = "url(#text_500KV_trans_name)";
    }
    let nodeDom = con
      .append("text")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr(
        "class",
        node.sublayerList
          ? node.sublayerList.map((d) => d.sublayerId).join(" ")
          : ""
      )
      .attr(
        "x",
        textStyle.align === "left"
          ? x
          : textStyle.align === "center"
          ? x + w / 2
          : x + w
      )
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .style("fill", fontColor)
      .style("user-select", "none")
      .style("font-size", node.fontSize)
      // .style('letter-spacing', '2px')
      .style("dominant-baseline", "hanging")
      .style(
        "text-anchor",
        textStyle.align === "left"
          ? "start"
          : textStyle.align === "center"
          ? "middle"
          : "end"
      )
      .attr(
        "transform",
        node.rotate === 0
          ? ""
          : `rotate(${node.rotate} ${x + w / 2} ${y + h / 2})`
      )
      .text(node.nodeText === "0000" ? "0" : node.nodeText);
    if (textStyle.duration === "竖向") {
      nodeDom
        .style("text-anchor", "start")
        .attr("rotate", "-90")
        .attr("transform", `rotate(90 ${x} ${y})`)
        .style("dominant-baseline", "");
    }
    return nodeDom;
  }

  /**
   * 绘制图片节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawImageNode(con, node) {
    let x = Number(node.nodePosition.split(",")[0]);
    let y = Number(node.nodePosition.split(",")[1]);
    let w = Number(node.nodeSize.split("*")[0]);
    let h = Number(node.nodeSize.split("*")[1]);
    let style = JSON.parse(node.nodeStyles);
    let nodeDom = con
      .append("image")
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .attr("x", x)
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .attr(
        "xlink:href",
        this.property.viewSetting.ftpIp +
          ":" +
          this.property.viewSetting.ftpPort +
          style.image
      );
    return nodeDom;
  }

  /**
   * 绘制组件节点
   * @param {Object} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawComponentNode(con, node) {
    if (node.compClass === "" || node.compClass === undefined) return;
    let x = Number(node.nodePosition.split(",")[0]);
    let y = Number(node.nodePosition.split(",")[1]);
    let w = Number(node.nodeSize.split("*")[0]);
    let h = Number(node.nodeSize.split("*")[1]);
    let internalComp = this.mapInternalBind[node.compClass];
    if (internalComp) {
      let container = con
        .append("foreignObject")
        .attr("id", `node_${node.nodeId}`)
        .attr(
          "class",
          node.sublayerList
            ? node.sublayerList.map((d) => d.sublayerId).join(" ")
            : ""
        )
        .attr("x", x)
        .attr("y", y)
        .attr("width", w)
        .attr("height", h)
        .append("xhtml:div")
        .node();
      internalComp.options["basic"] = {
        frame: [0, 0, w, h],
      };
      let opt = $.extend(
        true,
        internalComp.options,
        JSON.parse(node.nodeStyles).hasOwnProperty("customStyles")
          ? JSON.parse(node.nodeStyles).customStyles
          : {}
      );
      let comp = eval(
        `new ${internalComp.className}(node.nodeId, 'test', container, 2, {'property': opt, 'metaData': node.metaData})`
      );
      if (internalComp.className === "AreaBoard") {
        this.areaBoardMap[node.nodeId] = comp;
      }
      return comp.mainSVG;
    }
  }

  /**
   * 加载电厂svg数据
   */
  _loadTransformerSubstationSvgData() {
    return new Promise((resolve, reject) => {
      d3.json(`${this._foldPath}/svgData.json`).then((data) => {
        this.svgData = data;
        resolve();
      });
    });
  }

  /**
   * 绘制电厂节点
   * @param {Ibject} con 图层容器
   * @param {Object} node 节点数据
   */
  _drawTransformerSubstation(con, node) {
    let x = Number(node.nodePosition.split(",")[0]);
    let y = Number(node.nodePosition.split(",")[1]);
    let w = Number(node.nodeSize.split("*")[0]);
    let h = Number(node.nodeSize.split("*")[1]);
    let container = con
      .append("svg")
      .attr(
        "class",
        node.sublayerList
          ? node.sublayerList.map((d) => d.sublayerId).join(" ")
          : ""
      )
      .attr("x", x)
      .attr("y", y)
      .attr("width", w)
      .attr("height", h)
      .attr("id", `node_${node.nodeId.replace("#", "_")}`)
      .style("transform", "translateZ(0)")
      .style("transform-origin", "center center");
    if (node.metaData.type === "TransStation") {
      container.attr("viewBox", "0 0 246 246");
      let pp = new TransformerSubstation(node.nodeId, "test", container, 2, {
        x: x,
        y: y,
        w: w,
        h: h,
        type: this.nodeType,
        data: node.metaData,
        svgData: this.svgData,
      });
      this.transStationMap[node.nodeId] = pp;
    } else if (node.metaData.type === "PowerStation") {
      container.attr("viewBox", "0 0 209 209");
      let pp = new PowerPlant(node.nodeId, "test", container, 2, {
        x: x,
        y: y,
        w: w,
        h: h,
        type: this.nodeType,
        data: node.metaData,
      });
      this.powerPlantMap[node.nodeId] = pp;
    } else {
      container.attr("viewBox", "0 0 246 246");
      let pp = new ExchStation(node.nodeId, "test", container, 2, {
        x: x,
        y: y,
        w: w,
        h: h,
        type: this.nodeType,
        data: node.metaData,
      });
      this.exchStationMap[node.nodeId] = pp;
    }
  }

  /**
   *
   * @param {Object} nodeDom 节点容器
   * @param {Object} node 节点数据
   */
  _bindLinkMap(nodeDom, node) {
    if (nodeDom === undefined) return;
    nodeDom.on("click", () => {
      if (node.bindMap.mapId) {
        // this._sendMessageByWS(
        //   JSON.stringify({
        //     functionName: 'drawTopoByMapId',
        //     params: node.bindMap.mapId[0],
        //   })
        // );
        this._drawTopoByMapId(node.bindMap.mapId[0]);
      }
    });
  }

  /**
   * 更新拓扑图数据
   */
  _update(data) {
    if (this.nodeType === "m" || this.nodeType === "l") {
      this.topoData.nodes.forEach((node) => {
        if (node.metaData) {
          let nodeMeta = node.metaData;
          switch (nodeMeta.type) {
            case "DText":
              this._updateText(node.nodeId, data[node.nodeId]);
              break;
            case "TransStation":
              if (this.transStationMap.hasOwnProperty(node.nodeId)) {
                this.transStationMap[node.nodeId].update(data[node.nodeId]);
              }
              break;
            case "PowerStation":
              // 非火电厂不更新数据
              if (
                !this.powerPlantMap.hasOwnProperty(node.nodeId) ||
                nodeMeta.num === 0 ||
                nodeMeta.powerType !== "THERMAL"
              )
                break;
              this.powerPlantMap[node.nodeId].update(data[node.nodeId]);
              break;
            case "AreaBoard":
              if (!this.showSublayer.includes("KS5TXwwEDE")) break;
              this.areaBoardMap[node.nodeId].update(data[node.nodeId]);
              break;
            default:
              break;
          }
        }
      });
    }
    this.topoData.links.forEach((link) => {
      if (link.metaData) {
        let nodeMeta = link.metaData;
        switch (nodeMeta.type) {
          case "AcLine":
            this._updateAcLine(link.linkId, data[link.linkId]);
            break;
          default:
            break;
        }
      }
    });
  }

  /**
   * 更新文字组件
   */
  _updateText(id, data) {
    this.nodeCon
      .select(`#node_${id.replace("#", "_")}`)
      .classed("topo-flash", data.flash === "1")
      .text(data.value);
  }

  /**
   * 更新链路运动方向
   */
  _updateAcLine(nodeId, data) {
    //data:{ "isDivide": "0","direction": "1","flash": "0","status": "0"}
    //direction字典: -1 反向 1 正向 0无箭头
    //status字典: -1 灰 0 蓝 1红 2 紫 3 黄 4 橙 5 绿 6 虚
    let lineColorDic = {
      "-1": "#e800ff",
      0: "#2cebff",
      1: "#e70742",
      2: "#cd2cff",
      3: "#e4ff00",
      4: "#ff7800",
      5: "#00ff8c",
      6: "#00b4ff",
    };

    this.arrowCon
      .select(`#topoArrow_${nodeId.replace("#", "_")}`)
      .style("display", data.direction === "0" ? "none" : "block")
      .style(
        "animation-direction",
        data.direction === "1" ? "normal" : "reverse"
      )
      .style("transform", `rotate(${data.direction === "1" ? 0 : 180}deg)`);

    this.linkCon
      .select(`#topoLink_${nodeId.replace("#", "_")}`)
      .classed("topo-isDivide", data.isDivide === "1")
      .classed("topo-flash", data.flash === "1")
      .attr("stroke-dasharray", data.status === "6" ? "20 20" : "none")
      .style("stroke", lineColorDic[data.status]);
  }

  /**
   * 接收订阅数据
   */
  _getData(mapId) {
    new Promise((resolve, reject) => {
      $.ajax(`${this.property.viewSetting.dataUrl}`, {
        type: "get",
        contentType: "application/json",
        // data: {
        //   mapId: mapId,
        // },
        success: ({ data }) => {
          this.mapData = data;
          this._update(data);
          resolve();
        },
        error: () => {
          reject();
        },
      });
    });
  }
}

/**
 * 链路箭头
 */
class NariTopoArrow {
  constructor(id, code, con, workMode, option, link, linkDom) {
    this.id = id;
    this.con = con;
    this.option = option;
    this.link = link;
    this.linkDom = linkDom;
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this._draw();
  }

  _draw() {
    let w = this.option.width;
    let h = this.option.height;
    let arrowArr = [
      [10, 0],
      [5, -4],
      [5, -2],
      [0, -2],
      [0, 2],
      [5, 2],
      [5, 4],
    ];
    arrowArr.forEach((d) => {
      d[0] = (d[0] * w) / 10;
      d[1] = (d[1] * h) / 10;
      d = d.join(",");
    });
    let pathLength = this.linkDom.node().getTotalLength();
    this.con
      .append("polygon")
      .attr("id", `topoArrow_${this.id.replace("#", "_")}`)
      .attr("points", arrowArr.join(" "))
      .style("display", "none")
      // .style('fill', 'url(#arrow-fill-white)')
      .style("transform", "translateZ(0)")
      .style("fill", "#ff1800")
      .style("filter", "url(#arrow_shadow)")
      .style("offset-path", `path("${this.link.linkPath}")`)
      .style(
        "animation",
        `arrowMove ${(pathLength / 100) * 1}s linear infinite`
      );
    // w *= 4;
    // h *= 4;
    // this.con
    //   .append('image')
    //   .attr('id', `topoArrow_${this.id.replace('#', '_')}`)
    //   .attr('class', this.link.sublayerList ? this.link.sublayerList.map((d) => d.sublayerId).join(' ') : '')
    //   .attr('width', w)
    //   .attr('height', h)
    //   .attr('x', -w / 2)
    //   .attr('y', -h / 2)
    //   .attr('xlink:href', `${this._foldPath}/images/point.svg`)
    //   .style('offset-path', `path("${this.link.linkPath}")`)
    //   .style('animation', `arrowMove ${(pathLength / 100) * 1}s linear infinite`);
  }
}

/**
 * 变电站节点
 */
class TransformerSubstation {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this._draw();
  }

  _draw() {
    // if (this.option.data.volt === '_500KV') {
    //   this.con.attr('x', this.option.x - this.option.w / 2);
    //   this.con.attr('y', this.option.y - this.option.h / 2);
    //   this.con.attr('width', this.option.w * 2);
    //   this.con.attr('height', this.option.h * 2);
    // }

    this._getSwitchIndex();
    this._setSize(
      this.option.type === "l"
        ? 148
        : this.option.data.volt !== "_220KV"
        ? 112
        : 31
    );
    if (
      (this.option.type === "p" || this.option.type === "s") &&
      this.option.data.volt === "_500KV"
    ) {
      this.con
        .append("image")
        .attr("width", 246)
        .attr("height", 246)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/transformerSub_${this.option.type}_500_${
            Number(this.option.data["xfmr_num"]) >= 3
              ? 3
              : this.option.data["xfmr_num"]
          }.svg`
        );
      return;
    }
    if (
      (this.option.type === "p" || this.option.type === "s") &&
      this.option.data.volt === "_1000KV"
    ) {
      this.con
        .append("image")
        .attr("width", 246)
        .attr("height", 246)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/transformerSub_${this.option.type}_1000_${
            Number(this.option.data["xfmr_num"]) >= 3
              ? 3
              : this.option.data["xfmr_num"]
          }.svg`
        );
      return;
    }
    if (
      (this.option.type === "p" || this.option.type === "s") &&
      this.option.data.volt === "_220KV"
    ) {
      this.con
        .append("image")
        .attr("width", 246)
        .attr("height", 246)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/transformerSub_${this.option.type}_220.svg`
        );
      return;
    }
    this.defs = this.con.append("defs");
    this.con
      .append("circle")
      .attr("cx", 123)
      .attr("cy", 123)
      .attr("r", this.option.data["highbus_num"] === "0" ? 105 : 123)
      .style("fill", "#231815");
    if (
      Number(this.option.data["switch_num"]) >
      Number(this.option.data["lowbus_num"])
    ) {
      this.option.data["switch_num"] = this.option.data["lowbus_num"];
    }
    if (this.option.data["xfmr_num"] !== "0") {
      this._drawXfmr(this.option.data["xfmr_num"]);
    }
    this._drawLowBus(
      this.option.data["lowbus_num"],
      this.option.data["switch_num"]
    );
    if (this.option.data["switch_num"] !== "0") {
      this._drawSwitch(
        this.option.data["lowbus_num"],
        this.option.data["switch_num"]
      );
    }
    this._drawHighBus(this.option.data["highbus_num"]);
  }

  _setSize(l) {
    this.con
      .attr("x", this.option.x - (l - this.option.w) / 2)
      .attr("y", this.option.y - (l - this.option.h) / 2)
      .attr("width", l)
      .attr("height", l);
  }

  setType(type) {
    if (this.option.type !== type) {
      this.option.type = type;
      $(this.con.node()).empty();
      this._draw();
    }
  }

  _getSwitchIndex() {
    let switchNo = this.option.data["switchNo"].split(",");
    let switchArray = switchNo.map((d) => d.split("_"));
    this.switchIndex = [];
    switch (this.option.data["switch_num"]) {
      case "4":
        this.switchIndex = switchArray.map((d) => {
          if (d[1] === "3" && d[2] === "4") {
            return 1;
          } else if (d[1] === "1" && d[2] === "3") {
            return 2;
          } else if (d[1] === "2" && d[2] === "4") {
            return 3;
          } else if (d[1] === "1" && d[2] === "2") {
            return 4;
          }
        });
    }
  }

  _drawXfmr(num) {
    if (!this.option.svgData.xfmr.hasOwnProperty(num)) {
      console.warn(`无主变数量为${num}的svg数据`);
      return;
    }
    let g = this.con.append("g").attr("class", "xfmr");
    if (this.option.data.volt === "_220KV" || this.option.type === "m") {
      this.option.svgData.xfmr[num].forEach((d) => {
        let con = g.append("g");
        con
          .append("path")
          .attr("class", "topo_xfmr")
          .attr("d", d)
          .style("filter", "url(#shadow-xfmr)")
          .style("fill", "#0088ff");
        con
          .append("path")
          .attr("class", "topo_xfmr_inset")
          .attr("d", d)
          .style("filter", "url(#inset-shadow-blue)")
          .style("fill", "#0f4372")
          .style("stroke-width", 1)
          .style("stroke", "#2cebff");
      });
    } else {
      // g.append('path').attr('d', 'M 33 123 a 90 90 0 1 0 180 0 a 90 90 0 1 0 -180 0 z').style('filter', 'url(#center-bg-inset)');
      g.append("path")
        .attr("class", "xfmr-rate")
        .attr("d", this._genXfmrRate(0))
        .style("fill", "#00fe42")
        .style("filter", "url(#inset-water-0)");
      //绘制文字
      this.text = g
        .append("text")
        .attr("class", "topo-centerText")
        .text("0000");
    }
  }

  _genXfmrRate(k) {
    if (k >= 1) return "M 33 123 a 90 90 0 1 0 180 0 a 90 90 0 1 0 -180 0 z";
    let cx = 123;
    let cy = 123;
    let r = 90;
    let x0 = cx - 2 * r * Math.sqrt(k - k * k);
    let y0 = cy + (r - 2 * k * r);
    let x1 = cx + 2 * r * Math.sqrt(k - k * k);
    let y1 = cy + (r - 2 * k * r);
    return `M ${x0} ${y0} A ${r} ${r} 0 ${k < 0.5 ? 0 : 1} 0 ${x1} ${y1} Z`;
  }

  _drawLowBus(lowNum, switchNum) {
    if (!this.option.svgData.lowbus.hasOwnProperty(`${lowNum}-${switchNum}`)) {
      console.warn(`无低压数量为${lowNum}且开关数量为${switchNum}的svg数据`);
      return;
    }
    let g = this.con.append("g").attr("class", "lowbus");
    this.option.svgData.lowbus[`${lowNum}-${switchNum}`].forEach((d) => {
      let con = g.append("g");
      con
        .append("path")
        .attr("class", "TransformerSubstation")
        .attr("d", d)
        .style("filter", "url(#shadow-xfmr)")
        .style("fill", "#0088ff");
      con
        .append("path")
        .attr("class", "TransformerSubstation-inset")
        .attr("d", d)
        .style("filter", "url(#inset-shadow-blue)")
        .style("fill", "#2d99fc")
        .style("stroke-width", 1)
        .style("stroke", "#00e7ff");
    });
  }

  _drawSwitch(lowNum, switchNum) {
    if (!this.option.svgData.switch.hasOwnProperty(`${lowNum}-${switchNum}`)) {
      console.warn(`无低压数量为${lowNum}且开关数量为${switchNum}的svg数据`);
      return;
    }
    let g = this.con.append("g").attr("class", "switch");
    this.option.svgData.switch[`${lowNum}-${switchNum}`].forEach((d) => {
      let [x, y, w, h, rx, tf] = d.split(",");
      let con = g.append("g");
      con
        .append("rect")
        .attr("class", "TransformerSwitch")
        .attr("x", x)
        .attr("y", y)
        .attr("width", w)
        .attr("height", h)
        .attr("rx", rx)
        .attr("transform", tf === "null" ? "" : tf)
        .style("fill", "#0088ff");
    });
  }

  _drawHighBus(num) {
    if (num === "0") return;
    if (!this.option.svgData.highbus.hasOwnProperty(num)) {
      console.warn(`无高压数量为${num}的svg数据`);
      return;
    }
    let g = this.con.append("g").attr("class", "highbus");
    this.option.svgData.highbus[num].forEach((d) => {
      let con = g.append("g");
      con
        .append("path")
        .attr("class", "TransformerSubstation")
        .attr("d", d)
        .style("filter", "url(#shadow-xfmr)")
        .style("fill", "#ff0083");
      con
        .append("path")
        .attr("class", "TransformerSubstation-inset")
        .attr("d", d)
        .style("filter", "url(#inset-shadow-red)")
        .style("fill", "#fc0046")
        .style("stroke-width", 1)
        .style("stroke", "#e1b5bd");
    });
  }

  update(data) {
    let nodeMeta = this.option.data;
    let busColorDic = {
      0: "blue", //蓝色
      1: "red",
      2: "purple",
      3: "yellow",
      4: "orange",
      5: "green",
    };
    let xfmrColorDic = {
      "-1": { color: "gary", filter: "gary", insetColor: "", stroke: "" },
      0: {
        color: "#0088ff",
        filter: "blue",
        insetColor: "#0f4372",
        stroke: "#2cebff",
      },
      1: {
        color: "#ff2600",
        filter: "red",
        insetColor: "#72180f",
        stroke: "#ff3f2c",
      },
      2: {
        color: "#ff009c",
        filter: "purple",
        insetColor: "#720f4e",
        stroke: "#ff2cb5",
      },
      3: { color: "gary", filter: "yellow", insetColor: "", stroke: "" },
      4: { color: "gary", filter: "orange", insetColor: "", stroke: "" },
      5: { color: "gary", filter: "green", insetColor: "", stroke: "" },
    };
    let waterColorDic = {
      0: "#226d00", //绿
      1: "#a2451f", //黄
      2: "#7f1b2c", //红
    };
    //更新主变数据
    if (
      nodeMeta.xfmr_num !== "0" &&
      nodeMeta.volt !== "_220KV" &&
      this.option.type === "l"
    ) {
      this.con
        .select(".xfmr")
        .select(".xfmr-rate")
        .style("fill", waterColorDic[data.status])
        .style("filter", `url(#inset-water-${data.status})`)
        .attr("d", this._genXfmrRate(data.rate));
      this.text.text(data.value);
    } else {
      [...this.con.select(".xfmr").selectAll("g").nodes()].forEach((p, i) => {
        let color = xfmrColorDic[data.xfmr[i].status];
        d3.select(p).selectAll(".topo_xfmr").style("fill", color.color);
        d3.select(p)
          .selectAll(".topo_xfmr_inset")
          .style(
            "filter",
            `url(#inset-shadow-${xfmrColorDic[data.xfmr[i].status].filter})`
          )
          .style("fill", color.insetColor)
          .style("stroke", color.stroke);
      });
    }
    //更新高压母线数据
    if (nodeMeta.lowBusNo !== "") {
      [...this.con.select(".lowbus").selectAll("g").nodes()].forEach((p, i) => {
        d3.select(p)
          .selectAll(".TransformerSubstation-inset")
          .style(
            "filter",
            `url(#inset-shadow-${busColorDic[data.lowBus[i].status]})`
          );
      });
    }
    //更新低压母线数据
    if (nodeMeta.highBusNo !== "") {
      [...this.con.select(".highbus").selectAll("g").nodes()].forEach(
        (p, i) => {
          d3.select(p)
            .selectAll(".TransformerSubstation-inset")
            .style(
              "filter",
              `url(#inset-shadow-${busColorDic[data.highBus[i].status]})`
            );
        }
      );
    }
    //更新开关数据
    if (nodeMeta.switchNo !== "") {
      let switchArray = [...this.con.select(".switch").selectAll("g").nodes()];
      this.switchIndex.forEach((d, i) => {
        d3.select(switchArray[d - 1])
          .selectAll(".TransformerSwitch")
          .style("fill", data.switch[i].status === "0" ? "#0088ff" : "#19eb42");
      });
    }
  }
}

/**
 * 电厂节点
 */
class PowerPlant {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this.data = [];
    this.option.data.no.split(",").forEach((d) => {
      this.data.push({
        mvarate: 100,
        value: 0,
      });
    });
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this.fontSizeDic = [55, 50, 45, 40, 35, 30, 25, 20];
    this._draw();
  }

  _draw() {
    this._setSize(
      this.option.type === "l"
        ? 118
        : this.option.data.volt !== "_220KV"
        ? 118
        : 31
    );
    if (
      (this.option.type === "p" || this.option.type === "s") &&
      this.option.data.powerType === "THERMAL"
    ) {
      this.con
        .append("image")
        .attr("width", 209)
        .attr("height", 209)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/powerPlant_${this.option.type}${this.option.data.volt}.svg`
        );
      return;
    }
    if (this.option.type === "p" || this.option.type === "s") {
      this.con
        .append("image")
        .attr("width", 209)
        .attr("height", 209)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/powerPlant_${this.option.type}_${this.option.data.powerType}.svg`
        );
      return;
    }
    if (this.option.data.powerType !== "THERMAL") {
      this.con
        .append("image")
        .attr("width", 209)
        .attr("height", 209)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/powerPlant_${this.option.data.powerType}.svg`
        );
      return;
    }
    this.con
      .append("image")
      .attr("width", 209)
      .attr("height", 209)
      .attr(
        "xlink:href",
        `${this._foldPath}/images/powerPlant${this.option.data.volt}.svg`
      );
    if (this.option.type === "s" || this.option.data.powerType !== "THERMAL") {
      return;
    }

    this.drawRect = {
      x: 15,
      y: 17,
      width: 176,
      height: 176,
    };

    this.mainG = this.con
      .append("g")
      .attr("class", "container")
      .attr("transform", `translate(${this.drawRect.x}, ${this.drawRect.y})`);
    this.barNum = Number(this.option.data.num);
    this._genRange();
    this._drawBar();
    this._drawName();
    this._drawValue();
  }

  _setSize(l) {
    this.con
      .attr("x", this.option.x - (l - this.option.w) / 2)
      .attr("y", this.option.y - (l - this.option.h) / 2)
      .attr("width", l)
      .attr("height", l);
  }

  setType(type) {
    if (this.option.type !== type) {
      this.option.type = type;
      $(this.con.node()).empty();
      this._draw();
    }
  }

  _genRange() {
    this.xRange = [];
    this.data.forEach((d) => {
      this.xRange.push(
        d3
          .scaleLinear()
          .domain([0, Number(d.mvarate)])
          .range([this.drawRect.x, this.drawRect.width])
      );
    });
    this.yRange = d3
      .scaleBand()
      .domain(this.data.map((d, i) => i))
      .range([this.drawRect.y, this.drawRect.y + this.drawRect.height]);
  }

  _drawBar() {
    let barG = this.mainG.append("g").attr("class", "powerplant_bar");
    barG
      .selectAll(".rect")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("rect")
            .attr("class", "rect")
            .attr("x", 0)
            .attr("y", (d, i) => this.yRange.bandwidth() * i)
            .attr("width", (d, i) => this.xRange[i](d.value))
            .attr("height", this.yRange.bandwidth())
            .style("fill", `url(#powerplant-bar${this.option.data.volt})`),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .attr("y", (d, i) => this.yRange.bandwidth() * i)
            .attr("height", this.yRange.bandwidth())
            .attr("width", (d, i) => this.xRange[i](d.value)),
        (exit) => exit.remove()
      );
  }

  _drawName() {
    let nameG = this.mainG.append("g").attr("class", "powerplant_name");
    nameG
      .selectAll(".name")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("text")
            .attr("class", "name")
            .attr("x", 20)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .style("fill", "#fff")
            .style("font-size", `${60 - this.data.length * 5}px`)
            .style("dominant-baseline", "central")
            .text((d, i) => `#${i}`),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .style("font-size", `${60 - this.data.length * 5}px`)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .attr("width", (d, i) => this.xRange[i](d.value))
            .text((d, i) => `#${i}`),
        (exit) => exit.remove()
      );
  }

  _drawValue() {
    let valueG = this.mainG.append("g").attr("class", "powerplant_value");
    valueG
      .selectAll(".value")
      .data(this.data)
      .join(
        (enter) =>
          enter
            .append("text")
            .attr("class", "value")
            .attr("x", this.drawRect.width)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .style("fill", "#fff")
            .style("font-size", `${60 - this.data.length * 5}px`)
            .style("dominant-baseline", "central")
            .style("text-anchor", "end")
            .text((d) => `${Math.round(d.value)}`),
        (update) =>
          update
            .transition(d3.transition().duration(1000))
            .style("font-size", `${60 - this.data.length * 5}px`)
            .attr("y", (d, i) => this.yRange.bandwidth() * (i + 0.5))
            .text((d) => `${Math.round(d.value)}`),
        (exit) => exit.remove()
      );
  }

  update(data) {
    if (data === undefined) return;
    this.data = data.machineList;
    if (this.mainG === undefined) return;
    $(this.mainG.node()).empty();
    this._genRange();
    this._drawBar();
    this._drawName();
    this._drawValue();
  }
}

/**
 * 换流站组件
 */
class ExchStation {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this._draw();
  }

  _draw() {
    if (this.option.type === "p" || this.option.type === "s") {
      this.con
        .append("image")
        .attr("width", 246)
        .attr("height", 246)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/exchStation_${this.option.type}${this.option.data.volt}.svg`
        );
    } else {
      this.con
        .append("image")
        .attr("width", 246)
        .attr("height", 246)
        .attr(
          "xlink:href",
          `${this._foldPath}/images/exchStation_${this.option.type}.svg`
        );
    }
  }

  _setSize(l) {
    this.con
      .attr("x", this.option.x - (l - this.option.w) / 2)
      .attr("y", this.option.y - (l - this.option.h) / 2)
      .attr("width", l)
      .attr("height", l);
  }

  setType(type) {
    if (this.option.type !== type) {
      this.option.type = type;
      $(this.con.node()).empty();
      this._draw();
    }
  }
}

/**
 * 分区板组件
 */
class AreaBoard {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this._foldPath = WisUtil.scriptPath("NariTopoView");
    this._draw();
  }

  _draw() {
    this.nameStr = this.option.property.name.length;
    let svg = d3
      .select(this.con)
      .append("svg")
      .attr("id", `area_board_${this.id}`)
      .attr("width", this.option.property.basic.frame[2])
      .attr("height", this.option.property.basic.frame[3])
      .attr("viewBox", `0 0 ${this.nameStr === 3 ? 563 : 497} 120`);
    this.image = svg
      .append("image")
      .attr("width", this.nameStr === 3 ? 563 : 497)
      .attr("height", 120)
      .attr(
        "xlink:href",
        `${this._foldPath}/images/areaBoard${this.nameStr}-0.svg`
      );

    svg
      .append("text")
      .attr("class", "area-board-name")
      .style("transform", `translate(${this.nameStr === 3 ? 116 : 86}px, 56px)`)
      .text(this.option.property.name);
    this.text = svg
      .append("text")
      .attr("class", "area-board-value")
      .style(
        "transform",
        `translate(${this.nameStr === 3 ? 465 : 397}px, 50px)`
      )
      .text(this.option.property.value);
  }

  /**
   * 更新分区板数据
   * @param {Object} data 分区数据
   */
  update(data) {
    this.text.text(data.value);
    this.image.attr(
      "xlink:href",
      `${this._foldPath}/images/areaBoard${this.nameStr}-${data.status}.svg`
    );
  }
}
