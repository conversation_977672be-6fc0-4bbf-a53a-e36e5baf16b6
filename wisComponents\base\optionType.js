/**
 * <AUTHOR>
 * @description 组件属性数据类型列表
 * @date 2020/5/12
 */
const OptionType = {
    //输入框
    string: 'String',
    //输入框数组
    stringArray: 'StringArray',
    //checkBox
    boolean: 'Boolean',
    //checkBox数组
    booleanArray: 'BooleanArray',
    //整数输入框
    int: 'Int',
    //整数输入框数组
    intArray: 'IntArray',
    //小数输入框
    double: 'Double',
    //小数输入框数组
    doubleArray: 'DoubleArray',
    //范围输入
    range: 'Range',
    //颜色选择器
    color: 'Color',
    //渐变色选择器
    colorGradient: 'ColorGradient',
    //渐变色选择器数组
    colorGradientArray: 'ColorGradientArray',
    //颜色选择器数组
    colorArray: 'ColorArray',
    //下拉框
    enum: 'Enum',
    //多选下拉框
    multipleEnum: 'MultipleEnum',
    //日期选择器
    date: 'Date',
    //日期选择器数组
    dateArray: 'DateArray',
    //json对象
    jsonModel: 'JsonModel',
    //echarts属性
    echarts: 'ECharts'
}