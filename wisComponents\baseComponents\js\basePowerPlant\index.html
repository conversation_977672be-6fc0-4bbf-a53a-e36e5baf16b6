<!--
 * @Author: 陆晓夫
 * @Date: 2022-03-01 10:22:52
 * @LastEditors: 陆晓夫
 * @LastEditTime: 2022-05-26 17:26:25
-->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <link rel="stylesheet" type="text/css" href="v1.0/basePowerPlant.css" />
    <link rel="stylesheet" type="text/css" href="../baseComponents/css/fonts.css" />
</head>

<body style="background-color: #000000">
    <svg id="con"></svg>
    <script src="../../WisVisual/libs/d3.v5.min.js"></script>
    <script src="../../WisVisual/libs/anime.min.js"></script>
    <script src="../../WisVisual/libs/jquery.min.js"></script>
    <script src="../../WisVisual/libs/lodash.min.js"></script>
    <script src="../../WisVisual/Util/CompUtil.js"></script>
    <script src="../../WisVisual/Util/Util.js"></script>
    <!-- <script src="../../WisVisual/libs/reconnecting-websocket.min.js"></script> -->
    <!-- <script src="../../WisVisual/libs/stomp.js"></script> -->
    <!-- <script src="../../WisVisual/API/API.js"></script> -->
    <script src="../base/optionType.js"></script>
    <script src="../base/componentBase.js"></script>
    <!-- <script src="../imageView/v1.0/imageView.js"></script> -->
    <script src="../powerPlantStateBoard/v1.0/powerPlantStateBoard.js"></script>
    <!-- <script src="../cylinderBoard/v1.0/cylinderBoard.js"></script> -->
    <script src="../digitGearBoard/v1.0/digitGearBoard.js"></script>
    <script src="v1.0/basePowerPlant.js"></script>
    <script>
        let mapId = getUrlParam('mapId');
        let w = getUrlParam('w');
        let h = getUrlParam('h');

        // window.initAPIPromise.then(() => {
        draw();
        // });

        function getUrlParam(name) {
            var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }

        function draw() {
            window.currentSceneId = '225';
            console.log(mapId);
            if (mapId === null) {
                mapId = 'XJteTx7qVN';
            }
            if (w === null) {
                w = 14240;
            }
            if (h === null) {
                h = 6400;
            }
            let meta = {
                "mvarate1": "630",
                "keyActualEfficiency1": "0",
                "mvarate0": "1260.0",
                "rtKeyId2": "1101:320000000000530000",
                "no": "0,1",
                "rtKeyId1": "1101:320000000000520000",
                "rtKeyId0": "111:32000000220000",
                "mvarate2": "630",
                "nameSelected": "扬州第二厂4",
                "volType1": "UNKNOWN",
                "num": "2",
                "volType2": "UNKNOWN",
                "keyActualEfficiency2": "0",
                "type": "PowerStation",
                "keyRunningStatus2": "RUNNING",
                "keyRunningStatus1": "RUNNING",
                "layerId": "96003130",
                "volt": "_500KV",
                "name": "扬州第二厂",
                "keyDesc2": "扬州第二厂.2号机,UNKNOWN,630",
                "keyDesc1": "扬州第二厂.1号机,UNKNOWN,630",
                "id": "96003130",
                "powerType": "THERMAL"
            }

            let meta2 = {
                "mvarate1": "1110.0",
                "mvarate0": "4372.0000",
                "no": "1,2,3,4",
                "mvarate3": "1110.0",
                "mvarate2": "1110.0",
                "mvarate4": "1110.0",
                "num": "4",
                "type": "PowerStation",
                "keyRunningStatus2": "RUNNING",
                "keyRunningStatus3": "RUNNING",
                "keyRunningStatus4": "RUNNING",
                "id": "96005828",
                "keyActualEfficiency1": "0.0",
                "rtKeyId3": "1101:320000000004650000",
                "rtKeyId2": "1101:320000000000430000",
                "rtKeyId1": "1101:320000000000420000",
                "rtKeyId0": "111:32000000200000",
                "volType3": "UNKNOWN",
                "volType4": "UNKNOWN",
                "keyActualEfficiency4": "1013.8167114257812",
                "volType1": "UNKNOWN",
                "keyActualEfficiency3": "0.0",
                "volType2": "UNKNOWN",
                "keyActualEfficiency2": "1063.376953125",
                "rtKeyId4": "1101:320000000004670000",
                "keyRunningStatus1": "RUNNING",
                "layerId": "96005828",
                "volt": "_500KV",
                "name": "华东.田湾核厂",
                "keyDesc3": "江苏.田湾核厂/24kV.3号机,UNKNOWN,1110.0",
                "keyDesc2": "江苏.田湾核厂/24kV.2号机,UNKNOWN,1110.0",
                "keyDesc1": "江苏.田湾核厂/24kV.1号机,UNKNOWN,1110.0",
                "powerType": "NUCLEAR",
                "keyDesc4": "江苏.田湾核厂/24kV.4号机,UNKNOWN,1110.0"
            }
            window.comp = new BasePowerPlant('858', '123', d3.select('#con'), 0, {
                metaData: JSON.stringify(meta),
                size: 120

            });
        }
    </script>
</body>

</html>