/**
 * @description 折线图
 * <AUTHOR>
 * @date 2020-03-17
 * @class Line
 */
class Line {
    constructor(container, opts) {
        this._initProperty();
        this._container = container;
        if (typeof this.property.color != typeof opts.color) {
            delete opts.color;
        }
        this.property = $.extend(true, this.property, opts);
        this.keys = ["x", "y"];
    }
    /**
     * @description 初始化组件配置项
     */
    _initProperty() {
        this.property = {
            fontScale: 1,
            curveLine: true,
            color: "#ff0",
            width: 1,
            lineType: "line",
            symbol: "",
            symbolSize: 20,
            itemStyle: {
                borderWidth: 2,
                borderColor: "#ff0",
                color: "#ff0",
            },
            label: {
                position: "up",
                offset: [0, 0],
                color: "#fff",
                fontSize: 12,
                fontWeight: "normal",
                unit: "",
            },
        };

        this._optionDic = [
            {
                name: "curveLine",
                displayName: "是否平滑",
                description: "曲线是否平滑",
                type: OptionType.boolean,
                show: true,
                editable: true,
            },
            {
                name: "color",
                displayName: "曲线颜色",
                description: "曲线颜色",
                type: OptionType.color,
                show: true,
                editable: true,
            },
            {
                name: "width",
                displayName: "线宽",
                description: "曲线宽度",
                type: OptionType.int,
                show: true,
                editable: true,
            },
            {
                name: "lineType",
                displayName: "曲线类型",
                description: "绘制的曲线类型（实线/虚线）",
                type: OptionType.enum,
                options: [
                    {
                        name: "实线",
                        value: "line",
                    },
                    {
                        name: "虚线",
                        value: "dash",
                    },
                ],
                show: true,
                editable: true,
            },
            {
                name: "symbol",
                displayName: "点形状",
                description: "曲线上的数据点的形状",
                type: OptionType.enum,
                options: [
                    {
                        name: "无",
                        value: "",
                    },
                    {
                        name: "圆形",
                        value: "circle",
                    },
                    {
                        name: "三角形",
                        value: "triangle",
                    },
                    {
                        name: "矩形",
                        value: "rect",
                    },
                ],
                show: true,
                editable: true,
            },
            {
                name: "symbolSize",
                displayName: "点大小",
                description: "曲线上数据点的大小",
                type: OptionType.int,
                show: true,
                editable: true,
            },
            {
                name: "itemStyle",
                displayName: "点样式",
                description: "数据点的样式",
                children: [
                    {
                        name: "borderWidth",
                        displayName: "边框宽度",
                        description: "曲线上数据点图形边框宽度",
                        type: OptionType.int,
                        show: true,
                        editable: true,
                    },
                    {
                        name: "borderColor",
                        displayName: "边框颜色",
                        description: "曲线上数据点图形边框颜色",
                        type: OptionType.color,
                        show: true,
                        editable: true,
                    },
                    {
                        name: "color",
                        displayName: "颜色",
                        description: "曲线上数据点填充颜色",
                        type: OptionType.color,
                        show: true,
                        editable: true,
                    },
                ],
            },
            {
                name: "label",
                displayName: "数值",
                description: "曲线上每个数据点的数值",
                children: [
                    {
                        name: "position",
                        displayName: "位置",
                        description: "数据点文字相对于数据点的位置",
                        type: OptionType.enum,
                        options: [
                            {
                                name: "上",
                                value: "up",
                            },
                            {
                                name: "下",
                                value: "bottom",
                            },
                            {
                                name: "左",
                                value: "left",
                            },
                            {
                                name: "右",
                                value: "right",
                            },
                        ],
                        show: true,
                        editable: true,
                    },
                    {
                        name: "offset",
                        displayName: "偏移",
                        description: "数据点文字相对于数据点的位置偏移",
                        type: OptionType.doubleArray,
                        placeholder: ["x", "y"],
                        show: true,
                        editable: true,
                    },
                    {
                        name: "color",
                        displayName: "文字颜色",
                        description: "数据点的文字颜色",
                        type: OptionType.color,
                        show: true,
                        editable: true,
                    },
                    {
                        name: "fontSize",
                        displayName: "文字大小",
                        description: "数据点的文字大小",
                        type: OptionType.int,
                        show: true,
                        editable: true,
                    },
                    {
                        name: "fontWeight",
                        displayName: "文字粗细",
                        description: "数据点的文字粗细",
                        type: OptionType.enum,
                        options: [
                            {
                                name: "正常",
                                value: "normal",
                            },
                            {
                                name: "加细",
                                value: "lighter",
                            },
                            {
                                name: "加粗",
                                value: "bord",
                            },
                            {
                                name: "最粗",
                                value: "border",
                            },
                        ],
                        show: true,
                        editable: true,
                    },
                    {
                        name: "unit",
                        displayName: "单位",
                        description: "数据单位",
                        type: OptionType.string,
                        show: true,
                        editable: true,
                    },
                ],
            },
        ];
    }
    /**
     * @description 绘制线
     */
    _drawLine(line, data) {
        this.id = (Math.random() * 1000000000).toFixed(0);
        let path = this._container
            .append("path")
            .attr("id", `line_${this.id}`)
            .attr("d", line(data))
            .attr("stroke", this.property.color)
            .attr("stroke-width", this.property.width)
            .attr("fill", "none");
        if (this.property.lineType === "dash") {
            path.style("stroke-dasharray", "10 10");
        }
    }
    /**
     * @description 更新线
     */
    _updateLine(line, data) {
        this._container.select("path").transition().duration(500).attr("d", line(data));
    }
    /**
     * @description 绘制点
     */
    _drawItem(data) {
        let xRange = this.property.xRange;
        let yRange = this.property.yRange;
        if (!xRange && !yRange) return;
        let transition = d3.transition().duration(1000);
        this._container
            .selectAll(".item")
            .data(data)
            .join(
                (enter) =>
                    enter
                        .append("path")
                        .attr("class", "item")
                        .attr("d", WisCompUtil.getSymbol(this.property.symbol))
                        .style(
                            "transform",
                            (d) =>
                                `translate(${xRange(d.x)}px, ${yRange(d.y)}px) scale(${this.property.symbolSize
                                })`
                        )
                        .style(
                            "stroke-width",
                            this.property.itemStyle.borderWidth / this.property.symbolSize
                        )
                        .style("stroke", this.property.itemStyle.borderColor)
                        .style("fill", this.property.itemStyle.color),
                (update) =>
                    update
                        .transition(transition)
                        .style(
                            "transform",
                            (d) =>
                                `translate(${xRange(d.x)}px, ${yRange(d.y)}px) scale(${this.property.symbolSize
                                })`
                        ),
                (exit) => exit.remove()
            );

        this._container
            .selectAll(".itemText")
            .data(data)
            .join(
                (enter) =>
                    enter
                        .append("text")
                        .attr("class", "itemText")
                        .style("fill", this.property.label.color)
                        .style(
                            "dominant-baseline",
                            this.property.label.position === "up"
                                ? "auto"
                                : this.property.label.position === "bottom"
                                    ? "hanging"
                                    : "middle"
                        )
                        .style(
                            "text-anchor",
                            this.property.label.position === "left"
                                ? "end"
                                : this.property.label.position === "right"
                                    ? "start"
                                    : "middle"
                        )
                        .style("font-size", this.property.label.fontSize * this.property.fontScale)
                        .style(
                            "transform",
                            (d) =>
                                `translate(${xRange(d.x) + this.property.label.offset[0]}px, ${yRange(d.y) + this.property.label.offset[1]
                                }px)`
                        )
                        .text((d) => this.property.showZero ? `${d.y}${this.property.label.unit}` : ''),
                (update) =>
                    update
                        .transition(transition)
                        .style(
                            "transform",
                            (d) =>
                                `translate(${xRange(d.x) + this.property.label.offset[0]}px, ${yRange(d.y) + this.property.label.offset[1]
                                }px)`
                        )
                        .text((d) => this.property.showZero ? `${d.y}${this.property.label.unit}` : ''),
                (exit) => exit.remove()
            );
    }
    /**
     * @description 生成线路径
     */
    _getLine() {
        let xRange = this.property.xRange;
        let yRange = this.property.yRange;
        return !xRange && !yRange
            ? null
            : this.property.curveLine
                ? d3
                    .line()
                    .x((d) => xRange(d.x))
                    .y((d) => yRange(d.y))
                    .curve(d3.curveCardinal)
                : d3
                    .line()
                    .x((d) => xRange(d.x))
                    .y((d) => yRange(d.y));
    }
    /**
     * @description 更新配置
     */
    setOption(opt) {
        this.property = $.extend(true, this.property, opt);
    }
    /**
     * @description 更新数据
     */
    update(data) {
        //如果数据中y值为空字符串则剔除掉
        data = data.filter((d) => d.y !== undefined && d.y !== "");
        let line = this._getLine();
        if (line === null) return;
        if (this._container.select("path").empty()) {
            this._drawLine(line, data);
        } else {
            this._updateLine(line, data);
        }
        if (this.property.symbol !== 0) {
            this._drawItem(data);
        }

        let ani = anime({
            targets: `#line_${this.id}`,
            strokeDashoffset: [anime.setDashoffset, 0],
            easing: "easeInOutSine",
            duration: 1500,
            delay: function (el, i) {
                return i * 250;
            },
            direction: "normal",
        });

        setTimeout(() => {
            ani = null;
        }, 3100);
    }
}
