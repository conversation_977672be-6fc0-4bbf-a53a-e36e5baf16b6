<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 101 101" version="1.1"
    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
    xml:space="preserve" xmlns:serif="http://www.serif.com/"
    style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1,0,0,1,-38895,-3823)">
        <g id="水电" transform="matrix(0.804574,0,0,0.434783,34204.3,2000.86)">
            <g transform="matrix(-0.225981,-5.12125e-17,2.76747e-17,-0.418182,8429.66,4456.05)">
                <g>
                    <g id="背景" transform="matrix(-2.26939,2.77921e-16,-2.79749e-16,-2.28432,86556.4,10530.6)">
                        <path d="M33314.3,4358.05C33314.3,4345.71 33304.2,4335.69 33291.8,4335.69L33097.7,4335.69C33085.3,4335.69 33075.2,4345.71 33075.2,4358.05L33075.2,4550.85C33075.2,4563.18 33085.3,4573.19 33097.7,4573.19L33291.8,4573.19C33304.2,4573.19 33314.3,4563.18 33314.3,4550.85L33314.3,4358.05Z" style="fill:rgb(34,32,32);"/>
                    </g>
                    <g transform="matrix(-3.24542,3.97449e-16,-1.48168e-16,-1.20989,118802,5593.68)">
                        <path d="M33225.6,4428.83L33069.2,4428.83L33081.4,4372.83L33215.2,4372.83L33225.6,4428.83Z" style="fill:url(#_Linear1);"/>
                    </g>
                    <path d="M10949.4,578.483C10949.4,607.05 10972.6,630.242 11001.1,630.242L11447.6,630.242C11476.2,630.242 11499.4,607.05 11499.4,578.483L11499.4,132.001C11499.4,103.434 11476.2,80.242 11447.6,80.242L11001.1,80.242C10972.6,80.242 10949.4,103.434 10949.4,132.001L10949.4,578.483ZM10967.7,570.816C10967.7,593.478 10986.1,611.878 11008.8,611.878L11439.9,611.878C11462.6,611.878 11481,593.478 11481,570.816L11481,139.668C11481,117.006 11462.6,98.606 11439.9,98.606L11008.8,98.606C10986.1,98.606 10967.7,117.006 10967.7,139.668L10967.7,570.816Z" style="fill:url(#_Linear2);"/>
                    <g transform="matrix(-5.06102,6.19796e-16,-3.21561e-16,-2.62575,180082,12090)">
                        <path d="M33414.6,4514.8L33314.3,4514.8L33314.3,4550.9C33314.3,4559.12 33317.8,4565.8 33322,4565.8L33406.8,4565.8C33411.1,4565.8 33414.6,4559.06 33414.6,4550.76L33414.6,4514.8Z" style="fill:url(#_Linear3);"/>
                    </g>
                    <g transform="matrix(-2.2686,2.77824e-16,-2.9285e-16,-2.3913,93717.3,10164.4)">
                        <ellipse cx="36362.9" cy="4152.16" rx="111.824" ry="0.255" style="fill:white;"/>
                    </g>
                    <g transform="matrix(-7.39271,9.05346e-16,-9.05346e-16,-7.39271,256191,34264.5)">
                        <g transform="matrix(14,0,0,14,33166.2,4617.42)">
                        </g>
                        <text x="33127.5px" y="4617.42px" style="font-family:'MicrosoftYaHeiUI-Bold', 'Microsoft YaHei UI', sans-serif;font-weight:700;font-size:14px;fill:rgb(29,29,29);"><tspan x="33137.5px 33147.6px 33157.6px " y="4617.42px 4617.42px 4617.42px ">0</tspan></text>
                    </g>
                    <g transform="matrix(-0.100731,1.2336e-17,-1.2336e-17,-0.100731,11441.1,208.854)">
                        <path d="M317.441,0.286C529.027,192.68 634.82,356.169 634.82,490.868C632.465,664.489 491.047,804.001 317.41,804.001C143.773,804.001 2.355,664.489 0,490.868C0,356.169 105.793,192.68 317.441,0.286ZM317.441,79.106L312.582,83.786C141.371,248.036 57.695,385.251 57.695,490.868C57.695,634.305 173.973,750.587 317.41,750.587C460.848,750.587 577.125,634.305 577.125,490.868C577.125,387.532 496.961,253.888 333.121,94.2L317.441,79.048L317.441,79.106ZM291.754,237.153L291.754,425.216L455.125,425.216L318.492,692.333L318.492,523.403L182.039,523.403L291.754,237.153Z"/>
                        <path d="M354.174,-40.111C581.571,166.66 689.421,346.103 689.421,490.868C689.421,491.115 689.419,491.362 689.416,491.609C686.656,695.093 520.913,858.602 317.41,858.602C113.907,858.602 -51.836,695.093 -54.596,491.609C-54.599,491.362 -54.601,491.115 -54.601,490.868C-54.601,346.102 53.248,166.656 280.714,-40.117C301.542,-59.05 333.349,-59.047 354.174,-40.111ZM317.441,0.286C105.793,192.68 0,356.169 0,490.868C2.355,664.489 143.773,804.001 317.41,804.001C491.047,804.001 632.465,664.489 634.82,490.868C634.82,356.169 529.027,192.68 317.441,0.286ZM278.796,116.989C279.738,117.845 280.702,118.657 281.682,119.428L317.353,133.58L317.441,133.678C317.441,133.678 315.792,106.2 318.085,79.698L333.121,94.2C496.961,253.888 577.125,387.532 577.125,490.868C577.125,634.305 460.848,750.587 317.41,750.587C173.973,750.587 57.695,634.305 57.695,490.868C57.695,392.449 130.354,266.592 278.796,116.989ZM301.521,94.478L317.353,133.58C316.554,132.595 311.309,126.14 304.362,117.933C300.621,113.513 296.386,108.585 292.088,103.73C295.201,100.656 298.345,97.572 301.521,94.478ZM263.891,688.943C263.891,669.962 263.891,578.004 263.892,578.004L182.039,578.004C164.061,578.004 147.235,569.154 137.049,554.341C126.862,539.527 124.62,520.649 131.055,503.862L226.531,254.761C151.218,346.106 112.296,424.481 112.296,490.868C112.296,585.635 176.563,665.393 263.891,688.943ZM509.486,420.104C510.442,430.279 508.541,440.686 503.736,450.081L383.522,685.098C464.366,657.587 522.524,581.019 522.524,490.868C522.524,468.643 518.144,445.068 509.486,420.104ZM290.996,182.552C294.528,182.504 298.107,182.8 301.691,183.464C327.574,188.254 346.355,210.83 346.355,237.153L346.356,370.615L455.125,370.615C471.877,370.615 487.567,378.293 497.847,391.213C466.545,322.634 405.789,244.3 317.434,155.483C308.327,164.616 299.514,173.638 290.996,182.552ZM291.754,237.153L182.039,523.403L318.492,523.403L318.492,692.333L455.125,425.216L291.754,425.216L291.754,237.153Z" style="fill:transparent;"/>
                    </g>
                </g>
            </g>
            <g transform="matrix(0.85267,0,0,1.5304,5607.54,3740.68)">
                <path d="M293.217,323.195L296.294,347.807L300.602,382.275L315.325,382.275L298.548,323.172L296.625,322.555L293.217,323.195ZM337.555,382.298L322.279,328.395L320.738,327.958L318.639,328.38L323.515,367.389C320.788,361.999 320.167,358.601 319.445,352.824L316.168,326.607C317.724,326.295 319.282,325.982 320.838,325.669C321.919,325.977 323.001,326.284 324.083,326.591L324.937,329.597L337.531,332.701L338.436,339.945L337.562,339.744C336.017,336.555 334.582,334.747 334.363,334.848L333.384,334.677C338.61,343.843 339.646,348.649 341.125,357.373C342.17,363.539 344.464,375.924 348.675,379.909C350.64,381.77 353.356,382.291 358.134,382.275L344.388,333.844L342.618,333.377L341.399,333.731L346.297,372.922C343.667,367.331 342.812,363.035 342.067,357.069L338.949,332.128C340.164,331.774 341.379,331.419 342.594,331.065C343.792,331.382 344.989,331.699 346.186,332.015L347.045,335.046L357.867,337.712L358.729,344.61L358.582,344.576C357.179,341.612 355.91,339.943 355.709,340.041L354.93,339.907C356.656,343.092 358.097,346.308 359.252,349.656C362.151,358.06 361.829,367.014 365.056,374.672C366.91,379.069 369.918,382.296 376.452,382.275L364.147,338.926L363.076,338.638L361.44,338.927L365.719,373.154C364.065,368.4 362.983,363.354 362.448,358.407C362.083,355.048 361.352,352.011 360.501,349.416L358.962,337.099L360.726,336.779L359.2,330.1C335.418,323.847 310.823,317.737 287.042,311.48L287.042,309.752C311.291,316.133 336.354,322.367 360.603,328.742L362.374,336.488L363.173,336.353C364.097,336.603 365.022,336.851 365.946,337.099L378.775,382.298L386.903,382.843L386.903,389.883L325.756,392.101L290.562,382.497L298.38,382.497L294.079,348.078L290.743,321.393L296.765,320.262C297.966,320.647 299.167,321.032 300.368,321.417L301.016,323.703L314.814,327.102L315.767,334.733L314.937,334.543C313.192,330.915 311.442,328.831 311.178,328.939L309.981,328.729C309.981,328.729 311.848,331.247 313.792,335.42C318.185,344.849 317.486,352.37 320.069,361.806C322.364,370.201 327.468,382.298 337.555,382.298Z" style="fill:rgb(33,196,255);"/>
            </g>
        </g>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.05245e-15,-39.3447,17.1877,2.40917e-15,33148.3,4418.96)"><stop offset="0" style="stop-color:rgb(63,63,63);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(63,63,63);stop-opacity:0"/></linearGradient>
        <linearGradient id="_Linear2" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-1.00704e-13,-548.206,548.206,-1.00704e-13,11219.8,628.505)"><stop offset="0" style="stop-color:rgb(105,105,105);stop-opacity:1"/><stop offset="0.02" style="stop-color:rgb(158,158,158);stop-opacity:1"/><stop offset="0.05" style="stop-color:rgb(208,208,208);stop-opacity:1"/><stop offset="0.12" style="stop-color:rgb(229,229,229);stop-opacity:1"/><stop offset="0.2" style="stop-color:rgb(171,171,171);stop-opacity:1"/><stop offset="0.29" style="stop-color:rgb(109,109,109);stop-opacity:1"/><stop offset="0.51" style="stop-color:rgb(79,79,79);stop-opacity:1"/><stop offset="0.74" style="stop-color:rgb(105,105,105);stop-opacity:1"/><stop offset="0.82" style="stop-color:rgb(165,165,165);stop-opacity:1"/><stop offset="0.89" style="stop-color:rgb(229,229,229);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(105,105,105);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear3" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(3.18408e-15,52,-52,3.18408e-15,33371.3,4513.8)"><stop offset="0" style="stop-color:rgb(143,143,143);stop-opacity:1"/><stop offset="0.18" style="stop-color:rgb(168,168,168);stop-opacity:1"/><stop offset="0.39" style="stop-color:rgb(175,175,175);stop-opacity:1"/><stop offset="0.72" style="stop-color:rgb(160,160,160);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(111,111,111);stop-opacity:1"/></linearGradient>
    </defs>
</svg>