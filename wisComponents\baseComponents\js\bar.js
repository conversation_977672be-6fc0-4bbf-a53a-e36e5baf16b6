/**
 * @description 柱状图图
 * <AUTHOR>
 * @date 2020-03-17
 * @class Bar
 */
class Bar {
  constructor(container, opts) {
    this._initProperty();
    this._container = container;
    if (typeof this.property.color != typeof opts.color) {
      delete opts.color;
    }
    this.property = $.extend(true, this.property, opts);
    this.keys = ['x'];
    for (let i = 0; i < this.property.barNumber; i++) {
      this.keys.push(`y${i}`);
    }
  }
  /**
   * @description 初始化组件配置项
   */
  _initProperty() {
    this.property = {
      barTheme: 'default', // default theme1
      barNumber: 1,
      barMin: 0,
      color: [
        {
          type: 0,
          direction: 0,
          stops: [
            {
              offset: 0,
              color: '#ecd264',
            },
            {
              offset: 1,
              color: '#c5811f',
            },
          ],
        },
      ],
      barWidth: 10,
      barPadding: 10,
      stack: '',
      themeSetting: {
        height: 8,
        padding: 3,
      },
      label: {
        isShow: false,
        position: 'up',
        offset: [0, 0],
        color: '#fff',
        fontSize: 12,
        fontWeight: 'normal',
      },
    };

    this._optionDic = [
      {
        name: 'barTheme',
        displayName: '柱主题',
        description: '柱子主题样式',
        type: OptionType.enum,
        show: true,
        editable: true,
        options: [
          {
            name: '默认主题',
            value: 'default',
          },
          {
            name: '数码矩形',
            value: 'theme1',
          },
          {
            name: '阶梯柱',
            value: 'theme2',
          },
        ],
      },
      {
        name: 'themeSetting',
        displayName: '数码主题设置',
        description: '数码主题下的小矩形高度与间距设置',
        children: [
          {
            name: 'height',
            displayName: '小矩形高度',
            description: '数码主题下的小矩形高度大小',
            type: OptionType.int,
            show: true,
            editable: true,
          },
          {
            name: 'padding',
            displayName: '小矩形间距',
            description: '数码主题下的小矩形的间距大小',
            type: OptionType.int,
            show: true,
            editable: true,
          },
        ],
        show: true,
        editable: true,
      },
      {
        name: 'barNumber',
        displayName: '柱数量',
        description: '柱子数量',
        type: OptionType.int,
        show: true,
        editable: true,
      },
      {
        name: 'barMin',
        displayName: '柱最小值',
        description: '柱最小值',
        type: OptionType.int,
        show: true,
        editable: true,
      },
      {
        name: 'color',
        displayName: '填充颜色',
        description: '柱子填充颜色',
        type: OptionType.colorGradientArray,
        show: true,
        editable: true,
      },
      {
        name: 'barWidth',
        displayName: '柱宽',
        description: '每根柱子的宽度',
        type: OptionType.int,
        show: true,
        editable: true,
      },
      {
        name: 'barPadding',
        displayName: '柱间距',
        description: '每组柱子中各个柱子的间距',
        type: OptionType.int,
        show: true,
        editable: true,
      },
      {
        name: 'label',
        displayName: '数值',
        description: 'z柱子上每个数据点的数值',
        children: [
          {
            name: 'isShow',
            displayName: '是否显示文字',
            description: '是否显示柱上文字',
            type: OptionType.boolean,
            show: true,
            editable: true,
          },
          {
            name: 'position',
            displayName: '位置',
            description: '数据点文字相对于数据点的位置',
            type: OptionType.enum,
            options: [
              {
                name: '上',
                value: 'up',
              },
              {
                name: '下',
                value: 'bottom',
              },
              {
                name: '左',
                value: 'left',
              },
              {
                name: '右',
                value: 'right',
              },
            ],
            show: true,
            editable: true,
          },
          {
            name: 'offset',
            displayName: '偏移',
            description: '数据点文字相对于数据点的位置偏移',
            type: OptionType.doubleArray,
            placeholder: ['x', 'y'],
            show: true,
            editable: true,
          },
          {
            name: 'color',
            displayName: '文字颜色',
            description: '数据点的文字颜色',
            type: OptionType.color,
            show: true,
            editable: true,
          },
          {
            name: 'fontSize',
            displayName: '文字大小',
            description: '数据点的文字大小',
            type: OptionType.int,
            show: true,
            editable: true,
          },
          {
            name: 'fontWeight',
            displayName: '文字粗细',
            description: '数据点的文字粗细',
            type: OptionType.enum,
            options: [
              {
                name: '正常',
                value: 'normal',
              },
              {
                name: '加细',
                value: 'lighter',
              },
              {
                name: '加粗',
                value: 'bord',
              },
              {
                name: '最粗',
                value: 'border',
              },
            ],
            show: true,
            editable: true,
          },
        ],
      },
    ];
  }
  /**
   * @description 绘制线
   */
  _draw(data) {
    this.height = this.property.frame[3] - this.property.padding[0] - this.property.padding[1];
    this.width = this.property.frame[2] - this.property.padding[2] - this.property.padding[3];
    let xRange = this.property.xRange;
    let yRange = this.property.yRange;
    let xRangeType = this.property.xRangeType;
    let yRangeType = this.property.yRangeType;
    let keys = this.keys.slice(1);
    if (!xRange && !yRange) return;
    let transition = d3.transition().duration(1000);
    if (this.property.color.length < this.property.barNumber) {
      for (let i = this.property.color.length; i < this.property.barNumber; i++) {
        this.property.color.push({
          type: 0,
          direction: 0,
          stops: [
            {
              offset: 0,
              color: '#98abc5',
            },
            {
              offset: 1,
              color: '#98abc5',
            },
          ],
        });
      }
    }
    if (typeof this.property.color === 'string') {
      this.property.color = [];
      for (let i = this.property.color.length; i < this.property.barNumber; i++) {
        this.property.color.push({
          type: 0,
          direction: 0,
          stops: [
            {
              offset: 0,
              color: '#98abc5',
            },
            {
              offset: 1,
              color: '#98abc5',
            },
          ],
        });
      }
    }
    // 每组柱子的颜色列表
    let color = d3.scaleOrdinal().range(this.property.color.map((d) => WisCompUtil.setGradient(this._container, 'testGradient', d)));
    let innerXRange = d3
      .scaleBand()
      .domain(keys)
      .rangeRound([0, this.property.barWidth * keys.length]);

    var rectArr = [];
    var gContaniner = this._container
      .selectAll('g')
      .data(data)
      .join(
        (enter) => enter.append('g').attr('transform', (d) => {
          let offsetxRange = xRange(d.x) ? xRange(d.x) : 0;
          return `translate(${offsetxRange + (xRangeType === 'band' ? xRange.bandwidth() / 2 : 0) - ((this.property.barWidth + this.property.barPadding) * keys.length - this.property.barPadding) / 2},0)`;
        }),
        (update) =>
          update
            .transition(transition)
            // .attr("transform", d => `translate(${xRange(d.x) - ((this.property.barWidth + this.property.barPadding) * keys.length - this.property.barPadding) / 2},0)`),
            .attr('transform', (d) => {
              let offsetxRange = xRange(d.x) ? xRange(d.x) : 0;
              return `translate(${offsetxRange + (xRangeType === 'band' ? xRange.bandwidth() / 2 : 0) - ((this.property.barWidth + this.property.barPadding) * keys.length - this.property.barPadding) / 2},0)`;
            }),
        (exit) => exit.remove()
      )
      .selectAll('rect')
      .data((d) => {
        var gDataDic = keys.map((key) => ({ key, value: d[key] == undefined ? 0 : d[key], x: d.x }));
        if (this.property.barTheme == 'theme1') {
          rectArr.push(gDataDic);
        }
        return gDataDic;
      })
      .join(
        (enter) =>
          enter
            .append('rect')
            .attr('x', (d, i) => innerXRange(d.key) + this.property.barPadding * i)
            .attr('y', (d) => yRange(d.value > 0 ? d.value : 0))
            .attr('width', innerXRange.bandwidth())
            .attr('height', (d) => Math.abs(yRange(this.property.barMin) - yRange(d.value)))
            .attr('fill', (d) => color(d.key))
            .attr('data-x', (d) => d.x),
        (update) =>
          update
            .transition(transition)
            .attr('x', (d) => innerXRange(d.key))
            .attr('y', (d) => yRange(d.value > 0 ? d.value : 0))
            .attr('height', (d) => Math.abs(yRange(this.property.barMin) - yRange(d.value)))
            .attr('fill', (d) => color(d.key))
            .attr('data-x', (d) => d.key),
        (exit) => exit.remove()
      );
    if (this.property.barTheme == 'theme1') {
      let padding = this.property.themeSetting.padding;
      let barHeight = this.property.themeSetting.height;
      for (var index = 0; index < gContaniner._groups.length; index++) {
        var parentG = d3.select(gContaniner._parents[index]);
        var gArr = gContaniner._groups[index];
        for (var rectIndex = 0; rectIndex < gArr.length; rectIndex++) {
          //数据
          var d = rectArr[index][rectIndex];
          //g标签
          var d3gLabel = d3.select(gArr[rectIndex]);

          // //每个柱子的高度
          var tempHeight = Math.abs(yRange(this.property.barMin) - yRange(d.value));
          var path = this._generateDigitPath(tempHeight, barHeight, innerXRange.bandwidth(), padding, d.value > 0 ? yRange(d.value) : yRange(0));

          var defs = parentG.select('defs');
          var radomId = this._getRadomA();
          var clipPath = null;
          //线性渐变
          var linerGradient = null;
          if (!defs.empty()) {
            defs.remove();
          }
          defs = parentG.append('defs');
          clipPath = defs.append('clipPath').attr('id', `clip-path-${radomId}-${rectIndex}`);
          clipPath.append('path').attr('d', path);

          //线性渐变
          linerGradient = defs.append('linearGradient').attr('id', `linearGradient_${radomId}_${rectIndex}_linearColor`).attr('x1', '0%').attr('y1', '0%').attr('x2', '0%').attr('y2', '100%');
          // debugger
          linerGradient
            .selectAll('stop')
            .data(this.property.color[rectIndex].stops)
            .enter()
            .append('stop')
            .attr('offset', (d) => d.offset)
            .attr('stop-color', (d) => d.color);

          d3gLabel
            .transition()
            .attr('fill', `url(#${linerGradient.attr('id')}`)
            .attr('fill', (d) => color(d.key))
            .attr('clip-path', `url(#${clipPath.attr('id')})`);
        }
      }
    }

    if (this.property.label.isShow) {
      this._container
        .selectAll('g')
        .data(data)
        .selectAll('text')
        .data((d) => keys.map((key) => ({ key, value: d[key] })))
        .join(
          (enter) =>
            enter
              .append('text')
              .attr('class', 'itemText')
              .style('fill', this.property.label.color)
              .style('dominant-baseline', this.property.label.position === 'up' ? 'auto' : this.property.label.position === 'bottom' ? 'hanging' : 'middle')
              .style('text-anchor', this.property.label.position === 'left' ? 'end' : this.property.label.position === 'right' ? 'start' : 'middle')
              .style('font-size', this.property.label.fontSize * this.property.fontScale)
              .style('transform', (d, i) => `translate(${innerXRange(d.key) + this.property.barPadding * i + this.property.label.offset[0]}px, ${yRange(d.value) + this.property.label.offset[1]}px)`)
              .text((d) => this.property.showZero ? d.value : ''),
          // .attr("x", (d, i) => innerXRange(d.key) + this.property.barPadding * i)
          // .attr("y", (d) => yRange(d.value))
          // .attr("width", innerXRange.bandwidth())
          // .attr("height", (d) => this.height - yRange(d.value) - this.property.padding[0])
          // .attr("fill", (d) => color(d.key)),
          (update) =>
            update
              .transition(transition)
              .attr('x', (d) => innerXRange(d.key))
              .attr('y', (d) => yRange(d.value))
              .attr('height', (d) => this.height - yRange(d.value) - this.property.padding[0])
              .attr('fill', (d) => color(d.key)),
          (exit) => exit.remove()
        );
    }
  }
  /**
   * @description 生成随机id
   */
  _getRadomA() {
    var returnStr = '',
      range = 13,
      arr = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    for (var i = 0; i < range; i++) {
      var index = Math.round(Math.random() * (arr.length - 1));
      returnStr += arr[index];
    }
    return returnStr;
  }
  /**
 * @description 生成数码块
 */
  _generateDigitTube(offsetY, height, barWidth) {
    let rightX = barWidth;
    let digitTube = 'M' + 0 + ' ' + offsetY + ' L ' + rightX + ' ' + offsetY + ' L' + rightX + ' ' + (offsetY + height) + ' L' + 0 + ' ' + (offsetY + height) + ' ';
    return digitTube;
  }
  /**
 * @description 生成数码路径
 */
  _generateDigitPath(totalHeght, barHeight, barWidth, interval, dValueOffset) {
    let digitPath = '';
    for (let offsetY = 0; offsetY < totalHeght; offsetY += barHeight + interval) {
      digitPath += this._generateDigitTube(offsetY + dValueOffset, barHeight, barWidth);
    }
    return digitPath;
  }
  /**
 * @description 更新配置
 */
  setOption(opt) {
    this.property = $.extend(true, this.property, opt);
  }
  /**
 * @description 更新数据
 */
  update(data) {
    this._draw(data);
  }
}
