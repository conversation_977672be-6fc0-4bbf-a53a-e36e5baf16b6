/**
 * @license
 * Video.js 7.6.6 <http://videojs.com/>
 * Copyright Brightcove, Inc. <https://www.brightcove.com/>
 * Available under Apache License Version 2.0
 * <https://github.com/videojs/video.js/blob/master/LICENSE>
 *
 * Includes vtt.js <https://github.com/mozilla/vtt.js>
 * Available under Apache License Version 2.0
 * <https://github.com/mozilla/vtt.js/blob/master/LICENSE>
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("global/window"),require("global/document")):"function"==typeof define&&define.amd?define(["global/window","global/document"],t):(e=e||self).videojs=t(e.window,e.document)}(this,function(v,h){v=v&&v.hasOwnProperty("default")?v.default:v,h=h&&h.hasOwnProperty("default")?h.default:h;var d="7.6.6",u=[],e=function(s,o){return function(e,t,i){var n=o.levels[t],r=new RegExp("^("+n+")$");if("log"!==e&&i.unshift(e.toUpperCase()+":"),i.unshift(s+":"),u&&u.push([].concat(i)),v.console){var a=v.console[e];a||"debug"!==e||(a=v.console.info||v.console.log),a&&n&&r.test(e)&&a[Array.isArray(i)?"apply":"call"](v.console,i)}}};var p=function t(i){function n(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];r("log",a,t)}var r,a="info";return r=e(i,n),n.createLogger=function(e){return t(i+": "+e)},n.levels={all:"debug|log|warn|error",off:"",debug:"debug|log|warn|error",info:"log|warn|error",warn:"warn|error",error:"error",DEFAULT:a},n.level=function(e){if("string"==typeof e){if(!n.levels.hasOwnProperty(e))throw new Error('"'+e+'" in not a valid log level');a=e}return a},(n.history=function(){return u?[].concat(u):[]}).filter=function(t){return(u||[]).filter(function(e){return new RegExp(".*"+t+".*").test(e[0])})},n.history.clear=function(){u&&(u.length=0)},n.history.disable=function(){null!==u&&(u.length=0,u=null)},n.history.enable=function(){null===u&&(u=[])},n.error=function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return r("error",a,t)},n.warn=function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return r("warn",a,t)},n.debug=function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return r("debug",a,t)},n}("VIDEOJS"),f=p.createLogger,t=Object.prototype.toString,a=function(e){return s(e)?Object.keys(e):[]};function r(t,i){a(t).forEach(function(e){return i(t[e],e)})}function m(i){for(var e=arguments.length,t=new Array(1<e?e-1:0),n=1;n<e;n++)t[n-1]=arguments[n];return Object.assign?Object.assign.apply(Object,[i].concat(t)):(t.forEach(function(e){e&&r(e,function(e,t){i[t]=e})}),i)}function s(e){return!!e&&"object"==typeof e}function o(e){return s(e)&&"[object Object]"===t.call(e)&&e.constructor===Object}function n(e,t){if(!e||!t)return"";if("function"!=typeof v.getComputedStyle)return"";var i=v.getComputedStyle(e);return i?i.getPropertyValue(t)||i[t]:""}function l(e){return"string"==typeof e&&/\S/.test(e)}function i(e){if(/\s/.test(e))throw new Error("class has illegal whitespace characters")}function c(){return h===v.document}function g(e){return s(e)&&1===e.nodeType}function y(){try{return v.parent!==v.self}catch(e){return!0}}function _(n){return function(e,t){if(!l(e))return h[n](null);l(t)&&(t=h.querySelector(t));var i=g(t)?t:h;return i[n]&&i[n](e)}}function b(e,i,t,n){void 0===e&&(e="div"),void 0===i&&(i={}),void 0===t&&(t={});var r=h.createElement(e);return Object.getOwnPropertyNames(i).forEach(function(e){var t=i[e];-1!==e.indexOf("aria-")||"role"===e||"type"===e?(p.warn("Setting attributes in the second argument of createEl()\nhas been deprecated. Use the third argument instead.\ncreateEl(type, properties, attributes). Attempting to set "+e+" to "+t+"."),r.setAttribute(e,t)):"textContent"===e?T(r,t):r[e]=t}),Object.getOwnPropertyNames(t).forEach(function(e){r.setAttribute(e,t[e])}),n&&F(r,n),r}function T(e,t){return"undefined"==typeof e.textContent?e.innerText=t:e.textContent=t,e}function S(e,t){t.firstChild?t.insertBefore(e,t.firstChild):t.appendChild(e)}function k(e,t){return i(t),e.classList?e.classList.contains(t):function(e){return new RegExp("(^|\\s)"+e+"($|\\s)")}(t).test(e.className)}function C(e,t){return e.classList?e.classList.add(t):k(e,t)||(e.className=(e.className+" "+t).trim()),e}function E(e,t){return e.classList?e.classList.remove(t):(i(t),e.className=e.className.split(/\s+/).filter(function(e){return e!==t}).join(" ")),e}function w(e,t,i){var n=k(e,t);if("function"==typeof i&&(i=i(e,t)),"boolean"!=typeof i&&(i=!n),i!==n)return i?C(e,t):E(e,t),e}function A(i,n){Object.getOwnPropertyNames(n).forEach(function(e){var t=n[e];null===t||"undefined"==typeof t||!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)})}function P(e){var t={},i=",autoplay,controls,playsinline,loop,muted,default,defaultMuted,";if(e&&e.attributes&&0<e.attributes.length)for(var n=e.attributes,r=n.length-1;0<=r;r--){var a=n[r].name,s=n[r].value;"boolean"!=typeof e[a]&&-1===i.indexOf(","+a+",")||(s=null!==s),t[a]=s}return t}function I(e,t){return e.getAttribute(t)}function L(e,t,i){e.setAttribute(t,i)}function O(e,t){e.removeAttribute(t)}function x(){h.body.focus(),h.onselectstart=function(){return!1}}function D(){h.onselectstart=function(){return!0}}function U(e){if(e&&e.getBoundingClientRect&&e.parentNode){var t=e.getBoundingClientRect(),i={};return["bottom","height","left","right","top","width"].forEach(function(e){void 0!==t[e]&&(i[e]=t[e])}),i.height||(i.height=parseFloat(n(e,"height"))),i.width||(i.width=parseFloat(n(e,"width"))),i}}function R(e){var t;if(e.getBoundingClientRect&&e.parentNode&&(t=e.getBoundingClientRect()),!t)return{left:0,top:0};var i=h.documentElement,n=h.body,r=i.clientLeft||n.clientLeft||0,a=v.pageXOffset||n.scrollLeft,s=t.left+a-r,o=i.clientTop||n.clientTop||0,u=v.pageYOffset||n.scrollTop,l=t.top+u-o;return{left:Math.round(s),top:Math.round(l)}}function M(e,t){var i={},n=R(e),r=e.offsetWidth,a=e.offsetHeight,s=n.top,o=n.left,u=t.pageY,l=t.pageX;return t.changedTouches&&(l=t.changedTouches[0].pageX,u=t.changedTouches[0].pageY),i.y=Math.max(0,Math.min(1,(s-u+a)/a)),i.x=Math.max(0,Math.min(1,(l-o)/r)),i}function N(e){return s(e)&&3===e.nodeType}function B(e){for(;e.firstChild;)e.removeChild(e.firstChild);return e}function j(e){return"function"==typeof e&&(e=e()),(Array.isArray(e)?e:[e]).map(function(e){return"function"==typeof e&&(e=e()),g(e)||N(e)?e:"string"==typeof e&&/\S/.test(e)?h.createTextNode(e):void 0}).filter(function(e){return e})}function F(t,e){return j(e).forEach(function(e){return t.appendChild(e)}),t}function H(e,t){return F(B(e),t)}function V(e){return void 0===e.button&&void 0===e.buttons||(0===e.button&&void 0===e.buttons||("mouseup"===e.type&&0===e.button&&0===e.buttons||0===e.button&&1===e.buttons))}var q,W=_("querySelector"),z=_("querySelectorAll"),$=Object.freeze({isReal:c,isEl:g,isInFrame:y,createEl:b,textContent:T,prependTo:S,hasClass:k,addClass:C,removeClass:E,toggleClass:w,setAttributes:A,getAttributes:P,getAttribute:I,setAttribute:L,removeAttribute:O,blockTextSelection:x,unblockTextSelection:D,getBoundingClientRect:U,findPosition:R,getPointerPosition:M,isTextNode:N,emptyEl:B,normalizeContent:j,appendContent:F,insertContent:H,isSingleLeftClick:V,$:W,$$:z}),G=!1,X=function(){if(c()&&!1!==q.options.autoSetup){var e=Array.prototype.slice.call(h.getElementsByTagName("video")),t=Array.prototype.slice.call(h.getElementsByTagName("audio")),i=Array.prototype.slice.call(h.getElementsByTagName("video-js")),n=e.concat(t,i);if(n&&0<n.length)for(var r=0,a=n.length;r<a;r++){var s=n[r];if(!s||!s.getAttribute){K(1);break}void 0===s.player&&null!==s.getAttribute("data-setup")&&q(s)}else G||K(1)}};function K(e,t){t&&(q=t),v.setTimeout(X,e)}function Y(){G=!0,v.removeEventListener("load",Y)}c()&&("complete"===h.readyState?Y():v.addEventListener("load",Y));function Q(e){var t=h.createElement("style");return t.className=e,t}function J(e,t){e.styleSheet?e.styleSheet.cssText=t:e.textContent=t}var Z,ee=3;function te(){return ee++}v.WeakMap||(Z=function(){function e(){this.vdata="vdata"+Math.floor(v.performance&&v.performance.now()||Date.now()),this.data={}}var t=e.prototype;return t.set=function(e,t){var i=e[this.vdata]||te();return e[this.vdata]||(e[this.vdata]=i),this.data[i]=t,this},t.get=function(e){var t=e[this.vdata];if(t)return this.data[t];p("We have no data for this element",e)},t.has=function(e){return e[this.vdata]in this.data},t.delete=function(e){var t=e[this.vdata];t&&(delete this.data[t],delete e[this.vdata])},e}());var ie=v.WeakMap?new WeakMap:new Z;function ne(e,t){if(ie.has(e)){var i=ie.get(e);0===i.handlers[t].length&&(delete i.handlers[t],e.removeEventListener?e.removeEventListener(t,i.dispatcher,!1):e.detachEvent&&e.detachEvent("on"+t,i.dispatcher)),Object.getOwnPropertyNames(i.handlers).length<=0&&(delete i.handlers,delete i.dispatcher,delete i.disabled),0===Object.getOwnPropertyNames(i).length&&ie.delete(e)}}function re(t,i,e,n){e.forEach(function(e){t(i,e,n)})}function ae(e){function t(){return!0}function i(){return!1}if(!e||!e.isPropagationStopped){var n=e||v.event;for(var r in e={},n)"layerX"!==r&&"layerY"!==r&&"keyLocation"!==r&&"webkitMovementX"!==r&&"webkitMovementY"!==r&&("returnValue"===r&&n.preventDefault||(e[r]=n[r]));if(e.target||(e.target=e.srcElement||h),e.relatedTarget||(e.relatedTarget=e.fromElement===e.target?e.toElement:e.fromElement),e.preventDefault=function(){n.preventDefault&&n.preventDefault(),e.returnValue=!1,n.returnValue=!1,e.defaultPrevented=!0},e.defaultPrevented=!1,e.stopPropagation=function(){n.stopPropagation&&n.stopPropagation(),e.cancelBubble=!0,n.cancelBubble=!0,e.isPropagationStopped=t},e.isPropagationStopped=i,e.stopImmediatePropagation=function(){n.stopImmediatePropagation&&n.stopImmediatePropagation(),e.isImmediatePropagationStopped=t,e.stopPropagation()},e.isImmediatePropagationStopped=i,null!==e.clientX&&void 0!==e.clientX){var a=h.documentElement,s=h.body;e.pageX=e.clientX+(a&&a.scrollLeft||s&&s.scrollLeft||0)-(a&&a.clientLeft||s&&s.clientLeft||0),e.pageY=e.clientY+(a&&a.scrollTop||s&&s.scrollTop||0)-(a&&a.clientTop||s&&s.clientTop||0)}e.which=e.charCode||e.keyCode,null!==e.button&&void 0!==e.button&&(e.button=1&e.button?0:4&e.button?1:2&e.button?2:0)}return e}var se=!1;!function(){try{var e=Object.defineProperty({},"passive",{get:function(){se=!0}});v.addEventListener("test",null,e),v.removeEventListener("test",null,e)}catch(e){}}();var oe=["touchstart","touchmove"];function ue(s,e,t){if(Array.isArray(e))return re(ue,s,e,t);ie.has(s)||ie.set(s,{});var o=ie.get(s);if(o.handlers||(o.handlers={}),o.handlers[e]||(o.handlers[e]=[]),t.guid||(t.guid=te()),o.handlers[e].push(t),o.dispatcher||(o.disabled=!1,o.dispatcher=function(e,t){if(!o.disabled){e=ae(e);var i=o.handlers[e.type];if(i)for(var n=i.slice(0),r=0,a=n.length;r<a&&!e.isImmediatePropagationStopped();r++)try{n[r].call(s,e,t)}catch(e){p.error(e)}}}),1===o.handlers[e].length)if(s.addEventListener){var i=!1;se&&-1<oe.indexOf(e)&&(i={passive:!0}),s.addEventListener(e,o.dispatcher,i)}else s.attachEvent&&s.attachEvent("on"+e,o.dispatcher)}function le(e,t,i){if(ie.has(e)){var n=ie.get(e);if(n.handlers){if(Array.isArray(t))return re(le,e,t,i);function r(e,t){n.handlers[t]=[],ne(e,t)}if(void 0!==t){var a=n.handlers[t];if(a)if(i){if(i.guid)for(var s=0;s<a.length;s++)a[s].guid===i.guid&&a.splice(s--,1);ne(e,t)}else r(e,t)}else for(var o in n.handlers)Object.prototype.hasOwnProperty.call(n.handlers||{},o)&&r(e,o)}}}function ce(e,t,i){var n=ie.has(e)?ie.get(e):{},r=e.parentNode||e.ownerDocument;if("string"==typeof t?t={type:t,target:e}:t.target||(t.target=e),t=ae(t),n.dispatcher&&n.dispatcher.call(e,t,i),r&&!t.isPropagationStopped()&&!0===t.bubbles)ce.call(null,r,t,i);else if(!r&&!t.defaultPrevented&&t.target&&t.target[t.type]){ie.has(t.target)||ie.set(t.target,{});var a=ie.get(t.target);t.target[t.type]&&(a.disabled=!0,"function"==typeof t.target[t.type]&&t.target[t.type](),a.disabled=!1)}return!t.defaultPrevented}function he(e,t,i){if(Array.isArray(t))return re(he,e,t,i);function n(){le(e,t,n),i.apply(this,arguments)}n.guid=i.guid=i.guid||te(),ue(e,t,n)}function de(e,t,i){function n(){le(e,t,n),i.apply(this,arguments)}n.guid=i.guid=i.guid||te(),ue(e,t,n)}function pe(e,t,i){t.guid||(t.guid=te());var n=t.bind(e);return n.guid=i?i+"_"+t.guid:t.guid,n}function fe(t,i){var n=v.performance.now();return function(){var e=v.performance.now();i<=e-n&&(t.apply(void 0,arguments),n=e)}}function me(){}var ge,ye=Object.freeze({fixEvent:ae,on:ue,off:le,trigger:ce,one:he,any:de});me.prototype.allowedEvents_={},me.prototype.addEventListener=me.prototype.on=function(e,t){var i=this.addEventListener;this.addEventListener=function(){},ue(this,e,t),this.addEventListener=i},me.prototype.removeEventListener=me.prototype.off=function(e,t){le(this,e,t)},me.prototype.one=function(e,t){var i=this.addEventListener;this.addEventListener=function(){},he(this,e,t),this.addEventListener=i},me.prototype.any=function(e,t){var i=this.addEventListener;this.addEventListener=function(){},de(this,e,t),this.addEventListener=i},me.prototype.dispatchEvent=me.prototype.trigger=function(e){var t=e.type||e;"string"==typeof e&&(e={type:t}),e=ae(e),this.allowedEvents_[t]&&this["on"+t]&&this["on"+t](e),ce(this,e)},me.prototype.queueTrigger=function(e){var t=this;ge=ge||new Map;var i=e.type||e,n=ge.get(this);n||(n=new Map,ge.set(this,n));var r=n.get(i);n.delete(i),v.clearTimeout(r);var a=v.setTimeout(function(){0===n.size&&(n=null,ge.delete(t)),t.trigger(e)},0);n.set(i,a)};function ve(e){return"string"==typeof e&&/\S/.test(e)||Array.isArray(e)&&!!e.length}function _e(e){if(!e.nodeName&&!Ce(e))throw new Error("Invalid target; must be a DOM node or evented object.")}function be(e){if(!ve(e))throw new Error("Invalid event type; must be a non-empty string or array.")}function Te(e){if("function"!=typeof e)throw new Error("Invalid listener; must be a function.")}function Se(e,t){var i,n,r,a=t.length<3||t[0]===e||t[0]===e.eventBusEl_;return r=a?(i=e.eventBusEl_,3<=t.length&&t.shift(),n=t[0],t[1]):(i=t[0],n=t[1],t[2]),_e(i),be(n),Te(r),{isTargetingSelf:a,target:i,type:n,listener:r=pe(e,r)}}function ke(e,t,i,n){_e(e),e.nodeName?ye[t](e,i,n):e[t](i,n)}var Ce=function(t){return t instanceof me||!!t.eventBusEl_&&["on","one","off","trigger"].every(function(e){return"function"==typeof t[e]})},Ee={on:function(){for(var e=this,t=arguments.length,i=new Array(t),n=0;n<t;n++)i[n]=arguments[n];var r=Se(this,i),a=r.isTargetingSelf,s=r.target,o=r.type,u=r.listener;if(ke(s,"on",o,u),!a){function l(){return e.off(s,o,u)}l.guid=u.guid;function c(){return e.off("dispose",l)}c.guid=u.guid,ke(this,"on","dispose",l),ke(s,"on","dispose",c)}},one:function(){for(var n=this,e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var r=Se(this,t),a=r.isTargetingSelf,s=r.target,o=r.type,u=r.listener;if(a)ke(s,"one",o,u);else{function l(){n.off(s,o,l);for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];u.apply(null,t)}l.guid=u.guid,ke(s,"one",o,l)}},any:function(){for(var n=this,e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var r=Se(this,t),a=r.isTargetingSelf,s=r.target,o=r.type,u=r.listener;if(a)ke(s,"any",o,u);else{function l(){n.off(s,o,l);for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];u.apply(null,t)}l.guid=u.guid,ke(s,"any",o,l)}},off:function(e,t,i){if(!e||ve(e))le(this.eventBusEl_,e,t);else{var n=e,r=t;_e(n),be(r),Te(i),i=pe(this,i),this.off("dispose",i),n.nodeName?(le(n,r,i),le(n,"dispose",i)):Ce(n)&&(n.off(r,i),n.off("dispose",i))}},trigger:function(e,t){return ce(this.eventBusEl_,e,t)}};function we(e,t){void 0===t&&(t={});var i=t.eventBusKey;if(i){if(!e[i].nodeName)throw new Error('The eventBusKey "'+i+'" does not refer to an element.');e.eventBusEl_=e[i]}else e.eventBusEl_=b("span",{className:"vjs-event-bus"});return m(e,Ee),e.eventedCallbacks&&e.eventedCallbacks.forEach(function(e){e()}),e.on("dispose",function(){e.off(),v.setTimeout(function(){e.eventBusEl_=null},0)}),e}var Ae={state:{},setState:function(e){var i,n=this;return"function"==typeof e&&(e=e()),r(e,function(e,t){n.state[t]!==e&&((i=i||{})[t]={from:n.state[t],to:e}),n.state[t]=e}),i&&Ce(this)&&this.trigger({changes:i,type:"statechanged"}),i}};function Pe(e,t){return m(e,Ae),e.state=m({},e.state,t),"function"==typeof e.handleStateChanged&&Ce(e)&&e.on("statechanged",e.handleStateChanged),e}function Ie(e){return"string"!=typeof e?e:e.replace(/./,function(e){return e.toLowerCase()})}function Le(e){return"string"!=typeof e?e:e.replace(/./,function(e){return e.toUpperCase()})}function Oe(){for(var i={},e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.forEach(function(e){e&&r(e,function(e,t){o(e)?(o(i[t])||(i[t]={}),i[t]=Oe(i[t],e)):i[t]=e})}),i}var xe=function(){function l(e,t,i){if(!e&&this.play?this.player_=e=this:this.player_=e,this.parentComponent_=null,this.options_=Oe({},this.options_),t=this.options_=Oe(this.options_,t),this.id_=t.id||t.el&&t.el.id,!this.id_){var n=e&&e.id&&e.id()||"no_player";this.id_=n+"_component_"+te()}var r;this.name_=t.name||null,t.el?this.el_=t.el:!1!==t.createEl&&(this.el_=this.createEl()),!1!==t.evented&&we(this,{eventBusKey:this.el_?"el_":null}),Pe(this,this.constructor.defaultState),this.children_=[],this.childIndex_={},this.childNameIndex_={},v.Set||(r=function(){function e(){this.set_={}}var t=e.prototype;return t.has=function(e){return e in this.set_},t.delete=function(e){var t=this.has(e);return delete this.set_[e],t},t.add=function(e){return this.set_[e]=1,this},t.forEach=function(e,t){for(var i in this.set_)e.call(t,i,i,this)},e}()),this.setTimeoutIds_=v.Set?new Set:new r,this.setIntervalIds_=v.Set?new Set:new r,this.rafIds_=v.Set?new Set:new r,(this.clearingTimersOnDispose_=!1)!==t.initChildren&&this.initChildren(),this.ready(i),!1!==t.reportTouchActivity&&this.enableTouchActivity()}var e=l.prototype;return e.dispose=function(){if(this.trigger({type:"dispose",bubbles:!1}),this.children_)for(var e=this.children_.length-1;0<=e;e--)this.children_[e].dispose&&this.children_[e].dispose();this.children_=null,this.childIndex_=null,this.childNameIndex_=null,this.parentComponent_=null,this.el_&&(this.el_.parentNode&&this.el_.parentNode.removeChild(this.el_),ie.has(this.el_)&&ie.delete(this.el_),this.el_=null),this.player_=null},e.player=function(){return this.player_},e.options=function(e){return e&&(this.options_=Oe(this.options_,e)),this.options_},e.el=function(){return this.el_},e.createEl=function(e,t,i){return b(e,t,i)},e.localize=function(e,r,t){void 0===t&&(t=e);var i=this.player_.language&&this.player_.language(),n=this.player_.languages&&this.player_.languages(),a=n&&n[i],s=i&&i.split("-")[0],o=n&&n[s],u=t;return a&&a[e]?u=a[e]:o&&o[e]&&(u=o[e]),r&&(u=u.replace(/\{(\d+)\}/g,function(e,t){var i=r[t-1],n=i;return"undefined"==typeof i&&(n=e),n})),u},e.contentEl=function(){return this.contentEl_||this.el_},e.id=function(){return this.id_},e.name=function(){return this.name_},e.children=function(){return this.children_},e.getChildById=function(e){return this.childIndex_[e]},e.getChild=function(e){if(e)return this.childNameIndex_[e]},e.addChild=function(e,t,i){var n,r;if(void 0===t&&(t={}),void 0===i&&(i=this.children_.length),"string"==typeof e){r=Le(e);var a=t.componentClass||r;t.name=r;var s=l.getComponent(a);if(!s)throw new Error("Component "+a+" does not exist");if("function"!=typeof s)return null;n=new s(this.player_||this,t)}else n=e;if(n.parentComponent_&&n.parentComponent_.removeChild(n),this.children_.splice(i,0,n),n.parentComponent_=this,"function"==typeof n.id&&(this.childIndex_[n.id()]=n),(r=r||n.name&&Le(n.name()))&&(this.childNameIndex_[r]=n,this.childNameIndex_[Ie(r)]=n),"function"==typeof n.el&&n.el()){var o=this.contentEl().children[i]||null;this.contentEl().insertBefore(n.el(),o)}return n},e.removeChild=function(e){if("string"==typeof e&&(e=this.getChild(e)),e&&this.children_){for(var t=!1,i=this.children_.length-1;0<=i;i--)if(this.children_[i]===e){t=!0,this.children_.splice(i,1);break}if(t){e.parentComponent_=null,this.childIndex_[e.id()]=null,this.childNameIndex_[Le(e.name())]=null,this.childNameIndex_[Ie(e.name())]=null;var n=e.el();n&&n.parentNode===this.contentEl()&&this.contentEl().removeChild(e.el())}}},e.initChildren=function(){var r=this,n=this.options_.children;if(n){var e,a=this.options_,i=l.getComponent("Tech");(e=Array.isArray(n)?n:Object.keys(n)).concat(Object.keys(this.options_).filter(function(t){return!e.some(function(e){return"string"==typeof e?t===e:t===e.name})})).map(function(e){var t,i;return i="string"==typeof e?n[t=e]||r.options_[t]||{}:(t=e.name,e),{name:t,opts:i}}).filter(function(e){var t=l.getComponent(e.opts.componentClass||Le(e.name));return t&&!i.isTech(t)}).forEach(function(e){var t=e.name,i=e.opts;if(void 0!==a[t]&&(i=a[t]),!1!==i){!0===i&&(i={}),i.playerOptions=r.options_.playerOptions;var n=r.addChild(t,i);n&&(r[t]=n)}})}},e.buildCSSClass=function(){return""},e.ready=function(e,t){if(void 0===t&&(t=!1),e)return this.isReady_?void(t?e.call(this):this.setTimeout(e,1)):(this.readyQueue_=this.readyQueue_||[],void this.readyQueue_.push(e))},e.triggerReady=function(){this.isReady_=!0,this.setTimeout(function(){var e=this.readyQueue_;this.readyQueue_=[],e&&0<e.length&&e.forEach(function(e){e.call(this)},this),this.trigger("ready")},1)},e.$=function(e,t){return W(e,t||this.contentEl())},e.$$=function(e,t){return z(e,t||this.contentEl())},e.hasClass=function(e){return k(this.el_,e)},e.addClass=function(e){C(this.el_,e)},e.removeClass=function(e){E(this.el_,e)},e.toggleClass=function(e,t){w(this.el_,e,t)},e.show=function(){this.removeClass("vjs-hidden")},e.hide=function(){this.addClass("vjs-hidden")},e.lockShowing=function(){this.addClass("vjs-lock-showing")},e.unlockShowing=function(){this.removeClass("vjs-lock-showing")},e.getAttribute=function(e){return I(this.el_,e)},e.setAttribute=function(e,t){L(this.el_,e,t)},e.removeAttribute=function(e){O(this.el_,e)},e.width=function(e,t){return this.dimension("width",e,t)},e.height=function(e,t){return this.dimension("height",e,t)},e.dimensions=function(e,t){this.width(e,!0),this.height(t)},e.dimension=function(e,t,i){if(void 0!==t)return null!==t&&t==t||(t=0),-1!==(""+t).indexOf("%")||-1!==(""+t).indexOf("px")?this.el_.style[e]=t:this.el_.style[e]="auto"===t?"":t+"px",void(i||this.trigger("componentresize"));if(!this.el_)return 0;var n=this.el_.style[e],r=n.indexOf("px");return-1!==r?parseInt(n.slice(0,r),10):parseInt(this.el_["offset"+Le(e)],10)},e.currentDimension=function(e){var t=0;if("width"!==e&&"height"!==e)throw new Error("currentDimension only accepts width or height value");if(t=n(this.el_,e),0===(t=parseFloat(t))||isNaN(t)){var i="offset"+Le(e);t=this.el_[i]}return t},e.currentDimensions=function(){return{width:this.currentDimension("width"),height:this.currentDimension("height")}},e.currentWidth=function(){return this.currentDimension("width")},e.currentHeight=function(){return this.currentDimension("height")},e.focus=function(){this.el_.focus()},e.blur=function(){this.el_.blur()},e.handleKeyDown=function(e){this.player_&&(e.stopPropagation(),this.player_.handleKeyDown(e))},e.handleKeyPress=function(e){this.handleKeyDown(e)},e.emitTapEvents=function(){var n,t=0,r=null;this.on("touchstart",function(e){1===e.touches.length&&(r={pageX:e.touches[0].pageX,pageY:e.touches[0].pageY},t=v.performance.now(),n=!0)}),this.on("touchmove",function(e){if(1<e.touches.length)n=!1;else if(r){var t=e.touches[0].pageX-r.pageX,i=e.touches[0].pageY-r.pageY;10<Math.sqrt(t*t+i*i)&&(n=!1)}});function e(){n=!1}this.on("touchleave",e),this.on("touchcancel",e),this.on("touchend",function(e){!(r=null)===n&&v.performance.now()-t<200&&(e.preventDefault(),this.trigger("tap"))})},e.enableTouchActivity=function(){if(this.player()&&this.player().reportUserActivity){var t,i=pe(this.player(),this.player().reportUserActivity);this.on("touchstart",function(){i(),this.clearInterval(t),t=this.setInterval(i,250)});function e(e){i(),this.clearInterval(t)}this.on("touchmove",i),this.on("touchend",e),this.on("touchcancel",e)}},e.setTimeout=function(e,t){var i,n=this;return e=pe(this,e),this.clearTimersOnDispose_(),i=v.setTimeout(function(){n.setTimeoutIds_.has(i)&&n.setTimeoutIds_.delete(i),e()},t),this.setTimeoutIds_.add(i),i},e.clearTimeout=function(e){return this.setTimeoutIds_.has(e)&&(this.setTimeoutIds_.delete(e),v.clearTimeout(e)),e},e.setInterval=function(e,t){e=pe(this,e),this.clearTimersOnDispose_();var i=v.setInterval(e,t);return this.setIntervalIds_.add(i),i},e.clearInterval=function(e){return this.setIntervalIds_.has(e)&&(this.setIntervalIds_.delete(e),v.clearInterval(e)),e},e.requestAnimationFrame=function(e){var t,i=this;return this.supportsRaf_?(this.clearTimersOnDispose_(),e=pe(this,e),t=v.requestAnimationFrame(function(){i.rafIds_.has(t)&&i.rafIds_.delete(t),e()}),this.rafIds_.add(t),t):this.setTimeout(e,1e3/60)},e.cancelAnimationFrame=function(e){return this.supportsRaf_?(this.rafIds_.has(e)&&(this.rafIds_.delete(e),v.cancelAnimationFrame(e)),e):this.clearTimeout(e)},e.clearTimersOnDispose_=function(){var n=this;this.clearingTimersOnDispose_||(this.clearingTimersOnDispose_=!0,this.one("dispose",function(){[["rafIds_","cancelAnimationFrame"],["setTimeoutIds_","clearTimeout"],["setIntervalIds_","clearInterval"]].forEach(function(e){var t=e[0],i=e[1];n[t].forEach(n[i],n)}),n.clearingTimersOnDispose_=!1}))},l.registerComponent=function(e,t){if("string"!=typeof e||!e)throw new Error('Illegal component name, "'+e+'"; must be a non-empty string.');var i,n=l.getComponent("Tech"),r=n&&n.isTech(t),a=l===t||l.prototype.isPrototypeOf(t.prototype);if(r||!a)throw i=r?"techs must be registered using Tech.registerTech()":"must be a Component subclass",new Error('Illegal component, "'+e+'"; '+i+".");e=Le(e),l.components_||(l.components_={});var s=l.getComponent("Player");if("Player"===e&&s&&s.players){var o=s.players,u=Object.keys(o);if(o&&0<u.length&&u.map(function(e){return o[e]}).every(Boolean))throw new Error("Can not register Player component after player has been created.")}return l.components_[e]=t,l.components_[Ie(e)]=t},l.getComponent=function(e){if(e&&l.components_)return l.components_[e]},l}();function De(e,t){e.prototype=Object.create(t.prototype),(e.prototype.constructor=e).__proto__=t}function Ue(e,t){return(Ue=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Re(e,t,i){return(Re=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,i){var n=[null];n.push.apply(n,t);var r=new(Function.bind.apply(e,n));return i&&Ue(r,i.prototype),r}).apply(null,arguments)}function Me(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}xe.prototype.supportsRaf_="function"==typeof v.requestAnimationFrame&&"function"==typeof v.cancelAnimationFrame,xe.registerComponent("Component",xe);var Ne,Be,je,Fe,He=v.navigator&&v.navigator.userAgent||"",Ve=/AppleWebKit\/([\d.]+)/i.exec(He),qe=Ve?parseFloat(Ve.pop()):null,We=/iPad/i.test(He),ze=/iPhone/i.test(He)&&!We,$e=/iPod/i.test(He),Ge=ze||We||$e,Xe=(Ne=He.match(/OS (\d+)_/i))&&Ne[1]?Ne[1]:null,Ke=/Android/i.test(He),Ye=function(){var e=He.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),i=e[2]&&parseFloat(e[2]);return t&&i?parseFloat(e[1]+"."+e[2]):t||null}(),Qe=Ke&&Ye<5&&qe<537,Je=/Firefox/i.test(He),Ze=/Edge/i.test(He),et=!Ze&&(/Chrome/i.test(He)||/CriOS/i.test(He)),tt=(Be=He.match(/(Chrome|CriOS)\/(\d+)/))&&Be[2]?parseFloat(Be[2]):null,it=(je=/MSIE\s(\d+)\.\d/.exec(He),!(Fe=je&&parseFloat(je[1]))&&/Trident\/7.0/i.test(He)&&/rv:11.0/.test(He)&&(Fe=11),Fe),nt=/Safari/i.test(He)&&!et&&!Ke&&!Ze,rt=(nt||Ge)&&!et,at=/Windows/i.test(He),st=c()&&("ontouchstart"in v||v.navigator.maxTouchPoints||v.DocumentTouch&&v.document instanceof v.DocumentTouch),ot=Object.freeze({IS_IPAD:We,IS_IPHONE:ze,IS_IPOD:$e,IS_IOS:Ge,IOS_VERSION:Xe,IS_ANDROID:Ke,ANDROID_VERSION:Ye,IS_NATIVE_ANDROID:Qe,IS_FIREFOX:Je,IS_EDGE:Ze,IS_CHROME:et,CHROME_VERSION:tt,IE_VERSION:it,IS_SAFARI:nt,IS_ANY_SAFARI:rt,IS_WINDOWS:at,TOUCH_ENABLED:st});function ut(e,t,i,n){return function(e,t,i){if("number"!=typeof t||t<0||i<t)throw new Error("Failed to execute '"+e+"' on 'TimeRanges': The index provided ("+t+") is non-numeric or out of bounds (0-"+i+").")}(e,n,i.length-1),i[n][t]}function lt(e){return void 0===e||0===e.length?{length:0,start:function(){throw new Error("This TimeRanges object is empty")},end:function(){throw new Error("This TimeRanges object is empty")}}:{length:e.length,start:ut.bind(null,"start",0,e),end:ut.bind(null,"end",1,e)}}function ct(e,t){return Array.isArray(e)?lt(e):void 0===e||void 0===t?lt():lt([[e,t]])}function ht(e,t){var i,n,r=0;if(!t)return 0;e&&e.length||(e=ct(0,0));for(var a=0;a<e.length;a++)i=e.start(a),t<(n=e.end(a))&&(n=t),r+=n-i;return r/t}for(var dt,pt={prefixed:!0},ft=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror","fullscreen"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror","-webkit-full-screen"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror","-moz-full-screen"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError","-ms-fullscreen"]],mt=ft[0],gt=0;gt<ft.length;gt++)if(ft[gt][1]in h){dt=ft[gt];break}if(dt){for(var yt=0;yt<dt.length;yt++)pt[mt[yt]]=dt[yt];pt.prefixed=dt[0]!==mt[0]}function vt(e){if(e instanceof vt)return e;"number"==typeof e?this.code=e:"string"==typeof e?this.message=e:s(e)&&("number"==typeof e.code&&(this.code=e.code),m(this,e)),this.message||(this.message=vt.defaultMessages[this.code]||"")}vt.prototype.code=0,vt.prototype.message="",vt.prototype.status=null,vt.errorTypes=["MEDIA_ERR_CUSTOM","MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_ERR_ENCRYPTED"],vt.defaultMessages={1:"You aborted the media playback",2:"A network error caused the media download to fail part-way.",3:"The media playback was aborted due to a corruption problem or because the media used features your browser did not support.",4:"The media could not be loaded, either because the server or network failed or because the format is not supported.",5:"The media is encrypted and we do not have the keys to decrypt it."};for(var _t=0;_t<vt.errorTypes.length;_t++)vt[vt.errorTypes[_t]]=_t,vt.prototype[vt.errorTypes[_t]]=_t;var bt=function(e,t){var i,n=null;try{i=JSON.parse(e,t)}catch(e){n=e}return[n,i]};function Tt(e){return null!=e&&"function"==typeof e.then}function St(e){Tt(e)&&e.then(null,function(e){})}function kt(n){return["kind","label","language","id","inBandMetadataTrackDispatchType","mode","src"].reduce(function(e,t,i){return n[t]&&(e[t]=n[t]),e},{cues:n.cues&&Array.prototype.map.call(n.cues,function(e){return{startTime:e.startTime,endTime:e.endTime,text:e.text,id:e.id}})})}var Ct=function(e){var t=e.$$("track"),i=Array.prototype.map.call(t,function(e){return e.track});return Array.prototype.map.call(t,function(e){var t=kt(e.track);return e.src&&(t.src=e.src),t}).concat(Array.prototype.filter.call(e.textTracks(),function(e){return-1===i.indexOf(e)}).map(kt))},Et=function(e,i){return e.forEach(function(e){var t=i.addRemoteTextTrack(e).track;!e.src&&e.cues&&e.cues.forEach(function(e){return t.addCue(e)})}),i.textTracks()};function wt(e,t){return e(t={exports:{}},t.exports),t.exports}var At=wt(function(e,t){function i(e){if(e&&"object"==typeof e){var t=e.which||e.keyCode||e.charCode;t&&(e=t)}if("number"==typeof e)return s[e];var i,n=String(e);return(i=r[n.toLowerCase()])?i:(i=a[n.toLowerCase()])||(1===n.length?n.charCodeAt(0):void 0)}i.isEventKey=function(e,t){if(e&&"object"==typeof e){var i=e.which||e.keyCode||e.charCode;if(null==i)return!1;if("string"==typeof t){var n;if(n=r[t.toLowerCase()])return n===i;if(n=a[t.toLowerCase()])return n===i}else if("number"==typeof t)return t===i;return!1}};var r=(t=e.exports=i).code=t.codes={backspace:8,tab:9,enter:13,shift:16,ctrl:17,alt:18,"pause/break":19,"caps lock":20,esc:27,space:32,"page up":33,"page down":34,end:35,home:36,left:37,up:38,right:39,down:40,insert:45,delete:46,command:91,"left command":91,"right command":93,"numpad *":106,"numpad +":107,"numpad -":109,"numpad .":110,"numpad /":111,"num lock":144,"scroll lock":145,"my computer":182,"my calculator":183,";":186,"=":187,",":188,"-":189,".":190,"/":191,"`":192,"[":219,"\\":220,"]":221,"'":222},a=t.aliases={windows:91,"⇧":16,"⌥":18,"⌃":17,"⌘":91,ctl:17,control:17,option:18,pause:19,break:19,caps:20,return:13,escape:27,spc:32,spacebar:32,pgup:33,pgdn:34,ins:45,del:46,cmd:91};for(n=97;n<123;n++)r[String.fromCharCode(n)]=n-32;for(var n=48;n<58;n++)r[n-48]=n;for(n=1;n<13;n++)r["f"+n]=n+111;for(n=0;n<10;n++)r["numpad "+n]=n+96;var s=t.names=t.title={};for(n in r)s[r[n]]=n;for(var o in a)r[o]=a[o]}),Pt=(At.code,At.codes,At.aliases,At.names,At.title,"vjs-modal-dialog"),It=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).opened_=i.hasBeenOpened_=i.hasBeenFilled_=!1,i.closeable(!i.options_.uncloseable),i.content(i.options_.content),i.contentEl_=b("div",{className:Pt+"-content"},{role:"document"}),i.descEl_=b("p",{className:Pt+"-description vjs-control-text",id:i.el().getAttribute("aria-describedby")}),T(i.descEl_,i.description()),i.el_.appendChild(i.descEl_),i.el_.appendChild(i.contentEl_),i}De(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:this.buildCSSClass(),tabIndex:-1},{"aria-describedby":this.id()+"_description","aria-hidden":"true","aria-label":this.label(),role:"dialog"})},t.dispose=function(){this.contentEl_=null,this.descEl_=null,this.previouslyActiveEl_=null,n.prototype.dispose.call(this)},t.buildCSSClass=function(){return Pt+" vjs-hidden "+n.prototype.buildCSSClass.call(this)},t.label=function(){return this.localize(this.options_.label||"Modal Window")},t.description=function(){var e=this.options_.description||this.localize("This is a modal window.");return this.closeable()&&(e+=" "+this.localize("This modal can be closed by pressing the Escape key or activating the close button.")),e},t.open=function(){if(!this.opened_){var e=this.player();this.trigger("beforemodalopen"),this.opened_=!0,!this.options_.fillAlways&&(this.hasBeenOpened_||this.hasBeenFilled_)||this.fill(),this.wasPlaying_=!e.paused(),this.options_.pauseOnOpen&&this.wasPlaying_&&e.pause(),this.on("keydown",this.handleKeyDown),this.hadControls_=e.controls(),e.controls(!1),this.show(),this.conditionalFocus_(),this.el().setAttribute("aria-hidden","false"),this.trigger("modalopen"),this.hasBeenOpened_=!0}},t.opened=function(e){return"boolean"==typeof e&&this[e?"open":"close"](),this.opened_},t.close=function(){if(this.opened_){var e=this.player();this.trigger("beforemodalclose"),this.opened_=!1,this.wasPlaying_&&this.options_.pauseOnOpen&&e.play(),this.off("keydown",this.handleKeyDown),this.hadControls_&&e.controls(!0),this.hide(),this.el().setAttribute("aria-hidden","true"),this.trigger("modalclose"),this.conditionalBlur_(),this.options_.temporary&&this.dispose()}},t.closeable=function(e){if("boolean"==typeof e){var t=this.closeable_=!!e,i=this.getChild("closeButton");if(t&&!i){var n=this.contentEl_;this.contentEl_=this.el_,i=this.addChild("closeButton",{controlText:"Close Modal Dialog"}),this.contentEl_=n,this.on(i,"close",this.close)}!t&&i&&(this.off(i,"close",this.close),this.removeChild(i),i.dispose())}return this.closeable_},t.fill=function(){this.fillWith(this.content())},t.fillWith=function(e){var t=this.contentEl(),i=t.parentNode,n=t.nextSibling;this.trigger("beforemodalfill"),this.hasBeenFilled_=!0,i.removeChild(t),this.empty(),H(t,e),this.trigger("modalfill"),n?i.insertBefore(t,n):i.appendChild(t);var r=this.getChild("closeButton");r&&i.appendChild(r.el_)},t.empty=function(){this.trigger("beforemodalempty"),B(this.contentEl()),this.trigger("modalempty")},t.content=function(e){return"undefined"!=typeof e&&(this.content_=e),this.content_},t.conditionalFocus_=function(){var e=h.activeElement,t=this.player_.el_;this.previouslyActiveEl_=null,!t.contains(e)&&t!==e||(this.previouslyActiveEl_=e,this.focus())},t.conditionalBlur_=function(){this.previouslyActiveEl_&&(this.previouslyActiveEl_.focus(),this.previouslyActiveEl_=null)},t.handleKeyDown=function(e){if(e.stopPropagation(),At.isEventKey(e,"Escape")&&this.closeable())return e.preventDefault(),void this.close();if(At.isEventKey(e,"Tab")){for(var t,i=this.focusableEls_(),n=this.el_.querySelector(":focus"),r=0;r<i.length;r++)if(n===i[r]){t=r;break}h.activeElement===this.el_&&(t=0),e.shiftKey&&0===t?(i[i.length-1].focus(),e.preventDefault()):e.shiftKey||t!==i.length-1||(i[0].focus(),e.preventDefault())}},t.focusableEls_=function(){var e=this.el_.querySelectorAll("*");return Array.prototype.filter.call(e,function(e){return(e instanceof v.HTMLAnchorElement||e instanceof v.HTMLAreaElement)&&e.hasAttribute("href")||(e instanceof v.HTMLInputElement||e instanceof v.HTMLSelectElement||e instanceof v.HTMLTextAreaElement||e instanceof v.HTMLButtonElement)&&!e.hasAttribute("disabled")||e instanceof v.HTMLIFrameElement||e instanceof v.HTMLObjectElement||e instanceof v.HTMLEmbedElement||e.hasAttribute("tabindex")&&-1!==e.getAttribute("tabindex")||e.hasAttribute("contenteditable")})},e}(xe);It.prototype.options_={pauseOnOpen:!0,temporary:!0},xe.registerComponent("ModalDialog",It);var Lt=function(n){function e(e){var t;void 0===e&&(e=[]),(t=n.call(this)||this).tracks_=[],Object.defineProperty(Me(t),"length",{get:function(){return this.tracks_.length}});for(var i=0;i<e.length;i++)t.addTrack(e[i]);return t}De(e,n);var t=e.prototype;return t.addTrack=function(e){var t=this.tracks_.length;""+t in this||Object.defineProperty(this,t,{get:function(){return this.tracks_[t]}}),-1===this.tracks_.indexOf(e)&&(this.tracks_.push(e),this.trigger({track:e,type:"addtrack",target:this}))},t.removeTrack=function(e){for(var t,i=0,n=this.length;i<n;i++)if(this[i]===e){(t=this[i]).off&&t.off(),this.tracks_.splice(i,1);break}t&&this.trigger({track:t,type:"removetrack",target:this})},t.getTrackById=function(e){for(var t=null,i=0,n=this.length;i<n;i++){var r=this[i];if(r.id===e){t=r;break}}return t},e}(me);for(var Ot in Lt.prototype.allowedEvents_={change:"change",addtrack:"addtrack",removetrack:"removetrack"},Lt.prototype.allowedEvents_)Lt.prototype["on"+Ot]=null;function xt(e,t){for(var i=0;i<e.length;i++)Object.keys(e[i]).length&&t.id!==e[i].id&&(e[i].enabled=!1)}function Dt(e,t){for(var i=0;i<e.length;i++)Object.keys(e[i]).length&&t.id!==e[i].id&&(e[i].selected=!1)}function Ut(e){var t=["protocol","hostname","port","pathname","search","hash","host"],i=h.createElement("a");i.href=e;var n,r=""===i.host&&"file:"!==i.protocol;r&&((n=h.createElement("div")).innerHTML='<a href="'+e+'"></a>',i=n.firstChild,n.setAttribute("style","display:none; position:absolute;"),h.body.appendChild(n));for(var a={},s=0;s<t.length;s++)a[t[s]]=i[t[s]];return"http:"===a.protocol&&(a.host=a.host.replace(/:80$/,"")),"https:"===a.protocol&&(a.host=a.host.replace(/:443$/,"")),a.protocol||(a.protocol=v.location.protocol),r&&h.body.removeChild(n),a}function Rt(e){if(!e.match(/^https?:\/\//)){var t=h.createElement("div");t.innerHTML='<a href="'+e+'">x</a>',e=t.firstChild.href}return e}function Mt(e){if("string"==typeof e){var t=/^(\/?)([\s\S]*?)((?:\.{1,2}|[^\/]+?)(\.([^\.\/\?]+)))(?:[\/]*|[\?].*)$/.exec(e);if(t)return t.pop().toLowerCase()}return""}function Nt(e){var t=v.location,i=Ut(e);return(":"===i.protocol?t.protocol:i.protocol)+i.host!==t.protocol+t.host}var Bt=function(n){function e(e){var t;void 0===e&&(e=[]);for(var i=e.length-1;0<=i;i--)if(e[i].enabled){xt(e,e[i]);break}return(t=n.call(this,e)||this).changing_=!1,t}De(e,n);var t=e.prototype;return t.addTrack=function(e){var t=this;e.enabled&&xt(this,e),n.prototype.addTrack.call(this,e),e.addEventListener&&(e.enabledChange_=function(){t.changing_||(t.changing_=!0,xt(t,e),t.changing_=!1,t.trigger("change"))},e.addEventListener("enabledchange",e.enabledChange_))},t.removeTrack=function(e){n.prototype.removeTrack.call(this,e),e.removeEventListener&&e.enabledChange_&&(e.removeEventListener("enabledchange",e.enabledChange_),e.enabledChange_=null)},e}(Lt),jt=function(n){function e(e){var t;void 0===e&&(e=[]);for(var i=e.length-1;0<=i;i--)if(e[i].selected){Dt(e,e[i]);break}return(t=n.call(this,e)||this).changing_=!1,Object.defineProperty(Me(t),"selectedIndex",{get:function(){for(var e=0;e<this.length;e++)if(this[e].selected)return e;return-1},set:function(){}}),t}De(e,n);var t=e.prototype;return t.addTrack=function(e){var t=this;e.selected&&Dt(this,e),n.prototype.addTrack.call(this,e),e.addEventListener&&(e.selectedChange_=function(){t.changing_||(t.changing_=!0,Dt(t,e),t.changing_=!1,t.trigger("change"))},e.addEventListener("selectedchange",e.selectedChange_))},t.removeTrack=function(e){n.prototype.removeTrack.call(this,e),e.removeEventListener&&e.selectedChange_&&(e.removeEventListener("selectedchange",e.selectedChange_),e.selectedChange_=null)},e}(Lt),Ft=function(i){function e(){return i.apply(this,arguments)||this}De(e,i);var t=e.prototype;return t.addTrack=function(e){var t=this;i.prototype.addTrack.call(this,e),this.queueChange_||(this.queueChange_=function(){return t.queueTrigger("change")}),this.triggerSelectedlanguagechange||(this.triggerSelectedlanguagechange_=function(){return t.trigger("selectedlanguagechange")}),e.addEventListener("modechange",this.queueChange_);-1===["metadata","chapters"].indexOf(e.kind)&&e.addEventListener("modechange",this.triggerSelectedlanguagechange_)},t.removeTrack=function(e){i.prototype.removeTrack.call(this,e),e.removeEventListener&&(this.queueChange_&&e.removeEventListener("modechange",this.queueChange_),this.selectedlanguagechange_&&e.removeEventListener("modechange",this.triggerSelectedlanguagechange_))},e}(Lt),Ht=function(){function e(e){void 0===e&&(e=[]),this.trackElements_=[],Object.defineProperty(this,"length",{get:function(){return this.trackElements_.length}});for(var t=0,i=e.length;t<i;t++)this.addTrackElement_(e[t])}var t=e.prototype;return t.addTrackElement_=function(e){var t=this.trackElements_.length;""+t in this||Object.defineProperty(this,t,{get:function(){return this.trackElements_[t]}}),-1===this.trackElements_.indexOf(e)&&this.trackElements_.push(e)},t.getTrackElementByTrack_=function(e){for(var t,i=0,n=this.trackElements_.length;i<n;i++)if(e===this.trackElements_[i].track){t=this.trackElements_[i];break}return t},t.removeTrackElement_=function(e){for(var t=0,i=this.trackElements_.length;t<i;t++)if(e===this.trackElements_[t]){this.trackElements_[t].track&&"function"==typeof this.trackElements_[t].track.off&&this.trackElements_[t].track.off(),"function"==typeof this.trackElements_[t].off&&this.trackElements_[t].off(),this.trackElements_.splice(t,1);break}},e}(),Vt=function(){function t(e){t.prototype.setCues_.call(this,e),Object.defineProperty(this,"length",{get:function(){return this.length_}})}var e=t.prototype;return e.setCues_=function(e){var t=this.length||0,i=0,n=e.length;this.cues_=e,this.length_=e.length;function r(e){""+e in this||Object.defineProperty(this,""+e,{get:function(){return this.cues_[e]}})}if(t<n)for(i=t;i<n;i++)r.call(this,i)},e.getCueById=function(e){for(var t=null,i=0,n=this.length;i<n;i++){var r=this[i];if(r.id===e){t=r;break}}return t},t}(),qt={alternative:"alternative",captions:"captions",main:"main",sign:"sign",subtitles:"subtitles",commentary:"commentary"},Wt={alternative:"alternative",descriptions:"descriptions",main:"main","main-desc":"main-desc",translation:"translation",commentary:"commentary"},zt={subtitles:"subtitles",captions:"captions",descriptions:"descriptions",chapters:"chapters",metadata:"metadata"},$t={disabled:"disabled",hidden:"hidden",showing:"showing"},Gt=function(a){function e(e){var t;void 0===e&&(e={}),t=a.call(this)||this;function i(e){Object.defineProperty(Me(t),e,{get:function(){return n[e]},set:function(){}})}var n={id:e.id||"vjs_track_"+te(),kind:e.kind||"",label:e.label||"",language:e.language||""};for(var r in n)i(r);return t}return De(e,a),e}(me),Xt=Object.freeze({parseUrl:Ut,getAbsoluteURL:Rt,getFileExtension:Mt,isCrossOrigin:Nt}),Kt=function(e){var t=Yt.call(e);return"[object Function]"===t||"function"==typeof e&&"[object RegExp]"!==t||"undefined"!=typeof window&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)},Yt=Object.prototype.toString;function Qt(e){var t=ii.call(e),i="[object Arguments]"===t;return i=i||"[object Array]"!==t&&null!==e&&"object"==typeof e&&"number"==typeof e.length&&0<=e.length&&"[object Function]"===ii.call(e.callee)}var Jt,Zt=Array.prototype.slice,ei=Object.prototype.toString,ti=Function.prototype.bind||function(t){var i=this;if("function"!=typeof i||"[object Function]"!==ei.call(i))throw new TypeError("Function.prototype.bind called on incompatible "+i);for(var n,r=Zt.call(arguments,1),e=Math.max(0,i.length-r.length),a=[],s=0;s<e;s++)a.push("$"+s);if(n=Function("binder","return function ("+a.join(",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof n){var e=i.apply(this,r.concat(Zt.call(arguments)));return Object(e)===e?e:this}return i.apply(t,r.concat(Zt.call(arguments)))}),i.prototype){function o(){}o.prototype=i.prototype,n.prototype=new o,o.prototype=null}return n},ii=Object.prototype.toString;if(!Object.keys){function ni(e){var t=e.constructor;return t&&t.prototype===e}var ri=Object.prototype.hasOwnProperty,ai=Object.prototype.toString,si=Qt,oi=Object.prototype.propertyIsEnumerable,ui=!oi.call({toString:null},"toString"),li=oi.call(function(){},"prototype"),ci=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],hi={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$onmozfullscreenchange:!0,$onmozfullscreenerror:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},di=function(){if("undefined"==typeof window)return!1;for(var e in window)try{if(!hi["$"+e]&&ri.call(window,e)&&null!==window[e]&&"object"==typeof window[e])try{ni(window[e])}catch(e){return!0}}catch(e){return!0}return!1}();Jt=function(e){var t=null!==e&&"object"==typeof e,i="[object Function]"===ai.call(e),n=si(e),r=t&&"[object String]"===ai.call(e),a=[];if(!t&&!i&&!n)throw new TypeError("Object.keys called on a non-object");var s=li&&i;if(r&&0<e.length&&!ri.call(e,0))for(var o=0;o<e.length;++o)a.push(String(o));if(n&&0<e.length)for(var u=0;u<e.length;++u)a.push(String(u));else for(var l in e)s&&"prototype"===l||!ri.call(e,l)||a.push(String(l));if(ui)for(var c=function(e){if("undefined"==typeof window||!di)return ni(e);try{return ni(e)}catch(e){return!1}}(e),h=0;h<ci.length;++h)c&&"constructor"===ci[h]||!ri.call(e,ci[h])||a.push(ci[h]);return a}}var pi=Jt,fi=Array.prototype.slice,mi=Object.keys,gi=mi?function(e){return mi(e)}:pi,yi=Object.keys;gi.shim=function(){Object.keys?function(){var e=Object.keys(arguments);return e&&e.length===arguments.length}(1,2)||(Object.keys=function(e){return Qt(e)?yi(fi.call(e)):yi(e)}):Object.keys=gi;return Object.keys||gi};function vi(e,t,i,n){t in e&&(!function(e){return"function"==typeof e&&"[object Function]"===Si.call(e)}(n)||!n())||(Ei?Ci(e,t,{configurable:!0,enumerable:!1,value:i,writable:!0}):e[t]=i)}function _i(e,t,i){var n=2<arguments.length?i:{},r=bi(t);Ti&&(r=ki.call(r,Object.getOwnPropertySymbols(t)));for(var a=0;a<r.length;a+=1)vi(e,r[a],t[r[a]],n[r[a]])}var bi=gi,Ti="function"==typeof Symbol&&"symbol"==typeof Symbol("foo"),Si=Object.prototype.toString,ki=Array.prototype.concat,Ci=Object.defineProperty,Ei=Ci&&function(){var e={};try{for(var t in Ci(e,"x",{enumerable:!1,value:e}),e)return!1;return e.x===e}catch(e){return!1}}();_i.supportsDescriptors=!!Ei;function wi(e,t){if(1<arguments.length&&"boolean"!=typeof t)throw new TypeError('"allowMissing" argument must be a boolean');var i="$ "+e;if(!(i in Fi))throw new SyntaxError("intrinsic "+e+" does not exist!");if("undefined"==typeof Fi[i]&&!t)throw new TypeError("intrinsic "+e+" exists, but is not available. Please file an issue!");return Fi[i]}function Ai(e,t,i,n){var r=Wi[t];if("function"!=typeof r)throw new qi("unknown record type: "+t);if(!r(e,n))throw new Vi(i+" must be a "+t)}function Pi(e){return 0<=e?1:-1}function Ii(e){try{var t=Xi.call(e);return Ki.test(t)}catch(e){return!1}}function Li(e){if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;if("function"==typeof e&&!e.prototype)return!0;if(Qi)return function(e){try{return!Ii(e)&&(Xi.call(e),!0)}catch(e){return!1}}(e);if(Ii(e))return!1;var t=Yi.call(e);return"[object Function]"===t||"[object GeneratorFunction]"===t}function Oi(e){return null===e||"function"!=typeof e&&"object"!=typeof e}function xi(){var e=rn.ToString(rn.CheckObjectCoercible(this));return an(an(e,sn,""),on,"")}function Di(){return String.prototype.trim&&"​"==="​".trim()?String.prototype.trim:xi}var Ui,Ri=_i,Mi=Object.getOwnPropertyDescriptor?function(){return Object.getOwnPropertyDescriptor(arguments,"callee").get}():function(){throw new TypeError},Ni="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator,Bi=Object.getPrototypeOf||function(e){return e.__proto__},ji="undefined"==typeof Uint8Array?Ui:Bi(Uint8Array),Fi={"$ %Array%":Array,"$ %ArrayBuffer%":"undefined"==typeof ArrayBuffer?Ui:ArrayBuffer,"$ %ArrayBufferPrototype%":"undefined"==typeof ArrayBuffer?Ui:ArrayBuffer.prototype,"$ %ArrayIteratorPrototype%":Ni?Bi([][Symbol.iterator]()):Ui,"$ %ArrayPrototype%":Array.prototype,"$ %ArrayProto_entries%":Array.prototype.entries,"$ %ArrayProto_forEach%":Array.prototype.forEach,"$ %ArrayProto_keys%":Array.prototype.keys,"$ %ArrayProto_values%":Array.prototype.values,"$ %AsyncFromSyncIteratorPrototype%":Ui,"$ %AsyncFunction%":void 0,"$ %AsyncFunctionPrototype%":Ui,"$ %AsyncGenerator%":Ui,"$ %AsyncGeneratorFunction%":void 0,"$ %AsyncGeneratorPrototype%":Ui,"$ %AsyncIteratorPrototype%":Ui,"$ %Atomics%":"undefined"==typeof Atomics?Ui:Atomics,"$ %Boolean%":Boolean,"$ %BooleanPrototype%":Boolean.prototype,"$ %DataView%":"undefined"==typeof DataView?Ui:DataView,"$ %DataViewPrototype%":"undefined"==typeof DataView?Ui:DataView.prototype,"$ %Date%":Date,"$ %DatePrototype%":Date.prototype,"$ %decodeURI%":decodeURI,"$ %decodeURIComponent%":decodeURIComponent,"$ %encodeURI%":encodeURI,"$ %encodeURIComponent%":encodeURIComponent,"$ %Error%":Error,"$ %ErrorPrototype%":Error.prototype,"$ %eval%":eval,"$ %EvalError%":EvalError,"$ %EvalErrorPrototype%":EvalError.prototype,"$ %Float32Array%":"undefined"==typeof Float32Array?Ui:Float32Array,"$ %Float32ArrayPrototype%":"undefined"==typeof Float32Array?Ui:Float32Array.prototype,"$ %Float64Array%":"undefined"==typeof Float64Array?Ui:Float64Array,"$ %Float64ArrayPrototype%":"undefined"==typeof Float64Array?Ui:Float64Array.prototype,"$ %Function%":Function,"$ %FunctionPrototype%":Function.prototype,"$ %Generator%":Ui,"$ %GeneratorFunction%":void 0,"$ %GeneratorPrototype%":Ui,"$ %Int8Array%":"undefined"==typeof Int8Array?Ui:Int8Array,"$ %Int8ArrayPrototype%":"undefined"==typeof Int8Array?Ui:Int8Array.prototype,"$ %Int16Array%":"undefined"==typeof Int16Array?Ui:Int16Array,"$ %Int16ArrayPrototype%":"undefined"==typeof Int16Array?Ui:Int8Array.prototype,"$ %Int32Array%":"undefined"==typeof Int32Array?Ui:Int32Array,"$ %Int32ArrayPrototype%":"undefined"==typeof Int32Array?Ui:Int32Array.prototype,"$ %isFinite%":isFinite,"$ %isNaN%":isNaN,"$ %IteratorPrototype%":Ni?Bi(Bi([][Symbol.iterator]())):Ui,"$ %JSON%":JSON,"$ %JSONParse%":JSON.parse,"$ %Map%":"undefined"==typeof Map?Ui:Map,"$ %MapIteratorPrototype%":"undefined"!=typeof Map&&Ni?Bi((new Map)[Symbol.iterator]()):Ui,"$ %MapPrototype%":"undefined"==typeof Map?Ui:Map.prototype,"$ %Math%":Math,"$ %Number%":Number,"$ %NumberPrototype%":Number.prototype,"$ %Object%":Object,"$ %ObjectPrototype%":Object.prototype,"$ %ObjProto_toString%":Object.prototype.toString,"$ %ObjProto_valueOf%":Object.prototype.valueOf,"$ %parseFloat%":parseFloat,"$ %parseInt%":parseInt,"$ %Promise%":"undefined"==typeof Promise?Ui:Promise,"$ %PromisePrototype%":"undefined"==typeof Promise?Ui:Promise.prototype,"$ %PromiseProto_then%":"undefined"==typeof Promise?Ui:Promise.prototype.then,"$ %Promise_all%":"undefined"==typeof Promise?Ui:Promise.all,"$ %Promise_reject%":"undefined"==typeof Promise?Ui:Promise.reject,"$ %Promise_resolve%":"undefined"==typeof Promise?Ui:Promise.resolve,"$ %Proxy%":"undefined"==typeof Proxy?Ui:Proxy,"$ %RangeError%":RangeError,"$ %RangeErrorPrototype%":RangeError.prototype,"$ %ReferenceError%":ReferenceError,"$ %ReferenceErrorPrototype%":ReferenceError.prototype,"$ %Reflect%":"undefined"==typeof Reflect?Ui:Reflect,"$ %RegExp%":RegExp,"$ %RegExpPrototype%":RegExp.prototype,"$ %Set%":"undefined"==typeof Set?Ui:Set,"$ %SetIteratorPrototype%":"undefined"!=typeof Set&&Ni?Bi((new Set)[Symbol.iterator]()):Ui,"$ %SetPrototype%":"undefined"==typeof Set?Ui:Set.prototype,"$ %SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?Ui:SharedArrayBuffer,"$ %SharedArrayBufferPrototype%":"undefined"==typeof SharedArrayBuffer?Ui:SharedArrayBuffer.prototype,"$ %String%":String,"$ %StringIteratorPrototype%":Ni?Bi(""[Symbol.iterator]()):Ui,"$ %StringPrototype%":String.prototype,"$ %Symbol%":Ni?Symbol:Ui,"$ %SymbolPrototype%":Ni?Symbol.prototype:Ui,"$ %SyntaxError%":SyntaxError,"$ %SyntaxErrorPrototype%":SyntaxError.prototype,"$ %ThrowTypeError%":Mi,"$ %TypedArray%":ji,"$ %TypedArrayPrototype%":ji?ji.prototype:Ui,"$ %TypeError%":TypeError,"$ %TypeErrorPrototype%":TypeError.prototype,"$ %Uint8Array%":"undefined"==typeof Uint8Array?Ui:Uint8Array,"$ %Uint8ArrayPrototype%":"undefined"==typeof Uint8Array?Ui:Uint8Array.prototype,"$ %Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?Ui:Uint8ClampedArray,"$ %Uint8ClampedArrayPrototype%":"undefined"==typeof Uint8ClampedArray?Ui:Uint8ClampedArray.prototype,"$ %Uint16Array%":"undefined"==typeof Uint16Array?Ui:Uint16Array,"$ %Uint16ArrayPrototype%":"undefined"==typeof Uint16Array?Ui:Uint16Array.prototype,"$ %Uint32Array%":"undefined"==typeof Uint32Array?Ui:Uint32Array,"$ %Uint32ArrayPrototype%":"undefined"==typeof Uint32Array?Ui:Uint32Array.prototype,"$ %URIError%":URIError,"$ %URIErrorPrototype%":URIError.prototype,"$ %WeakMap%":"undefined"==typeof WeakMap?Ui:WeakMap,"$ %WeakMapPrototype%":"undefined"==typeof WeakMap?Ui:WeakMap.prototype,"$ %WeakSet%":"undefined"==typeof WeakSet?Ui:WeakSet,"$ %WeakSetPrototype%":"undefined"==typeof WeakSet?Ui:WeakSet.prototype},Hi=ti.call(Function.call,Object.prototype.hasOwnProperty),Vi=wi("%TypeError%"),qi=wi("%SyntaxError%"),Wi={"Property Descriptor":function(e,t){if("Object"!==e.Type(t))return!1;var i={"[[Configurable]]":!0,"[[Enumerable]]":!0,"[[Get]]":!0,"[[Set]]":!0,"[[Value]]":!0,"[[Writable]]":!0};for(var n in t)if(Hi(t,n)&&!i[n])return!1;var r=Hi(t,"[[Value]]"),a=Hi(t,"[[Get]]")||Hi(t,"[[Set]]");if(r&&a)throw new Vi("Property Descriptors may not be both accessor and data descriptors");return!0}},zi=Number.isNaN||function(e){return e!=e},$i=Number.isNaN||function(e){return e!=e},Gi=Number.isFinite||function(e){return"number"==typeof e&&!$i(e)&&e!==1/0&&e!==-1/0},Xi=Function.prototype.toString,Ki=/^\s*class\b/,Yi=Object.prototype.toString,Qi="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,Ji=Object.prototype.toString,Zi=function(e,t){var i;if((i=1<arguments.length?t:"[object Date]"===Ji.call(e)?String:Number)!==String&&i!==Number)throw new TypeError("invalid [[DefaultValue]] hint supplied");var n,r,a=i===String?["toString","valueOf"]:["valueOf","toString"];for(r=0;r<a.length;++r)if(Li(e[a[r]])&&(n=e[a[r]](),Oi(n)))return n;throw new TypeError("No default value")},en=wi("%Object%"),tn=wi("%TypeError%"),nn=wi("%String%"),rn={ToPrimitive:function(e,t){return Oi(e)?e:1<arguments.length?Zi(e,t):Zi(e)},ToBoolean:function(e){return!!e},ToNumber:function(e){return+e},ToInteger:function(e){var t=this.ToNumber(e);return zi(t)?0:0!==t&&Gi(t)?Pi(t)*Math.floor(Math.abs(t)):t},ToInt32:function(e){return this.ToNumber(e)>>0},ToUint32:function(e){return this.ToNumber(e)>>>0},ToUint16:function(e){var t=this.ToNumber(e);return zi(t)||0===t||!Gi(t)?0:function(e,t){var i=e%t;return Math.floor(0<=i?i:i+t)}(Pi(t)*Math.floor(Math.abs(t)),65536)},ToString:function(e){return nn(e)},ToObject:function(e){return this.CheckObjectCoercible(e),en(e)},CheckObjectCoercible:function(e,t){if(null==e)throw new tn(t||"Cannot call method on "+e);return e},IsCallable:Li,SameValue:function(e,t){return e===t?0!==e||1/e==1/t:zi(e)&&zi(t)},Type:function(e){return null===e?"Null":"undefined"==typeof e?"Undefined":"function"==typeof e||"object"==typeof e?"Object":"number"==typeof e?"Number":"boolean"==typeof e?"Boolean":"string"==typeof e?"String":void 0},IsPropertyDescriptor:function(e){if("Object"!==this.Type(e))return!1;var t={"[[Configurable]]":!0,"[[Enumerable]]":!0,"[[Get]]":!0,"[[Set]]":!0,"[[Value]]":!0,"[[Writable]]":!0};for(var i in e)if(Hi(e,i)&&!t[i])return!1;var n=Hi(e,"[[Value]]"),r=Hi(e,"[[Get]]")||Hi(e,"[[Set]]");if(n&&r)throw new tn("Property Descriptors may not be both accessor and data descriptors");return!0},IsAccessorDescriptor:function(e){return"undefined"!=typeof e&&(Ai(this,"Property Descriptor","Desc",e),!(!Hi(e,"[[Get]]")&&!Hi(e,"[[Set]]")))},IsDataDescriptor:function(e){return"undefined"!=typeof e&&(Ai(this,"Property Descriptor","Desc",e),!(!Hi(e,"[[Value]]")&&!Hi(e,"[[Writable]]")))},IsGenericDescriptor:function(e){return"undefined"!=typeof e&&(Ai(this,"Property Descriptor","Desc",e),!this.IsAccessorDescriptor(e)&&!this.IsDataDescriptor(e))},FromPropertyDescriptor:function(e){if("undefined"==typeof e)return e;if(Ai(this,"Property Descriptor","Desc",e),this.IsDataDescriptor(e))return{value:e["[[Value]]"],writable:!!e["[[Writable]]"],enumerable:!!e["[[Enumerable]]"],configurable:!!e["[[Configurable]]"]};if(this.IsAccessorDescriptor(e))return{get:e["[[Get]]"],set:e["[[Set]]"],enumerable:!!e["[[Enumerable]]"],configurable:!!e["[[Configurable]]"]};throw new tn("FromPropertyDescriptor must be called with a fully populated Property Descriptor")},ToPropertyDescriptor:function(e){if("Object"!==this.Type(e))throw new tn("ToPropertyDescriptor requires an object");var t={};if(Hi(e,"enumerable")&&(t["[[Enumerable]]"]=this.ToBoolean(e.enumerable)),Hi(e,"configurable")&&(t["[[Configurable]]"]=this.ToBoolean(e.configurable)),Hi(e,"value")&&(t["[[Value]]"]=e.value),Hi(e,"writable")&&(t["[[Writable]]"]=this.ToBoolean(e.writable)),Hi(e,"get")){var i=e.get;if("undefined"!=typeof i&&!this.IsCallable(i))throw new TypeError("getter must be a function");t["[[Get]]"]=i}if(Hi(e,"set")){var n=e.set;if("undefined"!=typeof n&&!this.IsCallable(n))throw new tn("setter must be a function");t["[[Set]]"]=n}if((Hi(t,"[[Get]]")||Hi(t,"[[Set]]"))&&(Hi(t,"[[Value]]")||Hi(t,"[[Writable]]")))throw new tn("Invalid property descriptor. Cannot both specify accessors and a value or writable attribute");return t}},an=ti.call(Function.call,String.prototype.replace),sn=/^[\x09\x0A\x0B\x0C\x0D\x20\xA0\u1680\u180E\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029\uFEFF]+/,on=/[\x09\x0A\x0B\x0C\x0D\x20\xA0\u1680\u180E\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029\uFEFF]+$/,un=ti.call(Function.call,Di());Ri(un,{getPolyfill:Di,implementation:xi,shim:function(){var e=Di();return Ri(String.prototype,{trim:e},{trim:function(){return String.prototype.trim!==e}}),e}});var ln=un,cn=Object.prototype.toString,hn=Object.prototype.hasOwnProperty,dn=function(e,t,i){if(!Li(t))throw new TypeError("iterator must be a function");var n;3<=arguments.length&&(n=i),"[object Array]"===cn.call(e)?function(e,t,i){for(var n=0,r=e.length;n<r;n++)hn.call(e,n)&&(null==i?t(e[n],n,e):t.call(i,e[n],n,e))}(e,t,n):"string"==typeof e?function(e,t,i){for(var n=0,r=e.length;n<r;n++)null==i?t(e.charAt(n),n,e):t.call(i,e.charAt(n),n,e)}(e,t,n):function(e,t,i){for(var n in e)hn.call(e,n)&&(null==i?t(e[n],n,e):t.call(i,e[n],n,e))}(e,t,n)},pn=function(e){if(!e)return{};var r={};return dn(ln(e).split("\n"),function(e){var t=e.indexOf(":"),i=ln(e.slice(0,t)).toLowerCase(),n=ln(e.slice(t+1));"undefined"==typeof r[i]?r[i]=n:!function(e){return"[object Array]"===Object.prototype.toString.call(e)}(r[i])?r[i]=[r[i],n]:r[i].push(n)}),r},fn=function(){for(var e={},t=0;t<arguments.length;t++){var i=arguments[t];for(var n in i)mn.call(i,n)&&(e[n]=i[n])}return e},mn=Object.prototype.hasOwnProperty;var gn=vn;function yn(e,t,i){var n=e;return Kt(t)?(i=t,"string"==typeof e&&(n={uri:e})):n=fn(t,{uri:e}),n.callback=i,n}function vn(e,t,i){return _n(t=yn(e,t,i))}function _n(n){if("undefined"==typeof n.callback)throw new Error("callback argument missing");var r=!1,a=function(e,t,i){r||(r=!0,n.callback(e,t,i))};function t(e){return clearTimeout(o),e instanceof Error||(e=new Error(""+(e||"Unknown XMLHttpRequest Error"))),e.statusCode=0,a(e,m)}function e(){if(!s){var e;clearTimeout(o),e=n.useXDR&&void 0===u.status?200:1223===u.status?204:u.status;var t=m,i=null;return 0!==e?(t={body:function(){var e=void 0;if(e=u.response?u.response:u.responseText||function(e){if("document"===e.responseType)return e.responseXML;var t=e.responseXML&&"parsererror"===e.responseXML.documentElement.nodeName;return""!==e.responseType||t?null:e.responseXML}(u),f)try{e=JSON.parse(e)}catch(e){}return e}(),statusCode:e,method:c,headers:{},url:l,rawRequest:u},u.getAllResponseHeaders&&(t.headers=pn(u.getAllResponseHeaders()))):i=new Error("Internal XMLHttpRequest Error"),a(i,t,t.body)}}var i,s,o,u=n.xhr||null,l=(u=u||(n.cors||n.useXDR?new vn.XDomainRequest:new vn.XMLHttpRequest)).url=n.uri||n.url,c=u.method=n.method||"GET",h=n.body||n.data,d=u.headers=n.headers||{},p=!!n.sync,f=!1,m={body:void 0,headers:{},statusCode:0,method:c,url:l,rawRequest:u};if("json"in n&&!1!==n.json&&(f=!0,d.accept||d.Accept||(d.Accept="application/json"),"GET"!==c&&"HEAD"!==c&&(d["content-type"]||d["Content-Type"]||(d["Content-Type"]="application/json"),h=JSON.stringify(!0===n.json?h:n.json))),u.onreadystatechange=function(){4===u.readyState&&setTimeout(e,0)},u.onload=e,u.onerror=t,u.onprogress=function(){},u.onabort=function(){s=!0},u.ontimeout=t,u.open(c,l,!p,n.username,n.password),p||(u.withCredentials=!!n.withCredentials),!p&&0<n.timeout&&(o=setTimeout(function(){if(!s){s=!0,u.abort("timeout");var e=new Error("XMLHttpRequest timeout");e.code="ETIMEDOUT",t(e)}},n.timeout)),u.setRequestHeader)for(i in d)d.hasOwnProperty(i)&&u.setRequestHeader(i,d[i]);else if(n.headers&&!function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}(n.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in n&&(u.responseType=n.responseType),"beforeSend"in n&&"function"==typeof n.beforeSend&&n.beforeSend(u),u.send(h||null),u}vn.XMLHttpRequest=v.XMLHttpRequest||function(){},vn.XDomainRequest="withCredentials"in new vn.XMLHttpRequest?vn.XMLHttpRequest:v.XDomainRequest,function(e,t){for(var i=0;i<e.length;i++)t(e[i])}(["get","put","post","patch","head","delete"],function(n){vn["delete"===n?"del":n]=function(e,t,i){return(t=yn(e,t,i)).method=n.toUpperCase(),_n(t)}});function bn(e,t){var i=new v.WebVTT.Parser(v,v.vttjs,v.WebVTT.StringDecoder()),n=[];i.oncue=function(e){t.addCue(e)},i.onparsingerror=function(e){n.push(e)},i.onflush=function(){t.trigger({type:"loadeddata",target:t})},i.parse(e),0<n.length&&(v.console&&v.console.groupCollapsed&&v.console.groupCollapsed("Text Track parsing errors for "+t.src),n.forEach(function(e){return p.error(e)}),v.console&&v.console.groupEnd&&v.console.groupEnd()),i.flush()}var Tn=function(l){function e(e){var t;if(void 0===e&&(e={}),!e.tech)throw new Error("A tech was not provided.");var i=Oe(e,{kind:zt[e.kind]||"subtitles",language:e.language||e.srclang||""}),n=$t[i.mode]||"disabled",r=i.default;"metadata"!==i.kind&&"chapters"!==i.kind||(n="hidden"),(t=l.call(this,i)||this).tech_=i.tech,t.cues_=[],t.activeCues_=[];var a=new Vt(t.cues_),s=new Vt(t.activeCues_),o=!1,u=pe(Me(t),function(){this.activeCues=this.activeCues,o&&(this.trigger("cuechange"),o=!1)});return"disabled"!==n&&t.tech_.ready(function(){t.tech_.on("timeupdate",u)},!0),Object.defineProperties(Me(t),{default:{get:function(){return r},set:function(){}},mode:{get:function(){return n},set:function(e){var t=this;$t[e]&&("disabled"!==(n=e)?this.tech_.ready(function(){t.tech_.on("timeupdate",u)},!0):this.tech_.off("timeupdate",u),this.trigger("modechange"))}},cues:{get:function(){return this.loaded_?a:null},set:function(){}},activeCues:{get:function(){if(!this.loaded_)return null;if(0===this.cues.length)return s;for(var e=this.tech_.currentTime(),t=[],i=0,n=this.cues.length;i<n;i++){var r=this.cues[i];r.startTime<=e&&r.endTime>=e?t.push(r):r.startTime===r.endTime&&r.startTime<=e&&r.startTime+.5>=e&&t.push(r)}if(o=!1,t.length!==this.activeCues_.length)o=!0;else for(var a=0;a<t.length;a++)-1===this.activeCues_.indexOf(t[a])&&(o=!0);return this.activeCues_=t,s.setCues_(this.activeCues_),s},set:function(){}}}),i.src?(t.src=i.src,function(e,n){var t={uri:e},i=Nt(e);i&&(t.cors=i),gn(t,pe(this,function(e,t,i){if(e)return p.error(e,t);n.loaded_=!0,"function"!=typeof v.WebVTT?n.tech_&&n.tech_.any(["vttjsloaded","vttjserror"],function(e){if("vttjserror"!==e.type)return bn(i,n);p.error("vttjs failed to load, stopping trying to process "+n.src)}):bn(i,n)}))}(i.src,Me(t))):t.loaded_=!0,t}De(e,l);var t=e.prototype;return t.addCue=function(e){var t=e;if(v.vttjs&&!(e instanceof v.vttjs.VTTCue)){for(var i in t=new v.vttjs.VTTCue(e.startTime,e.endTime,e.text),e)i in t||(t[i]=e[i]);t.id=e.id,t.originalCue_=e}for(var n=this.tech_.textTracks(),r=0;r<n.length;r++)n[r]!==this&&n[r].removeCue(t);this.cues_.push(t),this.cues.setCues_(this.cues_)},t.removeCue=function(e){for(var t=this.cues_.length;t--;){var i=this.cues_[t];if(i===e||i.originalCue_&&i.originalCue_===e){this.cues_.splice(t,1),this.cues.setCues_(this.cues_);break}}},e}(Gt);Tn.prototype.allowedEvents_={cuechange:"cuechange"};var Sn=function(r){function e(e){var t;void 0===e&&(e={});var i=Oe(e,{kind:Wt[e.kind]||""});t=r.call(this,i)||this;var n=!1;return Object.defineProperty(Me(t),"enabled",{get:function(){return n},set:function(e){"boolean"==typeof e&&e!==n&&(n=e,this.trigger("enabledchange"))}}),i.enabled&&(t.enabled=i.enabled),t.loaded_=!0,t}return De(e,r),e}(Gt),kn=function(r){function e(e){var t;void 0===e&&(e={});var i=Oe(e,{kind:qt[e.kind]||""});t=r.call(this,i)||this;var n=!1;return Object.defineProperty(Me(t),"selected",{get:function(){return n},set:function(e){"boolean"==typeof e&&e!==n&&(n=e,this.trigger("selectedchange"))}}),i.selected&&(t.selected=i.selected),t}return De(e,r),e}(Gt),Cn=function(r){function e(e){var t,i;void 0===e&&(e={}),t=r.call(this)||this;var n=new Tn(e);return t.kind=n.kind,t.src=n.src,t.srclang=n.language,t.label=n.label,t.default=n.default,Object.defineProperties(Me(t),{readyState:{get:function(){return i}},track:{get:function(){return n}}}),i=0,n.addEventListener("loadeddata",function(){i=2,t.trigger({type:"load",target:Me(t)})}),t}return De(e,r),e}(me);Cn.prototype.allowedEvents_={load:"load"},Cn.NONE=0,Cn.LOADING=1,Cn.LOADED=2,Cn.ERROR=3;var En={audio:{ListClass:Bt,TrackClass:Sn,capitalName:"Audio"},video:{ListClass:jt,TrackClass:kn,capitalName:"Video"},text:{ListClass:Ft,TrackClass:Tn,capitalName:"Text"}};Object.keys(En).forEach(function(e){En[e].getterName=e+"Tracks",En[e].privateName=e+"Tracks_"});var wn={remoteText:{ListClass:Ft,TrackClass:Tn,capitalName:"RemoteText",getterName:"remoteTextTracks",privateName:"remoteTextTracks_"},remoteTextEl:{ListClass:Ht,TrackClass:Cn,capitalName:"RemoteTextTrackEls",getterName:"remoteTextTrackEls",privateName:"remoteTextTrackEls_"}},An=Oe(En,wn);wn.names=Object.keys(wn),En.names=Object.keys(En),An.names=[].concat(wn.names).concat(En.names);var Pn=Object.create||function(e){if(1!==arguments.length)throw new Error("Object.create shim only accepts one parameter.");return In.prototype=e,new In};function In(){}function Ln(e,t){this.name="ParsingError",this.code=e.code,this.message=t||e.message}function On(e){function t(e,t,i,n){return 3600*(0|e)+60*(0|t)+(0|i)+(0|n)/1e3}var i=e.match(/^(\d+):(\d{2})(:\d{2})?\.(\d{3})/);return i?i[3]?t(i[1],i[2],i[3].replace(":",""),i[4]):59<i[1]?t(i[1],i[2],0,i[4]):t(0,i[1],i[2],i[4]):null}function xn(){this.values=Pn(null)}function Dn(e,t,i,n){var r=n?e.split(n):[e];for(var a in r)if("string"==typeof r[a]){var s=r[a].split(i);if(2===s.length)t(s[0],s[1])}}function Un(t,e,a){var i,n,s,r=t;function o(){var e=On(t);if(null===e)throw new Ln(Ln.Errors.BadTimeStamp,"Malformed timestamp: "+r);return t=t.replace(/^[^\sa-zA-Z-]+/,""),e}function u(){t=t.replace(/^\s+/,"")}if(u(),e.startTime=o(),u(),"--\x3e"!==t.substr(0,3))throw new Ln(Ln.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '--\x3e'): "+r);t=t.substr(3),u(),e.endTime=o(),u(),i=t,n=e,s=new xn,Dn(i,function(e,t){switch(e){case"region":for(var i=a.length-1;0<=i;i--)if(a[i].id===t){s.set(e,a[i].region);break}break;case"vertical":s.alt(e,t,["rl","lr"]);break;case"line":var n=t.split(","),r=n[0];s.integer(e,r),s.percent(e,r)&&s.set("snapToLines",!1),s.alt(e,r,["auto"]),2===n.length&&s.alt("lineAlign",n[1],["start","middle","end"]);break;case"position":n=t.split(","),s.percent(e,n[0]),2===n.length&&s.alt("positionAlign",n[1],["start","middle","end"]);break;case"size":s.percent(e,t);break;case"align":s.alt(e,t,["start","middle","end","left","right"])}},/:/,/\s/),n.region=s.get("region",null),n.vertical=s.get("vertical",""),n.line=s.get("line","auto"),n.lineAlign=s.get("lineAlign","start"),n.snapToLines=s.get("snapToLines",!0),n.size=s.get("size",100),n.align=s.get("align","middle"),n.position=s.get("position",{start:0,left:0,middle:50,end:100,right:100},n.align),n.positionAlign=s.get("positionAlign",{start:"start",left:"start",middle:"middle",end:"end",right:"end"},n.align)}((Ln.prototype=Pn(Error.prototype)).constructor=Ln).Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}},xn.prototype={set:function(e,t){this.get(e)||""===t||(this.values[e]=t)},get:function(e,t,i){return i?this.has(e)?this.values[e]:t[i]:this.has(e)?this.values[e]:t},has:function(e){return e in this.values},alt:function(e,t,i){for(var n=0;n<i.length;++n)if(t===i[n]){this.set(e,t);break}},integer:function(e,t){/^-?\d+$/.test(t)&&this.set(e,parseInt(t,10))},percent:function(e,t){return!!(t.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&0<=(t=parseFloat(t))&&t<=100)&&(this.set(e,t),!0)}};var Rn={"&amp;":"&","&lt;":"<","&gt;":">","&lrm;":"‎","&rlm;":"‏","&nbsp;":" "},Mn={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},Nn={v:"title",lang:"lang"},Bn={rt:"ruby"};function jn(a,i){function e(){if(!i)return null;var e,t=i.match(/^([^<]*)(<[^>]*>?)?/);return e=t[1]?t[1]:t[2],i=i.substr(e.length),e}function t(e){return Rn[e]}function n(e){for(;f=e.match(/&(amp|lt|gt|lrm|rlm|nbsp);/);)e=e.replace(f[0],t);return e}function r(e,t){var i=Mn[e];if(!i)return null;var n=a.document.createElement(i);n.localName=i;var r=Nn[e];return r&&t&&(n[r]=t.trim()),n}for(var s,o,u,l=a.document.createElement("div"),c=l,h=[];null!==(s=e());)if("<"!==s[0])c.appendChild(a.document.createTextNode(n(s)));else{if("/"===s[1]){h.length&&h[h.length-1]===s.substr(2).replace(">","")&&(h.pop(),c=c.parentNode);continue}var d,p=On(s.substr(1,s.length-2));if(p){d=a.document.createProcessingInstruction("timestamp",p),c.appendChild(d);continue}var f=s.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);if(!f)continue;if(!(d=r(f[1],f[3])))continue;if(o=c,Bn[(u=d).localName]&&Bn[u.localName]!==o.localName)continue;f[2]&&(d.className=f[2].substr(1).replace("."," ")),h.push(f[1]),c.appendChild(d),c=d}return l}var Fn=[[1470,1470],[1472,1472],[1475,1475],[1478,1478],[1488,1514],[1520,1524],[1544,1544],[1547,1547],[1549,1549],[1563,1563],[1566,1610],[1645,1647],[1649,1749],[1765,1766],[1774,1775],[1786,1805],[1807,1808],[1810,1839],[1869,1957],[1969,1969],[1984,2026],[2036,2037],[2042,2042],[2048,2069],[2074,2074],[2084,2084],[2088,2088],[2096,2110],[2112,2136],[2142,2142],[2208,2208],[2210,2220],[8207,8207],[64285,64285],[64287,64296],[64298,64310],[64312,64316],[64318,64318],[64320,64321],[64323,64324],[64326,64449],[64467,64829],[64848,64911],[64914,64967],[65008,65020],[65136,65140],[65142,65276],[67584,67589],[67592,67592],[67594,67637],[67639,67640],[67644,67644],[67647,67669],[67671,67679],[67840,67867],[67872,67897],[67903,67903],[67968,68023],[68030,68031],[68096,68096],[68112,68115],[68117,68119],[68121,68147],[68160,68167],[68176,68184],[68192,68223],[68352,68405],[68416,68437],[68440,68466],[68472,68479],[68608,68680],[126464,126467],[126469,126495],[126497,126498],[126500,126500],[126503,126503],[126505,126514],[126516,126519],[126521,126521],[126523,126523],[126530,126530],[126535,126535],[126537,126537],[126539,126539],[126541,126543],[126545,126546],[126548,126548],[126551,126551],[126553,126553],[126555,126555],[126557,126557],[126559,126559],[126561,126562],[126564,126564],[126567,126570],[126572,126578],[126580,126583],[126585,126588],[126590,126590],[126592,126601],[126603,126619],[126625,126627],[126629,126633],[126635,126651],[1114109,1114109]];function Hn(e){for(var t=0;t<Fn.length;t++){var i=Fn[t];if(e>=i[0]&&e<=i[1])return!0}return!1}function Vn(){}function qn(e,t,i){Vn.call(this),this.cue=t,this.cueDiv=jn(e,t.text);var n={color:"rgba(255, 255, 255, 1)",backgroundColor:"rgba(0, 0, 0, 0.8)",position:"relative",left:0,right:0,top:0,bottom:0,display:"inline",writingMode:""===t.vertical?"horizontal-tb":"lr"===t.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext"};this.applyStyles(n,this.cueDiv),this.div=e.document.createElement("div"),n={direction:function(e){var t=[],i="";if(!e||!e.childNodes)return"ltr";function r(e,t){for(var i=t.childNodes.length-1;0<=i;i--)e.push(t.childNodes[i])}function a(e){if(!e||!e.length)return null;var t=e.pop(),i=t.textContent||t.innerText;if(i){var n=i.match(/^.*(\n|\r)/);return n?n[e.length=0]:i}return"ruby"===t.tagName?a(e):t.childNodes?(r(e,t),a(e)):void 0}for(r(t,e);i=a(t);)for(var n=0;n<i.length;n++)if(Hn(i.charCodeAt(n)))return"rtl";return"ltr"}(this.cueDiv),writingMode:""===t.vertical?"horizontal-tb":"lr"===t.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext",textAlign:"middle"===t.align?"center":t.align,font:i.font,whiteSpace:"pre-line",position:"absolute"},this.applyStyles(n),this.div.appendChild(this.cueDiv);var r=0;switch(t.positionAlign){case"start":r=t.position;break;case"middle":r=t.position-t.size/2;break;case"end":r=t.position-t.size}""===t.vertical?this.applyStyles({left:this.formatStyle(r,"%"),width:this.formatStyle(t.size,"%")}):this.applyStyles({top:this.formatStyle(r,"%"),height:this.formatStyle(t.size,"%")}),this.move=function(e){this.applyStyles({top:this.formatStyle(e.top,"px"),bottom:this.formatStyle(e.bottom,"px"),left:this.formatStyle(e.left,"px"),right:this.formatStyle(e.right,"px"),height:this.formatStyle(e.height,"px"),width:this.formatStyle(e.width,"px")})}}function Wn(e){var t,i,n,r;if(e.div){i=e.div.offsetHeight,n=e.div.offsetWidth,r=e.div.offsetTop;var a=(a=e.div.childNodes)&&(a=a[0])&&a.getClientRects&&a.getClientRects();e=e.div.getBoundingClientRect(),t=a?Math.max(a[0]&&a[0].height||0,e.height/a.length):0}this.left=e.left,this.right=e.right,this.top=e.top||r,this.height=e.height||i,this.bottom=e.bottom||r+(e.height||i),this.width=e.width||n,this.lineHeight=void 0!==t?t:e.lineHeight}function zn(e,t,o,u){var i=new Wn(t),n=t.cue,r=function(e){if("number"==typeof e.line&&(e.snapToLines||0<=e.line&&e.line<=100))return e.line;if(!e.track||!e.track.textTrackList||!e.track.textTrackList.mediaElement)return-1;for(var t=e.track,i=t.textTrackList,n=0,r=0;r<i.length&&i[r]!==t;r++)"showing"===i[r].mode&&n++;return-1*++n}(n),a=[];if(n.snapToLines){var s;switch(n.vertical){case"":a=["+y","-y"],s="height";break;case"rl":a=["+x","-x"],s="width";break;case"lr":a=["-x","+x"],s="width"}var l=i.lineHeight,c=l*Math.round(r),h=o[s]+l,d=a[0];Math.abs(c)>h&&(c=c<0?-1:1,c*=Math.ceil(h/l)*l),r<0&&(c+=""===n.vertical?o.height:o.width,a=a.reverse()),i.move(d,c)}else{var p=i.lineHeight/o.height*100;switch(n.lineAlign){case"middle":r-=p/2;break;case"end":r-=p}switch(n.vertical){case"":t.applyStyles({top:t.formatStyle(r,"%")});break;case"rl":t.applyStyles({left:t.formatStyle(r,"%")});break;case"lr":t.applyStyles({right:t.formatStyle(r,"%")})}a=["+y","-x","+x","-y"],i=new Wn(t)}var f=function(e,t){for(var i,n=new Wn(e),r=1,a=0;a<t.length;a++){for(;e.overlapsOppositeAxis(o,t[a])||e.within(o)&&e.overlapsAny(u);)e.move(t[a]);if(e.within(o))return e;var s=e.intersectPercentage(o);s<r&&(i=new Wn(e),r=s),e=new Wn(n)}return i||n}(i,a);t.move(f.toCSSCompatValues(o))}function $n(){}Vn.prototype.applyStyles=function(e,t){for(var i in t=t||this.div,e)e.hasOwnProperty(i)&&(t.style[i]=e[i])},Vn.prototype.formatStyle=function(e,t){return 0===e?0:e+t},(qn.prototype=Pn(Vn.prototype)).constructor=qn,Wn.prototype.move=function(e,t){switch(t=void 0!==t?t:this.lineHeight,e){case"+x":this.left+=t,this.right+=t;break;case"-x":this.left-=t,this.right-=t;break;case"+y":this.top+=t,this.bottom+=t;break;case"-y":this.top-=t,this.bottom-=t}},Wn.prototype.overlaps=function(e){return this.left<e.right&&this.right>e.left&&this.top<e.bottom&&this.bottom>e.top},Wn.prototype.overlapsAny=function(e){for(var t=0;t<e.length;t++)if(this.overlaps(e[t]))return!0;return!1},Wn.prototype.within=function(e){return this.top>=e.top&&this.bottom<=e.bottom&&this.left>=e.left&&this.right<=e.right},Wn.prototype.overlapsOppositeAxis=function(e,t){switch(t){case"+x":return this.left<e.left;case"-x":return this.right>e.right;case"+y":return this.top<e.top;case"-y":return this.bottom>e.bottom}},Wn.prototype.intersectPercentage=function(e){return Math.max(0,Math.min(this.right,e.right)-Math.max(this.left,e.left))*Math.max(0,Math.min(this.bottom,e.bottom)-Math.max(this.top,e.top))/(this.height*this.width)},Wn.prototype.toCSSCompatValues=function(e){return{top:this.top-e.top,bottom:e.bottom-this.bottom,left:this.left-e.left,right:e.right-this.right,height:this.height,width:this.width}},Wn.getSimpleBoxPosition=function(e){var t=e.div?e.div.offsetHeight:e.tagName?e.offsetHeight:0,i=e.div?e.div.offsetWidth:e.tagName?e.offsetWidth:0,n=e.div?e.div.offsetTop:e.tagName?e.offsetTop:0;return{left:(e=e.div?e.div.getBoundingClientRect():e.tagName?e.getBoundingClientRect():e).left,right:e.right,top:e.top||n,height:e.height||t,bottom:e.bottom||n+(e.height||t),width:e.width||i}},$n.StringDecoder=function(){return{decode:function(e){if(!e)return"";if("string"!=typeof e)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(e))}}},$n.convertCueToDOMTree=function(e,t){return e&&t?jn(e,t):null};$n.processCues=function(n,r,e){if(!n||!r||!e)return null;for(;e.firstChild;)e.removeChild(e.firstChild);var a=n.document.createElement("div");if(a.style.position="absolute",a.style.left="0",a.style.right="0",a.style.top="0",a.style.bottom="0",a.style.margin="1.5%",e.appendChild(a),function(e){for(var t=0;t<e.length;t++)if(e[t].hasBeenReset||!e[t].displayState)return!0;return!1}(r)){var s=[],o=Wn.getSimpleBoxPosition(a),u={font:Math.round(.05*o.height*100)/100+"px sans-serif"};!function(){for(var e,t,i=0;i<r.length;i++)t=r[i],e=new qn(n,t,u),a.appendChild(e.div),zn(0,e,o,s),t.displayState=e.div,s.push(Wn.getSimpleBoxPosition(e))}()}else for(var t=0;t<r.length;t++)a.appendChild(r[t].displayState)},($n.Parser=function(e,t,i){i||(i=t,t={}),t=t||{},this.window=e,this.vttjs=t,this.state="INITIAL",this.buffer="",this.decoder=i||new TextDecoder("utf8"),this.regionList=[]}).prototype={reportOrThrowError:function(e){if(!(e instanceof Ln))throw e;this.onparsingerror&&this.onparsingerror(e)},parse:function(e){var n=this;function t(){for(var e=n.buffer,t=0;t<e.length&&"\r"!==e[t]&&"\n"!==e[t];)++t;var i=e.substr(0,t);return"\r"===e[t]&&++t,"\n"===e[t]&&++t,n.buffer=e.substr(t),i}function i(e){e.match(/X-TIMESTAMP-MAP/)?Dn(e,function(e,t){switch(e){case"X-TIMESTAMP-MAP":!function(e){var i=new xn;Dn(e,function(e,t){switch(e){case"MPEGT":i.integer(e+"S",t);break;case"LOCA":i.set(e+"L",On(t))}},/[^\d]:/,/,/),n.ontimestampmap&&n.ontimestampmap({MPEGTS:i.get("MPEGTS"),LOCAL:i.get("LOCAL")})}(t)}},/=/):Dn(e,function(e,t){switch(e){case"Region":!function(e){var r=new xn;if(Dn(e,function(e,t){switch(e){case"id":r.set(e,t);break;case"width":r.percent(e,t);break;case"lines":r.integer(e,t);break;case"regionanchor":case"viewportanchor":var i=t.split(",");if(2!==i.length)break;var n=new xn;if(n.percent("x",i[0]),n.percent("y",i[1]),!n.has("x")||!n.has("y"))break;r.set(e+"X",n.get("x")),r.set(e+"Y",n.get("y"));break;case"scroll":r.alt(e,t,["up"])}},/=/,/\s/),r.has("id")){var t=new(n.vttjs.VTTRegion||n.window.VTTRegion);t.width=r.get("width",100),t.lines=r.get("lines",3),t.regionAnchorX=r.get("regionanchorX",0),t.regionAnchorY=r.get("regionanchorY",100),t.viewportAnchorX=r.get("viewportanchorX",0),t.viewportAnchorY=r.get("viewportanchorY",100),t.scroll=r.get("scroll",""),n.onregion&&n.onregion(t),n.regionList.push({id:r.get("id"),region:t})}}(t)}},/:/)}e&&(n.buffer+=n.decoder.decode(e,{stream:!0}));try{var r;if("INITIAL"===n.state){if(!/\r\n|\n/.test(n.buffer))return this;var a=(r=t()).match(/^WEBVTT([ \t].*)?$/);if(!a||!a[0])throw new Ln(Ln.Errors.BadSignature);n.state="HEADER"}for(var s=!1;n.buffer;){if(!/\r\n|\n/.test(n.buffer))return this;switch(s?s=!1:r=t(),n.state){case"HEADER":/:/.test(r)?i(r):r||(n.state="ID");continue;case"NOTE":r||(n.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(r)){n.state="NOTE";break}if(!r)continue;if(n.cue=new(n.vttjs.VTTCue||n.window.VTTCue)(0,0,""),n.state="CUE",-1===r.indexOf("--\x3e")){n.cue.id=r;continue}case"CUE":try{Un(r,n.cue,n.regionList)}catch(e){n.reportOrThrowError(e),n.cue=null,n.state="BADCUE";continue}n.state="CUETEXT";continue;case"CUETEXT":var o=-1!==r.indexOf("--\x3e");if(!r||o&&(s=!0)){n.oncue&&n.oncue(n.cue),n.cue=null,n.state="ID";continue}n.cue.text&&(n.cue.text+="\n"),n.cue.text+=r;continue;case"BADCUE":r||(n.state="ID");continue}}}catch(e){n.reportOrThrowError(e),"CUETEXT"===n.state&&n.cue&&n.oncue&&n.oncue(n.cue),n.cue=null,n.state="INITIAL"===n.state?"BADWEBVTT":"BADCUE"}return this},flush:function(){var t=this;try{if(t.buffer+=t.decoder.decode(),!t.cue&&"HEADER"!==t.state||(t.buffer+="\n\n",t.parse()),"INITIAL"===t.state)throw new Ln(Ln.Errors.BadSignature)}catch(e){t.reportOrThrowError(e)}return t.onflush&&t.onflush(),this}};var Gn=$n,Xn={"":1,lr:1,rl:1},Kn={start:1,middle:1,end:1,left:1,right:1};function Yn(e){return"string"==typeof e&&(!!Kn[e.toLowerCase()]&&e.toLowerCase())}function Qn(e,t,i){this.hasBeenReset=!1;var n="",r=!1,a=e,s=t,o=i,u=null,l="",c=!0,h="auto",d="start",p=50,f="middle",m=50,g="middle";Object.defineProperties(this,{id:{enumerable:!0,get:function(){return n},set:function(e){n=""+e}},pauseOnExit:{enumerable:!0,get:function(){return r},set:function(e){r=!!e}},startTime:{enumerable:!0,get:function(){return a},set:function(e){if("number"!=typeof e)throw new TypeError("Start time must be set to a number.");a=e,this.hasBeenReset=!0}},endTime:{enumerable:!0,get:function(){return s},set:function(e){if("number"!=typeof e)throw new TypeError("End time must be set to a number.");s=e,this.hasBeenReset=!0}},text:{enumerable:!0,get:function(){return o},set:function(e){o=""+e,this.hasBeenReset=!0}},region:{enumerable:!0,get:function(){return u},set:function(e){u=e,this.hasBeenReset=!0}},vertical:{enumerable:!0,get:function(){return l},set:function(e){var t=function(e){return"string"==typeof e&&(!!Xn[e.toLowerCase()]&&e.toLowerCase())}(e);if(!1===t)throw new SyntaxError("An invalid or illegal string was specified.");l=t,this.hasBeenReset=!0}},snapToLines:{enumerable:!0,get:function(){return c},set:function(e){c=!!e,this.hasBeenReset=!0}},line:{enumerable:!0,get:function(){return h},set:function(e){if("number"!=typeof e&&"auto"!==e)throw new SyntaxError("An invalid number or illegal string was specified.");h=e,this.hasBeenReset=!0}},lineAlign:{enumerable:!0,get:function(){return d},set:function(e){var t=Yn(e);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");d=t,this.hasBeenReset=!0}},position:{enumerable:!0,get:function(){return p},set:function(e){if(e<0||100<e)throw new Error("Position must be between 0 and 100.");p=e,this.hasBeenReset=!0}},positionAlign:{enumerable:!0,get:function(){return f},set:function(e){var t=Yn(e);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");f=t,this.hasBeenReset=!0}},size:{enumerable:!0,get:function(){return m},set:function(e){if(e<0||100<e)throw new Error("Size must be between 0 and 100.");m=e,this.hasBeenReset=!0}},align:{enumerable:!0,get:function(){return g},set:function(e){var t=Yn(e);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");g=t,this.hasBeenReset=!0}}}),this.displayState=void 0}Qn.prototype.getCueAsHTML=function(){return WebVTT.convertCueToDOMTree(window,this.text)};var Jn=Qn,Zn={"":!0,up:!0};function er(e){return"number"==typeof e&&0<=e&&e<=100}function tr(){var t=100,i=3,n=0,r=100,a=0,s=100,o="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return t},set:function(e){if(!er(e))throw new Error("Width must be between 0 and 100.");t=e}},lines:{enumerable:!0,get:function(){return i},set:function(e){if("number"!=typeof e)throw new TypeError("Lines must be set to a number.");i=e}},regionAnchorY:{enumerable:!0,get:function(){return r},set:function(e){if(!er(e))throw new Error("RegionAnchorX must be between 0 and 100.");r=e}},regionAnchorX:{enumerable:!0,get:function(){return n},set:function(e){if(!er(e))throw new Error("RegionAnchorY must be between 0 and 100.");n=e}},viewportAnchorY:{enumerable:!0,get:function(){return s},set:function(e){if(!er(e))throw new Error("ViewportAnchorY must be between 0 and 100.");s=e}},viewportAnchorX:{enumerable:!0,get:function(){return a},set:function(e){if(!er(e))throw new Error("ViewportAnchorX must be between 0 and 100.");a=e}},scroll:{enumerable:!0,get:function(){return o},set:function(e){var t=function(e){return"string"==typeof e&&(!!Zn[e.toLowerCase()]&&e.toLowerCase())}(e);if(!1===t)throw new SyntaxError("An invalid or illegal string was specified.");o=t}}})}var ir=wt(function(e){var t=e.exports={WebVTT:Gn,VTTCue:Jn,VTTRegion:tr};v.vttjs=t,v.WebVTT=t.WebVTT;var i=t.VTTCue,n=t.VTTRegion,r=v.VTTCue,a=v.VTTRegion;t.shim=function(){v.VTTCue=i,v.VTTRegion=n},t.restore=function(){v.VTTCue=r,v.VTTRegion=a},v.VTTCue||t.shim()});ir.WebVTT,ir.VTTCue,ir.VTTRegion;var nr=function(t){function i(i,e){var n;return void 0===i&&(i={}),void 0===e&&(e=function(){}),i.reportTouchActivity=!1,(n=t.call(this,null,i,e)||this).hasStarted_=!1,n.on("playing",function(){this.hasStarted_=!0}),n.on("loadstart",function(){this.hasStarted_=!1}),An.names.forEach(function(e){var t=An[e];i&&i[t.getterName]&&(n[t.privateName]=i[t.getterName])}),n.featuresProgressEvents||n.manualProgressOn(),n.featuresTimeupdateEvents||n.manualTimeUpdatesOn(),["Text","Audio","Video"].forEach(function(e){!1===i["native"+e+"Tracks"]&&(n["featuresNative"+e+"Tracks"]=!1)}),!1===i.nativeCaptions||!1===i.nativeTextTracks?n.featuresNativeTextTracks=!1:!0!==i.nativeCaptions&&!0!==i.nativeTextTracks||(n.featuresNativeTextTracks=!0),n.featuresNativeTextTracks||n.emulateTextTracks(),n.autoRemoteTextTracks_=new An.text.ListClass,n.initTrackListeners(),i.nativeControlsForTouch||n.emitTapEvents(),n.constructor&&(n.name_=n.constructor.name||"Unknown Tech"),n}De(i,t);var e=i.prototype;return e.triggerSourceset=function(e){var t=this;this.isReady_||this.one("ready",function(){return t.setTimeout(function(){return t.triggerSourceset(e)},1)}),this.trigger({src:e,type:"sourceset"})},e.manualProgressOn=function(){this.on("durationchange",this.onDurationChange),this.manualProgress=!0,this.one("ready",this.trackProgress)},e.manualProgressOff=function(){this.manualProgress=!1,this.stopTrackingProgress(),this.off("durationchange",this.onDurationChange)},e.trackProgress=function(e){this.stopTrackingProgress(),this.progressInterval=this.setInterval(pe(this,function(){var e=this.bufferedPercent();this.bufferedPercent_!==e&&this.trigger("progress"),1===(this.bufferedPercent_=e)&&this.stopTrackingProgress()}),500)},e.onDurationChange=function(e){this.duration_=this.duration()},e.buffered=function(){return ct(0,0)},e.bufferedPercent=function(){return ht(this.buffered(),this.duration_)},e.stopTrackingProgress=function(){this.clearInterval(this.progressInterval)},e.manualTimeUpdatesOn=function(){this.manualTimeUpdates=!0,this.on("play",this.trackCurrentTime),this.on("pause",this.stopTrackingCurrentTime)},e.manualTimeUpdatesOff=function(){this.manualTimeUpdates=!1,this.stopTrackingCurrentTime(),this.off("play",this.trackCurrentTime),this.off("pause",this.stopTrackingCurrentTime)},e.trackCurrentTime=function(){this.currentTimeInterval&&this.stopTrackingCurrentTime(),this.currentTimeInterval=this.setInterval(function(){this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},250)},e.stopTrackingCurrentTime=function(){this.clearInterval(this.currentTimeInterval),this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.dispose=function(){this.clearTracks(En.names),this.manualProgress&&this.manualProgressOff(),this.manualTimeUpdates&&this.manualTimeUpdatesOff(),t.prototype.dispose.call(this)},e.clearTracks=function(e){var r=this;(e=[].concat(e)).forEach(function(e){for(var t=r[e+"Tracks"]()||[],i=t.length;i--;){var n=t[i];"text"===e&&r.removeRemoteTextTrack(n),t.removeTrack(n)}})},e.cleanupAutoTextTracks=function(){for(var e=this.autoRemoteTextTracks_||[],t=e.length;t--;){var i=e[t];this.removeRemoteTextTrack(i)}},e.reset=function(){},e.error=function(e){return void 0!==e&&(this.error_=new vt(e),this.trigger("error")),this.error_},e.played=function(){return this.hasStarted_?ct(0,0):ct()},e.setCurrentTime=function(){this.manualTimeUpdates&&this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.initTrackListeners=function(){var r=this;En.names.forEach(function(e){function t(){r.trigger(e+"trackchange")}var i=En[e],n=r[i.getterName]();n.addEventListener("removetrack",t),n.addEventListener("addtrack",t),r.on("dispose",function(){n.removeEventListener("removetrack",t),n.removeEventListener("addtrack",t)})})},e.addWebVttScript_=function(){var e=this;if(!v.WebVTT)if(h.body.contains(this.el())){if(!this.options_["vtt.js"]&&o(ir)&&0<Object.keys(ir).length)return void this.trigger("vttjsloaded");var t=h.createElement("script");t.src=this.options_["vtt.js"]||"https://vjs.zencdn.net/vttjs/0.14.1/vtt.min.js",t.onload=function(){e.trigger("vttjsloaded")},t.onerror=function(){e.trigger("vttjserror")},this.on("dispose",function(){t.onload=null,t.onerror=null}),v.WebVTT=!0,this.el().parentNode.appendChild(t)}else this.ready(this.addWebVttScript_)},e.emulateTextTracks=function(){function t(e){return n.addTrack(e.track)}function i(e){return n.removeTrack(e.track)}var e=this,n=this.textTracks(),r=this.remoteTextTracks();r.on("addtrack",t),r.on("removetrack",i),this.addWebVttScript_();function a(){return e.trigger("texttrackchange")}function s(){a();for(var e=0;e<n.length;e++){var t=n[e];t.removeEventListener("cuechange",a),"showing"===t.mode&&t.addEventListener("cuechange",a)}}s(),n.addEventListener("change",s),n.addEventListener("addtrack",s),n.addEventListener("removetrack",s),this.on("dispose",function(){r.off("addtrack",t),r.off("removetrack",i),n.removeEventListener("change",s),n.removeEventListener("addtrack",s),n.removeEventListener("removetrack",s);for(var e=0;e<n.length;e++){n[e].removeEventListener("cuechange",a)}})},e.addTextTrack=function(e,t,i){if(!e)throw new Error("TextTrack kind is required but was not provided");return function(e,t,i,n,r){void 0===r&&(r={});var a=e.textTracks();r.kind=t,i&&(r.label=i),n&&(r.language=n),r.tech=e;var s=new An.text.TrackClass(r);return a.addTrack(s),s}(this,e,t,i)},e.createRemoteTextTrack=function(e){var t=Oe(e,{tech:this});return new wn.remoteTextEl.TrackClass(t)},e.addRemoteTextTrack=function(e,t){var i=this;void 0===e&&(e={});var n=this.createRemoteTextTrack(e);return!0!==t&&!1!==t&&(p.warn('Calling addRemoteTextTrack without explicitly setting the "manualCleanup" parameter to `true` is deprecated and default to `false` in future version of video.js'),t=!0),this.remoteTextTrackEls().addTrackElement_(n),this.remoteTextTracks().addTrack(n.track),!0!==t&&this.ready(function(){return i.autoRemoteTextTracks_.addTrack(n.track)}),n},e.removeRemoteTextTrack=function(e){var t=this.remoteTextTrackEls().getTrackElementByTrack_(e);this.remoteTextTrackEls().removeTrackElement_(t),this.remoteTextTracks().removeTrack(e),this.autoRemoteTextTracks_.removeTrack(e)},e.getVideoPlaybackQuality=function(){return{}},e.requestPictureInPicture=function(){var e=this.options_.Promise||v.Promise;if(e)return e.reject()},e.setPoster=function(){},e.playsinline=function(){},e.setPlaysinline=function(){},e.overrideNativeAudioTracks=function(){},e.overrideNativeVideoTracks=function(){},e.canPlayType=function(){return""},i.canPlayType=function(){return""},i.canPlaySource=function(e,t){return i.canPlayType(e.type)},i.isTech=function(e){return e.prototype instanceof i||e instanceof i||e===i},i.registerTech=function(e,t){if(i.techs_||(i.techs_={}),!i.isTech(t))throw new Error("Tech "+e+" must be a Tech");if(!i.canPlayType)throw new Error("Techs must have a static canPlayType method on them");if(!i.canPlaySource)throw new Error("Techs must have a static canPlaySource method on them");return e=Le(e),i.techs_[e]=t,i.techs_[Ie(e)]=t,"Tech"!==e&&i.defaultTechOrder_.push(e),t},i.getTech=function(e){if(e)return i.techs_&&i.techs_[e]?i.techs_[e]:(e=Le(e),v&&v.videojs&&v.videojs[e]?(p.warn("The "+e+" tech was added to the videojs object when it should be registered using videojs.registerTech(name, tech)"),v.videojs[e]):void 0)},i}(xe);An.names.forEach(function(e){var t=An[e];nr.prototype[t.getterName]=function(){return this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName]}}),nr.prototype.featuresVolumeControl=!0,nr.prototype.featuresMuteControl=!0,nr.prototype.featuresFullscreenResize=!1,nr.prototype.featuresPlaybackRate=!1,nr.prototype.featuresProgressEvents=!1,nr.prototype.featuresSourceset=!1,nr.prototype.featuresTimeupdateEvents=!1,nr.prototype.featuresNativeTextTracks=!1,nr.withSourceHandlers=function(r){r.registerSourceHandler=function(e,t){var i=r.sourceHandlers;i=i||(r.sourceHandlers=[]),void 0===t&&(t=i.length),i.splice(t,0,e)},r.canPlayType=function(e){for(var t,i=r.sourceHandlers||[],n=0;n<i.length;n++)if(t=i[n].canPlayType(e))return t;return""},r.selectSourceHandler=function(e,t){for(var i=r.sourceHandlers||[],n=0;n<i.length;n++)if(i[n].canHandleSource(e,t))return i[n];return null},r.canPlaySource=function(e,t){var i=r.selectSourceHandler(e,t);return i?i.canHandleSource(e,t):""};["seekable","seeking","duration"].forEach(function(e){var t=this[e];"function"==typeof t&&(this[e]=function(){return this.sourceHandler_&&this.sourceHandler_[e]?this.sourceHandler_[e].apply(this.sourceHandler_,arguments):t.apply(this,arguments)})},r.prototype),r.prototype.setSource=function(e){var t=r.selectSourceHandler(e,this.options_);t||(r.nativeSourceHandler?t=r.nativeSourceHandler:p.error("No source handler found for the current source.")),this.disposeSourceHandler(),this.off("dispose",this.disposeSourceHandler),t!==r.nativeSourceHandler&&(this.currentSource_=e),this.sourceHandler_=t.handleSource(e,this,this.options_),this.one("dispose",this.disposeSourceHandler)},r.prototype.disposeSourceHandler=function(){this.currentSource_&&(this.clearTracks(["audio","video"]),this.currentSource_=null),this.cleanupAutoTextTracks(),this.sourceHandler_&&(this.sourceHandler_.dispose&&this.sourceHandler_.dispose(),this.sourceHandler_=null)}},xe.registerComponent("Tech",nr),nr.registerTech("Tech",nr),nr.defaultTechOrder_=[];var rr={},ar={},sr={};function or(e,t,i){e.setTimeout(function(){return function i(n,e,r,a,s,o){void 0===n&&(n={});void 0===e&&(e=[]);void 0===s&&(s=[]);void 0===o&&(o=!1);var t=e,u=t[0],l=t.slice(1);if("string"==typeof u)i(n,rr[u],r,a,s,o);else if(u){var c=pr(a,u);if(!c.setSource)return s.push(c),i(n,l,r,a,s,o);c.setSource(m({},n),function(e,t){if(e)return i(n,l,r,a,s,o);s.push(c),i(t,n.type===t.type?l:rr[t.type],r,a,s,o)})}else l.length?i(n,l,r,a,s,o):o?r(n,s):i(n,rr["*"],r,a,s,!0)}(t,rr[t.type],i,e)},1)}function ur(e,t,i,n){void 0===n&&(n=null);var r="call"+Le(i),a=e.reduce(dr(r),n),s=a===sr,o=s?null:t[i](a);return function(e,t,i,n){for(var r=e.length-1;0<=r;r--){var a=e[r];a[t]&&a[t](n,i)}}(e,i,o,s),o}var lr={buffered:1,currentTime:1,duration:1,seekable:1,played:1,paused:1,volume:1},cr={setCurrentTime:1,setVolume:1},hr={play:1,pause:1};function dr(i){return function(e,t){return e===sr?sr:t[i]?t[i](e):e}}function pr(e,t){var i=ar[e.id()],n=null;if(null==i)return n=t(e),ar[e.id()]=[[t,n]],n;for(var r=0;r<i.length;r++){var a=i[r],s=a[0],o=a[1];s===t&&(n=o)}return null===n&&(n=t(e),i.push([t,n])),n}function fr(e){void 0===e&&(e="");var t=Mt(e);return mr[t.toLowerCase()]||""}var mr={opus:"video/ogg",ogv:"video/ogg",mp4:"video/mp4",mov:"video/mp4",m4v:"video/mp4",mkv:"video/x-matroska",m4a:"audio/mp4",mp3:"audio/mpeg",aac:"audio/aac",oga:"audio/ogg",m3u8:"application/x-mpegURL",jpg:"image/jpeg",jpeg:"image/jpeg",gif:"image/gif",png:"image/png",svg:"image/svg+xml",webp:"image/webp"};function gr(e){if(!e.type){var t=fr(e.src);t&&(e.type=t)}return e}var yr=function(l){function e(e,t,i){var n,r=Oe({createEl:!1},t);if(n=l.call(this,e,r,i)||this,t.playerOptions.sources&&0!==t.playerOptions.sources.length)e.src(t.playerOptions.sources);else for(var a=0,s=t.playerOptions.techOrder;a<s.length;a++){var o=Le(s[a]),u=nr.getTech(o);if(o||(u=xe.getComponent(o)),u&&u.isSupported()){e.loadTech_(o);break}}return n}return De(e,l),e}(xe);xe.registerComponent("MediaLoader",yr);var vr=function(r){function e(e,t){var i;return(i=r.call(this,e,t)||this).emitTapEvents(),i.enable(),i}De(e,r);var t=e.prototype;return t.createEl=function(e,t,i){void 0===e&&(e="div"),void 0===t&&(t={}),void 0===i&&(i={}),t=m({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass(),tabIndex:0},t),"button"===e&&p.error("Creating a ClickableComponent with an HTML element of "+e+" is not supported; use a Button instead."),i=m({role:"button"},i),this.tabIndex_=t.tabIndex;var n=r.prototype.createEl.call(this,e,t,i);return this.createControlTextEl(n),n},t.dispose=function(){this.controlTextEl_=null,r.prototype.dispose.call(this)},t.createControlTextEl=function(e){return this.controlTextEl_=b("span",{className:"vjs-control-text"},{"aria-live":"polite"}),e&&e.appendChild(this.controlTextEl_),this.controlText(this.controlText_,e),this.controlTextEl_},t.controlText=function(e,t){if(void 0===t&&(t=this.el()),void 0===e)return this.controlText_||"Need Text";var i=this.localize(e);this.controlText_=e,T(this.controlTextEl_,i),this.nonIconControl||t.setAttribute("title",i)},t.buildCSSClass=function(){return"vjs-control vjs-button "+r.prototype.buildCSSClass.call(this)},t.enable=function(){this.enabled_||(this.enabled_=!0,this.removeClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","false"),"undefined"!=typeof this.tabIndex_&&this.el_.setAttribute("tabIndex",this.tabIndex_),this.on(["tap","click"],this.handleClick),this.on("keydown",this.handleKeyDown))},t.disable=function(){this.enabled_=!1,this.addClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","true"),"undefined"!=typeof this.tabIndex_&&this.el_.removeAttribute("tabIndex"),this.off("mouseover",this.handleMouseOver),this.off("mouseout",this.handleMouseOut),this.off(["tap","click"],this.handleClick),this.off("keydown",this.handleKeyDown)},t.handleClick=function(e){},t.handleKeyDown=function(e){At.isEventKey(e,"Space")||At.isEventKey(e,"Enter")?(e.preventDefault(),e.stopPropagation(),this.trigger("click")):r.prototype.handleKeyDown.call(this,e)},e}(xe);xe.registerComponent("ClickableComponent",vr);var _r=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).update(),e.on("posterchange",pe(Me(i),i.update)),i}De(e,n);var t=e.prototype;return t.dispose=function(){this.player().off("posterchange",this.update),n.prototype.dispose.call(this)},t.createEl=function(){return b("div",{className:"vjs-poster",tabIndex:-1})},t.update=function(e){var t=this.player().poster();this.setSrc(t),t?this.show():this.hide()},t.setSrc=function(e){var t="";e&&(t='url("'+e+'")'),this.el_.style.backgroundImage=t},t.handleClick=function(e){this.player_.controls()&&(this.player_.tech(!0)&&this.player_.tech(!0).focus(),this.player_.paused()?St(this.player_.play()):this.player_.pause())},e}(vr);xe.registerComponent("PosterImage",_r);var br="#222",Tr={monospace:"monospace",sansSerif:"sans-serif",serif:"serif",monospaceSansSerif:'"Andale Mono", "Lucida Console", monospace',monospaceSerif:'"Courier New", monospace',proportionalSansSerif:"sans-serif",proportionalSerif:"serif",casual:'"Comic Sans MS", Impact, fantasy',script:'"Monotype Corsiva", cursive',smallcaps:'"Andale Mono", "Lucida Console", monospace, sans-serif'};function Sr(e,t){var i;if(4===e.length)i=e[1]+e[1]+e[2]+e[2]+e[3]+e[3];else{if(7!==e.length)throw new Error("Invalid color code provided, "+e+"; must be formatted as e.g. #f0e or #f604e2.");i=e.slice(1)}return"rgba("+parseInt(i.slice(0,2),16)+","+parseInt(i.slice(2,4),16)+","+parseInt(i.slice(4,6),16)+","+t+")"}function kr(e,t,i){try{e.style[t]=i}catch(e){return}}var Cr=function(a){function e(i,e,t){var n;n=a.call(this,i,e,t)||this;var r=pe(Me(n),n.updateDisplay);return i.on("loadstart",pe(Me(n),n.toggleDisplay)),i.on("texttrackchange",r),i.on("loadedmetadata",pe(Me(n),n.preselectTrack)),i.ready(pe(Me(n),function(){if(i.tech_&&i.tech_.featuresNativeTextTracks)this.hide();else{i.on("fullscreenchange",r),i.on("playerresize",r),v.addEventListener("orientationchange",r),i.on("dispose",function(){return v.removeEventListener("orientationchange",r)});for(var e=this.options_.playerOptions.tracks||[],t=0;t<e.length;t++)this.player_.addRemoteTextTrack(e[t],!0);this.preselectTrack()}})),n}De(e,a);var t=e.prototype;return t.preselectTrack=function(){for(var e,t,i,n={captions:1,subtitles:1},r=this.player_.textTracks(),a=this.player_.cache_.selectedLanguage,s=0;s<r.length;s++){var o=r[s];a&&a.enabled&&a.language&&a.language===o.language&&o.kind in n?i=o.kind===a.kind?o:i||o:a&&!a.enabled?t=e=i=null:o.default&&("descriptions"!==o.kind||e?o.kind in n&&!t&&(t=o):e=o)}i?i.mode="showing":t?t.mode="showing":e&&(e.mode="showing")},t.toggleDisplay=function(){this.player_.tech_&&this.player_.tech_.featuresNativeTextTracks?this.hide():this.show()},t.createEl=function(){return a.prototype.createEl.call(this,"div",{className:"vjs-text-track-display"},{"aria-live":"off","aria-atomic":"true"})},t.clearDisplay=function(){"function"==typeof v.WebVTT&&v.WebVTT.processCues(v,[],this.el_)},t.updateDisplay=function(){var e=this.player_.textTracks(),t=this.options_.allowMultipleShowingTracks;if(this.clearDisplay(),t){for(var i=[],n=0;n<e.length;++n){var r=e[n];"showing"===r.mode&&i.push(r)}this.updateForTrack(i)}else{for(var a=null,s=null,o=e.length;o--;){var u=e[o];"showing"===u.mode&&("descriptions"===u.kind?a=u:s=u)}s?("off"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","off"),this.updateForTrack(s)):a&&("assertive"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","assertive"),this.updateForTrack(a))}},t.updateDisplayState=function(e){for(var t=this.player_.textTrackSettings.getValues(),i=e.activeCues,n=i.length;n--;){var r=i[n];if(r){var a=r.displayState;if(t.color&&(a.firstChild.style.color=t.color),t.textOpacity&&kr(a.firstChild,"color",Sr(t.color||"#fff",t.textOpacity)),t.backgroundColor&&(a.firstChild.style.backgroundColor=t.backgroundColor),t.backgroundOpacity&&kr(a.firstChild,"backgroundColor",Sr(t.backgroundColor||"#000",t.backgroundOpacity)),t.windowColor&&(t.windowOpacity?kr(a,"backgroundColor",Sr(t.windowColor,t.windowOpacity)):a.style.backgroundColor=t.windowColor),t.edgeStyle&&("dropshadow"===t.edgeStyle?a.firstChild.style.textShadow="2px 2px 3px #222, 2px 2px 4px #222, 2px 2px 5px "+br:"raised"===t.edgeStyle?a.firstChild.style.textShadow="1px 1px #222, 2px 2px #222, 3px 3px "+br:"depressed"===t.edgeStyle?a.firstChild.style.textShadow="1px 1px #ccc, 0 1px #ccc, -1px -1px #222, 0 -1px "+br:"uniform"===t.edgeStyle&&(a.firstChild.style.textShadow="0 0 4px #222, 0 0 4px #222, 0 0 4px #222, 0 0 4px "+br)),t.fontPercent&&1!==t.fontPercent){var s=v.parseFloat(a.style.fontSize);a.style.fontSize=s*t.fontPercent+"px",a.style.height="auto",a.style.top="auto",a.style.bottom="2px"}t.fontFamily&&"default"!==t.fontFamily&&("small-caps"===t.fontFamily?a.firstChild.style.fontVariant="small-caps":a.firstChild.style.fontFamily=Tr[t.fontFamily])}}},t.updateForTrack=function(e){if(Array.isArray(e)||(e=[e]),"function"==typeof v.WebVTT&&!e.every(function(e){return!e.activeCues})){for(var t=[],i=0;i<e.length;++i)for(var n=e[i],r=0;r<n.activeCues.length;++r)t.push(n.activeCues[r]);v.WebVTT.processCues(v,t,this.el_);for(var a=0;a<e.length;++a){for(var s=e[a],o=0;o<s.activeCues.length;++o){var u=s.activeCues[o].displayState;C(u,"vjs-text-track-cue"),C(u,"vjs-text-track-cue-"+(s.language?s.language:a))}this.player_.textTrackSettings&&this.updateDisplayState(s)}}},e}(xe);xe.registerComponent("TextTrackDisplay",Cr);var Er=function(r){function e(){return r.apply(this,arguments)||this}return De(e,r),e.prototype.createEl=function(){var e=this.player_.isAudio(),t=this.localize(e?"Audio Player":"Video Player"),i=b("span",{className:"vjs-control-text",innerHTML:this.localize("{1} is loading.",[t])}),n=r.prototype.createEl.call(this,"div",{className:"vjs-loading-spinner",dir:"ltr"});return n.appendChild(i),n},e}(xe);xe.registerComponent("LoadingSpinner",Er);var wr=function(t){function e(){return t.apply(this,arguments)||this}De(e,t);var i=e.prototype;return i.createEl=function(e,t,i){void 0===t&&(t={}),void 0===i&&(i={}),t=m({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass()},t),i=m({type:"button"},i);var n=xe.prototype.createEl.call(this,"button",t,i);return this.createControlTextEl(n),n},i.addChild=function(e,t){void 0===t&&(t={});var i=this.constructor.name;return p.warn("Adding an actionable (user controllable) child to a Button ("+i+") is not supported; use a ClickableComponent instead."),xe.prototype.addChild.call(this,e,t)},i.enable=function(){t.prototype.enable.call(this),this.el_.removeAttribute("disabled")},i.disable=function(){t.prototype.disable.call(this),this.el_.setAttribute("disabled","disabled")},i.handleKeyDown=function(e){At.isEventKey(e,"Space")||At.isEventKey(e,"Enter")?e.stopPropagation():t.prototype.handleKeyDown.call(this,e)},e}(vr);xe.registerComponent("Button",wr);var Ar=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).mouseused_=!1,i.on("mousedown",i.handleMouseDown),i}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-big-play-button"},t.handleClick=function(e){var t=this.player_.play();if(this.mouseused_&&e.clientX&&e.clientY)return St(t),void(this.player_.tech(!0)&&this.player_.tech(!0).focus());var i=this.player_.getChild("controlBar"),n=i&&i.getChild("playToggle");if(n){function r(){return n.focus()}Tt(t)?t.then(r,function(){}):this.setTimeout(r,1)}else this.player_.tech(!0).focus()},t.handleKeyDown=function(e){this.mouseused_=!1,n.prototype.handleKeyDown.call(this,e)},t.handleMouseDown=function(e){this.mouseused_=!0},e}(wr);Ar.prototype.controlText_="Play Video",xe.registerComponent("BigPlayButton",Ar);var Pr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).controlText(t&&t.controlText||i.localize("Close")),i}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-close-button "+n.prototype.buildCSSClass.call(this)},t.handleClick=function(e){this.trigger({type:"close",bubbles:!1})},t.handleKeyDown=function(e){At.isEventKey(e,"Esc")?(e.preventDefault(),e.stopPropagation(),this.trigger("click")):n.prototype.handleKeyDown.call(this,e)},e}(wr);xe.registerComponent("CloseButton",Pr);var Ir=function(n){function e(e,t){var i;return void 0===t&&(t={}),i=n.call(this,e,t)||this,t.replay=void 0===t.replay||t.replay,i.on(e,"play",i.handlePlay),i.on(e,"pause",i.handlePause),t.replay&&i.on(e,"ended",i.handleEnded),i}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-play-control "+n.prototype.buildCSSClass.call(this)},t.handleClick=function(e){this.player_.paused()?this.player_.play():this.player_.pause()},t.handleSeeked=function(e){this.removeClass("vjs-ended"),this.player_.paused()?this.handlePause(e):this.handlePlay(e)},t.handlePlay=function(e){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.controlText("Pause")},t.handlePause=function(e){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.controlText("Play")},t.handleEnded=function(e){this.removeClass("vjs-playing"),this.addClass("vjs-ended"),this.controlText("Replay"),this.one(this.player_,"seeked",this.handleSeeked)},e}(wr);Ir.prototype.controlText_="Play",xe.registerComponent("PlayToggle",Ir);function Lr(e,t){e=e<0?0:e;var i=Math.floor(e%60),n=Math.floor(e/60%60),r=Math.floor(e/3600),a=Math.floor(t/60%60),s=Math.floor(t/3600);return!isNaN(e)&&e!==1/0||(r=n=i="-"),(r=0<r||0<s?r+":":"")+(n=((r||10<=a)&&n<10?"0"+n:n)+":")+(i=i<10?"0"+i:i)}var Or=Lr;function xr(e,t){return void 0===t&&(t=e),Or(e,t)}var Dr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).throttledUpdateContent=fe(pe(Me(i),i.updateContent),30),i.on(e,"timeupdate",i.throttledUpdateContent),i}De(e,n);var t=e.prototype;return t.createEl=function(){var e=this.buildCSSClass(),t=n.prototype.createEl.call(this,"div",{className:e+" vjs-time-control vjs-control",innerHTML:'<span class="vjs-control-text" role="presentation">'+this.localize(this.labelText_)+" </span>"});return this.contentEl_=b("span",{className:e+"-display"},{"aria-live":"off",role:"presentation"}),this.updateTextNode_(),t.appendChild(this.contentEl_),t},t.dispose=function(){this.contentEl_=null,this.textNode_=null,n.prototype.dispose.call(this)},t.updateTextNode_=function(){if(this.contentEl_){for(;this.contentEl_.firstChild;)this.contentEl_.removeChild(this.contentEl_.firstChild);this.textNode_=h.createTextNode(this.formattedTime_||this.formatTime_(0)),this.contentEl_.appendChild(this.textNode_)}},t.formatTime_=function(e){return xr(e)},t.updateFormattedTime_=function(e){var t=this.formatTime_(e);t!==this.formattedTime_&&(this.formattedTime_=t,this.requestAnimationFrame(this.updateTextNode_))},t.updateContent=function(e){},e}(xe);Dr.prototype.labelText_="Time",Dr.prototype.controlText_="Time",xe.registerComponent("TimeDisplay",Dr);var Ur=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).on(e,"ended",i.handleEnded),i}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-current-time"},t.updateContent=function(e){var t=this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime();this.updateFormattedTime_(t)},t.handleEnded=function(e){this.player_.duration()&&this.updateFormattedTime_(this.player_.duration())},e}(Dr);Ur.prototype.labelText_="Current Time",Ur.prototype.controlText_="Current Time",xe.registerComponent("CurrentTimeDisplay",Ur);var Rr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).on(e,"durationchange",i.updateContent),i.on(e,"loadstart",i.updateContent),i.on(e,"loadedmetadata",i.throttledUpdateContent),i}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-duration"},t.updateContent=function(e){var t=this.player_.duration();this.duration_!==t&&(this.duration_=t,this.updateFormattedTime_(t))},e}(Dr);Rr.prototype.labelText_="Duration",Rr.prototype.controlText_="Duration",xe.registerComponent("DurationDisplay",Rr);var Mr=function(e){function t(){return e.apply(this,arguments)||this}return De(t,e),t.prototype.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-time-control vjs-time-divider",innerHTML:"<div><span>/</span></div>"},{"aria-hidden":!0})},t}(xe);xe.registerComponent("TimeDivider",Mr);var Nr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).on(e,"durationchange",i.throttledUpdateContent),i.on(e,"ended",i.handleEnded),i}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-remaining-time"},t.createEl=function(){var e=n.prototype.createEl.call(this);return e.insertBefore(b("span",{},{"aria-hidden":!0},"-"),this.contentEl_),e},t.updateContent=function(e){"number"==typeof this.player_.duration()&&(this.player_.remainingTimeDisplay?this.updateFormattedTime_(this.player_.remainingTimeDisplay()):this.updateFormattedTime_(this.player_.remainingTime()))},t.handleEnded=function(e){this.player_.duration()&&this.updateFormattedTime_(0)},e}(Dr);Nr.prototype.labelText_="Remaining Time",Nr.prototype.controlText_="Remaining Time",xe.registerComponent("RemainingTimeDisplay",Nr);var Br=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).updateShowing(),i.on(i.player(),"durationchange",i.updateShowing),i}De(e,n);var t=e.prototype;return t.createEl=function(){var e=n.prototype.createEl.call(this,"div",{className:"vjs-live-control vjs-control"});return this.contentEl_=b("div",{className:"vjs-live-display",innerHTML:'<span class="vjs-control-text">'+this.localize("Stream Type")+" </span>"+this.localize("LIVE")},{"aria-live":"off"}),e.appendChild(this.contentEl_),e},t.dispose=function(){this.contentEl_=null,n.prototype.dispose.call(this)},t.updateShowing=function(e){this.player().duration()===1/0?this.show():this.hide()},e}(xe);xe.registerComponent("LiveDisplay",Br);var jr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).updateLiveEdgeStatus(),i.player_.liveTracker&&i.on(i.player_.liveTracker,"liveedgechange",i.updateLiveEdgeStatus),i}De(e,n);var t=e.prototype;return t.createEl=function(){var e=n.prototype.createEl.call(this,"button",{className:"vjs-seek-to-live-control vjs-control"});return this.textEl_=b("span",{className:"vjs-seek-to-live-text",innerHTML:this.localize("LIVE")},{"aria-hidden":"true"}),e.appendChild(this.textEl_),e},t.updateLiveEdgeStatus=function(e){!this.player_.liveTracker||this.player_.liveTracker.atLiveEdge()?(this.setAttribute("aria-disabled",!0),this.addClass("vjs-at-live-edge"),this.controlText("Seek to live, currently playing live")):(this.setAttribute("aria-disabled",!1),this.removeClass("vjs-at-live-edge"),this.controlText("Seek to live, currently behind live"))},t.handleClick=function(){this.player_.liveTracker.seekToLiveEdge()},t.dispose=function(){this.player_.liveTracker&&this.off(this.player_.liveTracker,"liveedgechange",this.updateLiveEdgeStatus),this.textEl_=null,n.prototype.dispose.call(this)},e}(wr);jr.prototype.controlText_="Seek to live, currently playing live",xe.registerComponent("SeekToLive",jr);var Fr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).bar=i.getChild(i.options_.barName),i.vertical(!!i.options_.vertical),i.enable(),i}De(e,n);var t=e.prototype;return t.enabled=function(){return this.enabled_},t.enable=function(){this.enabled()||(this.on("mousedown",this.handleMouseDown),this.on("touchstart",this.handleMouseDown),this.on("keydown",this.handleKeyDown),this.on("click",this.handleClick),this.on(this.player_,"controlsvisible",this.update),this.playerEvent&&this.on(this.player_,this.playerEvent,this.update),this.removeClass("disabled"),this.setAttribute("tabindex",0),this.enabled_=!0)},t.disable=function(){if(this.enabled()){var e=this.bar.el_.ownerDocument;this.off("mousedown",this.handleMouseDown),this.off("touchstart",this.handleMouseDown),this.off("keydown",this.handleKeyDown),this.off("click",this.handleClick),this.off(this.player_,"controlsvisible",this.update),this.off(e,"mousemove",this.handleMouseMove),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchmove",this.handleMouseMove),this.off(e,"touchend",this.handleMouseUp),this.removeAttribute("tabindex"),this.addClass("disabled"),this.playerEvent&&this.off(this.player_,this.playerEvent,this.update),this.enabled_=!1}},t.createEl=function(e,t,i){return void 0===t&&(t={}),void 0===i&&(i={}),t.className=t.className+" vjs-slider",t=m({tabIndex:0},t),i=m({role:"slider","aria-valuenow":0,"aria-valuemin":0,"aria-valuemax":100,tabIndex:0},i),n.prototype.createEl.call(this,e,t,i)},t.handleMouseDown=function(e){var t=this.bar.el_.ownerDocument;"mousedown"===e.type&&e.preventDefault(),"touchstart"!==e.type||et||e.preventDefault(),x(),this.addClass("vjs-sliding"),this.trigger("slideractive"),this.on(t,"mousemove",this.handleMouseMove),this.on(t,"mouseup",this.handleMouseUp),this.on(t,"touchmove",this.handleMouseMove),this.on(t,"touchend",this.handleMouseUp),this.handleMouseMove(e)},t.handleMouseMove=function(e){},t.handleMouseUp=function(){var e=this.bar.el_.ownerDocument;D(),this.removeClass("vjs-sliding"),this.trigger("sliderinactive"),this.off(e,"mousemove",this.handleMouseMove),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchmove",this.handleMouseMove),this.off(e,"touchend",this.handleMouseUp),this.update()},t.update=function(){if(this.el_){var e=this.getPercent(),t=this.bar;if(t){("number"!=typeof e||e!=e||e<0||e===1/0)&&(e=0);var i=(100*e).toFixed(2)+"%",n=t.el().style,r=this.vertical()?"height":"width";return n[r]!==i&&(n[r]=i),e}}},t.calculateDistance=function(e){var t=M(this.el_,e);return this.vertical()?t.y:t.x},t.handleKeyDown=function(e){At.isEventKey(e,"Left")||At.isEventKey(e,"Down")?(e.preventDefault(),e.stopPropagation(),this.stepBack()):At.isEventKey(e,"Right")||At.isEventKey(e,"Up")?(e.preventDefault(),e.stopPropagation(),this.stepForward()):n.prototype.handleKeyDown.call(this,e)},t.handleClick=function(e){e.stopPropagation(),e.preventDefault()},t.vertical=function(e){if(void 0===e)return this.vertical_||!1;this.vertical_=!!e,this.vertical_?this.addClass("vjs-slider-vertical"):this.addClass("vjs-slider-horizontal")},e}(xe);xe.registerComponent("Slider",Fr);var Hr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).partEls_=[],i.on(e,"progress",i.update),i}De(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-load-progress",innerHTML:'<span class="vjs-control-text"><span>'+this.localize("Loaded")+'</span>: <span class="vjs-control-text-loaded-percentage">0%</span></span>'})},t.dispose=function(){this.partEls_=null,n.prototype.dispose.call(this)},t.update=function(e){function t(e,t,i){var n=e/t||0;return n=100*(1<=n?1:n),i&&(n=n.toFixed(2)),n+"%"}var i=this.player_.liveTracker,n=this.player_.buffered(),r=i&&i.isLive()?i.seekableEnd():this.player_.duration(),a=this.player_.bufferedEnd(),s=this.partEls_,o=this.$(".vjs-control-text-loaded-percentage");this.el_.style.width=t(a,r),T(o,t(a,r,!0));for(var u=0;u<n.length;u++){var l=n.start(u),c=n.end(u),h=s[u];h||(h=this.el_.appendChild(b()),s[u]=h),h.style.left=t(l,a),h.style.width=t(c-l,a)}for(var d=s.length;d>n.length;d--)this.el_.removeChild(s[d-1]);s.length=n.length},e}(xe);xe.registerComponent("LoadProgressBar",Hr);var Vr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).update=fe(pe(Me(i),i.update),30),i}De(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-time-tooltip"},{"aria-hidden":"true"})},t.update=function(e,t,i){var n=U(this.el_),r=U(this.player_.el()),a=e.width*t;if(r&&n){var s=e.left-r.left+a,o=e.width-a+(r.right-e.right),u=n.width/2;s<u?u+=u-s:o<u&&(u=o),u<0?u=0:u>n.width&&(u=n.width),this.el_.style.right="-"+u+"px",this.write(i)}},t.write=function(e){T(this.el_,e)},t.updateTime=function(r,a,s,o){var u=this;this.rafId_&&this.cancelAnimationFrame(this.rafId_),this.rafId_=this.requestAnimationFrame(function(){var e,t=u.player_.duration();if(u.player_.liveTracker&&u.player_.liveTracker.isLive()){var i=u.player_.liveTracker.liveWindow(),n=i-a*i;e=(n<1?"":"-")+xr(n,i)}else e=xr(s,t);u.update(r,a,e),o&&o()})},e}(xe);xe.registerComponent("TimeTooltip",Vr);var qr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).update=fe(pe(Me(i),i.update),30),i}De(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-play-progress vjs-slider-bar"},{"aria-hidden":"true"})},t.update=function(e,t){var i=this.getChild("timeTooltip");if(i){var n=this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime();i.updateTime(e,t,n)}},e}(xe);qr.prototype.options_={children:[]},Ge||Ke||qr.prototype.options_.children.push("timeTooltip"),xe.registerComponent("PlayProgressBar",qr);var Wr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).update=fe(pe(Me(i),i.update),30),i}De(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-mouse-display"})},t.update=function(e,t){var i=this,n=t*this.player_.duration();this.getChild("timeTooltip").updateTime(e,t,n,function(){i.el_.style.left=e.width*t+"px"})},e}(xe);Wr.prototype.options_={children:["timeTooltip"]},xe.registerComponent("MouseTimeDisplay",Wr);var zr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).setEventHandlers_(),i}De(e,n);var t=e.prototype;return t.setEventHandlers_=function(){this.update=fe(pe(this,this.update),30),this.on(this.player_,"timeupdate",this.update),this.on(this.player_,"ended",this.handleEnded),this.on(this.player_,"durationchange",this.update),this.player_.liveTracker&&this.on(this.player_.liveTracker,"liveedgechange",this.update),this.updateInterval=null,this.on(this.player_,["playing"],this.enableInterval_),this.on(this.player_,["ended","pause","waiting"],this.disableInterval_),"hidden"in h&&"visibilityState"in h&&this.on(h,"visibilitychange",this.toggleVisibility_)},t.toggleVisibility_=function(e){h.hidden?this.disableInterval_(e):(this.enableInterval_(),this.requestAnimationFrame(this.update))},t.enableInterval_=function(){var e=this;this.clearInterval(this.updateInterval),this.updateInterval=this.setInterval(function(){e.requestAnimationFrame(e.update)},30)},t.disableInterval_=function(e){this.player_.liveTracker&&this.player_.liveTracker.isLive()&&"ended"!==e.type||this.clearInterval(this.updateInterval)},t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-progress-holder"},{"aria-label":this.localize("Progress Bar")})},t.update_=function(e,t){var i=this.player_.liveTracker,n=this.player_.duration();i&&i.isLive()&&(n=this.player_.liveTracker.liveCurrentTime()),this.el_.setAttribute("aria-valuenow",(100*t).toFixed(2)),this.el_.setAttribute("aria-valuetext",this.localize("progress bar timing: currentTime={1} duration={2}",[xr(e,n),xr(n,n)],"{1} of {2}")),this.bar&&this.bar.update(U(this.el_),t)},t.update=function(e){if(null!==this.el().offsetParent){var t=n.prototype.update.call(this);return this.update_(this.getCurrentTime_(),t),t}},t.getCurrentTime_=function(){return this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime()},t.handleEnded=function(e){this.update_(this.player_.duration(),1)},t.getPercent=function(){var e,t=this.getCurrentTime_(),i=this.player_.liveTracker;return i&&i.isLive()?(e=(t-i.seekableStart())/i.liveWindow(),i.atLiveEdge()&&(e=1)):e=t/this.player_.duration(),1<=e?1:e||0},t.handleMouseDown=function(e){V(e)&&(e.stopPropagation(),this.player_.scrubbing(!0),this.videoWasPlaying=!this.player_.paused(),this.player_.pause(),n.prototype.handleMouseDown.call(this,e))},t.handleMouseMove=function(e){if(V(e)){var t,i=this.calculateDistance(e),n=this.player_.liveTracker;if(n&&n.isLive()){var r=n.seekableStart(),a=n.liveCurrentTime();if(a<=(t=r+i*n.liveWindow())&&(t=a),t<=r&&(t=r+.1),t===1/0)return}else(t=i*this.player_.duration())===this.player_.duration()&&(t-=.1);this.player_.currentTime(t)}},t.enable=function(){n.prototype.enable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.show()},t.disable=function(){n.prototype.disable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.hide()},t.handleMouseUp=function(e){n.prototype.handleMouseUp.call(this,e),e&&e.stopPropagation(),this.player_.scrubbing(!1),this.player_.trigger({type:"timeupdate",target:this,manuallyTriggered:!0}),this.videoWasPlaying&&St(this.player_.play())},t.stepForward=function(){this.player_.currentTime(this.player_.currentTime()+5)},t.stepBack=function(){this.player_.currentTime(this.player_.currentTime()-5)},t.handleAction=function(e){this.player_.paused()?this.player_.play():this.player_.pause()},t.handleKeyDown=function(e){if(At.isEventKey(e,"Space")||At.isEventKey(e,"Enter"))e.preventDefault(),e.stopPropagation(),this.handleAction(e);else if(At.isEventKey(e,"Home"))e.preventDefault(),e.stopPropagation(),this.player_.currentTime(0);else if(At.isEventKey(e,"End"))e.preventDefault(),e.stopPropagation(),this.player_.currentTime(this.player_.duration());else if(/^[0-9]$/.test(At(e))){e.preventDefault(),e.stopPropagation();var t=10*(At.codes[At(e)]-At.codes[0])/100;this.player_.currentTime(this.player_.duration()*t)}else At.isEventKey(e,"PgDn")?(e.preventDefault(),e.stopPropagation(),this.player_.currentTime(this.player_.currentTime()-60)):At.isEventKey(e,"PgUp")?(e.preventDefault(),e.stopPropagation(),this.player_.currentTime(this.player_.currentTime()+60)):n.prototype.handleKeyDown.call(this,e)},e}(Fr);zr.prototype.options_={children:["loadProgressBar","playProgressBar"],barName:"playProgressBar"},Ge||Ke||zr.prototype.options_.children.splice(1,0,"mouseTimeDisplay"),xe.registerComponent("SeekBar",zr);var $r=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).handleMouseMove=fe(pe(Me(i),i.handleMouseMove),30),i.throttledHandleMouseSeek=fe(pe(Me(i),i.handleMouseSeek),30),i.enable(),i}De(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-progress-control vjs-control"})},t.handleMouseMove=function(e){var t=this.getChild("seekBar");if(t){var i=t.getChild("mouseTimeDisplay"),n=t.el(),r=U(n),a=M(n,e).x;1<a?a=1:a<0&&(a=0),i&&i.update(r,a)}},t.handleMouseSeek=function(e){var t=this.getChild("seekBar");t&&t.handleMouseMove(e)},t.enabled=function(){return this.enabled_},t.disable=function(){this.children().forEach(function(e){return e.disable&&e.disable()}),this.enabled()&&(this.off(["mousedown","touchstart"],this.handleMouseDown),this.off(this.el_,"mousemove",this.handleMouseMove),this.handleMouseUp(),this.addClass("disabled"),this.enabled_=!1)},t.enable=function(){this.children().forEach(function(e){return e.enable&&e.enable()}),this.enabled()||(this.on(["mousedown","touchstart"],this.handleMouseDown),this.on(this.el_,"mousemove",this.handleMouseMove),this.removeClass("disabled"),this.enabled_=!0)},t.handleMouseDown=function(e){var t=this.el_.ownerDocument,i=this.getChild("seekBar");i&&i.handleMouseDown(e),this.on(t,"mousemove",this.throttledHandleMouseSeek),this.on(t,"touchmove",this.throttledHandleMouseSeek),this.on(t,"mouseup",this.handleMouseUp),this.on(t,"touchend",this.handleMouseUp)},t.handleMouseUp=function(e){var t=this.el_.ownerDocument,i=this.getChild("seekBar");i&&i.handleMouseUp(e),this.off(t,"mousemove",this.throttledHandleMouseSeek),this.off(t,"touchmove",this.throttledHandleMouseSeek),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchend",this.handleMouseUp)},e}(xe);$r.prototype.options_={children:["seekBar"]},xe.registerComponent("ProgressControl",$r);var Gr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).on(e,["enterpictureinpicture","leavepictureinpicture"],i.handlePictureInPictureChange),h.pictureInPictureEnabled||i.disable(),i}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-picture-in-picture-control "+n.prototype.buildCSSClass.call(this)},t.handlePictureInPictureChange=function(e){this.player_.isInPictureInPicture()?this.controlText("Exit Picture-in-Picture"):this.controlText("Picture-in-Picture")},t.handleClick=function(e){this.player_.isInPictureInPicture()?this.player_.exitPictureInPicture():this.player_.requestPictureInPicture()},e}(wr);Gr.prototype.controlText_="Picture-in-Picture",xe.registerComponent("PictureInPictureToggle",Gr);var Xr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).on(e,"fullscreenchange",i.handleFullscreenChange),!1===h[e.fsApi_.fullscreenEnabled]&&i.disable(),i}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-fullscreen-control "+n.prototype.buildCSSClass.call(this)},t.handleFullscreenChange=function(e){this.player_.isFullscreen()?this.controlText("Non-Fullscreen"):this.controlText("Fullscreen")},t.handleClick=function(e){this.player_.isFullscreen()?this.player_.exitFullscreen():this.player_.requestFullscreen()},e}(wr);Xr.prototype.controlText_="Fullscreen",xe.registerComponent("FullscreenToggle",Xr);var Kr=function(e){function t(){return e.apply(this,arguments)||this}return De(t,e),t.prototype.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-volume-level",innerHTML:'<span class="vjs-control-text"></span>'})},t}(xe);xe.registerComponent("VolumeLevel",Kr);var Yr=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).on("slideractive",i.updateLastVolume_),i.on(e,"volumechange",i.updateARIAAttributes),e.ready(function(){return i.updateARIAAttributes()}),i}De(e,n);var t=e.prototype;return t.createEl=function(){return n.prototype.createEl.call(this,"div",{className:"vjs-volume-bar vjs-slider-bar"},{"aria-label":this.localize("Volume Level"),"aria-live":"polite"})},t.handleMouseDown=function(e){V(e)&&n.prototype.handleMouseDown.call(this,e)},t.handleMouseMove=function(e){V(e)&&(this.checkMuted(),this.player_.volume(this.calculateDistance(e)))},t.checkMuted=function(){this.player_.muted()&&this.player_.muted(!1)},t.getPercent=function(){return this.player_.muted()?0:this.player_.volume()},t.stepForward=function(){this.checkMuted(),this.player_.volume(this.player_.volume()+.1)},t.stepBack=function(){this.checkMuted(),this.player_.volume(this.player_.volume()-.1)},t.updateARIAAttributes=function(e){var t=this.player_.muted()?0:this.volumeAsPercentage_();this.el_.setAttribute("aria-valuenow",t),this.el_.setAttribute("aria-valuetext",t+"%")},t.volumeAsPercentage_=function(){return Math.round(100*this.player_.volume())},t.updateLastVolume_=function(){var e=this,t=this.player_.volume();this.one("sliderinactive",function(){0===e.player_.volume()&&e.player_.lastVolume_(t)})},e}(Fr);Yr.prototype.options_={children:["volumeLevel"],barName:"volumeLevel"},Yr.prototype.playerEvent="volumechange",xe.registerComponent("VolumeBar",Yr);var Qr=function(n){function e(e,t){var i;return void 0===t&&(t={}),t.vertical=t.vertical||!1,"undefined"!=typeof t.volumeBar&&!o(t.volumeBar)||(t.volumeBar=t.volumeBar||{},t.volumeBar.vertical=t.vertical),function(e,t){t.tech_&&!t.tech_.featuresVolumeControl&&e.addClass("vjs-hidden"),e.on(t,"loadstart",function(){t.tech_.featuresVolumeControl?e.removeClass("vjs-hidden"):e.addClass("vjs-hidden")})}(Me(i=n.call(this,e,t)||this),e),i.throttledHandleMouseMove=fe(pe(Me(i),i.handleMouseMove),30),i.on("mousedown",i.handleMouseDown),i.on("touchstart",i.handleMouseDown),i.on(i.volumeBar,["focus","slideractive"],function(){i.volumeBar.addClass("vjs-slider-active"),i.addClass("vjs-slider-active"),i.trigger("slideractive")}),i.on(i.volumeBar,["blur","sliderinactive"],function(){i.volumeBar.removeClass("vjs-slider-active"),i.removeClass("vjs-slider-active"),i.trigger("sliderinactive")}),i}De(e,n);var t=e.prototype;return t.createEl=function(){var e="vjs-volume-horizontal";return this.options_.vertical&&(e="vjs-volume-vertical"),n.prototype.createEl.call(this,"div",{className:"vjs-volume-control vjs-control "+e})},t.handleMouseDown=function(e){var t=this.el_.ownerDocument;this.on(t,"mousemove",this.throttledHandleMouseMove),this.on(t,"touchmove",this.throttledHandleMouseMove),this.on(t,"mouseup",this.handleMouseUp),this.on(t,"touchend",this.handleMouseUp)},t.handleMouseUp=function(e){var t=this.el_.ownerDocument;this.off(t,"mousemove",this.throttledHandleMouseMove),this.off(t,"touchmove",this.throttledHandleMouseMove),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchend",this.handleMouseUp)},t.handleMouseMove=function(e){this.volumeBar.handleMouseMove(e)},e}(xe);Qr.prototype.options_={children:["volumeBar"]},xe.registerComponent("VolumeControl",Qr);var Jr=function(n){function e(e,t){var i;return function(e,t){t.tech_&&!t.tech_.featuresMuteControl&&e.addClass("vjs-hidden"),e.on(t,"loadstart",function(){t.tech_.featuresMuteControl?e.removeClass("vjs-hidden"):e.addClass("vjs-hidden")})}(Me(i=n.call(this,e,t)||this),e),i.on(e,["loadstart","volumechange"],i.update),i}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-mute-control "+n.prototype.buildCSSClass.call(this)},t.handleClick=function(e){var t=this.player_.volume(),i=this.player_.lastVolume_();if(0===t){var n=i<.1?.1:i;this.player_.volume(n),this.player_.muted(!1)}else this.player_.muted(!this.player_.muted())},t.update=function(e){this.updateIcon_(),this.updateControlText_()},t.updateIcon_=function(){var e=this.player_.volume(),t=3;Ge&&this.player_.tech_&&this.player_.tech_.el_&&this.player_.muted(this.player_.tech_.el_.muted),0===e||this.player_.muted()?t=0:e<.33?t=1:e<.67&&(t=2);for(var i=0;i<4;i++)E(this.el_,"vjs-vol-"+i);C(this.el_,"vjs-vol-"+t)},t.updateControlText_=function(){var e=this.player_.muted()||0===this.player_.volume()?"Unmute":"Mute";this.controlText()!==e&&this.controlText(e)},e}(wr);Jr.prototype.controlText_="Mute",xe.registerComponent("MuteToggle",Jr);var Zr=function(n){function e(e,t){var i;return void 0===t&&(t={}),"undefined"!=typeof t.inline?t.inline=t.inline:t.inline=!0,"undefined"!=typeof t.volumeControl&&!o(t.volumeControl)||(t.volumeControl=t.volumeControl||{},t.volumeControl.vertical=!t.inline),(i=n.call(this,e,t)||this).on(e,["loadstart"],i.volumePanelState_),i.on(i.muteToggle,"keyup",i.handleKeyPress),i.on(i.volumeControl,"keyup",i.handleVolumeControlKeyUp),i.on("keydown",i.handleKeyPress),i.on("mouseover",i.handleMouseOver),i.on("mouseout",i.handleMouseOut),i.on(i.volumeControl,["slideractive"],i.sliderActive_),i.on(i.volumeControl,["sliderinactive"],i.sliderInactive_),i}De(e,n);var t=e.prototype;return t.sliderActive_=function(){this.addClass("vjs-slider-active")},t.sliderInactive_=function(){this.removeClass("vjs-slider-active")},t.volumePanelState_=function(){this.volumeControl.hasClass("vjs-hidden")&&this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-hidden"),this.volumeControl.hasClass("vjs-hidden")&&!this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-mute-toggle-only")},t.createEl=function(){var e="vjs-volume-panel-horizontal";return this.options_.inline||(e="vjs-volume-panel-vertical"),n.prototype.createEl.call(this,"div",{className:"vjs-volume-panel vjs-control "+e})},t.dispose=function(){this.handleMouseOut(),n.prototype.dispose.call(this)},t.handleVolumeControlKeyUp=function(e){At.isEventKey(e,"Esc")&&this.muteToggle.focus()},t.handleMouseOver=function(e){this.addClass("vjs-hover"),ue(h,"keyup",pe(this,this.handleKeyPress))},t.handleMouseOut=function(e){this.removeClass("vjs-hover"),le(h,"keyup",pe(this,this.handleKeyPress))},t.handleKeyPress=function(e){At.isEventKey(e,"Esc")&&this.handleMouseOut()},e}(xe);Zr.prototype.options_={children:["muteToggle","volumeControl"]},xe.registerComponent("VolumePanel",Zr);var ea=function(n){function e(e,t){var i;return i=n.call(this,e,t)||this,t&&(i.menuButton_=t.menuButton),i.focusedChild_=-1,i.on("keydown",i.handleKeyDown),i.boundHandleBlur_=pe(Me(i),i.handleBlur),i.boundHandleTapClick_=pe(Me(i),i.handleTapClick),i}De(e,n);var t=e.prototype;return t.addEventListenerForItem=function(e){e instanceof xe&&(this.on(e,"blur",this.boundHandleBlur_),this.on(e,["tap","click"],this.boundHandleTapClick_))},t.removeEventListenerForItem=function(e){e instanceof xe&&(this.off(e,"blur",this.boundHandleBlur_),this.off(e,["tap","click"],this.boundHandleTapClick_))},t.removeChild=function(e){"string"==typeof e&&(e=this.getChild(e)),this.removeEventListenerForItem(e),n.prototype.removeChild.call(this,e)},t.addItem=function(e){var t=this.addChild(e);t&&this.addEventListenerForItem(t)},t.createEl=function(){var e=this.options_.contentElType||"ul";this.contentEl_=b(e,{className:"vjs-menu-content"}),this.contentEl_.setAttribute("role","menu");var t=n.prototype.createEl.call(this,"div",{append:this.contentEl_,className:"vjs-menu"});return t.appendChild(this.contentEl_),ue(t,"click",function(e){e.preventDefault(),e.stopImmediatePropagation()}),t},t.dispose=function(){this.contentEl_=null,this.boundHandleBlur_=null,this.boundHandleTapClick_=null,n.prototype.dispose.call(this)},t.handleBlur=function(e){var t=e.relatedTarget||h.activeElement;if(!this.children().some(function(e){return e.el()===t})){var i=this.menuButton_;i&&i.buttonPressed_&&t!==i.el().firstChild&&i.unpressButton()}},t.handleTapClick=function(t){if(this.menuButton_){this.menuButton_.unpressButton();var e=this.children();if(!Array.isArray(e))return;var i=e.filter(function(e){return e.el()===t.target})[0];if(!i)return;"CaptionSettingsMenuItem"!==i.name()&&this.menuButton_.focus()}},t.handleKeyDown=function(e){At.isEventKey(e,"Left")||At.isEventKey(e,"Down")?(e.preventDefault(),e.stopPropagation(),this.stepForward()):(At.isEventKey(e,"Right")||At.isEventKey(e,"Up"))&&(e.preventDefault(),e.stopPropagation(),this.stepBack())},t.stepForward=function(){var e=0;void 0!==this.focusedChild_&&(e=this.focusedChild_+1),this.focus(e)},t.stepBack=function(){var e=0;void 0!==this.focusedChild_&&(e=this.focusedChild_-1),this.focus(e)},t.focus=function(e){void 0===e&&(e=0);var t=this.children().slice();t.length&&t[0].className&&/vjs-menu-title/.test(t[0].className)&&t.shift(),0<t.length&&(e<0?e=0:e>=t.length&&(e=t.length-1),t[this.focusedChild_=e].el_.focus())},e}(xe);xe.registerComponent("Menu",ea);var ta=function(r){function e(e,t){var i;void 0===t&&(t={}),(i=r.call(this,e,t)||this).menuButton_=new wr(e,t),i.menuButton_.controlText(i.controlText_),i.menuButton_.el_.setAttribute("aria-haspopup","true");var n=wr.prototype.buildCSSClass();return i.menuButton_.el_.className=i.buildCSSClass()+" "+n,i.menuButton_.removeClass("vjs-control"),i.addChild(i.menuButton_),i.update(),i.enabled_=!0,i.on(i.menuButton_,"tap",i.handleClick),i.on(i.menuButton_,"click",i.handleClick),i.on(i.menuButton_,"keydown",i.handleKeyDown),i.on(i.menuButton_,"mouseenter",function(){i.addClass("vjs-hover"),i.menu.show(),ue(h,"keyup",pe(Me(i),i.handleMenuKeyUp))}),i.on("mouseleave",i.handleMouseLeave),i.on("keydown",i.handleSubmenuKeyDown),i}De(e,r);var t=e.prototype;return t.update=function(){var e=this.createMenu();this.menu&&(this.menu.dispose(),this.removeChild(this.menu)),this.menu=e,this.addChild(e),this.buttonPressed_=!1,this.menuButton_.el_.setAttribute("aria-expanded","false"),this.items&&this.items.length<=this.hideThreshold_?this.hide():this.show()},t.createMenu=function(){var e=new ea(this.player_,{menuButton:this});if(this.hideThreshold_=0,this.options_.title){var t=b("li",{className:"vjs-menu-title",innerHTML:Le(this.options_.title),tabIndex:-1});this.hideThreshold_+=1;var i=new xe(this.player_,{el:t});e.addItem(i)}if(this.items=this.createItems(),this.items)for(var n=0;n<this.items.length;n++)e.addItem(this.items[n]);return e},t.createItems=function(){},t.createEl=function(){return r.prototype.createEl.call(this,"div",{className:this.buildWrapperCSSClass()},{})},t.buildWrapperCSSClass=function(){var e="vjs-menu-button";return!0===this.options_.inline?e+="-inline":e+="-popup","vjs-menu-button "+e+" "+wr.prototype.buildCSSClass()+" "+r.prototype.buildCSSClass.call(this)},t.buildCSSClass=function(){var e="vjs-menu-button";return!0===this.options_.inline?e+="-inline":e+="-popup","vjs-menu-button "+e+" "+r.prototype.buildCSSClass.call(this)},t.controlText=function(e,t){return void 0===t&&(t=this.menuButton_.el()),this.menuButton_.controlText(e,t)},t.dispose=function(){this.handleMouseLeave(),r.prototype.dispose.call(this)},t.handleClick=function(e){this.buttonPressed_?this.unpressButton():this.pressButton()},t.handleMouseLeave=function(e){this.removeClass("vjs-hover"),le(h,"keyup",pe(this,this.handleMenuKeyUp))},t.focus=function(){this.menuButton_.focus()},t.blur=function(){this.menuButton_.blur()},t.handleKeyDown=function(e){At.isEventKey(e,"Esc")||At.isEventKey(e,"Tab")?(this.buttonPressed_&&this.unpressButton(),At.isEventKey(e,"Tab")||(e.preventDefault(),this.menuButton_.focus())):(At.isEventKey(e,"Up")||At.isEventKey(e,"Down"))&&(this.buttonPressed_||(e.preventDefault(),this.pressButton()))},t.handleMenuKeyUp=function(e){(At.isEventKey(e,"Esc")||At.isEventKey(e,"Tab"))&&this.removeClass("vjs-hover")},t.handleSubmenuKeyPress=function(e){this.handleSubmenuKeyDown(e)},t.handleSubmenuKeyDown=function(e){(At.isEventKey(e,"Esc")||At.isEventKey(e,"Tab"))&&(this.buttonPressed_&&this.unpressButton(),At.isEventKey(e,"Tab")||(e.preventDefault(),this.menuButton_.focus()))},t.pressButton=function(){if(this.enabled_){if(this.buttonPressed_=!0,this.menu.show(),this.menu.lockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","true"),Ge&&y())return;this.menu.focus()}},t.unpressButton=function(){this.enabled_&&(this.buttonPressed_=!1,this.menu.unlockShowing(),this.menu.hide(),this.menuButton_.el_.setAttribute("aria-expanded","false"))},t.disable=function(){this.unpressButton(),this.enabled_=!1,this.addClass("vjs-disabled"),this.menuButton_.disable()},t.enable=function(){this.enabled_=!0,this.removeClass("vjs-disabled"),this.menuButton_.enable()},e}(xe);xe.registerComponent("MenuButton",ta);var ia=function(a){function e(e,t){var i,n=t.tracks;if((i=a.call(this,e,t)||this).items.length<=1&&i.hide(),!n)return Me(i);var r=pe(Me(i),i.update);return n.addEventListener("removetrack",r),n.addEventListener("addtrack",r),i.player_.on("ready",r),i.player_.on("dispose",function(){n.removeEventListener("removetrack",r),n.removeEventListener("addtrack",r)}),i}return De(e,a),e}(ta);xe.registerComponent("TrackButton",ia);var na=["Tab","Esc","Up","Down","Right","Left"],ra=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).selectable=t.selectable,i.isSelected_=t.selected||!1,i.multiSelectable=t.multiSelectable,i.selected(i.isSelected_),i.selectable?i.multiSelectable?i.el_.setAttribute("role","menuitemcheckbox"):i.el_.setAttribute("role","menuitemradio"):i.el_.setAttribute("role","menuitem"),i}De(e,n);var t=e.prototype;return t.createEl=function(e,t,i){return this.nonIconControl=!0,n.prototype.createEl.call(this,"li",m({className:"vjs-menu-item",innerHTML:'<span class="vjs-menu-item-text">'+this.localize(this.options_.label)+"</span>",tabIndex:-1},t),i)},t.handleKeyDown=function(t){na.some(function(e){return At.isEventKey(t,e)})||n.prototype.handleKeyDown.call(this,t)},t.handleClick=function(e){this.selected(!0)},t.selected=function(e){this.selectable&&(e?(this.addClass("vjs-selected"),this.el_.setAttribute("aria-checked","true"),this.controlText(", selected"),this.isSelected_=!0):(this.removeClass("vjs-selected"),this.el_.setAttribute("aria-checked","false"),this.controlText(""),this.isSelected_=!1))},e}(vr);xe.registerComponent("MenuItem",ra);var aa=function(u){function e(e,t){var n,i=t.track,r=e.textTracks();t.label=i.label||i.language||"Unknown",t.selected="showing"===i.mode,(n=u.call(this,e,t)||this).track=i,n.kinds=(t.kinds||[t.kind||n.track.kind]).filter(Boolean);function a(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];n.handleTracksChange.apply(Me(n),t)}function s(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];n.handleSelectedLanguageChange.apply(Me(n),t)}var o;e.on(["loadstart","texttrackchange"],a),r.addEventListener("change",a),r.addEventListener("selectedlanguagechange",s),n.on("dispose",function(){e.off(["loadstart","texttrackchange"],a),r.removeEventListener("change",a),r.removeEventListener("selectedlanguagechange",s)}),void 0===r.onchange&&n.on(["tap","click"],function(){if("object"!=typeof v.Event)try{o=new v.Event("change")}catch(e){}o||(o=h.createEvent("Event")).initEvent("change",!0,!0),r.dispatchEvent(o)});return n.handleTracksChange(),n}De(e,u);var t=e.prototype;return t.handleClick=function(e){var t=this.track,i=this.player_.textTracks();if(u.prototype.handleClick.call(this,e),i)for(var n=0;n<i.length;n++){var r=i[n];-1!==this.kinds.indexOf(r.kind)&&(r===t?"showing"!==r.mode&&(r.mode="showing"):"disabled"!==r.mode&&(r.mode="disabled"))}},t.handleTracksChange=function(e){var t="showing"===this.track.mode;t!==this.isSelected_&&this.selected(t)},t.handleSelectedLanguageChange=function(e){if("showing"===this.track.mode){var t=this.player_.cache_.selectedLanguage;if(t&&t.enabled&&t.language===this.track.language&&t.kind!==this.track.kind)return;this.player_.cache_.selectedLanguage={enabled:!0,language:this.track.language,kind:this.track.kind}}},t.dispose=function(){this.track=null,u.prototype.dispose.call(this)},e}(ra);xe.registerComponent("TextTrackMenuItem",aa);var sa=function(i){function e(e,t){return t.track={player:e,kind:t.kind,kinds:t.kinds,default:!1,mode:"disabled"},t.kinds||(t.kinds=[t.kind]),t.label?t.track.label=t.label:t.track.label=t.kinds.join(" and ")+" off",t.selectable=!0,t.multiSelectable=!1,i.call(this,e,t)||this}De(e,i);var t=e.prototype;return t.handleTracksChange=function(e){for(var t=this.player().textTracks(),i=!0,n=0,r=t.length;n<r;n++){var a=t[n];if(-1<this.options_.kinds.indexOf(a.kind)&&"showing"===a.mode){i=!1;break}}i!==this.isSelected_&&this.selected(i)},t.handleSelectedLanguageChange=function(e){for(var t=this.player().textTracks(),i=!0,n=0,r=t.length;n<r;n++){var a=t[n];if(-1<["captions","descriptions","subtitles"].indexOf(a.kind)&&"showing"===a.mode){i=!1;break}}i&&(this.player_.cache_.selectedLanguage={enabled:!1})},e}(aa);xe.registerComponent("OffTextTrackMenuItem",sa);var oa=function(i){function e(e,t){return void 0===t&&(t={}),t.tracks=e.textTracks(),i.call(this,e,t)||this}return De(e,i),e.prototype.createItems=function(e,t){var i;void 0===e&&(e=[]),void 0===t&&(t=aa),this.label_&&(i=this.label_+" off"),e.push(new sa(this.player_,{kinds:this.kinds_,kind:this.kind_,label:i})),this.hideThreshold_+=1;var n=this.player_.textTracks();Array.isArray(this.kinds_)||(this.kinds_=[this.kind_]);for(var r=0;r<n.length;r++){var a=n[r];if(-1<this.kinds_.indexOf(a.kind)){var s=new t(this.player_,{track:a,kinds:this.kinds_,kind:this.kind_,selectable:!0,multiSelectable:!1});s.addClass("vjs-"+a.kind+"-menu-item"),e.push(s)}}return e},e}(ia);xe.registerComponent("TextTrackButton",oa);var ua=function(s){function e(e,t){var i,n=t.track,r=t.cue,a=e.currentTime();return t.selectable=!0,t.multiSelectable=!1,t.label=r.text,t.selected=r.startTime<=a&&a<r.endTime,(i=s.call(this,e,t)||this).track=n,i.cue=r,n.addEventListener("cuechange",pe(Me(i),i.update)),i}De(e,s);var t=e.prototype;return t.handleClick=function(e){s.prototype.handleClick.call(this),this.player_.currentTime(this.cue.startTime),this.update(this.cue.startTime)},t.update=function(e){var t=this.cue,i=this.player_.currentTime();this.selected(t.startTime<=i&&i<t.endTime)},e}(ra);xe.registerComponent("ChaptersTrackMenuItem",ua);var la=function(n){function e(e,t,i){return n.call(this,e,t,i)||this}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-chapters-button "+n.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-chapters-button "+n.prototype.buildWrapperCSSClass.call(this)},t.update=function(e){this.track_&&(!e||"addtrack"!==e.type&&"removetrack"!==e.type)||this.setTrack(this.findChaptersTrack()),n.prototype.update.call(this)},t.setTrack=function(e){if(this.track_!==e){if(this.updateHandler_||(this.updateHandler_=this.update.bind(this)),this.track_){var t=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);t&&t.removeEventListener("load",this.updateHandler_),this.track_=null}if(this.track_=e,this.track_){this.track_.mode="hidden";var i=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);i&&i.addEventListener("load",this.updateHandler_)}}},t.findChaptersTrack=function(){for(var e=this.player_.textTracks()||[],t=e.length-1;0<=t;t--){var i=e[t];if(i.kind===this.kind_)return i}},t.getMenuCaption=function(){return this.track_&&this.track_.label?this.track_.label:this.localize(Le(this.kind_))},t.createMenu=function(){return this.options_.title=this.getMenuCaption(),n.prototype.createMenu.call(this)},t.createItems=function(){var e=[];if(!this.track_)return e;var t=this.track_.cues;if(!t)return e;for(var i=0,n=t.length;i<n;i++){var r=t[i],a=new ua(this.player_,{track:this.track_,cue:r});e.push(a)}return e},e}(oa);la.prototype.kind_="chapters",la.prototype.controlText_="Chapters",xe.registerComponent("ChaptersButton",la);var ca=function(s){function e(e,t,i){var n;n=s.call(this,e,t,i)||this;var r=e.textTracks(),a=pe(Me(n),n.handleTracksChange);return r.addEventListener("change",a),n.on("dispose",function(){r.removeEventListener("change",a)}),n}De(e,s);var t=e.prototype;return t.handleTracksChange=function(e){for(var t=this.player().textTracks(),i=!1,n=0,r=t.length;n<r;n++){var a=t[n];if(a.kind!==this.kind_&&"showing"===a.mode){i=!0;break}}i?this.disable():this.enable()},t.buildCSSClass=function(){return"vjs-descriptions-button "+s.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-descriptions-button "+s.prototype.buildWrapperCSSClass.call(this)},e}(oa);ca.prototype.kind_="descriptions",ca.prototype.controlText_="Descriptions",xe.registerComponent("DescriptionsButton",ca);var ha=function(n){function e(e,t,i){return n.call(this,e,t,i)||this}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-subtitles-button "+n.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-subtitles-button "+n.prototype.buildWrapperCSSClass.call(this)},e}(oa);ha.prototype.kind_="subtitles",ha.prototype.controlText_="Subtitles",xe.registerComponent("SubtitlesButton",ha);var da=function(n){function e(e,t){var i;return t.track={player:e,kind:t.kind,label:t.kind+" settings",selectable:!1,default:!1,mode:"disabled"},t.selectable=!1,t.name="CaptionSettingsMenuItem",(i=n.call(this,e,t)||this).addClass("vjs-texttrack-settings"),i.controlText(", opens "+t.kind+" settings dialog"),i}return De(e,n),e.prototype.handleClick=function(e){this.player().getChild("textTrackSettings").open()},e}(aa);xe.registerComponent("CaptionSettingsMenuItem",da);var pa=function(n){function e(e,t,i){return n.call(this,e,t,i)||this}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-captions-button "+n.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-captions-button "+n.prototype.buildWrapperCSSClass.call(this)},t.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(e.push(new da(this.player_,{kind:this.kind_})),this.hideThreshold_+=1),n.prototype.createItems.call(this,e)},e}(oa);pa.prototype.kind_="captions",pa.prototype.controlText_="Captions",xe.registerComponent("CaptionsButton",pa);var fa=function(r){function e(){return r.apply(this,arguments)||this}return De(e,r),e.prototype.createEl=function(e,t,i){var n='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);return"captions"===this.options_.track.kind&&(n+='\n        <span aria-hidden="true" class="vjs-icon-placeholder"></span>\n        <span class="vjs-control-text"> '+this.localize("Captions")+"</span>\n      "),n+="</span>",r.prototype.createEl.call(this,e,m({innerHTML:n},t),i)},e}(aa);xe.registerComponent("SubsCapsMenuItem",fa);var ma=function(n){function e(e,t){var i;return void 0===t&&(t={}),(i=n.call(this,e,t)||this).label_="subtitles",-1<["en","en-us","en-ca","fr-ca"].indexOf(i.player_.language_)&&(i.label_="captions"),i.menuButton_.controlText(Le(i.label_)),i}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-subs-caps-button "+n.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-subs-caps-button "+n.prototype.buildWrapperCSSClass.call(this)},t.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(e.push(new da(this.player_,{kind:this.label_})),this.hideThreshold_+=1),e=n.prototype.createItems.call(this,e,fa)},e}(oa);ma.prototype.kinds_=["captions","subtitles"],ma.prototype.controlText_="Subtitles",xe.registerComponent("SubsCapsButton",ma);var ga=function(s){function e(e,t){var n,i=t.track,r=e.audioTracks();t.label=i.label||i.language||"Unknown",t.selected=i.enabled,(n=s.call(this,e,t)||this).track=i,n.addClass("vjs-"+i.kind+"-menu-item");function a(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];n.handleTracksChange.apply(Me(n),t)}return r.addEventListener("change",a),n.on("dispose",function(){r.removeEventListener("change",a)}),n}De(e,s);var t=e.prototype;return t.createEl=function(e,t,i){var n='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);return"main-desc"===this.options_.track.kind&&(n+='\n        <span aria-hidden="true" class="vjs-icon-placeholder"></span>\n        <span class="vjs-control-text"> '+this.localize("Descriptions")+"</span>\n      "),n+="</span>",s.prototype.createEl.call(this,e,m({innerHTML:n},t),i)},t.handleClick=function(e){var t=this.player_.audioTracks();s.prototype.handleClick.call(this,e);for(var i=0;i<t.length;i++){var n=t[i];n.enabled=n===this.track}},t.handleTracksChange=function(e){this.selected(this.track.enabled)},e}(ra);xe.registerComponent("AudioTrackMenuItem",ga);var ya=function(i){function e(e,t){return void 0===t&&(t={}),t.tracks=e.audioTracks(),i.call(this,e,t)||this}De(e,i);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-audio-button "+i.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-audio-button "+i.prototype.buildWrapperCSSClass.call(this)},t.createItems=function(e){void 0===e&&(e=[]),this.hideThreshold_=1;for(var t=this.player_.audioTracks(),i=0;i<t.length;i++){var n=t[i];e.push(new ga(this.player_,{track:n,selectable:!0,multiSelectable:!1}))}return e},e}(ia);ya.prototype.controlText_="Audio Track",xe.registerComponent("AudioTrackButton",ya);var va=function(a){function e(e,t){var i,n=t.rate,r=parseFloat(n,10);return t.label=n,t.selected=1===r,t.selectable=!0,t.multiSelectable=!1,(i=a.call(this,e,t)||this).label=n,i.rate=r,i.on(e,"ratechange",i.update),i}De(e,a);var t=e.prototype;return t.handleClick=function(e){a.prototype.handleClick.call(this),this.player().playbackRate(this.rate)},t.update=function(e){this.selected(this.player().playbackRate()===this.rate)},e}(ra);va.prototype.contentElType="button",xe.registerComponent("PlaybackRateMenuItem",va);var _a=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).updateVisibility(),i.updateLabel(),i.on(e,"loadstart",i.updateVisibility),i.on(e,"ratechange",i.updateLabel),i}De(e,n);var t=e.prototype;return t.createEl=function(){var e=n.prototype.createEl.call(this);return this.labelEl_=b("div",{className:"vjs-playback-rate-value",innerHTML:"1x"}),e.appendChild(this.labelEl_),e},t.dispose=function(){this.labelEl_=null,n.prototype.dispose.call(this)},t.buildCSSClass=function(){return"vjs-playback-rate "+n.prototype.buildCSSClass.call(this)},t.buildWrapperCSSClass=function(){return"vjs-playback-rate "+n.prototype.buildWrapperCSSClass.call(this)},t.createMenu=function(){var e=new ea(this.player()),t=this.playbackRates();if(t)for(var i=t.length-1;0<=i;i--)e.addChild(new va(this.player(),{rate:t[i]+"x"}));return e},t.updateARIAAttributes=function(){this.el().setAttribute("aria-valuenow",this.player().playbackRate())},t.handleClick=function(e){for(var t=this.player().playbackRate(),i=this.playbackRates(),n=i[0],r=0;r<i.length;r++)if(i[r]>t){n=i[r];break}this.player().playbackRate(n)},t.playbackRates=function(){return this.options_.playbackRates||this.options_.playerOptions&&this.options_.playerOptions.playbackRates},t.playbackRateSupported=function(){return this.player().tech_&&this.player().tech_.featuresPlaybackRate&&this.playbackRates()&&0<this.playbackRates().length},t.updateVisibility=function(e){this.playbackRateSupported()?this.removeClass("vjs-hidden"):this.addClass("vjs-hidden")},t.updateLabel=function(e){this.playbackRateSupported()&&(this.labelEl_.innerHTML=this.player().playbackRate()+"x")},e}(ta);_a.prototype.controlText_="Playback Rate",xe.registerComponent("PlaybackRateMenuButton",_a);var ba=function(e){function t(){return e.apply(this,arguments)||this}De(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-spacer "+e.prototype.buildCSSClass.call(this)},i.createEl=function(){return e.prototype.createEl.call(this,"div",{className:this.buildCSSClass()})},t}(xe);xe.registerComponent("Spacer",ba);var Ta=function(t){function e(){return t.apply(this,arguments)||this}De(e,t);var i=e.prototype;return i.buildCSSClass=function(){return"vjs-custom-control-spacer "+t.prototype.buildCSSClass.call(this)},i.createEl=function(){var e=t.prototype.createEl.call(this,{className:this.buildCSSClass()});return e.innerHTML=" ",e},e}(ba);xe.registerComponent("CustomControlSpacer",Ta);var Sa=function(e){function t(){return e.apply(this,arguments)||this}return De(t,e),t.prototype.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-control-bar",dir:"ltr"})},t}(xe);Sa.prototype.options_={children:["playToggle","volumePanel","currentTimeDisplay","timeDivider","durationDisplay","progressControl","liveDisplay","seekToLive","remainingTimeDisplay","customControlSpacer","playbackRateMenuButton","chaptersButton","descriptionsButton","subsCapsButton","audioTrackButton","fullscreenToggle"]},"exitPictureInPicture"in h&&Sa.prototype.options_.children.splice(Sa.prototype.options_.children.length-1,0,"pictureInPictureToggle"),xe.registerComponent("ControlBar",Sa);var ka=function(n){function e(e,t){var i;return(i=n.call(this,e,t)||this).on(e,"error",i.open),i}De(e,n);var t=e.prototype;return t.buildCSSClass=function(){return"vjs-error-display "+n.prototype.buildCSSClass.call(this)},t.content=function(){var e=this.player().error();return e?this.localize(e.message):""},e}(It);ka.prototype.options_=Oe(It.prototype.options_,{pauseOnOpen:!1,fillAlways:!0,temporary:!1,uncloseable:!0}),xe.registerComponent("ErrorDisplay",ka);var Ca="vjs-text-track-settings",Ea=["#000","Black"],wa=["#00F","Blue"],Aa=["#0FF","Cyan"],Pa=["#0F0","Green"],Ia=["#F0F","Magenta"],La=["#F00","Red"],Oa=["#FFF","White"],xa=["#FF0","Yellow"],Da=["1","Opaque"],Ua=["0.5","Semi-Transparent"],Ra=["0","Transparent"],Ma={backgroundColor:{selector:".vjs-bg-color > select",id:"captions-background-color-%s",label:"Color",options:[Ea,Oa,La,Pa,wa,xa,Ia,Aa]},backgroundOpacity:{selector:".vjs-bg-opacity > select",id:"captions-background-opacity-%s",label:"Transparency",options:[Da,Ua,Ra]},color:{selector:".vjs-fg-color > select",id:"captions-foreground-color-%s",label:"Color",options:[Oa,Ea,La,Pa,wa,xa,Ia,Aa]},edgeStyle:{selector:".vjs-edge-style > select",id:"%s",label:"Text Edge Style",options:[["none","None"],["raised","Raised"],["depressed","Depressed"],["uniform","Uniform"],["dropshadow","Dropshadow"]]},fontFamily:{selector:".vjs-font-family > select",id:"captions-font-family-%s",label:"Font Family",options:[["proportionalSansSerif","Proportional Sans-Serif"],["monospaceSansSerif","Monospace Sans-Serif"],["proportionalSerif","Proportional Serif"],["monospaceSerif","Monospace Serif"],["casual","Casual"],["script","Script"],["small-caps","Small Caps"]]},fontPercent:{selector:".vjs-font-percent > select",id:"captions-font-size-%s",label:"Font Size",options:[["0.50","50%"],["0.75","75%"],["1.00","100%"],["1.25","125%"],["1.50","150%"],["1.75","175%"],["2.00","200%"],["3.00","300%"],["4.00","400%"]],default:2,parser:function(e){return"1.00"===e?null:Number(e)}},textOpacity:{selector:".vjs-text-opacity > select",id:"captions-foreground-opacity-%s",label:"Transparency",options:[Da,Ua]},windowColor:{selector:".vjs-window-color > select",id:"captions-window-color-%s",label:"Color"},windowOpacity:{selector:".vjs-window-opacity > select",id:"captions-window-opacity-%s",label:"Transparency",options:[Ra,Ua,Da]}};function Na(e,t){if(t&&(e=t(e)),e&&"none"!==e)return e}Ma.windowColor.options=Ma.backgroundColor.options;var Ba=function(n){function e(e,t){var i;return t.temporary=!1,(i=n.call(this,e,t)||this).updateDisplay=pe(Me(i),i.updateDisplay),i.fill(),i.hasBeenOpened_=i.hasBeenFilled_=!0,i.endDialog=b("p",{className:"vjs-control-text",textContent:i.localize("End of dialog window.")}),i.el().appendChild(i.endDialog),i.setDefaults(),void 0===t.persistTextTrackSettings&&(i.options_.persistTextTrackSettings=i.options_.playerOptions.persistTextTrackSettings),i.on(i.$(".vjs-done-button"),"click",function(){i.saveSettings(),i.close()}),i.on(i.$(".vjs-default-button"),"click",function(){i.setDefaults(),i.updateDisplay()}),r(Ma,function(e){i.on(i.$(e.selector),"change",i.updateDisplay)}),i.options_.persistTextTrackSettings&&i.restoreSettings(),i}De(e,n);var t=e.prototype;return t.dispose=function(){this.endDialog=null,n.prototype.dispose.call(this)},t.createElSelect_=function(e,t,i){var n=this;void 0===t&&(t=""),void 0===i&&(i="label");var r=Ma[e],a=r.id.replace("%s",this.id_),s=[t,a].join(" ").trim();return["<"+i+' id="'+a+'" class="'+("label"===i?"vjs-label":"")+'">',this.localize(r.label),"</"+i+">",'<select aria-labelledby="'+s+'">'].concat(r.options.map(function(e){var t=a+"-"+e[1].replace(/\W+/g,"");return['<option id="'+t+'" value="'+e[0]+'" ','aria-labelledby="'+s+" "+t+'">',n.localize(e[1]),"</option>"].join("")})).concat("</select>").join("")},t.createElFgColor_=function(){var e="captions-text-legend-"+this.id_;return['<fieldset class="vjs-fg-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Text"),"</legend>",this.createElSelect_("color",e),'<span class="vjs-text-opacity vjs-opacity">',this.createElSelect_("textOpacity",e),"</span>","</fieldset>"].join("")},t.createElBgColor_=function(){var e="captions-background-"+this.id_;return['<fieldset class="vjs-bg-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Background"),"</legend>",this.createElSelect_("backgroundColor",e),'<span class="vjs-bg-opacity vjs-opacity">',this.createElSelect_("backgroundOpacity",e),"</span>","</fieldset>"].join("")},t.createElWinColor_=function(){var e="captions-window-"+this.id_;return['<fieldset class="vjs-window-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Window"),"</legend>",this.createElSelect_("windowColor",e),'<span class="vjs-window-opacity vjs-opacity">',this.createElSelect_("windowOpacity",e),"</span>","</fieldset>"].join("")},t.createElColors_=function(){return b("div",{className:"vjs-track-settings-colors",innerHTML:[this.createElFgColor_(),this.createElBgColor_(),this.createElWinColor_()].join("")})},t.createElFont_=function(){return b("div",{className:"vjs-track-settings-font",innerHTML:['<fieldset class="vjs-font-percent vjs-track-setting">',this.createElSelect_("fontPercent","","legend"),"</fieldset>",'<fieldset class="vjs-edge-style vjs-track-setting">',this.createElSelect_("edgeStyle","","legend"),"</fieldset>",'<fieldset class="vjs-font-family vjs-track-setting">',this.createElSelect_("fontFamily","","legend"),"</fieldset>"].join("")})},t.createElControls_=function(){var e=this.localize("restore all settings to the default values");return b("div",{className:"vjs-track-settings-controls",innerHTML:['<button type="button" class="vjs-default-button" title="'+e+'">',this.localize("Reset"),'<span class="vjs-control-text"> '+e+"</span>","</button>",'<button type="button" class="vjs-done-button">'+this.localize("Done")+"</button>"].join("")})},t.content=function(){return[this.createElColors_(),this.createElFont_(),this.createElControls_()]},t.label=function(){return this.localize("Caption Settings Dialog")},t.description=function(){return this.localize("Beginning of dialog window. Escape will cancel and close the window.")},t.buildCSSClass=function(){return n.prototype.buildCSSClass.call(this)+" vjs-text-track-settings"},t.getValues=function(){var r=this;return function(i,n,e){return void 0===e&&(e=0),a(i).reduce(function(e,t){return n(e,i[t],t)},e)}(Ma,function(e,t,i){var n=function(e,t){return Na(e.options[e.options.selectedIndex].value,t)}(r.$(t.selector),t.parser);return void 0!==n&&(e[i]=n),e},{})},t.setValues=function(i){var n=this;r(Ma,function(e,t){!function(e,t,i){if(t)for(var n=0;n<e.options.length;n++)if(Na(e.options[n].value,i)===t){e.selectedIndex=n;break}}(n.$(e.selector),i[t],e.parser)})},t.setDefaults=function(){var i=this;r(Ma,function(e){var t=e.hasOwnProperty("default")?e.default:0;i.$(e.selector).selectedIndex=t})},t.restoreSettings=function(){var e;try{e=JSON.parse(v.localStorage.getItem(Ca))}catch(e){p.warn(e)}e&&this.setValues(e)},t.saveSettings=function(){if(this.options_.persistTextTrackSettings){var e=this.getValues();try{Object.keys(e).length?v.localStorage.setItem(Ca,JSON.stringify(e)):v.localStorage.removeItem(Ca)}catch(e){p.warn(e)}}},t.updateDisplay=function(){var e=this.player_.getChild("textTrackDisplay");e&&e.updateDisplay()},t.conditionalBlur_=function(){this.previouslyActiveEl_=null;var e=this.player_.controlBar,t=e&&e.subsCapsButton,i=e&&e.captionsButton;t?t.focus():i&&i.focus()},e}(It);xe.registerComponent("TextTrackSettings",Ba);var ja=function(a){function e(e,t){var i,n=t.ResizeObserver||v.ResizeObserver;null===t.ResizeObserver&&(n=!1);var r=Oe({createEl:!n,reportTouchActivity:!1},t);return(i=a.call(this,e,r)||this).ResizeObserver=t.ResizeObserver||v.ResizeObserver,i.loadListener_=null,i.resizeObserver_=null,i.debouncedHandler_=function(n,r,a,s){var o;void 0===s&&(s=v);function e(){var e=this,t=arguments,i=function(){i=o=null,a||n.apply(e,t)};!o&&a&&n.apply(e,t),s.clearTimeout(o),o=s.setTimeout(i,r)}return e.cancel=function(){s.clearTimeout(o),o=null},e}(function(){i.resizeHandler()},100,!1,Me(i)),n?(i.resizeObserver_=new i.ResizeObserver(i.debouncedHandler_),i.resizeObserver_.observe(e.el())):(i.loadListener_=function(){if(i.el_&&i.el_.contentWindow){var e=i.debouncedHandler_,t=i.unloadListener_=function(){le(this,"resize",e),le(this,"unload",t),t=null};ue(i.el_.contentWindow,"unload",t),ue(i.el_.contentWindow,"resize",e)}},i.one("load",i.loadListener_)),i}De(e,a);var t=e.prototype;return t.createEl=function(){return a.prototype.createEl.call(this,"iframe",{className:"vjs-resize-manager",tabIndex:-1},{"aria-hidden":"true"})},t.resizeHandler=function(){this.player_&&this.player_.trigger&&this.player_.trigger("playerresize")},t.dispose=function(){this.debouncedHandler_&&this.debouncedHandler_.cancel(),this.resizeObserver_&&(this.player_.el()&&this.resizeObserver_.unobserve(this.player_.el()),this.resizeObserver_.disconnect()),this.loadListener_&&this.off("load",this.loadListener_),this.el_&&this.el_.contentWindow&&this.unloadListener_&&this.unloadListener_.call(this.el_.contentWindow),this.ResizeObserver=null,this.resizeObserver=null,this.debouncedHandler_=null,this.loadListener_=null,a.prototype.dispose.call(this)},e}(xe);xe.registerComponent("ResizeManager",ja);var Fa=function(r){function e(e,t){var i,n=Oe({createEl:!1},t);return(i=r.call(this,e,n)||this).reset_(),i.on(i.player_,"durationchange",i.handleDurationchange),it&&"hidden"in h&&"visibilityState"in h&&i.on(h,"visibilitychange",i.handleVisibilityChange),i}De(e,r);var t=e.prototype;return t.handleVisibilityChange=function(){this.player_.duration()===1/0&&(h.hidden?this.stopTracking():this.startTracking())},t.isBehind_=function(){if(!this.timeupdateSeen_)return!1;var e=this.liveCurrentTime(),t=this.player_.currentTime(),i=2*this.seekableIncrement_+.07;return e!==1/0&&t<=e-i},t.trackLive_=function(){this.pastSeekEnd_=this.pastSeekEnd_;var e=this.player_.seekable();if(e&&e.length){var t=this.seekableEnd();t!==this.lastSeekEnd_&&(this.lastSeekEnd_&&(this.seekableIncrementList_=this.seekableIncrementList_.slice(-11),this.seekableIncrementList_.push(Math.abs(t-this.lastSeekEnd_)),3<this.seekableIncrementList_.length&&(this.seekableIncrement_=function(e){var t=Math.floor(e.length/2),i=[].concat(e).sort(function(e,t){return e-t});return e.length%2!=0?i[t]:(i[t-1]+i[t])/2}(this.seekableIncrementList_))),this.pastSeekEnd_=0,this.lastSeekEnd_=t,this.trigger("seekableendchange")),this.pastSeekEnd_=this.pastSeekEnd()+.03,this.isBehind_()!==this.behindLiveEdge()&&(this.behindLiveEdge_=this.isBehind_(),this.trigger("liveedgechange"))}},t.handleDurationchange=function(){this.player_.duration()===1/0?this.startTracking():this.stopTracking()},t.startTracking=function(){var e=this;this.isTracking()||(this.timeupdateSeen_||(this.timeupdateSeen_=this.player_.hasStarted()),this.trackingInterval_=this.setInterval(this.trackLive_,30),this.trackLive_(),this.on(this.player_,"play",this.trackLive_),this.on(this.player_,"pause",this.trackLive_),this.timeupdateSeen_||(this.one(this.player_,"play",this.handlePlay),this.handleTimeupdate=function(){e.timeupdateSeen_=!0,e.handleTimeupdate=null},this.one(this.player_,"timeupdate",this.handleTimeupdate)))},t.handlePlay=function(){this.one(this.player_,"timeupdate",this.seekToLiveEdge)},t.reset_=function(){this.pastSeekEnd_=0,this.lastSeekEnd_=null,this.behindLiveEdge_=null,this.timeupdateSeen_=!1,this.clearInterval(this.trackingInterval_),this.trackingInterval_=null,this.seekableIncrement_=12,this.seekableIncrementList_=[],this.off(this.player_,"play",this.trackLive_),this.off(this.player_,"pause",this.trackLive_),this.off(this.player_,"play",this.handlePlay),this.off(this.player_,"timeupdate",this.seekToLiveEdge),this.handleTimeupdate&&(this.off(this.player_,"timeupdate",this.handleTimeupdate),this.handleTimeupdate=null)},t.stopTracking=function(){this.isTracking()&&this.reset_()},t.seekableEnd=function(){for(var e=this.player_.seekable(),t=[],i=e?e.length:0;i--;)t.push(e.end(i));return t.length?t.sort()[t.length-1]:1/0},t.seekableStart=function(){for(var e=this.player_.seekable(),t=[],i=e?e.length:0;i--;)t.push(e.start(i));return t.length?t.sort()[0]:0},t.liveWindow=function(){var e=this.liveCurrentTime();return e===1/0?1/0:e-this.seekableStart()},t.isLive=function(){return this.isTracking()},t.atLiveEdge=function(){return!this.behindLiveEdge()},t.liveCurrentTime=function(){return this.pastSeekEnd()+this.seekableEnd()},t.pastSeekEnd=function(){return this.pastSeekEnd_},t.behindLiveEdge=function(){return this.behindLiveEdge_},t.isTracking=function(){return"number"==typeof this.trackingInterval_},t.seekToLiveEdge=function(){this.atLiveEdge()||(this.player_.currentTime(this.liveCurrentTime()),this.player_.paused()&&this.player_.play())},t.dispose=function(){this.stopTracking(),r.prototype.dispose.call(this)},e}(xe);xe.registerComponent("LiveTracker",Fa);function Ha(e){var t=e.el();if(t.hasAttribute("src"))return e.triggerSourceset(t.src),!0;var i=e.$$("source"),n=[],r="";if(!i.length)return!1;for(var a=0;a<i.length;a++){var s=i[a].src;s&&-1===n.indexOf(s)&&n.push(s)}return!!n.length&&(1===n.length&&(r=n[0]),e.triggerSourceset(r),!0)}function Va(e,t){for(var i={},n=0;n<e.length&&!((i=Object.getOwnPropertyDescriptor(e[n],t))&&i.set&&i.get);n++);return i.enumerable=!0,i.configurable=!0,i}function qa(a){var s=a.el();if(!s.resetSourceWatch_){function t(r){return function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var n=r.apply(s,t);return Ha(a),n}}var i={},e=function(e){return Va([e.el(),v.HTMLMediaElement.prototype,v.Element.prototype,za],"innerHTML")}(a);["append","appendChild","insertAdjacentHTML"].forEach(function(e){s[e]&&(i[e]=s[e],s[e]=t(i[e]))}),Object.defineProperty(s,"innerHTML",Oe(e,{set:t(e.set)})),s.resetSourceWatch_=function(){s.resetSourceWatch_=null,Object.keys(i).forEach(function(e){s[e]=i[e]}),Object.defineProperty(s,"innerHTML",e)},a.one("sourceset",s.resetSourceWatch_)}}function Wa(n){if(n.featuresSourceset){var r=n.el();if(!r.resetSourceset_){var i=function(e){return Va([e.el(),v.HTMLMediaElement.prototype,$a],"src")}(n),a=r.setAttribute,t=r.load;Object.defineProperty(r,"src",Oe(i,{set:function(e){var t=i.set.call(r,e);return n.triggerSourceset(r.src),t}})),r.setAttribute=function(e,t){var i=a.call(r,e,t);return/src/i.test(e)&&n.triggerSourceset(r.src),i},r.load=function(){var e=t.call(r);return Ha(n)||(n.triggerSourceset(""),qa(n)),e},r.currentSrc?n.triggerSourceset(r.currentSrc):Ha(n)||qa(n),r.resetSourceset_=function(){r.resetSourceset_=null,r.load=t,r.setAttribute=a,Object.defineProperty(r,"src",i),r.resetSourceWatch_&&r.resetSourceWatch_()}}}}var za=Object.defineProperty({},"innerHTML",{get:function(){return this.cloneNode(!0).innerHTML},set:function(e){var t=h.createElement(this.nodeName.toLowerCase());t.innerHTML=e;for(var i=h.createDocumentFragment();t.childNodes.length;)i.appendChild(t.childNodes[0]);return this.innerText="",v.Element.prototype.appendChild.call(this,i),this.innerHTML}}),$a=Object.defineProperty({},"src",{get:function(){return this.hasAttribute("src")?Rt(v.Element.prototype.getAttribute.call(this,"src")):""},set:function(e){return v.Element.prototype.setAttribute.call(this,"src",e),e}}),Ga=function(c){function o(e,t){var i;i=c.call(this,e,t)||this;var n=e.source,r=!1;if(n&&(i.el_.currentSrc!==n.src||e.tag&&3===e.tag.initNetworkState_)?i.setSource(n):i.handleLateInit_(i.el_),e.enableSourceset&&i.setupSourcesetHandling_(),i.el_.hasChildNodes()){for(var a=i.el_.childNodes,s=a.length,o=[];s--;){var u=a[s];"track"===u.nodeName.toLowerCase()&&(i.featuresNativeTextTracks?(i.remoteTextTrackEls().addTrackElement_(u),i.remoteTextTracks().addTrack(u.track),i.textTracks().addTrack(u.track),r||i.el_.hasAttribute("crossorigin")||!Nt(u.src)||(r=!0)):o.push(u))}for(var l=0;l<o.length;l++)i.el_.removeChild(o[l])}return i.proxyNativeTracks_(),i.featuresNativeTextTracks&&r&&p.warn("Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\nThis may prevent text tracks from loading."),i.restoreMetadataTracksInIOSNativePlayer_(),(st||ze||Qe)&&!0===e.nativeControlsForTouch&&i.setControls(!0),i.proxyWebkitFullscreen_(),i.triggerReady(),i}De(o,c);var e=o.prototype;return e.dispose=function(){this.el_&&this.el_.resetSourceset_&&this.el_.resetSourceset_(),o.disposeMediaElement(this.el_),this.options_=null,c.prototype.dispose.call(this)},e.setupSourcesetHandling_=function(){Wa(this)},e.restoreMetadataTracksInIOSNativePlayer_=function(){function e(){i=[];for(var e=0;e<n.length;e++){var t=n[e];"metadata"===t.kind&&i.push({track:t,storedMode:t.mode})}}var i,n=this.textTracks();e(),n.addEventListener("change",e),this.on("dispose",function(){return n.removeEventListener("change",e)});function r(){for(var e=0;e<i.length;e++){var t=i[e];"disabled"===t.track.mode&&t.track.mode!==t.storedMode&&(t.track.mode=t.storedMode)}n.removeEventListener("change",r)}this.on("webkitbeginfullscreen",function(){n.removeEventListener("change",e),n.removeEventListener("change",r),n.addEventListener("change",r)}),this.on("webkitendfullscreen",function(){n.removeEventListener("change",e),n.addEventListener("change",e),n.removeEventListener("change",r)})},e.overrideNative_=function(e,t){var i=this;if(t===this["featuresNative"+e+"Tracks"]){var n=e.toLowerCase();this[n+"TracksListeners_"]&&Object.keys(this[n+"TracksListeners_"]).forEach(function(e){i.el()[n+"Tracks"].removeEventListener(e,i[n+"TracksListeners_"][e])}),this["featuresNative"+e+"Tracks"]=!t,this[n+"TracksListeners_"]=null,this.proxyNativeTracksForType_(n)}},e.overrideNativeAudioTracks=function(e){this.overrideNative_("Audio",e)},e.overrideNativeVideoTracks=function(e){this.overrideNative_("Video",e)},e.proxyNativeTracksForType_=function(e){var n=this,t=En[e],r=this.el()[t.getterName],a=this[t.getterName]();if(this["featuresNative"+t.capitalName+"Tracks"]&&r&&r.addEventListener){function i(){for(var e=[],t=0;t<a.length;t++){for(var i=!1,n=0;n<r.length;n++)if(r[n]===a[t]){i=!0;break}i||e.push(a[t])}for(;e.length;)a.removeTrack(e.shift())}var s={change:function(e){a.trigger({type:"change",target:a,currentTarget:a,srcElement:a})},addtrack:function(e){a.addTrack(e.track)},removetrack:function(e){a.removeTrack(e.track)}};this[t.getterName+"Listeners_"]=s,Object.keys(s).forEach(function(t){var i=s[t];r.addEventListener(t,i),n.on("dispose",function(e){return r.removeEventListener(t,i)})}),this.on("loadstart",i),this.on("dispose",function(e){return n.off("loadstart",i)})}},e.proxyNativeTracks_=function(){var t=this;En.names.forEach(function(e){t.proxyNativeTracksForType_(e)})},e.createEl=function(){var e=this.options_.tag;if(!e||!this.options_.playerElIngest&&!this.movingMediaElementInDOM){if(e){var t=e.cloneNode(!0);e.parentNode&&e.parentNode.insertBefore(t,e),o.disposeMediaElement(e),e=t}else{e=h.createElement("video");var i=Oe({},this.options_.tag&&P(this.options_.tag));st&&!0===this.options_.nativeControlsForTouch||delete i.controls,A(e,m(i,{id:this.options_.techId,class:"vjs-tech"}))}e.playerId=this.options_.playerId}"undefined"!=typeof this.options_.preload&&L(e,"preload",this.options_.preload);for(var n=["loop","muted","playsinline","autoplay"],r=0;r<n.length;r++){var a=n[r],s=this.options_[a];"undefined"!=typeof s&&(s?L(e,a,a):O(e,a),e[a]=s)}return e},e.handleLateInit_=function(e){if(0!==e.networkState&&3!==e.networkState){if(0===e.readyState){function t(){i=!0}var i=!1;this.on("loadstart",t);function n(){i||this.trigger("loadstart")}return this.on("loadedmetadata",n),void this.ready(function(){this.off("loadstart",t),this.off("loadedmetadata",n),i||this.trigger("loadstart")})}var r=["loadstart"];r.push("loadedmetadata"),2<=e.readyState&&r.push("loadeddata"),3<=e.readyState&&r.push("canplay"),4<=e.readyState&&r.push("canplaythrough"),this.ready(function(){r.forEach(function(e){this.trigger(e)},this)})}},e.setCurrentTime=function(e){try{this.el_.currentTime=e}catch(e){p(e,"Video is not ready. (Video.js)")}},e.duration=function(){var t=this;if(this.el_.duration===1/0&&Ke&&et&&0===this.el_.currentTime){return this.on("timeupdate",function e(){0<t.el_.currentTime&&(t.el_.duration===1/0&&t.trigger("durationchange"),t.off("timeupdate",e))}),NaN}return this.el_.duration||NaN},e.width=function(){return this.el_.offsetWidth},e.height=function(){return this.el_.offsetHeight},e.proxyWebkitFullscreen_=function(){var e=this;if("webkitDisplayingFullscreen"in this.el_){function t(){this.trigger("fullscreenchange",{isFullscreen:!1})}function i(){"webkitPresentationMode"in this.el_&&"picture-in-picture"!==this.el_.webkitPresentationMode&&(this.one("webkitendfullscreen",t),this.trigger("fullscreenchange",{isFullscreen:!0}))}this.on("webkitbeginfullscreen",i),this.on("dispose",function(){e.off("webkitbeginfullscreen",i),e.off("webkitendfullscreen",t)})}},e.supportsFullScreen=function(){if("function"==typeof this.el_.webkitEnterFullScreen){var e=v.navigator&&v.navigator.userAgent||"";if(/Android/.test(e)||!/Chrome|Mac OS X 10.5/.test(e))return!0}return!1},e.enterFullScreen=function(){var e=this.el_;e.paused&&e.networkState<=e.HAVE_METADATA?(this.el_.play(),this.setTimeout(function(){e.pause(),e.webkitEnterFullScreen()},0)):e.webkitEnterFullScreen()},e.exitFullScreen=function(){this.el_.webkitExitFullScreen()},e.requestPictureInPicture=function(){return this.el_.requestPictureInPicture()},e.src=function(e){if(void 0===e)return this.el_.src;this.setSrc(e)},e.reset=function(){o.resetMediaElement(this.el_)},e.currentSrc=function(){return this.currentSource_?this.currentSource_.src:this.el_.currentSrc},e.setControls=function(e){this.el_.controls=!!e},e.addTextTrack=function(e,t,i){return this.featuresNativeTextTracks?this.el_.addTextTrack(e,t,i):c.prototype.addTextTrack.call(this,e,t,i)},e.createRemoteTextTrack=function(e){if(!this.featuresNativeTextTracks)return c.prototype.createRemoteTextTrack.call(this,e);var t=h.createElement("track");return e.kind&&(t.kind=e.kind),e.label&&(t.label=e.label),(e.language||e.srclang)&&(t.srclang=e.language||e.srclang),e.default&&(t.default=e.default),e.id&&(t.id=e.id),e.src&&(t.src=e.src),t},e.addRemoteTextTrack=function(e,t){var i=c.prototype.addRemoteTextTrack.call(this,e,t);return this.featuresNativeTextTracks&&this.el().appendChild(i),i},e.removeRemoteTextTrack=function(e){if(c.prototype.removeRemoteTextTrack.call(this,e),this.featuresNativeTextTracks)for(var t=this.$$("track"),i=t.length;i--;)e!==t[i]&&e!==t[i].track||this.el().removeChild(t[i])},e.getVideoPlaybackQuality=function(){if("function"==typeof this.el().getVideoPlaybackQuality)return this.el().getVideoPlaybackQuality();var e={};return"undefined"!=typeof this.el().webkitDroppedFrameCount&&"undefined"!=typeof this.el().webkitDecodedFrameCount&&(e.droppedVideoFrames=this.el().webkitDroppedFrameCount,e.totalVideoFrames=this.el().webkitDecodedFrameCount),v.performance&&"function"==typeof v.performance.now?e.creationTime=v.performance.now():v.performance&&v.performance.timing&&"number"==typeof v.performance.timing.navigationStart&&(e.creationTime=v.Date.now()-v.performance.timing.navigationStart),e},o}(nr);if(c()){Ga.TEST_VID=h.createElement("video");var Xa=h.createElement("track");Xa.kind="captions",Xa.srclang="en",Xa.label="English",Ga.TEST_VID.appendChild(Xa)}Ga.isSupported=function(){try{Ga.TEST_VID.volume=.5}catch(e){return!1}return!(!Ga.TEST_VID||!Ga.TEST_VID.canPlayType)},Ga.canPlayType=function(e){return Ga.TEST_VID.canPlayType(e)},Ga.canPlaySource=function(e,t){return Ga.canPlayType(e.type)},Ga.canControlVolume=function(){try{var e=Ga.TEST_VID.volume;return Ga.TEST_VID.volume=e/2+.1,e!==Ga.TEST_VID.volume}catch(e){return!1}},Ga.canMuteVolume=function(){try{var e=Ga.TEST_VID.muted;return Ga.TEST_VID.muted=!e,Ga.TEST_VID.muted?L(Ga.TEST_VID,"muted","muted"):O(Ga.TEST_VID,"muted"),e!==Ga.TEST_VID.muted}catch(e){return!1}},Ga.canControlPlaybackRate=function(){if(Ke&&et&&tt<58)return!1;try{var e=Ga.TEST_VID.playbackRate;return Ga.TEST_VID.playbackRate=e/2+.1,e!==Ga.TEST_VID.playbackRate}catch(e){return!1}},Ga.canOverrideAttributes=function(){try{function e(){}Object.defineProperty(h.createElement("video"),"src",{get:e,set:e}),Object.defineProperty(h.createElement("audio"),"src",{get:e,set:e}),Object.defineProperty(h.createElement("video"),"innerHTML",{get:e,set:e}),Object.defineProperty(h.createElement("audio"),"innerHTML",{get:e,set:e})}catch(e){return!1}return!0},Ga.supportsNativeTextTracks=function(){return rt||Ge&&et},Ga.supportsNativeVideoTracks=function(){return!(!Ga.TEST_VID||!Ga.TEST_VID.videoTracks)},Ga.supportsNativeAudioTracks=function(){return!(!Ga.TEST_VID||!Ga.TEST_VID.audioTracks)},Ga.Events=["loadstart","suspend","abort","error","emptied","stalled","loadedmetadata","loadeddata","canplay","canplaythrough","playing","waiting","seeking","seeked","ended","durationchange","timeupdate","progress","play","pause","ratechange","resize","volumechange"],Ga.prototype.featuresVolumeControl=Ga.canControlVolume(),Ga.prototype.featuresMuteControl=Ga.canMuteVolume(),Ga.prototype.featuresPlaybackRate=Ga.canControlPlaybackRate(),Ga.prototype.featuresSourceset=Ga.canOverrideAttributes(),Ga.prototype.movingMediaElementInDOM=!Ge,Ga.prototype.featuresFullscreenResize=!0,Ga.prototype.featuresProgressEvents=!0,Ga.prototype.featuresTimeupdateEvents=!0,Ga.prototype.featuresNativeTextTracks=Ga.supportsNativeTextTracks(),Ga.prototype.featuresNativeVideoTracks=Ga.supportsNativeVideoTracks(),Ga.prototype.featuresNativeAudioTracks=Ga.supportsNativeAudioTracks();var Ka=Ga.TEST_VID&&Ga.TEST_VID.constructor.prototype.canPlayType,Ya=/^application\/(?:x-|vnd\.apple\.)mpegurl/i;Ga.patchCanPlayType=function(){4<=Ye&&!Je&&!et&&(Ga.TEST_VID.constructor.prototype.canPlayType=function(e){return e&&Ya.test(e)?"maybe":Ka.call(this,e)})},Ga.unpatchCanPlayType=function(){var e=Ga.TEST_VID.constructor.prototype.canPlayType;return Ga.TEST_VID.constructor.prototype.canPlayType=Ka,e},Ga.patchCanPlayType(),Ga.disposeMediaElement=function(e){if(e){for(e.parentNode&&e.parentNode.removeChild(e);e.hasChildNodes();)e.removeChild(e.firstChild);e.removeAttribute("src"),"function"==typeof e.load&&function(){try{e.load()}catch(e){}}()}},Ga.resetMediaElement=function(e){if(e){for(var t=e.querySelectorAll("source"),i=t.length;i--;)e.removeChild(t[i]);e.removeAttribute("src"),"function"==typeof e.load&&function(){try{e.load()}catch(e){}}()}},["muted","defaultMuted","autoplay","controls","loop","playsinline"].forEach(function(e){Ga.prototype[e]=function(){return this.el_[e]||this.el_.hasAttribute(e)}}),["muted","defaultMuted","autoplay","loop","playsinline"].forEach(function(t){Ga.prototype["set"+Le(t)]=function(e){(this.el_[t]=e)?this.el_.setAttribute(t,t):this.el_.removeAttribute(t)}}),["paused","currentTime","buffered","volume","poster","preload","error","seeking","seekable","ended","playbackRate","defaultPlaybackRate","played","networkState","readyState","videoWidth","videoHeight"].forEach(function(e){Ga.prototype[e]=function(){return this.el_[e]}}),["volume","src","poster","preload","playbackRate","defaultPlaybackRate"].forEach(function(t){Ga.prototype["set"+Le(t)]=function(e){this.el_[t]=e}}),["pause","load","play"].forEach(function(e){Ga.prototype[e]=function(){return this.el_[e]()}}),nr.withSourceHandlers(Ga),Ga.nativeSourceHandler={},Ga.nativeSourceHandler.canPlayType=function(e){try{return Ga.TEST_VID.canPlayType(e)}catch(e){return""}},Ga.nativeSourceHandler.canHandleSource=function(e,t){if(e.type)return Ga.nativeSourceHandler.canPlayType(e.type);if(e.src){var i=Mt(e.src);return Ga.nativeSourceHandler.canPlayType("video/"+i)}return""},Ga.nativeSourceHandler.handleSource=function(e,t,i){t.setSrc(e.src)},Ga.nativeSourceHandler.dispose=function(){},Ga.registerSourceHandler(Ga.nativeSourceHandler),nr.registerTech("Html5",Ga);var Qa=["progress","abort","suspend","emptied","stalled","loadedmetadata","loadeddata","timeupdate","resize","volumechange","texttrackchange"],Ja={canplay:"CanPlay",canplaythrough:"CanPlayThrough",playing:"Playing",seeked:"Seeked"},Za=["tiny","xsmall","small","medium","large","xlarge","huge"],es={};Za.forEach(function(e){var t="x"===e.charAt(0)?"x-"+e.substring(1):e;es[e]="vjs-layout-"+t});var ts={tiny:210,xsmall:320,small:425,medium:768,large:1440,xlarge:2560,huge:1/0},is=function(c){function l(e,t,i){var n;if(e.id=e.id||t.id||"vjs_video_"+te(),(t=m(l.getTagSettings(e),t)).initChildren=!1,t.createEl=!1,t.evented=!1,t.reportTouchActivity=!1,!t.language)if("function"==typeof e.closest){var r=e.closest("[lang]");r&&r.getAttribute&&(t.language=r.getAttribute("lang"))}else for(var a=e;a&&1===a.nodeType;){if(P(a).hasOwnProperty("lang")){t.language=a.getAttribute("lang");break}a=a.parentNode}if((n=c.call(this,null,t,i)||this).boundDocumentFullscreenChange_=pe(Me(n),n.documentFullscreenChange_),n.boundFullWindowOnEscKey_=pe(Me(n),n.fullWindowOnEscKey),n.log=f(n.id_),n.fsApi_=pt,n.isPosterFromTech_=!1,n.queuedCallbacks_=[],n.isReady_=!1,n.hasStarted_=!1,n.userActive_=!1,!n.options_||!n.options_.techOrder||!n.options_.techOrder.length)throw new Error("No techOrder specified. Did you overwrite videojs.options instead of just changing the properties you want to override?");if(n.tag=e,n.tagAttributes=e&&P(e),n.language(n.options_.language),t.languages){var s={};Object.getOwnPropertyNames(t.languages).forEach(function(e){s[e.toLowerCase()]=t.languages[e]}),n.languages_=s}else n.languages_=l.prototype.options_.languages;n.resetCache_(),n.poster_=t.poster||"",n.controls_=!!t.controls,e.controls=!1,e.removeAttribute("controls"),n.changingSrc_=!1,n.playCallbacks_=[],n.playTerminatedQueue_=[],e.hasAttribute("autoplay")?n.autoplay(!0):n.autoplay(n.options_.autoplay),t.plugins&&Object.keys(t.plugins).forEach(function(e){if("function"!=typeof n[e])throw new Error('plugin "'+e+'" does not exist')}),n.scrubbing_=!1,n.el_=n.createEl(),we(Me(n),{eventBusKey:"el_"}),n.fluid_&&n.on("playerreset",n.updateStyleEl_);var o=Oe(n.options_);t.plugins&&Object.keys(t.plugins).forEach(function(e){n[e](t.plugins[e])}),n.options_.playerOptions=o,n.middleware_=[],n.initChildren(),n.isAudio("audio"===e.nodeName.toLowerCase()),n.controls()?n.addClass("vjs-controls-enabled"):n.addClass("vjs-controls-disabled"),n.el_.setAttribute("role","region"),n.isAudio()?n.el_.setAttribute("aria-label",n.localize("Audio Player")):n.el_.setAttribute("aria-label",n.localize("Video Player")),n.isAudio()&&n.addClass("vjs-audio"),n.flexNotSupported_()&&n.addClass("vjs-no-flex"),st&&n.addClass("vjs-touch-enabled"),Ge||n.addClass("vjs-workinghover"),l.players[n.id_]=Me(n);var u=d.split(".")[0];return n.addClass("vjs-v"+u),n.userActive(!0),n.reportUserActivity(),n.one("play",n.listenForUserActivity_),n.on("stageclick",n.handleStageClick_),n.on("keydown",n.handleKeyDown),n.breakpoints(n.options_.breakpoints),n.responsive(n.options_.responsive),n}De(l,c);var e=l.prototype;return e.dispose=function(){var n=this;this.trigger("dispose"),this.off("dispose"),le(h,this.fsApi_.fullscreenchange,this.boundDocumentFullscreenChange_),le(h,"keydown",this.boundFullWindowOnEscKey_),this.styleEl_&&this.styleEl_.parentNode&&(this.styleEl_.parentNode.removeChild(this.styleEl_),this.styleEl_=null),l.players[this.id_]=null,this.tag&&this.tag.player&&(this.tag.player=null),this.el_&&this.el_.player&&(this.el_.player=null),this.tech_&&(this.tech_.dispose(),this.isPosterFromTech_=!1,this.poster_=""),this.playerElIngest_&&(this.playerElIngest_=null),this.tag&&(this.tag=null),function(e){ar[e.id()]=null}(this),An.names.forEach(function(e){var t=An[e],i=n[t.getterName]();i&&i.off&&i.off()}),c.prototype.dispose.call(this)},e.createEl=function(){var t,i=this.tag,e=this.playerElIngest_=i.parentNode&&i.parentNode.hasAttribute&&i.parentNode.hasAttribute("data-vjs-player"),n="video-js"===this.tag.tagName.toLowerCase();e?t=this.el_=i.parentNode:n||(t=this.el_=c.prototype.createEl.call(this,"div"));var r=P(i);if(n){for(t=this.el_=i,i=this.tag=h.createElement("video");t.children.length;)i.appendChild(t.firstChild);k(t,"video-js")||C(t,"video-js"),t.appendChild(i),e=this.playerElIngest_=t,Object.keys(t).forEach(function(e){try{i[e]=t[e]}catch(e){}})}if(i.setAttribute("tabindex","-1"),r.tabindex="-1",(it||et&&at)&&(i.setAttribute("role","application"),r.role="application"),i.removeAttribute("width"),i.removeAttribute("height"),"width"in r&&delete r.width,"height"in r&&delete r.height,Object.getOwnPropertyNames(r).forEach(function(e){n&&"class"===e||t.setAttribute(e,r[e]),n&&i.setAttribute(e,r[e])}),i.playerId=i.id,i.id+="_html5_api",i.className="vjs-tech",i.player=t.player=this,this.addClass("vjs-paused"),!0!==v.VIDEOJS_NO_DYNAMIC_STYLE){this.styleEl_=Q("vjs-styles-dimensions");var a=W(".vjs-styles-defaults"),s=W("head");s.insertBefore(this.styleEl_,a?a.nextSibling:s.firstChild)}this.fill_=!1,this.fluid_=!1,this.width(this.options_.width),this.height(this.options_.height),this.fill(this.options_.fill),this.fluid(this.options_.fluid),this.aspectRatio(this.options_.aspectRatio);for(var o=i.getElementsByTagName("a"),u=0;u<o.length;u++){var l=o.item(u);C(l,"vjs-hidden"),l.setAttribute("hidden","hidden")}return i.initNetworkState_=i.networkState,i.parentNode&&!e&&i.parentNode.insertBefore(t,i),S(i,t),this.children_.unshift(i),this.el_.setAttribute("lang",this.language_),this.el_=t},e.width=function(e){return this.dimension("width",e)},e.height=function(e){return this.dimension("height",e)},e.dimension=function(e,t){var i=e+"_";if(void 0===t)return this[i]||0;if(""===t)return this[i]=void 0,void this.updateStyleEl_();var n=parseFloat(t);isNaN(n)?p.error('Improper value "'+t+'" supplied for for '+e):(this[i]=n,this.updateStyleEl_())},e.fluid=function(e){if(void 0===e)return!!this.fluid_;this.fluid_=!!e,Ce(this)&&this.off("playerreset",this.updateStyleEl_),e?(this.addClass("vjs-fluid"),this.fill(!1),function(e,t){Ce(e)?t():(e.eventedCallbacks||(e.eventedCallbacks=[]),e.eventedCallbacks.push(t))}(function(){this.on("playerreset",this.updateStyleEl_)})):this.removeClass("vjs-fluid"),this.updateStyleEl_()},e.fill=function(e){if(void 0===e)return!!this.fill_;this.fill_=!!e,e?(this.addClass("vjs-fill"),this.fluid(!1)):this.removeClass("vjs-fill")},e.aspectRatio=function(e){if(void 0===e)return this.aspectRatio_;if(!/^\d+\:\d+$/.test(e))throw new Error("Improper value supplied for aspect ratio. The format should be width:height, for example 16:9.");this.aspectRatio_=e,this.fluid(!0),this.updateStyleEl_()},e.updateStyleEl_=function(){if(!0!==v.VIDEOJS_NO_DYNAMIC_STYLE){var e,t,i,n=(void 0!==this.aspectRatio_&&"auto"!==this.aspectRatio_?this.aspectRatio_:0<this.videoWidth()?this.videoWidth()+":"+this.videoHeight():"16:9").split(":"),r=n[1]/n[0];e=void 0!==this.width_?this.width_:void 0!==this.height_?this.height_/r:this.videoWidth()||300,t=void 0!==this.height_?this.height_:e*r,i=/^[^a-zA-Z]/.test(this.id())?"dimensions-"+this.id():this.id()+"-dimensions",this.addClass(i),J(this.styleEl_,"\n      ."+i+" {\n        width: "+e+"px;\n        height: "+t+"px;\n      }\n\n      ."+i+".vjs-fluid {\n        padding-top: "+100*r+"%;\n      }\n    ")}else{var a="number"==typeof this.width_?this.width_:this.options_.width,s="number"==typeof this.height_?this.height_:this.options_.height,o=this.tech_&&this.tech_.el();o&&(0<=a&&(o.width=a),0<=s&&(o.height=s))}},e.loadTech_=function(e,t){var i=this;this.tech_&&this.unloadTech_();var n=Le(e),r=e.charAt(0).toLowerCase()+e.slice(1);"Html5"!==n&&this.tag&&(nr.getTech("Html5").disposeMediaElement(this.tag),this.tag.player=null,this.tag=null),this.techName_=n,this.isReady_=!1;var a={source:t,autoplay:"string"!=typeof this.autoplay()&&this.autoplay(),nativeControlsForTouch:this.options_.nativeControlsForTouch,playerId:this.id(),techId:this.id()+"_"+r+"_api",playsinline:this.options_.playsinline,preload:this.options_.preload,loop:this.options_.loop,muted:this.options_.muted,poster:this.poster(),language:this.language(),playerElIngest:this.playerElIngest_||!1,"vtt.js":this.options_["vtt.js"],canOverridePoster:!!this.options_.techCanOverridePoster,enableSourceset:this.options_.enableSourceset,Promise:this.options_.Promise};An.names.forEach(function(e){var t=An[e];a[t.getterName]=i[t.privateName]}),m(a,this.options_[n]),m(a,this.options_[r]),m(a,this.options_[e.toLowerCase()]),this.tag&&(a.tag=this.tag),t&&t.src===this.cache_.src&&0<this.cache_.currentTime&&(a.startTime=this.cache_.currentTime);var s=nr.getTech(e);if(!s)throw new Error("No Tech named '"+n+"' exists! '"+n+"' should be registered using videojs.registerTech()'");this.tech_=new s(a),this.tech_.ready(pe(this,this.handleTechReady_),!0),Et(this.textTracksJson_||[],this.tech_),Qa.forEach(function(e){i.on(i.tech_,e,i["handleTech"+Le(e)+"_"])}),Object.keys(Ja).forEach(function(t){i.on(i.tech_,t,function(e){0===i.tech_.playbackRate()&&i.tech_.seeking()?i.queuedCallbacks_.push({callback:i["handleTech"+Ja[t]+"_"].bind(i),event:e}):i["handleTech"+Ja[t]+"_"](e)})}),this.on(this.tech_,"loadstart",this.handleTechLoadStart_),this.on(this.tech_,"sourceset",this.handleTechSourceset_),this.on(this.tech_,"waiting",this.handleTechWaiting_),this.on(this.tech_,"ended",this.handleTechEnded_),this.on(this.tech_,"seeking",this.handleTechSeeking_),this.on(this.tech_,"play",this.handleTechPlay_),this.on(this.tech_,"firstplay",this.handleTechFirstPlay_),this.on(this.tech_,"pause",this.handleTechPause_),this.on(this.tech_,"durationchange",this.handleTechDurationChange_),this.on(this.tech_,"fullscreenchange",this.handleTechFullscreenChange_),this.on(this.tech_,"enterpictureinpicture",this.handleTechEnterPictureInPicture_),this.on(this.tech_,"leavepictureinpicture",this.handleTechLeavePictureInPicture_),this.on(this.tech_,"error",this.handleTechError_),this.on(this.tech_,"loadedmetadata",this.updateStyleEl_),this.on(this.tech_,"posterchange",this.handleTechPosterChange_),this.on(this.tech_,"textdata",this.handleTechTextData_),this.on(this.tech_,"ratechange",this.handleTechRateChange_),this.usingNativeControls(this.techGet_("controls")),this.controls()&&!this.usingNativeControls()&&this.addTechControlsListeners_(),this.tech_.el().parentNode===this.el()||"Html5"===n&&this.tag||S(this.tech_.el(),this.el()),this.tag&&(this.tag.player=null,this.tag=null)},e.unloadTech_=function(){var i=this;An.names.forEach(function(e){var t=An[e];i[t.privateName]=i[t.getterName]()}),this.textTracksJson_=Ct(this.tech_),this.isReady_=!1,this.tech_.dispose(),this.tech_=!1,this.isPosterFromTech_&&(this.poster_="",this.trigger("posterchange")),this.isPosterFromTech_=!1},e.tech=function(e){return void 0===e&&p.warn("Using the tech directly can be dangerous. I hope you know what you're doing.\nSee https://github.com/videojs/video.js/issues/2617 for more info.\n"),this.tech_},e.addTechControlsListeners_=function(){this.removeTechControlsListeners_(),this.on(this.tech_,"mouseup",this.handleTechClick_),this.on(this.tech_,"dblclick",this.handleTechDoubleClick_),this.on(this.tech_,"touchstart",this.handleTechTouchStart_),this.on(this.tech_,"touchmove",this.handleTechTouchMove_),this.on(this.tech_,"touchend",this.handleTechTouchEnd_),this.on(this.tech_,"tap",this.handleTechTap_)},e.removeTechControlsListeners_=function(){this.off(this.tech_,"tap",this.handleTechTap_),this.off(this.tech_,"touchstart",this.handleTechTouchStart_),this.off(this.tech_,"touchmove",this.handleTechTouchMove_),this.off(this.tech_,"touchend",this.handleTechTouchEnd_),this.off(this.tech_,"mouseup",this.handleTechClick_),this.off(this.tech_,"dblclick",this.handleTechDoubleClick_)},e.handleTechReady_=function(){this.triggerReady(),this.cache_.volume&&this.techCall_("setVolume",this.cache_.volume),this.handleTechPosterChange_(),this.handleTechDurationChange_()},e.handleTechLoadStart_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-seeking"),this.error(null),this.handleTechDurationChange_(),this.paused()?(this.hasStarted(!1),this.trigger("loadstart")):(this.trigger("loadstart"),this.trigger("firstplay")),this.manualAutoplay_(this.autoplay())},e.manualAutoplay_=function(t){var n=this;if(this.tech_&&"string"==typeof t){function e(){var e=n.muted();n.muted(!0);function t(){n.muted(e)}n.playTerminatedQueue_.push(t);var i=n.play();if(Tt(i))return i.catch(t)}var i;if("any"===t&&!0!==this.muted()?Tt(i=this.play())&&(i=i.catch(e)):i="muted"===t&&!0!==this.muted()?e():this.play(),Tt(i))return i.then(function(){n.trigger({type:"autoplay-success",autoplay:t})}).catch(function(e){n.trigger({type:"autoplay-failure",autoplay:t})})}},e.updateSourceCaches_=function(e){void 0===e&&(e="");var t=e,i="";"string"!=typeof t&&(t=e.src,i=e.type),this.cache_.source=this.cache_.source||{},this.cache_.sources=this.cache_.sources||[],t&&!i&&(i=function(e,t){if(!t)return"";if(e.cache_.source.src===t&&e.cache_.source.type)return e.cache_.source.type;var i=e.cache_.sources.filter(function(e){return e.src===t});if(i.length)return i[0].type;for(var n=e.$$("source"),r=0;r<n.length;r++){var a=n[r];if(a.type&&a.src&&a.src===t)return a.type}return fr(t)}(this,t)),this.cache_.source=Oe({},e,{src:t,type:i});for(var n=this.cache_.sources.filter(function(e){return e.src&&e.src===t}),r=[],a=this.$$("source"),s=[],o=0;o<a.length;o++){var u=P(a[o]);r.push(u),u.src&&u.src===t&&s.push(u.src)}s.length&&!n.length?this.cache_.sources=r:n.length||(this.cache_.sources=[this.cache_.source]),this.cache_.src=t},e.handleTechSourceset_=function(e){var i=this;if(!this.changingSrc_){var t=function(e){return i.updateSourceCaches_(e)},n=this.currentSource().src,r=e.src;n&&!/^blob:/.test(n)&&/^blob:/.test(r)&&(this.lastSource_&&(this.lastSource_.tech===r||this.lastSource_.player===n)||(t=function(){})),t(r),e.src||this.tech_.any(["sourceset","loadstart"],function(e){if("sourceset"!==e.type){var t=i.techGet("currentSrc");i.lastSource_.tech=t,i.updateSourceCaches_(t)}})}this.lastSource_={player:this.currentSource().src,tech:e.src},this.trigger({src:e.src,type:"sourceset"})},e.hasStarted=function(e){if(void 0===e)return this.hasStarted_;e!==this.hasStarted_&&(this.hasStarted_=e,this.hasStarted_?(this.addClass("vjs-has-started"),this.trigger("firstplay")):this.removeClass("vjs-has-started"))},e.handleTechPlay_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.hasStarted(!0),this.trigger("play")},e.handleTechRateChange_=function(){0<this.tech_.playbackRate()&&0===this.cache_.lastPlaybackRate&&(this.queuedCallbacks_.forEach(function(e){return e.callback(e.event)}),this.queuedCallbacks_=[]),this.cache_.lastPlaybackRate=this.tech_.playbackRate(),this.trigger("ratechange")},e.handleTechWaiting_=function(){var t=this;this.addClass("vjs-waiting"),this.trigger("waiting");var i=this.currentTime();this.on("timeupdate",function e(){i!==t.currentTime()&&(t.removeClass("vjs-waiting"),t.off("timeupdate",e))})},e.handleTechCanPlay_=function(){this.removeClass("vjs-waiting"),this.trigger("canplay")},e.handleTechCanPlayThrough_=function(){this.removeClass("vjs-waiting"),this.trigger("canplaythrough")},e.handleTechPlaying_=function(){this.removeClass("vjs-waiting"),this.trigger("playing")},e.handleTechSeeking_=function(){this.addClass("vjs-seeking"),this.trigger("seeking")},e.handleTechSeeked_=function(){this.removeClass("vjs-seeking"),this.removeClass("vjs-ended"),this.trigger("seeked")},e.handleTechFirstPlay_=function(){this.options_.starttime&&(p.warn("Passing the `starttime` option to the player will be deprecated in 6.0"),this.currentTime(this.options_.starttime)),this.addClass("vjs-has-started"),this.trigger("firstplay")},e.handleTechPause_=function(){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.trigger("pause")},e.handleTechEnded_=function(){this.addClass("vjs-ended"),this.options_.loop?(this.currentTime(0),this.play()):this.paused()||this.pause(),this.trigger("ended")},e.handleTechDurationChange_=function(){this.duration(this.techGet_("duration"))},e.handleTechClick_=function(e){V(e)&&this.controls_&&(this.paused()?St(this.play()):this.pause())},e.handleTechDoubleClick_=function(t){this.controls_&&(Array.prototype.some.call(this.$$(".vjs-control-bar, .vjs-modal-dialog"),function(e){return e.contains(t.target)})||void 0!==this.options_&&void 0!==this.options_.userActions&&void 0!==this.options_.userActions.doubleClick&&!1===this.options_.userActions.doubleClick||(void 0!==this.options_&&void 0!==this.options_.userActions&&"function"==typeof this.options_.userActions.doubleClick?this.options_.userActions.doubleClick.call(this,t):this.isFullscreen()?this.exitFullscreen():this.requestFullscreen()))},e.handleTechTap_=function(){this.userActive(!this.userActive())},e.handleTechTouchStart_=function(){this.userWasActive=this.userActive()},e.handleTechTouchMove_=function(){this.userWasActive&&this.reportUserActivity()},e.handleTechTouchEnd_=function(e){e.preventDefault()},e.handleStageClick_=function(){this.reportUserActivity()},e.toggleFullscreenClass_=function(){this.isFullscreen()?this.addClass("vjs-fullscreen"):this.removeClass("vjs-fullscreen")},e.documentFullscreenChange_=function(e){var t=this.el(),i=h[this.fsApi_.fullscreenElement]===t;!i&&t.matches?i=t.matches(":"+this.fsApi_.fullscreen):!i&&t.msMatchesSelector&&(i=t.msMatchesSelector(":"+this.fsApi_.fullscreen)),this.isFullscreen(i),!1===this.isFullscreen()&&le(h,this.fsApi_.fullscreenchange,this.boundDocumentFullscreenChange_),this.fsApi_.prefixed&&this.trigger("fullscreenchange")},e.handleTechFullscreenChange_=function(e,t){t&&this.isFullscreen(t.isFullscreen),this.trigger("fullscreenchange")},e.togglePictureInPictureClass_=function(){this.isInPictureInPicture()?this.addClass("vjs-picture-in-picture"):this.removeClass("vjs-picture-in-picture")},e.handleTechEnterPictureInPicture_=function(e){this.isInPictureInPicture(!0)},e.handleTechLeavePictureInPicture_=function(e){this.isInPictureInPicture(!1)},e.handleTechError_=function(){var e=this.tech_.error();this.error(e)},e.handleTechTextData_=function(e,t){var i=null;1<arguments.length&&(i=t),this.trigger("textdata",i)},e.getCache=function(){return this.cache_},e.resetCache_=function(){this.cache_={currentTime:0,inactivityTimeout:this.options_.inactivityTimeout,duration:NaN,lastVolume:1,lastPlaybackRate:this.defaultPlaybackRate(),media:null,src:"",source:{},sources:[],volume:1}},e.techCall_=function(e,t){this.ready(function(){if(e in cr)return function(e,t,i,n){return t[i](e.reduce(dr(i),n))}(this.middleware_,this.tech_,e,t);if(e in hr)return ur(this.middleware_,this.tech_,e,t);try{this.tech_&&this.tech_[e](t)}catch(e){throw p(e),e}},!0)},e.techGet_=function(t){if(this.tech_&&this.tech_.isReady_){if(t in lr)return function(e,t,i){return e.reduceRight(dr(i),t[i]())}(this.middleware_,this.tech_,t);if(t in hr)return ur(this.middleware_,this.tech_,t);try{return this.tech_[t]()}catch(e){if(void 0===this.tech_[t])throw p("Video.js: "+t+" method not defined for "+this.techName_+" playback technology.",e),e;if("TypeError"===e.name)throw p("Video.js: "+t+" unavailable on "+this.techName_+" playback technology element.",e),this.tech_.isReady_=!1,e;throw p(e),e}}},e.play=function(){var t=this,e=this.options_.Promise||v.Promise;return e?new e(function(e){t.play_(e)}):this.play_()},e.play_=function(e){var t=this;void 0===e&&(e=St),this.playCallbacks_.push(e);var i=Boolean(!this.changingSrc_&&(this.src()||this.currentSrc()));if(this.waitToPlay_&&(this.off(["ready","loadstart"],this.waitToPlay_),this.waitToPlay_=null),!this.isReady_||!i)return this.waitToPlay_=function(e){t.play_()},this.one(["ready","loadstart"],this.waitToPlay_),void(i||!rt&&!Ge||this.load());var n=this.techGet_("play");null===n?this.runPlayTerminatedQueue_():this.runPlayCallbacks_(n)},e.runPlayTerminatedQueue_=function(){var e=this.playTerminatedQueue_.slice(0);this.playTerminatedQueue_=[],e.forEach(function(e){e()})},e.runPlayCallbacks_=function(t){var e=this.playCallbacks_.slice(0);this.playCallbacks_=[],this.playTerminatedQueue_=[],e.forEach(function(e){e(t)})},e.pause=function(){this.techCall_("pause")},e.paused=function(){return!1!==this.techGet_("paused")},e.played=function(){return this.techGet_("played")||ct(0,0)},e.scrubbing=function(e){if("undefined"==typeof e)return this.scrubbing_;this.scrubbing_=!!e,e?this.addClass("vjs-scrubbing"):this.removeClass("vjs-scrubbing")},e.currentTime=function(e){return"undefined"!=typeof e?(e<0&&(e=0),void this.techCall_("setCurrentTime",e)):(this.cache_.currentTime=this.techGet_("currentTime")||0,this.cache_.currentTime)},e.duration=function(e){if(void 0===e)return void 0!==this.cache_.duration?this.cache_.duration:NaN;(e=parseFloat(e))<0&&(e=1/0),e!==this.cache_.duration&&((this.cache_.duration=e)===1/0?(this.addClass("vjs-live"),this.options_.liveui&&this.player_.liveTracker&&this.addClass("vjs-liveui")):(this.removeClass("vjs-live"),this.removeClass("vjs-liveui")),isNaN(e)||this.trigger("durationchange"))},e.remainingTime=function(){return this.duration()-this.currentTime()},e.remainingTimeDisplay=function(){return Math.floor(this.duration())-Math.floor(this.currentTime())},e.buffered=function(){var e=this.techGet_("buffered");return e&&e.length||(e=ct(0,0)),e},e.bufferedPercent=function(){return ht(this.buffered(),this.duration())},e.bufferedEnd=function(){var e=this.buffered(),t=this.duration(),i=e.end(e.length-1);return t<i&&(i=t),i},e.volume=function(e){var t;return void 0!==e?(t=Math.max(0,Math.min(1,parseFloat(e))),this.cache_.volume=t,this.techCall_("setVolume",t),void(0<t&&this.lastVolume_(t))):(t=parseFloat(this.techGet_("volume")),isNaN(t)?1:t)},e.muted=function(e){if(void 0===e)return this.techGet_("muted")||!1;this.techCall_("setMuted",e)},e.defaultMuted=function(e){return void 0!==e?this.techCall_("setDefaultMuted",e):this.techGet_("defaultMuted")||!1},e.lastVolume_=function(e){if(void 0===e||0===e)return this.cache_.lastVolume;this.cache_.lastVolume=e},e.supportsFullScreen=function(){return this.techGet_("supportsFullScreen")||!1},e.isFullscreen=function(e){return void 0!==e?(this.isFullscreen_=!!e,void this.toggleFullscreenClass_()):!!this.isFullscreen_},e.requestFullscreen=function(e){var t;this.isFullscreen(!0),this.fsApi_.requestFullscreen?(ue(h,this.fsApi_.fullscreenchange,this.boundDocumentFullscreenChange_),this.fsApi_.prefixed||(t=this.options_.fullscreen&&this.options_.fullscreen.options||{},void 0!==e&&(t=e)),St(this.el_[this.fsApi_.requestFullscreen](t))):this.tech_.supportsFullScreen()?this.techCall_("enterFullScreen"):(this.enterFullWindow(),this.trigger("fullscreenchange"))},e.exitFullscreen=function(){this.isFullscreen(!1),this.fsApi_.requestFullscreen?St(h[this.fsApi_.exitFullscreen]()):this.tech_.supportsFullScreen()?this.techCall_("exitFullScreen"):(this.exitFullWindow(),this.trigger("fullscreenchange"))},e.enterFullWindow=function(){this.isFullWindow=!0,this.docOrigOverflow=h.documentElement.style.overflow,ue(h,"keydown",this.boundFullWindowOnEscKey_),h.documentElement.style.overflow="hidden",C(h.body,"vjs-full-window"),this.trigger("enterFullWindow")},e.fullWindowOnEscKey=function(e){At.isEventKey(e,"Esc")&&(!0===this.isFullscreen()?this.exitFullscreen():this.exitFullWindow())},e.exitFullWindow=function(){this.isFullWindow=!1,le(h,"keydown",this.boundFullWindowOnEscKey_),h.documentElement.style.overflow=this.docOrigOverflow,E(h.body,"vjs-full-window"),this.trigger("exitFullWindow")},e.isInPictureInPicture=function(e){return void 0!==e?(this.isInPictureInPicture_=!!e,void this.togglePictureInPictureClass_()):!!this.isInPictureInPicture_},e.requestPictureInPicture=function(){if("pictureInPictureEnabled"in h)return this.techGet_("requestPictureInPicture")},e.exitPictureInPicture=function(){if("pictureInPictureEnabled"in h)return h.exitPictureInPicture()},e.handleKeyDown=function(e){var t=this.options_.userActions;if(t&&t.hotkeys){!function(e){var t=e.tagName.toLowerCase();if(e.isContentEditable)return!0;if("input"===t)return-1===["button","checkbox","hidden","radio","reset","submit"].indexOf(e.type);return-1!==["textarea"].indexOf(t)}(this.el_.ownerDocument.activeElement)&&("function"==typeof t.hotkeys?t.hotkeys.call(this,e):this.handleHotkeys(e))}},e.handleHotkeys=function(e){var t=this.options_.userActions?this.options_.userActions.hotkeys:{},i=t.fullscreenKey,n=void 0===i?function(e){return At.isEventKey(e,"f")}:i,r=t.muteKey,a=void 0===r?function(e){return At.isEventKey(e,"m")}:r,s=t.playPauseKey,o=void 0===s?function(e){return At.isEventKey(e,"k")||At.isEventKey(e,"Space")}:s;if(n.call(this,e)){e.preventDefault(),e.stopPropagation();var u=xe.getComponent("FullscreenToggle");!1!==h[this.fsApi_.fullscreenEnabled]&&u.prototype.handleClick.call(this,e)}else if(a.call(this,e)){e.preventDefault(),e.stopPropagation(),xe.getComponent("MuteToggle").prototype.handleClick.call(this,e)}else if(o.call(this,e)){e.preventDefault(),e.stopPropagation(),xe.getComponent("PlayToggle").prototype.handleClick.call(this,e)}},e.canPlayType=function(e){for(var t,i=0,n=this.options_.techOrder;i<n.length;i++){var r=n[i],a=nr.getTech(r);if(a=a||xe.getComponent(r)){if(a.isSupported()&&(t=a.canPlayType(e)))return t}else p.error('The "'+r+'" tech is undefined. Skipped browser support check for that tech.')}return""},e.selectSource=function(e){function t(e,i,n){var r;return e.some(function(t){return i.some(function(e){if(r=n(t,e))return!0})}),r}function i(e,t){var i=e[0];if(e[1].canPlaySource(t,r.options_[i.toLowerCase()]))return{source:t,tech:i}}var n,r=this,a=this.options_.techOrder.map(function(e){return[e,nr.getTech(e)]}).filter(function(e){var t=e[0],i=e[1];return i?i.isSupported():(p.error('The "'+t+'" tech is undefined. Skipped browser support check for that tech.'),!1)});return(this.options_.sourceOrder?t(e,a,(n=i,function(e,t){return n(t,e)})):t(a,e,i))||!1},e.src=function(e){var i=this;if("undefined"==typeof e)return this.cache_.src||"";var n=function t(e){if(Array.isArray(e)){var i=[];e.forEach(function(e){e=t(e),Array.isArray(e)?i=i.concat(e):s(e)&&i.push(e)}),e=i}else e="string"==typeof e&&e.trim()?[gr({src:e})]:s(e)&&"string"==typeof e.src&&e.src&&e.src.trim()?[gr(e)]:[];return e}(e);n.length?(this.changingSrc_=!0,this.cache_.sources=n,this.updateSourceCaches_(n[0]),or(this,n[0],function(e,t){if(i.middleware_=t,i.cache_.sources=n,i.updateSourceCaches_(e),i.src_(e))return 1<n.length?i.src(n.slice(1)):(i.changingSrc_=!1,i.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0),void i.triggerReady());!function(e,t){e.forEach(function(e){return e.setTech&&e.setTech(t)})}(t,i.tech_)})):this.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0)},e.src_=function(e){var t=this,i=this.selectSource([e]);return!i||(function(e,t){return Le(e)===Le(t)}(i.tech,this.techName_)?this.ready(function(){this.tech_.constructor.prototype.hasOwnProperty("setSource")?this.techCall_("setSource",e):this.techCall_("src",e.src),this.changingSrc_=!1},!0):(this.changingSrc_=!0,this.loadTech_(i.tech,i.source),this.tech_.ready(function(){t.changingSrc_=!1})),!1)},e.load=function(){this.techCall_("load")},e.reset=function(){var e=this,t=this.options_.Promise||v.Promise;this.paused()||!t?this.doReset_():St(this.play().then(function(){return e.doReset_()}))},e.doReset_=function(){this.tech_&&this.tech_.clearTracks("text"),this.resetCache_(),this.poster(""),this.loadTech_(this.options_.techOrder[0],null),this.techCall_("reset"),this.resetControlBarUI_(),Ce(this)&&this.trigger("playerreset")},e.resetControlBarUI_=function(){this.resetProgressBar_(),this.resetPlaybackRate_(),this.resetVolumeBar_()},e.resetProgressBar_=function(){this.currentTime(0);var e=this.controlBar,t=e.durationDisplay,i=e.remainingTimeDisplay;t&&t.updateContent(),i&&i.updateContent()},e.resetPlaybackRate_=function(){this.playbackRate(this.defaultPlaybackRate()),this.handleTechRateChange_()},e.resetVolumeBar_=function(){this.volume(1),this.trigger("volumechange")},e.currentSources=function(){var e=this.currentSource(),t=[];return 0!==Object.keys(e).length&&t.push(e),this.cache_.sources||t},e.currentSource=function(){return this.cache_.source||{}},e.currentSrc=function(){return this.currentSource()&&this.currentSource().src||""},e.currentType=function(){return this.currentSource()&&this.currentSource().type||""},e.preload=function(e){return void 0!==e?(this.techCall_("setPreload",e),void(this.options_.preload=e)):this.techGet_("preload")},e.autoplay=function(e){if(void 0===e)return this.options_.autoplay||!1;var t;"string"==typeof e&&/(any|play|muted)/.test(e)?(this.options_.autoplay=e,this.manualAutoplay_(e),t=!1):this.options_.autoplay=!!e,t="undefined"==typeof t?this.options_.autoplay:t,this.tech_&&this.techCall_("setAutoplay",t)},e.playsinline=function(e){return void 0!==e?(this.techCall_("setPlaysinline",e),this.options_.playsinline=e,this):this.techGet_("playsinline")},e.loop=function(e){return void 0!==e?(this.techCall_("setLoop",e),void(this.options_.loop=e)):this.techGet_("loop")},e.poster=function(e){if(void 0===e)return this.poster_;(e=e||"")!==this.poster_&&(this.poster_=e,this.techCall_("setPoster",e),this.isPosterFromTech_=!1,this.trigger("posterchange"))},e.handleTechPosterChange_=function(){if((!this.poster_||this.options_.techCanOverridePoster)&&this.tech_&&this.tech_.poster){var e=this.tech_.poster()||"";e!==this.poster_&&(this.poster_=e,this.isPosterFromTech_=!0,this.trigger("posterchange"))}},e.controls=function(e){if(void 0===e)return!!this.controls_;e=!!e,this.controls_!==e&&(this.controls_=e,this.usingNativeControls()&&this.techCall_("setControls",e),this.controls_?(this.removeClass("vjs-controls-disabled"),this.addClass("vjs-controls-enabled"),this.trigger("controlsenabled"),this.usingNativeControls()||this.addTechControlsListeners_()):(this.removeClass("vjs-controls-enabled"),this.addClass("vjs-controls-disabled"),this.trigger("controlsdisabled"),this.usingNativeControls()||this.removeTechControlsListeners_()))},e.usingNativeControls=function(e){if(void 0===e)return!!this.usingNativeControls_;e=!!e,this.usingNativeControls_!==e&&(this.usingNativeControls_=e,this.usingNativeControls_?(this.addClass("vjs-using-native-controls"),this.trigger("usingnativecontrols")):(this.removeClass("vjs-using-native-controls"),this.trigger("usingcustomcontrols")))},e.error=function(e){if(void 0===e)return this.error_||null;if(this.options_.suppressNotSupportedError&&e&&e.message&&e.message===this.localize(this.options_.notSupportedMessage)){function t(){this.error(e)}return this.options_.suppressNotSupportedError=!1,this.any(["click","touchstart"],t),void this.one("loadstart",function(){this.off(["click","touchstart"],t)})}if(null===e)return this.error_=e,this.removeClass("vjs-error"),void(this.errorDisplay&&this.errorDisplay.close());this.error_=new vt(e),this.addClass("vjs-error"),p.error("(CODE:"+this.error_.code+" "+vt.errorTypes[this.error_.code]+")",this.error_.message,this.error_),this.trigger("error")},e.reportUserActivity=function(e){this.userActivity_=!0},e.userActive=function(e){if(void 0===e)return this.userActive_;if((e=!!e)!==this.userActive_){if(this.userActive_=e,this.userActive_)return this.userActivity_=!0,this.removeClass("vjs-user-inactive"),this.addClass("vjs-user-active"),void this.trigger("useractive");this.tech_&&this.tech_.one("mousemove",function(e){e.stopPropagation(),e.preventDefault()}),this.userActivity_=!1,this.removeClass("vjs-user-active"),this.addClass("vjs-user-inactive"),this.trigger("userinactive")}},e.listenForUserActivity_=function(){var t,i,n,r=pe(this,this.reportUserActivity);this.on("mousedown",function(){r(),this.clearInterval(t),t=this.setInterval(r,250)}),this.on("mousemove",function(e){e.screenX===i&&e.screenY===n||(i=e.screenX,n=e.screenY,r())}),this.on("mouseup",function(e){r(),this.clearInterval(t)});var a,e=this.getChild("controlBar");!e||Ge||Ke||(e.on("mouseenter",function(e){this.player().cache_.inactivityTimeout=this.player().options_.inactivityTimeout,this.player().options_.inactivityTimeout=0}),e.on("mouseleave",function(e){this.player().options_.inactivityTimeout=this.player().cache_.inactivityTimeout})),this.on("keydown",r),this.on("keyup",r),this.setInterval(function(){if(this.userActivity_){this.userActivity_=!1,this.userActive(!0),this.clearTimeout(a);var e=this.options_.inactivityTimeout;e<=0||(a=this.setTimeout(function(){this.userActivity_||this.userActive(!1)},e))}},250)},e.playbackRate=function(e){if(void 0===e)return this.tech_&&this.tech_.featuresPlaybackRate?this.cache_.lastPlaybackRate||this.techGet_("playbackRate"):1;this.techCall_("setPlaybackRate",e)},e.defaultPlaybackRate=function(e){return void 0!==e?this.techCall_("setDefaultPlaybackRate",e):this.tech_&&this.tech_.featuresPlaybackRate?this.techGet_("defaultPlaybackRate"):1},e.isAudio=function(e){if(void 0===e)return!!this.isAudio_;this.isAudio_=!!e},e.addTextTrack=function(e,t,i){if(this.tech_)return this.tech_.addTextTrack(e,t,i)},e.addRemoteTextTrack=function(e,t){if(this.tech_)return this.tech_.addRemoteTextTrack(e,t)},e.removeRemoteTextTrack=function(e){void 0===e&&(e={});var t=e.track;if(t=t||e,this.tech_)return this.tech_.removeRemoteTextTrack(t)},e.getVideoPlaybackQuality=function(){return this.techGet_("getVideoPlaybackQuality")},e.videoWidth=function(){return this.tech_&&this.tech_.videoWidth&&this.tech_.videoWidth()||0},e.videoHeight=function(){return this.tech_&&this.tech_.videoHeight&&this.tech_.videoHeight()||0},e.language=function(e){if(void 0===e)return this.language_;this.language_=String(e).toLowerCase()},e.languages=function(){return Oe(l.prototype.options_.languages,this.languages_)},e.toJSON=function(){var e=Oe(this.options_),t=e.tracks;e.tracks=[];for(var i=0;i<t.length;i++){var n=t[i];(n=Oe(n)).player=void 0,e.tracks[i]=n}return e},e.createModal=function(e,t){var i=this;(t=t||{}).content=e||"";var n=new It(this,t);return this.addChild(n),n.on("dispose",function(){i.removeChild(n)}),n.open(),n},e.updateCurrentBreakpoint_=function(){if(this.responsive())for(var e=this.currentBreakpoint(),t=this.currentWidth(),i=0;i<Za.length;i++){var n=Za[i];if(t<=this.breakpoints_[n]){if(e===n)return;e&&this.removeClass(es[e]),this.addClass(es[n]),this.breakpoint_=n;break}}},e.removeCurrentBreakpoint_=function(){var e=this.currentBreakpointClass();this.breakpoint_="",e&&this.removeClass(e)},e.breakpoints=function(e){return void 0===e||(this.breakpoint_="",this.breakpoints_=m({},ts,e),this.updateCurrentBreakpoint_()),m(this.breakpoints_)},e.responsive=function(e){return void 0===e?this.responsive_:(e=Boolean(e))!==this.responsive_?((this.responsive_=e)?(this.on("playerresize",this.updateCurrentBreakpoint_),this.updateCurrentBreakpoint_()):(this.off("playerresize",this.updateCurrentBreakpoint_),this.removeCurrentBreakpoint_()),e):void 0},e.currentBreakpoint=function(){return this.breakpoint_},e.currentBreakpointClass=function(){return es[this.breakpoint_]||""},e.loadMedia=function(e,t){var i=this;if(e&&"object"==typeof e){this.reset(),this.cache_.media=Oe(e);var n=this.cache_.media,r=n.artwork,a=n.poster,s=n.src,o=n.textTracks;!r&&a&&(this.cache_.media.artwork=[{src:a,type:fr(a)}]),s&&this.src(s),a&&this.poster(a),Array.isArray(o)&&o.forEach(function(e){return i.addRemoteTextTrack(e,!1)}),this.ready(t)}},e.getMedia=function(){if(this.cache_.media)return Oe(this.cache_.media);var e=this.poster(),t={src:this.currentSources(),textTracks:Array.prototype.map.call(this.remoteTextTracks(),function(e){return{kind:e.kind,label:e.label,language:e.language,src:e.src}})};return e&&(t.poster=e,t.artwork=[{src:t.poster,type:fr(t.poster)}]),t},l.getTagSettings=function(e){var t={sources:[],tracks:[]},i=P(e),n=i["data-setup"];if(k(e,"vjs-fill")&&(i.fill=!0),k(e,"vjs-fluid")&&(i.fluid=!0),null!==n){var r=bt(n||"{}"),a=r[0],s=r[1];a&&p.error(a),m(i,s)}if(m(t,i),e.hasChildNodes())for(var o=e.childNodes,u=0,l=o.length;u<l;u++){var c=o[u],h=c.nodeName.toLowerCase();"source"===h?t.sources.push(P(c)):"track"===h&&t.tracks.push(P(c))}return t},e.flexNotSupported_=function(){var e=h.createElement("i");return!("flexBasis"in e.style||"webkitFlexBasis"in e.style||"mozFlexBasis"in e.style||"msFlexBasis"in e.style||"msFlexOrder"in e.style)},l}(xe);An.names.forEach(function(e){var t=An[e];is.prototype[t.getterName]=function(){return this.tech_?this.tech_[t.getterName]():(this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName])}}),is.players={};var ns=v.navigator;is.prototype.options_={techOrder:nr.defaultTechOrder_,html5:{},flash:{},inactivityTimeout:2e3,playbackRates:[],liveui:!1,children:["mediaLoader","posterImage","textTrackDisplay","loadingSpinner","bigPlayButton","liveTracker","controlBar","errorDisplay","textTrackSettings","resizeManager"],language:ns&&(ns.languages&&ns.languages[0]||ns.userLanguage||ns.language)||"en",languages:{},notSupportedMessage:"No compatible source was found for this media.",fullscreen:{options:{navigationUI:"hide"}},breakpoints:{},responsive:!1},["ended","seeking","seekable","networkState","readyState"].forEach(function(e){is.prototype[e]=function(){return this.techGet_(e)}}),Qa.forEach(function(e){is.prototype["handleTech"+Le(e)+"_"]=function(){return this.trigger(e)}}),xe.registerComponent("Player",is);function rs(e){return hs.hasOwnProperty(e)}function as(e){return rs(e)?hs[e]:void 0}function ss(e,t){e[cs]=e[cs]||{},e[cs][t]=!0}function os(e,t,i){var n=(i?"before":"")+"pluginsetup";e.trigger(n,t),e.trigger(n+":"+t.name,t)}function us(r,a){return a.prototype.name=r,function(){os(this,{name:r,plugin:a,instance:null},!0);for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var n=Re(a,[this].concat(t));return this[r]=function(){return n},os(this,n.getEventHash()),n}}var ls="plugin",cs="activePlugins_",hs={},ds=function(){function i(e){if(this.constructor===i)throw new Error("Plugin must be sub-classed; not directly instantiated.");this.player=e,we(this),delete this.trigger,Pe(this,this.constructor.defaultState),ss(e,this.name),this.dispose=pe(this,this.dispose),e.on("dispose",this.dispose)}var e=i.prototype;return e.version=function(){return this.constructor.VERSION},e.getEventHash=function(e){return void 0===e&&(e={}),e.name=this.name,e.plugin=this.constructor,e.instance=this,e},e.trigger=function(e,t){return void 0===t&&(t={}),ce(this.eventBusEl_,e,this.getEventHash(t))},e.handleStateChanged=function(e){},e.dispose=function(){var e=this.name,t=this.player;this.trigger("dispose"),this.off(),t.off("dispose",this.dispose),t[cs][e]=!1,this.player=this.state=null,t[e]=us(e,hs[e])},i.isBasic=function(e){var t="string"==typeof e?as(e):e;return"function"==typeof t&&!i.prototype.isPrototypeOf(t.prototype)},i.registerPlugin=function(e,t){if("string"!=typeof e)throw new Error('Illegal plugin name, "'+e+'", must be a string, was '+typeof e+".");if(rs(e))p.warn('A plugin named "'+e+'" already exists. You may want to avoid re-registering plugins!');else if(is.prototype.hasOwnProperty(e))throw new Error('Illegal plugin name, "'+e+'", cannot share a name with an existing player method!');if("function"!=typeof t)throw new Error('Illegal plugin for "'+e+'", must be a function, was '+typeof t+".");return hs[e]=t,e!==ls&&(i.isBasic(t)?is.prototype[e]=function(t,i){function n(){os(this,{name:t,plugin:i,instance:null},!0);var e=i.apply(this,arguments);return ss(this,t),os(this,{name:t,plugin:i,instance:e}),e}return Object.keys(i).forEach(function(e){n[e]=i[e]}),n}(e,t):is.prototype[e]=us(e,t)),t},i.deregisterPlugin=function(e){if(e===ls)throw new Error("Cannot de-register base plugin.");rs(e)&&(delete hs[e],delete is.prototype[e])},i.getPlugins=function(e){var i;return void 0===e&&(e=Object.keys(hs)),e.forEach(function(e){var t=as(e);t&&((i=i||{})[e]=t)}),i},i.getPluginVersion=function(e){var t=as(e);return t&&t.VERSION||""},i}();ds.getPlugin=as,ds.BASE_PLUGIN_NAME=ls,ds.registerPlugin(ls,ds),is.prototype.usingPlugin=function(e){return!!this[cs]&&!0===this[cs][e]},is.prototype.hasPlugin=function(e){return!!rs(e)};var ps=function(e){return 0===e.indexOf("#")?e.slice(1):e};function fs(e,i,t){var n=fs.getPlayer(e);if(n)return i&&p.warn('Player "'+e+'" is already initialised. Options will not be applied.'),t&&n.ready(t),n;var r="string"==typeof e?W("#"+ps(e)):e;if(!g(r))throw new TypeError("The element or ID supplied is not valid. (videojs)");r.ownerDocument.defaultView&&r.ownerDocument.body.contains(r)||p.warn("The element supplied is not included in the DOM"),i=i||{},fs.hooks("beforesetup").forEach(function(e){var t=e(r,Oe(i));s(t)&&!Array.isArray(t)?i=Oe(i,t):p.error("please return an object in beforesetup hooks")});var a=xe.getComponent("Player");return n=new a(r,i,t),fs.hooks("setup").forEach(function(e){return e(n)}),n}if(fs.hooks_={},fs.hooks=function(e,t){return fs.hooks_[e]=fs.hooks_[e]||[],t&&(fs.hooks_[e]=fs.hooks_[e].concat(t)),fs.hooks_[e]},fs.hook=function(e,t){fs.hooks(e,t)},fs.hookOnce=function(i,e){fs.hooks(i,[].concat(e).map(function(t){return function e(){return fs.removeHook(i,e),t.apply(void 0,arguments)}}))},fs.removeHook=function(e,t){var i=fs.hooks(e).indexOf(t);return!(i<=-1)&&(fs.hooks_[e]=fs.hooks_[e].slice(),fs.hooks_[e].splice(i,1),!0)},!0!==v.VIDEOJS_NO_DYNAMIC_STYLE&&c()){var ms=W(".vjs-styles-defaults");if(!ms){ms=Q("vjs-styles-defaults");var gs=W("head");gs&&gs.insertBefore(ms,gs.firstChild),J(ms,"\n      .video-js {\n        width: 300px;\n        height: 150px;\n      }\n\n      .vjs-fluid {\n        padding-top: 56.25%\n      }\n    ")}}K(1,fs),fs.VERSION=d,fs.options=is.prototype.options_,fs.getPlayers=function(){return is.players},fs.getPlayer=function(e){var t,i=is.players;if("string"==typeof e){var n=ps(e),r=i[n];if(r)return r;t=W("#"+n)}else t=e;if(g(t)){var a=t,s=a.player,o=a.playerId;if(s||i[o])return s||i[o]}},fs.getAllPlayers=function(){return Object.keys(is.players).map(function(e){return is.players[e]}).filter(Boolean)},fs.players=is.players,fs.getComponent=xe.getComponent,fs.registerComponent=function(e,t){nr.isTech(t)&&p.warn("The "+e+" tech was registered as a component. It should instead be registered using videojs.registerTech(name, tech)"),xe.registerComponent.call(xe,e,t)},fs.getTech=nr.getTech,fs.registerTech=nr.registerTech,fs.use=function(e,t){rr[e]=rr[e]||[],rr[e].push(t)},Object.defineProperty(fs,"middleware",{value:{},writeable:!1,enumerable:!0}),Object.defineProperty(fs.middleware,"TERMINATOR",{value:sr,writeable:!1,enumerable:!0}),fs.browser=ot,fs.TOUCH_ENABLED=st,fs.extend=function(e,t){void 0===t&&(t={});var i=function(){e.apply(this,arguments)},n={};for(var r in"object"==typeof t?(t.constructor!==Object.prototype.constructor&&(i=t.constructor),n=t):"function"==typeof t&&(i=t),function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(e.super_=t)}(i,e),n)n.hasOwnProperty(r)&&(i.prototype[r]=n[r]);return i},fs.mergeOptions=Oe,fs.bind=pe,fs.registerPlugin=ds.registerPlugin,fs.deregisterPlugin=ds.deregisterPlugin,fs.plugin=function(e,t){return p.warn("videojs.plugin() is deprecated; use videojs.registerPlugin() instead"),ds.registerPlugin(e,t)},fs.getPlugins=ds.getPlugins,fs.getPlugin=ds.getPlugin,fs.getPluginVersion=ds.getPluginVersion,fs.addLanguage=function(e,t){var i;return e=(""+e).toLowerCase(),fs.options.languages=Oe(fs.options.languages,((i={})[e]=t,i)),fs.options.languages[e]},fs.log=p,fs.createLogger=f,fs.createTimeRange=fs.createTimeRanges=ct,fs.formatTime=xr,fs.setFormatTime=function(e){Or=e},fs.resetFormatTime=function(){Or=Lr},fs.parseUrl=Ut,fs.isCrossOrigin=Nt,fs.EventTarget=me,fs.on=ue,fs.one=he,fs.off=le,fs.trigger=ce,fs.xhr=gn,fs.TextTrack=Tn,fs.AudioTrack=Sn,fs.VideoTrack=kn,["isEl","isTextNode","createEl","hasClass","addClass","removeClass","toggleClass","setAttributes","getAttributes","emptyEl","appendContent","insertContent"].forEach(function(e){fs[e]=function(){return p.warn("videojs."+e+"() is deprecated; use videojs.dom."+e+"() instead"),$[e].apply(null,arguments)}}),fs.computedStyle=n,fs.dom=$,fs.url=Xt;var ys=wt(function(e,t){var i,c,n,r,h;i=/^((?:[a-zA-Z0-9+\-.]+:)?)(\/\/[^\/?#]*)?((?:[^\/\?#]*\/)*.*?)??(;.*?)?(\?.*?)?(#.*?)?$/,c=/^([^\/?#]*)(.*)$/,n=/(?:\/|^)\.(?=\/)/g,r=/(?:\/|^)\.\.\/(?!\.\.\/).*?(?=\/)/g,h={buildAbsoluteURL:function(e,t,i){if(i=i||{},e=e.trim(),!(t=t.trim())){if(!i.alwaysNormalize)return e;var n=h.parseURL(e);if(!n)throw new Error("Error trying to parse base URL.");return n.path=h.normalizePath(n.path),h.buildURLFromParts(n)}var r=h.parseURL(t);if(!r)throw new Error("Error trying to parse relative URL.");if(r.scheme)return i.alwaysNormalize?(r.path=h.normalizePath(r.path),h.buildURLFromParts(r)):t;var a=h.parseURL(e);if(!a)throw new Error("Error trying to parse base URL.");if(!a.netLoc&&a.path&&"/"!==a.path[0]){var s=c.exec(a.path);a.netLoc=s[1],a.path=s[2]}a.netLoc&&!a.path&&(a.path="/");var o={scheme:a.scheme,netLoc:r.netLoc,path:null,params:r.params,query:r.query,fragment:r.fragment};if(!r.netLoc&&(o.netLoc=a.netLoc,"/"!==r.path[0]))if(r.path){var u=a.path,l=u.substring(0,u.lastIndexOf("/")+1)+r.path;o.path=h.normalizePath(l)}else o.path=a.path,r.params||(o.params=a.params,r.query||(o.query=a.query));return null===o.path&&(o.path=i.alwaysNormalize?h.normalizePath(r.path):r.path),h.buildURLFromParts(o)},parseURL:function(e){var t=i.exec(e);return t?{scheme:t[1]||"",netLoc:t[2]||"",path:t[3]||"",params:t[4]||"",query:t[5]||"",fragment:t[6]||""}:null},normalizePath:function(e){for(e=e.split("").reverse().join("").replace(n,"");e.length!==(e=e.replace(r,"")).length;);return e.split("").reverse().join("")},buildURLFromParts:function(e){return e.scheme+e.netLoc+e.path+e.params+e.query+e.fragment}},e.exports=h});
/*! @name m3u8-parser @version 4.4.0 @license Apache-2.0 */function vs(){return(vs=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}function _s(e,t){e.prototype=Object.create(t.prototype),(e.prototype.constructor=e).__proto__=t}function bs(e){for(var t,i=e.split(new RegExp('(?:^|,)((?:[^=]*)=(?:"[^"]*"|[^,]*))')),n={},r=i.length;r--;)""!==i[r]&&((t=/([^=]*)=(.*)/.exec(i[r]).slice(1))[0]=t[0].replace(/^\s+|\s+$/g,""),t[1]=t[1].replace(/^\s+|\s+$/g,""),t[1]=t[1].replace(/^['"](.*)['"]$/g,"$1"),n[t[0]]=t[1]);return n}var Ts=function(){function e(){this.listeners={}}var t=e.prototype;return t.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},t.off=function(e,t){if(!this.listeners[e])return!1;var i=this.listeners[e].indexOf(t);return this.listeners[e].splice(i,1),-1<i},t.trigger=function(e,t){var i,n,r,a=this.listeners[e];if(a)if(2===arguments.length)for(n=a.length,i=0;i<n;++i)a[i].call(this,t);else for(r=Array.prototype.slice.call(arguments,1),n=a.length,i=0;i<n;++i)a[i].apply(this,r)},t.dispose=function(){this.listeners={}},t.pipe=function(t){this.on("data",function(e){t.push(e)})},e}(),Ss=function(t){function e(){var e;return(e=t.call(this)||this).buffer="",e}return _s(e,t),e.prototype.push=function(e){var t;for(this.buffer+=e,t=this.buffer.indexOf("\n");-1<t;t=this.buffer.indexOf("\n"))this.trigger("data",this.buffer.substring(0,t)),this.buffer=this.buffer.substring(t+1)},e}(Ts),ks=function(t){function e(){var e;return(e=t.call(this)||this).customParsers=[],e.tagMappers=[],e}_s(e,t);var i=e.prototype;return i.push=function(n){var u,l,c=this;0!==(n=n.trim()).length&&("#"===n[0]?this.tagMappers.reduce(function(e,t){var i=t(n);return i===n?e:e.concat([i])},[n]).forEach(function(e){for(var t=0;t<c.customParsers.length;t++)if(c.customParsers[t].call(c,e))return;if(0===e.indexOf("#EXT"))if(e=e.replace("\r",""),u=/^#EXTM3U/.exec(e))c.trigger("data",{type:"tag",tagType:"m3u"});else{if(u=/^#EXTINF:?([0-9\.]*)?,?(.*)?$/.exec(e))return l={type:"tag",tagType:"inf"},u[1]&&(l.duration=parseFloat(u[1])),u[2]&&(l.title=u[2]),void c.trigger("data",l);if(u=/^#EXT-X-TARGETDURATION:?([0-9.]*)?/.exec(e))return l={type:"tag",tagType:"targetduration"},u[1]&&(l.duration=parseInt(u[1],10)),void c.trigger("data",l);if(u=/^#ZEN-TOTAL-DURATION:?([0-9.]*)?/.exec(e))return l={type:"tag",tagType:"totalduration"},u[1]&&(l.duration=parseInt(u[1],10)),void c.trigger("data",l);if(u=/^#EXT-X-VERSION:?([0-9.]*)?/.exec(e))return l={type:"tag",tagType:"version"},u[1]&&(l.version=parseInt(u[1],10)),void c.trigger("data",l);if(u=/^#EXT-X-MEDIA-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return l={type:"tag",tagType:"media-sequence"},u[1]&&(l.number=parseInt(u[1],10)),void c.trigger("data",l);if(u=/^#EXT-X-DISCONTINUITY-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return l={type:"tag",tagType:"discontinuity-sequence"},u[1]&&(l.number=parseInt(u[1],10)),void c.trigger("data",l);if(u=/^#EXT-X-PLAYLIST-TYPE:?(.*)?$/.exec(e))return l={type:"tag",tagType:"playlist-type"},u[1]&&(l.playlistType=u[1]),void c.trigger("data",l);if(u=/^#EXT-X-BYTERANGE:?([0-9.]*)?@?([0-9.]*)?/.exec(e))return l={type:"tag",tagType:"byterange"},u[1]&&(l.length=parseInt(u[1],10)),u[2]&&(l.offset=parseInt(u[2],10)),void c.trigger("data",l);if(u=/^#EXT-X-ALLOW-CACHE:?(YES|NO)?/.exec(e))return l={type:"tag",tagType:"allow-cache"},u[1]&&(l.allowed=!/NO/.test(u[1])),void c.trigger("data",l);if(u=/^#EXT-X-MAP:?(.*)$/.exec(e)){if(l={type:"tag",tagType:"map"},u[1]){var i=bs(u[1]);if(i.URI&&(l.uri=i.URI),i.BYTERANGE){var n=i.BYTERANGE.split("@"),r=n[0],a=n[1];l.byterange={},r&&(l.byterange.length=parseInt(r,10)),a&&(l.byterange.offset=parseInt(a,10))}}c.trigger("data",l)}else if(u=/^#EXT-X-STREAM-INF:?(.*)$/.exec(e)){if(l={type:"tag",tagType:"stream-inf"},u[1]){if(l.attributes=bs(u[1]),l.attributes.RESOLUTION){var s=l.attributes.RESOLUTION.split("x"),o={};s[0]&&(o.width=parseInt(s[0],10)),s[1]&&(o.height=parseInt(s[1],10)),l.attributes.RESOLUTION=o}l.attributes.BANDWIDTH&&(l.attributes.BANDWIDTH=parseInt(l.attributes.BANDWIDTH,10)),l.attributes["PROGRAM-ID"]&&(l.attributes["PROGRAM-ID"]=parseInt(l.attributes["PROGRAM-ID"],10))}c.trigger("data",l)}else{if(u=/^#EXT-X-MEDIA:?(.*)$/.exec(e))return l={type:"tag",tagType:"media"},u[1]&&(l.attributes=bs(u[1])),void c.trigger("data",l);if(u=/^#EXT-X-ENDLIST/.exec(e))c.trigger("data",{type:"tag",tagType:"endlist"});else if(u=/^#EXT-X-DISCONTINUITY/.exec(e))c.trigger("data",{type:"tag",tagType:"discontinuity"});else{if(u=/^#EXT-X-PROGRAM-DATE-TIME:?(.*)$/.exec(e))return l={type:"tag",tagType:"program-date-time"},u[1]&&(l.dateTimeString=u[1],l.dateTimeObject=new Date(u[1])),void c.trigger("data",l);if(u=/^#EXT-X-KEY:?(.*)$/.exec(e))return l={type:"tag",tagType:"key"},u[1]&&(l.attributes=bs(u[1]),l.attributes.IV&&("0x"===l.attributes.IV.substring(0,2).toLowerCase()&&(l.attributes.IV=l.attributes.IV.substring(2)),l.attributes.IV=l.attributes.IV.match(/.{8}/g),l.attributes.IV[0]=parseInt(l.attributes.IV[0],16),l.attributes.IV[1]=parseInt(l.attributes.IV[1],16),l.attributes.IV[2]=parseInt(l.attributes.IV[2],16),l.attributes.IV[3]=parseInt(l.attributes.IV[3],16),l.attributes.IV=new Uint32Array(l.attributes.IV))),void c.trigger("data",l);if(u=/^#EXT-X-START:?(.*)$/.exec(e))return l={type:"tag",tagType:"start"},u[1]&&(l.attributes=bs(u[1]),l.attributes["TIME-OFFSET"]=parseFloat(l.attributes["TIME-OFFSET"]),l.attributes.PRECISE=/YES/.test(l.attributes.PRECISE)),void c.trigger("data",l);if(u=/^#EXT-X-CUE-OUT-CONT:?(.*)?$/.exec(e))return l={type:"tag",tagType:"cue-out-cont"},u[1]?l.data=u[1]:l.data="",void c.trigger("data",l);if(u=/^#EXT-X-CUE-OUT:?(.*)?$/.exec(e))return l={type:"tag",tagType:"cue-out"},u[1]?l.data=u[1]:l.data="",void c.trigger("data",l);if(u=/^#EXT-X-CUE-IN:?(.*)?$/.exec(e))return l={type:"tag",tagType:"cue-in"},u[1]?l.data=u[1]:l.data="",void c.trigger("data",l);c.trigger("data",{type:"tag",data:e.slice(4)})}}}else c.trigger("data",{type:"comment",text:e.slice(1)})}):this.trigger("data",{type:"uri",uri:n}))},i.addParser=function(e){var t=this,i=e.expression,n=e.customType,r=e.dataParser,a=e.segment;"function"!=typeof r&&(r=function(e){return e}),this.customParsers.push(function(e){if(i.exec(e))return t.trigger("data",{type:"custom",data:r(e),customType:n,segment:a}),!0})},i.addTagMapper=function(e){var t=e.expression,i=e.map;this.tagMappers.push(function(e){return t.test(e)?i(e):e})},e}(Ts);function Cs(e){return!!e&&"object"==typeof e}function Es(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.reduce(function(t,i){return Object.keys(i).forEach(function(e){Array.isArray(t[e])&&Array.isArray(i[e])?t[e]=t[e].concat(i[e]):Cs(t[e])&&Cs(i[e])?t[e]=Es(t[e],i[e]):t[e]=i[e]}),t},{})}function ws(e){return e.reduce(function(e,t){return e.concat(t)},[])}function As(e){if(!e.length)return[];for(var t=[],i=0;i<e.length;i++)t.push(e[i]);return t}var Ps=function(t){function e(){var e;(e=t.call(this)||this).lineStream=new Ss,e.parseStream=new ks,e.lineStream.pipe(e.parseStream);var r,a,s=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e),o=[],u={},l={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},c=0;return e.manifest={allowCache:!0,discontinuityStarts:[],segments:[]},e.parseStream.on("data",function(t){var i,n;({tag:function(){({"allow-cache":function(){this.manifest.allowCache=t.allowed,"allowed"in t||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange:function(){var e={};"length"in t&&((u.byterange=e).length=t.length,"offset"in t||(this.trigger("info",{message:"defaulting offset to zero"}),t.offset=0)),"offset"in t&&((u.byterange=e).offset=t.offset)},endlist:function(){this.manifest.endList=!0},inf:function(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),0<t.duration&&(u.duration=t.duration),0===t.duration&&(u.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=o},key:function(){if(t.attributes)if("NONE"!==t.attributes.METHOD)if(t.attributes.URI){if("urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"===t.attributes.KEYFORMAT){return-1===["SAMPLE-AES","SAMPLE-AES-CTR","SAMPLE-AES-CENC"].indexOf(t.attributes.METHOD)?void this.trigger("warn",{message:"invalid key method provided for Widevine"}):("SAMPLE-AES-CENC"===t.attributes.METHOD&&this.trigger("warn",{message:"SAMPLE-AES-CENC is deprecated, please use SAMPLE-AES-CTR instead"}),"data:text/plain;base64,"!==t.attributes.URI.substring(0,23)?void this.trigger("warn",{message:"invalid key URI provided for Widevine"}):t.attributes.KEYID&&"0x"===t.attributes.KEYID.substring(0,2)?void(this.manifest.contentProtection={"com.widevine.alpha":{attributes:{schemeIdUri:t.attributes.KEYFORMAT,keyId:t.attributes.KEYID.substring(2)},pssh:function(e){for(var t=v.atob(e||""),i=new Uint8Array(t.length),n=0;n<t.length;n++)i[n]=t.charCodeAt(n);return i}(t.attributes.URI.split(",")[1])}}):void this.trigger("warn",{message:"invalid key ID provided for Widevine"}))}t.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),a={method:t.attributes.METHOD||"AES-128",uri:t.attributes.URI},"undefined"!=typeof t.attributes.IV&&(a.iv=t.attributes.IV)}else this.trigger("warn",{message:"ignoring key declaration without URI"});else a=null;else this.trigger("warn",{message:"ignoring key declaration without attribute list"})},"media-sequence":function(){isFinite(t.number)?this.manifest.mediaSequence=t.number:this.trigger("warn",{message:"ignoring invalid media sequence: "+t.number})},"discontinuity-sequence":function(){isFinite(t.number)?(this.manifest.discontinuitySequence=t.number,c=t.number):this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+t.number})},"playlist-type":function(){/VOD|EVENT/.test(t.playlistType)?this.manifest.playlistType=t.playlistType:this.trigger("warn",{message:"ignoring unknown playlist type: "+t.playlist})},map:function(){r={},t.uri&&(r.uri=t.uri),t.byterange&&(r.byterange=t.byterange)},"stream-inf":function(){this.manifest.playlists=o,this.manifest.mediaGroups=this.manifest.mediaGroups||l,t.attributes?(u.attributes||(u.attributes={}),vs(u.attributes,t.attributes)):this.trigger("warn",{message:"ignoring empty stream-inf attributes"})},media:function(){if(this.manifest.mediaGroups=this.manifest.mediaGroups||l,t.attributes&&t.attributes.TYPE&&t.attributes["GROUP-ID"]&&t.attributes.NAME){var e=this.manifest.mediaGroups[t.attributes.TYPE];e[t.attributes["GROUP-ID"]]=e[t.attributes["GROUP-ID"]]||{},i=e[t.attributes["GROUP-ID"]],(n={default:/yes/i.test(t.attributes.DEFAULT)}).default?n.autoselect=!0:n.autoselect=/yes/i.test(t.attributes.AUTOSELECT),t.attributes.LANGUAGE&&(n.language=t.attributes.LANGUAGE),t.attributes.URI&&(n.uri=t.attributes.URI),t.attributes["INSTREAM-ID"]&&(n.instreamId=t.attributes["INSTREAM-ID"]),t.attributes.CHARACTERISTICS&&(n.characteristics=t.attributes.CHARACTERISTICS),t.attributes.FORCED&&(n.forced=/yes/i.test(t.attributes.FORCED)),i[t.attributes.NAME]=n}else this.trigger("warn",{message:"ignoring incomplete or missing media group"})},discontinuity:function(){c+=1,u.discontinuity=!0,this.manifest.discontinuityStarts.push(o.length)},"program-date-time":function(){"undefined"==typeof this.manifest.dateTimeString&&(this.manifest.dateTimeString=t.dateTimeString,this.manifest.dateTimeObject=t.dateTimeObject),u.dateTimeString=t.dateTimeString,u.dateTimeObject=t.dateTimeObject},targetduration:function(){!isFinite(t.duration)||t.duration<0?this.trigger("warn",{message:"ignoring invalid target duration: "+t.duration}):this.manifest.targetDuration=t.duration},totalduration:function(){!isFinite(t.duration)||t.duration<0?this.trigger("warn",{message:"ignoring invalid total duration: "+t.duration}):this.manifest.totalDuration=t.duration},start:function(){t.attributes&&!isNaN(t.attributes["TIME-OFFSET"])?this.manifest.start={timeOffset:t.attributes["TIME-OFFSET"],precise:t.attributes.PRECISE}:this.trigger("warn",{message:"ignoring start declaration without appropriate attribute list"})},"cue-out":function(){u.cueOut=t.data},"cue-out-cont":function(){u.cueOutCont=t.data},"cue-in":function(){u.cueIn=t.data}}[t.tagType]||function(){}).call(s)},uri:function(){u.uri=t.uri,o.push(u),!this.manifest.targetDuration||"duration"in u||(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),u.duration=this.manifest.targetDuration),a&&(u.key=a),u.timeline=c,r&&(u.map=r),u={}},comment:function(){},custom:function(){t.segment?(u.custom=u.custom||{},u.custom[t.customType]=t.data):(this.manifest.custom=this.manifest.custom||{},this.manifest.custom[t.customType]=t.data)}})[t.type].call(s)}),e}_s(e,t);var i=e.prototype;return i.push=function(e){this.lineStream.push(e)},i.end=function(){this.lineStream.push("\n")},i.addParser=function(e){this.parseStream.addParser(e)},i.addTagMapper=function(e){this.parseStream.addTagMapper(e)},e}(Ts),Is="INVALID_NUMBER_OF_PERIOD",Ls="DASH_EMPTY_MANIFEST",Os="DASH_INVALID_XML",xs="NO_BASE_URL",Ds="SEGMENT_TIME_UNSPECIFIED",Us="UNSUPPORTED_UTC_TIMING_SCHEME";
/*! @name mpd-parser @version 0.8.1 @license Apache-2.0 */function Rs(e,t){return/^[a-z]+:/i.test(t)?t:(/\/\//i.test(e)||(e=eo.buildAbsoluteURL(v.location.href,e)),eo.buildAbsoluteURL(e,t))}function Ms(e){var t=e.baseUrl,i=void 0===t?"":t,n=e.source,r=void 0===n?"":n,a=e.range,s=void 0===a?"":a,o=e.indexRange,u=void 0===o?"":o,l={uri:r,resolvedUri:Rs(i||"",r)};if(s||u){var c=(s||u).split("-"),h=parseInt(c[0],10),d=parseInt(c[1],10);l.byterange={length:d-h+1,offset:h}}return l}function Ns(e){var t=e.type,i=void 0===t?"static":t,n=e.duration,r=e.timescale,a=void 0===r?1:r,s=e.sourceDuration,o=to[i](e),u=function(e,t){for(var i=[],n=e;n<t;n++)i.push(n);return i}(o.start,o.end).map(function(o){return function(e,t){var i=o.duration,n=o.timescale,r=void 0===n?1:n,a=o.periodIndex,s=o.startNumber;return{number:(void 0===s?1:s)+e,duration:i/r,timeline:a,time:t*i}}}(e));if("static"===i){var l=u.length-1;u[l].duration=s-n/a*l}return u}function Bs(e){var t=e.baseUrl,i=e.initialization,n=void 0===i?{}:i,r=e.sourceDuration,a=e.timescale,s=void 0===a?1:a,o=e.indexRange,u=void 0===o?"":o,l=e.duration;if(!t)throw new Error(xs);var c=Ms({baseUrl:t,source:n.sourceURL,range:n.range}),h=Ms({baseUrl:t,source:t,indexRange:u});if(h.map=c,l){var d=Ns(e);d.length&&(h.duration=d[0].duration,h.timeline=d[0].timeline)}else r&&(h.duration=r/s,h.timeline=0);return h.number=0,[h]}function js(e,t,i){for(var n=e.sidx.map?e.sidx.map:null,r=e.sidx.duration,a=e.timeline||0,s=e.sidx.byterange,o=s.offset+s.length,u=t.timescale,l=t.references.filter(function(e){return 1!==e.referenceType}),c=[],h=o+t.firstOffset,d=0;d<l.length;d++){var p=t.references[d],f=p.referencedSize,m=p.subsegmentDuration,g=Bs({baseUrl:i,timescale:u,timeline:a,periodIndex:a,duration:m,sourceDuration:r,indexRange:h+"-"+(h+f-1)})[0];n&&(g.map=n),c.push(g),h+=f}return e.segments=c,e}function Fs(e){return function(t){return Object.keys(t).map(function(e){return t[e]})}(e.reduce(function(e,t){var i,n=t.attributes.id+(t.attributes.lang||"");e[n]?(t.segments[0]&&(t.segments[0].discontinuity=!0),(i=e[n].segments).push.apply(i,t.segments),t.attributes.contentProtection&&(e[n].attributes.contentProtection=t.attributes.contentProtection)):e[n]=t;return e},{})).map(function(e){return e.discontinuityStarts=function(e,n){return e.reduce(function(e,t,i){return t[n]&&e.push(i),e},[])}(e.segments,"discontinuity"),e})}function Hs(e,t){if(void 0===t&&(t={}),!Object.keys(t).length)return e;for(var i in e){var n=e[i];if(n.sidx){var r=n.sidx.uri+"-"+(s=n.sidx.byterange,void 0,o=s.offset+s.length-1,s.offset+"-"+o),a=t[r]&&t[r].sidx;n.sidx&&a&&js(n,a,n.sidx.resolvedUri)}}var s,o;return e}function Vs(e){var t,i=e.attributes,n=e.segments,r=e.sidx,a={attributes:(t={NAME:i.id,AUDIO:"audio",SUBTITLES:"subs",RESOLUTION:{width:i.width,height:i.height},CODECS:i.codecs,BANDWIDTH:i.bandwidth},t["PROGRAM-ID"]=1,t),uri:"",endList:"static"===(i.type||"static"),timeline:i.periodIndex,resolvedUri:"",targetDuration:i.duration,segments:n,mediaSequence:n.length?n[0].number:1};return i.contentProtection&&(a.contentProtection=i.contentProtection),r&&(a.sidx=r),a}function qs(e,t){var i;if(void 0===t&&(t={}),!e.length)return{};var n=e[0].attributes,r=n.sourceDuration,a=n.minimumUpdatePeriod,s=void 0===a?0:a,o=Fs(e.filter(function(e){var t=e.attributes;return"video/mp4"===t.mimeType||"video"===t.contentType})).map(Vs),u=Fs(e.filter(function(e){var t=e.attributes;return"audio/mp4"===t.mimeType||"audio"===t.contentType})),l=e.filter(function(e){var t=e.attributes;return"text/vtt"===t.mimeType||"text"===t.contentType}),c={allowCache:!0,discontinuityStarts:[],segments:[],endList:!0,mediaGroups:(i={AUDIO:{},VIDEO:{}},i["CLOSED-CAPTIONS"]={},i.SUBTITLES={},i),uri:"",duration:r,playlists:Hs(o,t),minimumUpdatePeriod:1e3*s};return u.length&&(c.mediaGroups.AUDIO.audio=function(e,s){var o;void 0===s&&(s={});var t=e.reduce(function(e,t){var i=t.attributes.role&&t.attributes.role.value||"",n=t.attributes.lang||"",r="main";if(n){var a=i?" ("+i+")":"";r=t.attributes.lang+a}return e[r]&&e[r].playlists[0].attributes.BANDWIDTH>t.attributes.bandwidth||(e[r]={language:n,autoselect:!0,default:"main"===i,playlists:Hs([function(e){var t,i=e.attributes,n=e.segments,r=e.sidx,a={attributes:(t={NAME:i.id,BANDWIDTH:i.bandwidth,CODECS:i.codecs},t["PROGRAM-ID"]=1,t),uri:"",endList:"static"===(i.type||"static"),timeline:i.periodIndex,resolvedUri:"",targetDuration:i.duration,segments:n,mediaSequence:n.length?n[0].number:1};return i.contentProtection&&(a.contentProtection=i.contentProtection),r&&(a.sidx=r),a}(t)],s),uri:""},"undefined"==typeof o&&"main"===i&&((o=t).default=!0)),e},{});o||(t[Object.keys(t)[0]].default=!0);return t}(u,t)),l.length&&(c.mediaGroups.SUBTITLES.subs=function(e,n){return void 0===n&&(n={}),e.reduce(function(e,t){var i=t.attributes.lang||"text";return e[i]||(e[i]={language:i,default:!1,autoselect:!1,playlists:Hs([function(e){var t,i=e.attributes,n=e.segments;return"undefined"==typeof n&&(n=[{uri:i.baseUrl,timeline:i.periodIndex,resolvedUri:i.baseUrl||"",duration:i.sourceDuration,number:0}],i.duration=i.sourceDuration),{attributes:(t={NAME:i.id,BANDWIDTH:i.bandwidth},t["PROGRAM-ID"]=1,t),uri:"",endList:"static"===(i.type||"static"),timeline:i.periodIndex,resolvedUri:i.baseUrl||"",targetDuration:i.duration,segments:n,mediaSequence:n.length?n[0].number:1}}(t)],n),uri:""}),e},{})}(l,t)),c}function Ws(e,t){for(var i,n,r,a,s,o,u,l,c,h,d,p,f=e.type,m=void 0===f?"static":f,g=e.minimumUpdatePeriod,y=void 0===g?0:g,v=e.media,_=void 0===v?"":v,b=e.sourceDuration,T=e.timescale,S=void 0===T?1:T,k=e.startNumber,C=void 0===k?1:k,E=e.periodIndex,w=[],A=-1,P=0;P<t.length;P++){var I=t[P],L=I.d,O=I.r||0,x=I.t||0;A<0&&(A=x),x&&A<x&&(A=x);var D=void 0;if(O<0){var U=P+1;D=U===t.length?"dynamic"===m&&0<y&&0<_.indexOf("$Number$")?(n=A,r=L,void 0,a=(i=e).NOW,s=i.clientOffset,o=i.availabilityStartTime,u=i.timescale,l=void 0===u?1:u,c=i.start,h=void 0===c?0:c,d=i.minimumUpdatePeriod,p=(a+s)/1e3+(void 0===d?0:d)-(o+h),Math.ceil((p*l-n)/r)):(b*S-A)/L:(t[U].t-A)/L}else D=O+1;for(var R=C+w.length+D,M=C+w.length;M<R;)w.push({number:M,duration:L/S,time:A,timeline:E}),A+=L,M++}return w}function zs(e,t){return e.replace(io,function(a){return function(e,t,i,n){if("$$"===e)return"$";if("undefined"==typeof a[t])return e;var r=""+a[t];return"RepresentationID"===t?r:(n=i?parseInt(n,10):1)<=r.length?r:new Array(n-r.length+1).join("0")+r}}(t))}function $s(i,e){var n={RepresentationID:i.id,Bandwidth:i.bandwidth||0},t=i.initialization,r=void 0===t?{sourceURL:"",range:""}:t,a=Ms({baseUrl:i.baseUrl,source:zs(r.sourceURL,n),range:r.range});return function(e,t){return e.duration||t?e.duration?Ns(e):Ws(e,t):[{number:e.startNumber||1,duration:e.sourceDuration,time:0,timeline:e.periodIndex}]}(i,e).map(function(e){n.Number=e.number,n.Time=e.time;var t=zs(i.media||"",n);return{uri:t,timeline:e.timeline,duration:e.duration,resolvedUri:Rs(i.baseUrl||"",t),map:a,number:e.number}})}function Gs(t,e){var i=t.duration,n=t.segmentUrls,r=void 0===n?[]:n;if(!i&&!e||i&&e)throw new Error(Ds);var a,s=r.map(function(e){return function(e,t){var i=e.baseUrl,n=e.initialization,r=void 0===n?{}:n,a=Ms({baseUrl:i,source:r.sourceURL,range:r.range}),s=Ms({baseUrl:i,source:t.media,range:t.mediaRange});return s.map=a,s}(t,e)});return i&&(a=Ns(t)),e&&(a=Ws(t,e)),a.map(function(e,t){if(s[t]){var i=s[t];return i.timeline=e.timeline,i.duration=e.duration,i.number=e.number,i}}).filter(function(e){return e})}function Xs(e){var t,i,n=e.attributes,r=e.segmentInfo;r.template?(i=$s,t=Es(n,r.template)):r.base?(i=Bs,t=Es(n,r.base)):r.list&&(i=Gs,t=Es(n,r.list));var a={attributes:n};if(!i)return a;var s=i(t,r.timeline);if(t.duration){var o=t,u=o.duration,l=o.timescale,c=void 0===l?1:l;t.duration=u/c}else s.length?t.duration=s.reduce(function(e,t){return Math.max(e,Math.ceil(t.duration))},0):t.duration=0;return a.attributes=t,a.segments=s,r.base&&t.indexRange&&(a.sidx=s[0],a.segments=[]),a}function Ks(e,t){return As(e.childNodes).filter(function(e){return e.tagName===t})}function Ys(e){return e.textContent.trim()}function Qs(e){var t=/P(?:(\d*)Y)?(?:(\d*)M)?(?:(\d*)D)?(?:T(?:(\d*)H)?(?:(\d*)M)?(?:([\d.]*)S)?)?/.exec(e);if(!t)return 0;var i=t.slice(1),n=i[0],r=i[1],a=i[2],s=i[3],o=i[4],u=i[5];return 31536e3*parseFloat(n||0)+2592e3*parseFloat(r||0)+86400*parseFloat(a||0)+3600*parseFloat(s||0)+60*parseFloat(o||0)+parseFloat(u||0)}function Js(e){return e&&e.attributes?As(e.attributes).reduce(function(e,t){var i=no[t.name]||no.DEFAULT;return e[t.name]=i(t.value),e},{}):{}}var Zs,eo=(function(e,t){var i,c,n,r,h;i=/^((?:[a-zA-Z0-9+\-.]+:)?)(\/\/[^\/?#]*)?((?:[^\/\?#]*\/)*.*?)??(;.*?)?(\?.*?)?(#.*?)?$/,c=/^([^\/?#]*)(.*)$/,n=/(?:\/|^)\.(?=\/)/g,r=/(?:\/|^)\.\.\/(?!\.\.\/).*?(?=\/)/g,h={buildAbsoluteURL:function(e,t,i){if(i=i||{},e=e.trim(),!(t=t.trim())){if(!i.alwaysNormalize)return e;var n=h.parseURL(e);if(!n)throw new Error("Error trying to parse base URL.");return n.path=h.normalizePath(n.path),h.buildURLFromParts(n)}var r=h.parseURL(t);if(!r)throw new Error("Error trying to parse relative URL.");if(r.scheme)return i.alwaysNormalize?(r.path=h.normalizePath(r.path),h.buildURLFromParts(r)):t;var a=h.parseURL(e);if(!a)throw new Error("Error trying to parse base URL.");if(!a.netLoc&&a.path&&"/"!==a.path[0]){var s=c.exec(a.path);a.netLoc=s[1],a.path=s[2]}a.netLoc&&!a.path&&(a.path="/");var o={scheme:a.scheme,netLoc:r.netLoc,path:null,params:r.params,query:r.query,fragment:r.fragment};if(!r.netLoc&&(o.netLoc=a.netLoc,"/"!==r.path[0]))if(r.path){var u=a.path,l=u.substring(0,u.lastIndexOf("/")+1)+r.path;o.path=h.normalizePath(l)}else o.path=a.path,r.params||(o.params=a.params,r.query||(o.query=a.query));return null===o.path&&(o.path=i.alwaysNormalize?h.normalizePath(r.path):r.path),h.buildURLFromParts(o)},parseURL:function(e){var t=i.exec(e);return t?{scheme:t[1]||"",netLoc:t[2]||"",path:t[3]||"",params:t[4]||"",query:t[5]||"",fragment:t[6]||""}:null},normalizePath:function(e){for(e=e.split("").reverse().join("").replace(n,"");e.length!==(e=e.replace(r,"")).length;);return e.split("").reverse().join("")},buildURLFromParts:function(e){return e.scheme+e.netLoc+e.path+e.params+e.query+e.fragment}},e.exports=h}(Zs={exports:{}},Zs.exports),Zs.exports),to={static:function(e){var t=e.duration,i=e.timescale,n=void 0===i?1:i,r=e.sourceDuration;return{start:0,end:Math.ceil(r/(t/n))}},dynamic:function(e){var t=e.NOW,i=e.clientOffset,n=e.availabilityStartTime,r=e.timescale,a=void 0===r?1:r,s=e.duration,o=e.start,u=void 0===o?0:o,l=e.minimumUpdatePeriod,c=void 0===l?0:l,h=e.timeShiftBufferDepth,d=void 0===h?1/0:h,p=(t+i)/1e3,f=n+u,m=p+c-f,g=Math.ceil(m*a/s),y=Math.floor((p-f-d)*a/s),v=Math.floor((p-f)*a/s);return{start:Math.max(0,y),end:Math.min(g,v)}}},io=/\$([A-z]*)(?:(%0)([0-9]+)d)?\$/g,no={mediaPresentationDuration:function(e){return Qs(e)},availabilityStartTime:function(e){return function(e){return/^\d+-\d+-\d+T\d+:\d+:\d+(\.\d+)?$/.test(e)&&(e+="Z"),Date.parse(e)}(e)/1e3},minimumUpdatePeriod:function(e){return Qs(e)},timeShiftBufferDepth:function(e){return Qs(e)},start:function(e){return Qs(e)},width:function(e){return parseInt(e,10)},height:function(e){return parseInt(e,10)},bandwidth:function(e){return parseInt(e,10)},startNumber:function(e){return parseInt(e,10)},timescale:function(e){return parseInt(e,10)},duration:function(e){var t=parseInt(e,10);return isNaN(t)?Qs(e):t},d:function(e){return parseInt(e,10)},t:function(e){return parseInt(e,10)},r:function(e){return parseInt(e,10)},DEFAULT:function(e){return e}};function ro(e,i){return i.length?ws(e.map(function(t){return i.map(function(e){return Rs(t,Ys(e))})})):e}function ao(e){var t=Ks(e,"SegmentTemplate")[0],i=Ks(e,"SegmentList")[0],n=i&&Ks(i,"SegmentURL").map(function(e){return Es({tag:"SegmentURL"},Js(e))}),r=Ks(e,"SegmentBase")[0],a=i||t,s=a&&Ks(a,"SegmentTimeline")[0],o=i||r||t,u=o&&Ks(o,"Initialization")[0],l=t&&Js(t);l&&u?l.initialization=u&&Js(u):l&&l.initialization&&(l.initialization={sourceURL:l.initialization});var c={template:l,timeline:s&&Ks(s,"S").map(function(e){return Js(e)}),list:i&&Es(Js(i),{segmentUrls:n,initialization:Js(u)}),base:r&&Es(Js(r),{initialization:Js(u)})};return Object.keys(c).forEach(function(e){c[e]||delete c[e]}),c}function so(e){return e.reduce(function(e,t){var i=Js(t),n=_o[i.schemeIdUri];if(n){e[n]={attributes:i};var r=Ks(t,"cenc:pssh")[0];if(r){var a=Ys(r),s=a&&function(e){for(var t=v.atob(e),i=new Uint8Array(t.length),n=0;n<t.length;n++)i[n]=t.charCodeAt(n);return i}(a);e[n].pssh=s}}return e},{})}function oo(c,h,d){return function(e){var t=Js(e),i=ro(h,Ks(e,"BaseURL")),n=Ks(e,"Role")[0],r={role:Js(n)},a=Es(c,t,r),s=so(Ks(e,"ContentProtection"));Object.keys(s).length&&(a=Es(a,{contentProtection:s}));var o=ao(e),u=Ks(e,"Representation"),l=Es(d,o);return ws(u.map(function(a,s,o){return function(e){var t=Ks(e,"BaseURL"),i=ro(s,t),n=Es(a,Js(e)),r=ao(e);return i.map(function(e){return{segmentInfo:Es(o,r),attributes:Es(n,{baseUrl:e})}})}}(a,i,l)))}}function uo(e,t){void 0===t&&(t={});var i=t,n=i.manifestUri,r=void 0===n?"":n,a=i.NOW,s=void 0===a?Date.now():a,o=i.clientOffset,u=void 0===o?0:o,l=Ks(e,"Period");if(!l.length)throw new Error(Is);var c=Js(e),h=ro([r],Ks(e,"BaseURL"));return c.sourceDuration=c.mediaPresentationDuration||0,c.NOW=s,c.clientOffset=u,ws(l.map(function(l,c){return function(e,t){var i=ro(c,Ks(e,"BaseURL")),n=Js(e),r=parseInt(n.id,10),a=v.isNaN(r)?t:r,s=Es(l,{periodIndex:a}),o=Ks(e,"AdaptationSet"),u=ao(e);return ws(o.map(oo(s,i,u)))}}(c,h)))}function lo(e){if(""===e)throw new Error(Ls);var t=(new v.DOMParser).parseFromString(e,"application/xml"),i=t&&"MPD"===t.documentElement.tagName?t.documentElement:null;if(!i||i&&0<i.getElementsByTagName("parsererror").length)throw new Error(Os);return i}function co(e,t){return void 0===t&&(t={}),qs(function(e){return e.map(Xs)}(uo(lo(e),t)),t.sidxMapping)}function ho(e){return function(e){var t=Ks(e,"UTCTiming")[0];if(!t)return null;var i=Js(t);switch(i.schemeIdUri){case"urn:mpeg:dash:utc:http-head:2014":case"urn:mpeg:dash:utc:http-head:2012":i.method="HEAD";break;case"urn:mpeg:dash:utc:http-xsdate:2014":case"urn:mpeg:dash:utc:http-iso:2014":case"urn:mpeg:dash:utc:http-xsdate:2012":case"urn:mpeg:dash:utc:http-iso:2012":i.method="GET";break;case"urn:mpeg:dash:utc:direct:2014":case"urn:mpeg:dash:utc:direct:2012":i.method="DIRECT",i.value=Date.parse(i.value);break;case"urn:mpeg:dash:utc:http-ntp:2014":case"urn:mpeg:dash:utc:ntp:2014":case"urn:mpeg:dash:utc:sntp:2014":default:throw new Error(Us)}return i}(lo(e))}var po,fo,mo,go,yo,vo,_o={"urn:uuid:1077efec-c0b2-4d02-ace3-3c1e52e2fb4b":"org.w3.clearkey","urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed":"com.widevine.alpha","urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95":"com.microsoft.playready","urn:uuid:f239e769-efa3-4850-9c16-a903c6932efb":"com.adobe.primetime"},bo=function(e){return e>>>0},To=function(e){return("00"+e.toString(16)).slice(-2)};mo=function(e){return po(e,["moov","trak"]).reduce(function(e,t){var i,n,r,a,s;return(i=po(t,["tkhd"])[0])?(n=i[0],a=bo(i[r=0===n?12:20]<<24|i[r+1]<<16|i[r+2]<<8|i[r+3]),(s=po(t,["mdia","mdhd"])[0])?(r=0===(n=s[0])?12:20,e[a]=bo(s[r]<<24|s[r+1]<<16|s[r+2]<<8|s[r+3]),e):null):null},{})},go=function(r,e){var t,i,n;return t=po(e,["moof","traf"]),i=[].concat.apply([],t.map(function(n){return po(n,["tfhd"]).map(function(e){var t,i;return t=bo(e[4]<<24|e[5]<<16|e[6]<<8|e[7]),i=r[t]||9e4,(po(n,["tfdt"]).map(function(e){var t,i;return t=e[0],i=bo(e[4]<<24|e[5]<<16|e[6]<<8|e[7]),1===t&&(i*=Math.pow(2,32),i+=bo(e[8]<<24|e[9]<<16|e[10]<<8|e[11])),i})[0]||1/0)/i})})),n=Math.min.apply(null,i),isFinite(n)?n:0},yo=function(e){var t=po(e,["moov","trak"]),o=[];return t.forEach(function(e){var t=po(e,["mdia","hdlr"]),s=po(e,["tkhd"]);t.forEach(function(e,t){var i,n,r=fo(e.subarray(8,12)),a=s[t];"vide"===r&&(n=0===(i=new DataView(a.buffer,a.byteOffset,a.byteLength)).getUint8(0)?i.getUint32(12):i.getUint32(20),o.push(n))})}),o},vo=function(e){var t=po(e,["moov","trak"]),p=[];return t.forEach(function(e){var t,i,n={},r=po(e,["tkhd"])[0];r&&(i=(t=new DataView(r.buffer,r.byteOffset,r.byteLength)).getUint8(0),n.id=0===i?t.getUint32(12):t.getUint32(20));var a=po(e,["mdia","hdlr"])[0];if(a){var s=fo(a.subarray(8,12));n.type="vide"===s?"video":"soun"===s?"audio":s}var o=po(e,["mdia","minf","stbl","stsd"])[0];if(o){var u=o.subarray(8);n.codec=fo(u.subarray(4,8));var l,c=po(u,[n.codec])[0];c&&(/^[a-z]vc[1-9]$/i.test(n.codec)?(l=c.subarray(78),"avcC"===fo(l.subarray(4,8))&&11<l.length?(n.codec+=".",n.codec+=To(l[9]),n.codec+=To(l[10]),n.codec+=To(l[11])):n.codec="avc1.4d400d"):/^mp4[a,v]$/i.test(n.codec)&&(l=c.subarray(28),"esds"===fo(l.subarray(4,8))&&20<l.length&&0!==l[19]?(n.codec+="."+To(l[19]),n.codec+="."+To(l[20]>>>2&63).replace(/^0/,"")):n.codec="mp4a.40.2"))}var h=po(e,["mdia","mdhd"])[0];if(h&&r){var d=0===i?12:20;n.timescale=bo(h[d]<<24|h[1+d]<<16|h[2+d]<<8|h[3+d])}p.push(n)}),p};function So(e){return new Date(1e3*e-20828448e5)}function ko(e){return{isLeading:(12&e[0])>>>2,dependsOn:3&e[0],isDependedOn:(192&e[1])>>>6,hasRedundancy:(48&e[1])>>>4,paddingValue:(14&e[1])>>>1,isNonSyncSample:1&e[1],degradationPriority:e[2]<<8|e[3]}}function Co(){this.init=function(){var a={};this.on=function(e,t){a[e]||(a[e]=[]),a[e]=a[e].concat(t)},this.off=function(e,t){var i;return!!a[e]&&(i=a[e].indexOf(t),a[e]=a[e].slice(),a[e].splice(i,1),-1<i)},this.trigger=function(e){var t,i,n,r;if(t=a[e])if(2===arguments.length)for(n=t.length,i=0;i<n;++i)t[i].call(this,arguments[1]);else{for(r=[],i=arguments.length,i=1;i<arguments.length;++i)r.push(arguments[i]);for(n=t.length,i=0;i<n;++i)t[i].apply(this,r)}},this.dispose=function(){a={}}}}var Eo,wo,Ao={findBox:po=function(e,t){var i,n,r,a,s,o=[];if(!t.length)return null;for(i=0;i<e.byteLength;)n=bo(e[i]<<24|e[i+1]<<16|e[i+2]<<8|e[i+3]),r=fo(e.subarray(i+4,i+8)),a=1<n?i+n:e.byteLength,r===t[0]&&(1===t.length?o.push(e.subarray(i+8,a)):(s=po(e.subarray(i+8,a),t.slice(1))).length&&(o=o.concat(s))),i=a;return o},parseType:fo=function(e){var t="";return t+=String.fromCharCode(e[0]),t+=String.fromCharCode(e[1]),t+=String.fromCharCode(e[2]),t+=String.fromCharCode(e[3])},timescale:mo,startTime:go,videoTrackIds:yo,tracks:vo},Po=Ao.parseType,Io={avc1:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{dataReferenceIndex:t.getUint16(6),width:t.getUint16(24),height:t.getUint16(26),horizresolution:t.getUint16(28)+t.getUint16(30)/16,vertresolution:t.getUint16(32)+t.getUint16(34)/16,frameCount:t.getUint16(40),depth:t.getUint16(74),config:Eo(e.subarray(78,e.byteLength))}},avcC:function(e){var t,i,n,r,a=new DataView(e.buffer,e.byteOffset,e.byteLength),s={configurationVersion:e[0],avcProfileIndication:e[1],profileCompatibility:e[2],avcLevelIndication:e[3],lengthSizeMinusOne:3&e[4],sps:[],pps:[]},o=31&e[5];for(n=6,r=0;r<o;r++)i=a.getUint16(n),n+=2,s.sps.push(new Uint8Array(e.subarray(n,n+i))),n+=i;for(t=e[n],n++,r=0;r<t;r++)i=a.getUint16(n),n+=2,s.pps.push(new Uint8Array(e.subarray(n,n+i))),n+=i;return s},btrt:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{bufferSizeDB:t.getUint32(0),maxBitrate:t.getUint32(4),avgBitrate:t.getUint32(8)}},esds:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),esId:e[6]<<8|e[7],streamPriority:31&e[8],decoderConfig:{objectProfileIndication:e[11],streamType:e[12]>>>2&63,bufferSize:e[13]<<16|e[14]<<8|e[15],maxBitrate:e[16]<<24|e[17]<<16|e[18]<<8|e[19],avgBitrate:e[20]<<24|e[21]<<16|e[22]<<8|e[23],decoderConfigDescriptor:{tag:e[24],length:e[25],audioObjectType:e[26]>>>3&31,samplingFrequencyIndex:(7&e[26])<<1|e[27]>>>7&1,channelConfiguration:e[27]>>>3&15}}}},ftyp:function(e){for(var t=new DataView(e.buffer,e.byteOffset,e.byteLength),i={majorBrand:Po(e.subarray(0,4)),minorVersion:t.getUint32(4),compatibleBrands:[]},n=8;n<e.byteLength;)i.compatibleBrands.push(Po(e.subarray(n,n+4))),n+=4;return i},dinf:function(e){return{boxes:Eo(e)}},dref:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),dataReferences:Eo(e.subarray(8))}},hdlr:function(e){var t={version:new DataView(e.buffer,e.byteOffset,e.byteLength).getUint8(0),flags:new Uint8Array(e.subarray(1,4)),handlerType:Po(e.subarray(8,12)),name:""},i=8;for(i=24;i<e.byteLength;i++){if(0===e[i]){i++;break}t.name+=String.fromCharCode(e[i])}return t.name=decodeURIComponent(escape(t.name)),t},mdat:function(e){return{byteLength:e.byteLength,nals:function(e){var t,i,n=new DataView(e.buffer,e.byteOffset,e.byteLength),r=[];for(t=0;t+4<e.length;t+=i)if(i=n.getUint32(t),t+=4,i<=0)r.push("<span style='color:red;'>MALFORMED DATA</span>");else switch(31&e[t]){case 1:r.push("slice_layer_without_partitioning_rbsp");break;case 5:r.push("slice_layer_without_partitioning_rbsp_idr");break;case 6:r.push("sei_rbsp");break;case 7:r.push("seq_parameter_set_rbsp");break;case 8:r.push("pic_parameter_set_rbsp");break;case 9:r.push("access_unit_delimiter_rbsp");break;default:r.push("UNKNOWN NAL - "+e[t]&31)}return r}(e)}},mdhd:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n=4,r={version:i.getUint8(0),flags:new Uint8Array(e.subarray(1,4)),language:""};return 1===r.version?(n+=4,r.creationTime=So(i.getUint32(n)),n+=8,r.modificationTime=So(i.getUint32(n)),n+=4,r.timescale=i.getUint32(n),n+=8):(r.creationTime=So(i.getUint32(n)),n+=4,r.modificationTime=So(i.getUint32(n)),n+=4,r.timescale=i.getUint32(n),n+=4),r.duration=i.getUint32(n),n+=4,t=i.getUint16(n),r.language+=String.fromCharCode(96+(t>>10)),r.language+=String.fromCharCode(96+((992&t)>>5)),r.language+=String.fromCharCode(96+(31&t)),r},mdia:function(e){return{boxes:Eo(e)}},mfhd:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),sequenceNumber:e[4]<<24|e[5]<<16|e[6]<<8|e[7]}},minf:function(e){return{boxes:Eo(e)}},mp4a:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),i={dataReferenceIndex:t.getUint16(6),channelcount:t.getUint16(16),samplesize:t.getUint16(18),samplerate:t.getUint16(24)+t.getUint16(26)/65536};return 28<e.byteLength&&(i.streamDescriptor=Eo(e.subarray(28))[0]),i},moof:function(e){return{boxes:Eo(e)}},moov:function(e){return{boxes:Eo(e)}},mvex:function(e){return{boxes:Eo(e)}},mvhd:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),i=4,n={version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4))};return 1===n.version?(i+=4,n.creationTime=So(t.getUint32(i)),i+=8,n.modificationTime=So(t.getUint32(i)),i+=4,n.timescale=t.getUint32(i),i+=8):(n.creationTime=So(t.getUint32(i)),i+=4,n.modificationTime=So(t.getUint32(i)),i+=4,n.timescale=t.getUint32(i),i+=4),n.duration=t.getUint32(i),i+=4,n.rate=t.getUint16(i)+t.getUint16(i+2)/16,i+=4,n.volume=t.getUint8(i)+t.getUint8(i+1)/8,i+=2,i+=2,i+=8,n.matrix=new Uint32Array(e.subarray(i,i+36)),i+=36,i+=24,n.nextTrackId=t.getUint32(i),n},pdin:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4)),rate:t.getUint32(4),initialDelay:t.getUint32(8)}},sdtp:function(e){var t,i={version:e[0],flags:new Uint8Array(e.subarray(1,4)),samples:[]};for(t=4;t<e.byteLength;t++)i.samples.push({dependsOn:(48&e[t])>>4,isDependedOn:(12&e[t])>>2,hasRedundancy:3&e[t]});return i},sidx:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),references:[],referenceId:i.getUint32(4),timescale:i.getUint32(8),earliestPresentationTime:i.getUint32(12),firstOffset:i.getUint32(16)},r=i.getUint16(22);for(t=24;r;t+=12,r--)n.references.push({referenceType:(128&e[t])>>>7,referencedSize:2147483647&i.getUint32(t),subsegmentDuration:i.getUint32(t+4),startsWithSap:!!(128&e[t+8]),sapType:(112&e[t+8])>>>4,sapDeltaTime:268435455&i.getUint32(t+8)});return n},smhd:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),balance:e[4]+e[5]/256}},stbl:function(e){return{boxes:Eo(e)}},stco:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),chunkOffsets:[]},r=i.getUint32(4);for(t=8;r;t+=4,r--)n.chunkOffsets.push(i.getUint32(t));return n},stsc:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n=i.getUint32(4),r={version:e[0],flags:new Uint8Array(e.subarray(1,4)),sampleToChunks:[]};for(t=8;n;t+=12,n--)r.sampleToChunks.push({firstChunk:i.getUint32(t),samplesPerChunk:i.getUint32(t+4),sampleDescriptionIndex:i.getUint32(t+8)});return r},stsd:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),sampleDescriptions:Eo(e.subarray(8))}},stsz:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),sampleSize:i.getUint32(4),entries:[]};for(t=12;t<e.byteLength;t+=4)n.entries.push(i.getUint32(t));return n},stts:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),timeToSamples:[]},r=i.getUint32(4);for(t=8;r;t+=8,r--)n.timeToSamples.push({sampleCount:i.getUint32(t),sampleDelta:i.getUint32(t+4)});return n},styp:function(e){return Io.ftyp(e)},tfdt:function(e){var t={version:e[0],flags:new Uint8Array(e.subarray(1,4)),baseMediaDecodeTime:e[4]<<24|e[5]<<16|e[6]<<8|e[7]};return 1===t.version&&(t.baseMediaDecodeTime*=Math.pow(2,32),t.baseMediaDecodeTime+=e[8]<<24|e[9]<<16|e[10]<<8|e[11]),t},tfhd:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),trackId:i.getUint32(4)},r=1&n.flags[2],a=2&n.flags[2],s=8&n.flags[2],o=16&n.flags[2],u=32&n.flags[2],l=65536&n.flags[0],c=131072&n.flags[0];return t=8,r&&(t+=4,n.baseDataOffset=i.getUint32(12),t+=4),a&&(n.sampleDescriptionIndex=i.getUint32(t),t+=4),s&&(n.defaultSampleDuration=i.getUint32(t),t+=4),o&&(n.defaultSampleSize=i.getUint32(t),t+=4),u&&(n.defaultSampleFlags=i.getUint32(t)),l&&(n.durationIsEmpty=!0),!r&&c&&(n.baseDataOffsetIsMoof=!0),n},tkhd:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),i=4,n={version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4))};return 1===n.version?(i+=4,n.creationTime=So(t.getUint32(i)),i+=8,n.modificationTime=So(t.getUint32(i)),i+=4,n.trackId=t.getUint32(i),i+=4,i+=8):(n.creationTime=So(t.getUint32(i)),i+=4,n.modificationTime=So(t.getUint32(i)),i+=4,n.trackId=t.getUint32(i),i+=4,i+=4),n.duration=t.getUint32(i),i+=4,i+=8,n.layer=t.getUint16(i),i+=2,n.alternateGroup=t.getUint16(i),i+=2,n.volume=t.getUint8(i)+t.getUint8(i+1)/8,i+=2,i+=2,n.matrix=new Uint32Array(e.subarray(i,i+36)),i+=36,n.width=t.getUint16(i)+t.getUint16(i+2)/16,i+=4,n.height=t.getUint16(i)+t.getUint16(i+2)/16,n},traf:function(e){return{boxes:Eo(e)}},trak:function(e){return{boxes:Eo(e)}},trex:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),trackId:t.getUint32(4),defaultSampleDescriptionIndex:t.getUint32(8),defaultSampleDuration:t.getUint32(12),defaultSampleSize:t.getUint32(16),sampleDependsOn:3&e[20],sampleIsDependedOn:(192&e[21])>>6,sampleHasRedundancy:(48&e[21])>>4,samplePaddingValue:(14&e[21])>>1,sampleIsDifferenceSample:!!(1&e[21]),sampleDegradationPriority:t.getUint16(22)}},trun:function(e){var t,i={version:e[0],flags:new Uint8Array(e.subarray(1,4)),samples:[]},n=new DataView(e.buffer,e.byteOffset,e.byteLength),r=1&i.flags[2],a=4&i.flags[2],s=1&i.flags[1],o=2&i.flags[1],u=4&i.flags[1],l=8&i.flags[1],c=n.getUint32(4),h=8;for(r&&(i.dataOffset=n.getInt32(h),h+=4),a&&c&&(t={flags:ko(e.subarray(h,h+4))},h+=4,s&&(t.duration=n.getUint32(h),h+=4),o&&(t.size=n.getUint32(h),h+=4),l&&(t.compositionTimeOffset=n.getUint32(h),h+=4),i.samples.push(t),c--);c--;)t={},s&&(t.duration=n.getUint32(h),h+=4),o&&(t.size=n.getUint32(h),h+=4),u&&(t.flags=ko(e.subarray(h,h+4)),h+=4),l&&(t.compositionTimeOffset=n.getUint32(h),h+=4),i.samples.push(t);return i},"url ":function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4))}},vmhd:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),graphicsmode:t.getUint16(4),opcolor:new Uint16Array([t.getUint16(6),t.getUint16(8),t.getUint16(10)])}}},Lo={inspect:Eo=function(e){for(var t,i,n,r,a,s=0,o=[],u=new ArrayBuffer(e.length),l=new Uint8Array(u),c=0;c<e.length;++c)l[c]=e[c];for(t=new DataView(u);s<e.byteLength;)i=t.getUint32(s),n=Po(e.subarray(s+4,s+8)),r=1<i?s+i:e.byteLength,(a=(Io[n]||function(e){return{data:e}})(e.subarray(s+8,r))).size=i,a.type=n,o.push(a),s=r;return o},textify:wo=function(e,t){var a;return t=t||0,a=new Array(2*t+1).join(" "),e.map(function(r,e){return a+r.type+"\n"+Object.keys(r).filter(function(e){return"type"!==e&&"boxes"!==e}).map(function(e){var t=a+"  "+e+": ",i=r[e];if(i instanceof Uint8Array||i instanceof Uint32Array){var n=Array.prototype.slice.call(new Uint8Array(i.buffer,i.byteOffset,i.byteLength)).map(function(e){return" "+("00"+e.toString(16)).slice(-2)}).join("").match(/.{1,24}/g);return n?1===n.length?t+"<"+n.join("").slice(1)+">":t+"<\n"+n.map(function(e){return a+"  "+e}).join("\n")+"\n"+a+"  >":t+"<>"}return t+JSON.stringify(i,null,2).split("\n").map(function(e,t){return 0===t?e:a+"  "+e}).join("\n")}).join("\n")+(r.boxes?"\n"+wo(r.boxes,t+1):"")}).join("\n")},parseTfdt:Io.tfdt,parseHdlr:Io.hdlr,parseTfhd:Io.tfhd,parseTrun:Io.trun,parseSidx:Io.sidx},Oo=function(e){for(var t=0,i={payloadType:-1,payloadSize:0},n=0,r=0;t<e.byteLength&&128!==e[t];){for(;255===e[t];)n+=255,t++;for(n+=e[t++];255===e[t];)r+=255,t++;if(r+=e[t++],!i.payload&&4===n){i.payloadType=n,i.payloadSize=r,i.payload=e.subarray(t,t+r);break}t+=r,r=n=0}return i},xo=function(e){return 181!==e.payload[0]?null:49!=(e.payload[1]<<8|e.payload[2])?null:"GA94"!==String.fromCharCode(e.payload[3],e.payload[4],e.payload[5],e.payload[6])?null:3!==e.payload[7]?null:e.payload.subarray(8,e.payload.length-1)},Do=function(e,t){var i,n,r,a,s=[];if(!(64&t[0]))return s;for(n=31&t[0],i=0;i<n;i++)a={type:3&t[2+(r=3*i)],pts:e},4&t[2+r]&&(a.ccData=t[3+r]<<8|t[4+r],s.push(a));return s},Uo=function(e){for(var t,i,n=e.byteLength,r=[],a=1;a<n-2;)0===e[a]&&0===e[a+1]&&3===e[a+2]?(r.push(a+2),a+=2):a++;if(0===r.length)return e;t=n-r.length,i=new Uint8Array(t);var s=0;for(a=0;a<t;s++,a++)s===r[0]&&(s++,r.shift()),i[a]=e[s];return i},Ro=4;Co.prototype.pipe=function(t){return this.on("data",function(e){t.push(e)}),this.on("done",function(e){t.flush(e)}),this.on("partialdone",function(e){t.partialFlush(e)}),this.on("endedtimeline",function(e){t.endTimeline(e)}),this.on("reset",function(e){t.reset(e)}),t},Co.prototype.push=function(e){this.trigger("data",e)},Co.prototype.flush=function(e){this.trigger("done",e)},Co.prototype.partialFlush=function(e){this.trigger("partialdone",e)},Co.prototype.endTimeline=function(e){this.trigger("endedtimeline",e)},Co.prototype.reset=function(e){this.trigger("reset",e)};function Mo(){Mo.prototype.init.call(this),this.captionPackets_=[],this.ccStreams_=[new Vo(0,0),new Vo(0,1),new Vo(1,0),new Vo(1,1)],this.reset(),this.ccStreams_.forEach(function(e){e.on("data",this.trigger.bind(this,"data")),e.on("partialdone",this.trigger.bind(this,"partialdone")),e.on("done",this.trigger.bind(this,"done"))},this)}var No=Co;(Mo.prototype=new No).push=function(e){var t,i,n;if("sei_rbsp"===e.nalUnitType&&(t=Oo(e.escapedRBSP)).payloadType===Ro&&(i=xo(t)))if(e.dts<this.latestDts_)this.ignoreNextEqualDts_=!0;else{if(e.dts===this.latestDts_&&this.ignoreNextEqualDts_)return this.numSameDts_--,void(this.numSameDts_||(this.ignoreNextEqualDts_=!1));n=Do(e.pts,i),this.captionPackets_=this.captionPackets_.concat(n),this.latestDts_!==e.dts&&(this.numSameDts_=0),this.numSameDts_++,this.latestDts_=e.dts}},Mo.prototype.flushCCStreams=function(t){this.ccStreams_.forEach(function(e){return"flush"===t?e.flush():e.partialFlush()},this)},Mo.prototype.flushStream=function(e){this.captionPackets_.length&&(this.captionPackets_.forEach(function(e,t){e.presortIndex=t}),this.captionPackets_.sort(function(e,t){return e.pts===t.pts?e.presortIndex-t.presortIndex:e.pts-t.pts}),this.captionPackets_.forEach(function(e){e.type<2&&this.dispatchCea608Packet(e)},this),this.captionPackets_.length=0),this.flushCCStreams(e)},Mo.prototype.flush=function(){return this.flushStream("flush")},Mo.prototype.partialFlush=function(){return this.flushStream("partialFlush")},Mo.prototype.reset=function(){this.latestDts_=null,this.ignoreNextEqualDts_=!1,this.numSameDts_=0,this.activeCea608Channel_=[null,null],this.ccStreams_.forEach(function(e){e.reset()})},Mo.prototype.dispatchCea608Packet=function(e){this.setsTextOrXDSActive(e)?this.activeCea608Channel_[e.type]=null:this.setsChannel1Active(e)?this.activeCea608Channel_[e.type]=0:this.setsChannel2Active(e)&&(this.activeCea608Channel_[e.type]=1),null!==this.activeCea608Channel_[e.type]&&this.ccStreams_[(e.type<<1)+this.activeCea608Channel_[e.type]].push(e)},Mo.prototype.setsChannel1Active=function(e){return 4096==(30720&e.ccData)},Mo.prototype.setsChannel2Active=function(e){return 6144==(30720&e.ccData)},Mo.prototype.setsTextOrXDSActive=function(e){return 256==(28928&e.ccData)||4138==(30974&e.ccData)||6186==(30974&e.ccData)};function Bo(e){return null===e?"":(e=Fo[e]||e,String.fromCharCode(e))}function jo(){for(var e=[],t=15;t--;)e.push("");return e}var Fo={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,304:174,305:176,306:189,307:191,308:8482,309:162,310:163,311:9834,312:224,313:160,314:232,315:226,316:234,317:238,318:244,319:251,544:193,545:201,546:211,547:218,548:220,549:252,550:8216,551:161,552:42,553:39,554:8212,555:169,556:8480,557:8226,558:8220,559:8221,560:192,561:194,562:199,563:200,564:202,565:203,566:235,567:206,568:207,569:239,570:212,571:217,572:249,573:219,574:171,575:187,800:195,801:227,802:205,803:204,804:236,805:210,806:242,807:213,808:245,809:123,810:125,811:92,812:94,813:95,814:124,815:126,816:196,817:228,818:214,819:246,820:223,821:165,822:164,823:9474,824:197,825:229,826:216,827:248,828:9484,829:9488,830:9492,831:9496},Ho=[4352,4384,4608,4640,5376,5408,5632,5664,5888,5920,4096,4864,4896,5120,5152],Vo=function e(t,i){e.prototype.init.call(this),this.field_=t||0,this.dataChannel_=i||0,this.name_="CC"+(1+(this.field_<<1|this.dataChannel_)),this.setConstants(),this.reset(),this.push=function(e){var t,i,n,r,a;if((t=32639&e.ccData)!==this.lastControlCode_){if(4096==(61440&t)?this.lastControlCode_=t:t!==this.PADDING_&&(this.lastControlCode_=null),n=t>>>8,r=255&t,t!==this.PADDING_)if(t===this.RESUME_CAPTION_LOADING_)this.mode_="popOn";else if(t===this.END_OF_CAPTION_)this.mode_="popOn",this.clearFormatting(e.pts),this.flushDisplayed(e.pts),i=this.displayed_,this.displayed_=this.nonDisplayed_,this.nonDisplayed_=i,this.startPts_=e.pts;else if(t===this.ROLL_UP_2_ROWS_)this.rollUpRows_=2,this.setRollUp(e.pts);else if(t===this.ROLL_UP_3_ROWS_)this.rollUpRows_=3,this.setRollUp(e.pts);else if(t===this.ROLL_UP_4_ROWS_)this.rollUpRows_=4,this.setRollUp(e.pts);else if(t===this.CARRIAGE_RETURN_)this.clearFormatting(e.pts),this.flushDisplayed(e.pts),this.shiftRowsUp_(),this.startPts_=e.pts;else if(t===this.BACKSPACE_)"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[this.row_]=this.displayed_[this.row_].slice(0,-1);else if(t===this.ERASE_DISPLAYED_MEMORY_)this.flushDisplayed(e.pts),this.displayed_=jo();else if(t===this.ERASE_NON_DISPLAYED_MEMORY_)this.nonDisplayed_=jo();else if(t===this.RESUME_DIRECT_CAPTIONING_)"paintOn"!==this.mode_&&(this.flushDisplayed(e.pts),this.displayed_=jo()),this.mode_="paintOn",this.startPts_=e.pts;else if(this.isSpecialCharacter(n,r))a=Bo((n=(3&n)<<8)|r),this[this.mode_](e.pts,a),this.column_++;else if(this.isExtCharacter(n,r))"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[this.row_]=this.displayed_[this.row_].slice(0,-1),a=Bo((n=(3&n)<<8)|r),this[this.mode_](e.pts,a),this.column_++;else if(this.isMidRowCode(n,r))this.clearFormatting(e.pts),this[this.mode_](e.pts," "),this.column_++,14==(14&r)&&this.addFormatting(e.pts,["i"]),1==(1&r)&&this.addFormatting(e.pts,["u"]);else if(this.isOffsetControlCode(n,r))this.column_+=3&r;else if(this.isPAC(n,r)){var s=Ho.indexOf(7968&t);"rollUp"===this.mode_&&(s-this.rollUpRows_+1<0&&(s=this.rollUpRows_-1),this.setRollUp(e.pts,s)),s!==this.row_&&(this.clearFormatting(e.pts),this.row_=s),1&r&&-1===this.formatting_.indexOf("u")&&this.addFormatting(e.pts,["u"]),16==(16&t)&&(this.column_=4*((14&t)>>1)),this.isColorPAC(r)&&14==(14&r)&&this.addFormatting(e.pts,["i"])}else this.isNormalChar(n)&&(0===r&&(r=null),a=Bo(n),a+=Bo(r),this[this.mode_](e.pts,a),this.column_+=a.length)}else this.lastControlCode_=null}};Vo.prototype=new No,Vo.prototype.flushDisplayed=function(e){var t=this.displayed_.map(function(e){try{return e.trim()}catch(e){return""}}).join("\n").replace(/^\n+|\n+$/g,"");t.length&&this.trigger("data",{startPts:this.startPts_,endPts:e,text:t,stream:this.name_})},Vo.prototype.reset=function(){this.mode_="popOn",this.topRow_=0,this.startPts_=0,this.displayed_=jo(),this.nonDisplayed_=jo(),this.lastControlCode_=null,this.column_=0,this.row_=14,this.rollUpRows_=2,this.formatting_=[]},Vo.prototype.setConstants=function(){0===this.dataChannel_?(this.BASE_=16,this.EXT_=17,this.CONTROL_=(20|this.field_)<<8,this.OFFSET_=23):1===this.dataChannel_&&(this.BASE_=24,this.EXT_=25,this.CONTROL_=(28|this.field_)<<8,this.OFFSET_=31),this.PADDING_=0,this.RESUME_CAPTION_LOADING_=32|this.CONTROL_,this.END_OF_CAPTION_=47|this.CONTROL_,this.ROLL_UP_2_ROWS_=37|this.CONTROL_,this.ROLL_UP_3_ROWS_=38|this.CONTROL_,this.ROLL_UP_4_ROWS_=39|this.CONTROL_,this.CARRIAGE_RETURN_=45|this.CONTROL_,this.RESUME_DIRECT_CAPTIONING_=41|this.CONTROL_,this.BACKSPACE_=33|this.CONTROL_,this.ERASE_DISPLAYED_MEMORY_=44|this.CONTROL_,this.ERASE_NON_DISPLAYED_MEMORY_=46|this.CONTROL_},Vo.prototype.isSpecialCharacter=function(e,t){return e===this.EXT_&&48<=t&&t<=63},Vo.prototype.isExtCharacter=function(e,t){return(e===this.EXT_+1||e===this.EXT_+2)&&32<=t&&t<=63},Vo.prototype.isMidRowCode=function(e,t){return e===this.EXT_&&32<=t&&t<=47},Vo.prototype.isOffsetControlCode=function(e,t){return e===this.OFFSET_&&33<=t&&t<=35},Vo.prototype.isPAC=function(e,t){return e>=this.BASE_&&e<this.BASE_+8&&64<=t&&t<=127},Vo.prototype.isColorPAC=function(e){return 64<=e&&e<=79||96<=e&&e<=127},Vo.prototype.isNormalChar=function(e){return 32<=e&&e<=127},Vo.prototype.setRollUp=function(e,t){if("rollUp"!==this.mode_&&(this.row_=14,this.mode_="rollUp",this.flushDisplayed(e),this.nonDisplayed_=jo(),this.displayed_=jo()),void 0!==t&&t!==this.row_)for(var i=0;i<this.rollUpRows_;i++)this.displayed_[t-i]=this.displayed_[this.row_-i],this.displayed_[this.row_-i]="";void 0===t&&(t=this.row_),this.topRow_=t-this.rollUpRows_+1},Vo.prototype.addFormatting=function(e,t){this.formatting_=this.formatting_.concat(t);var i=t.reduce(function(e,t){return e+"<"+t+">"},"");this[this.mode_](e,i)},Vo.prototype.clearFormatting=function(e){if(this.formatting_.length){var t=this.formatting_.reverse().reduce(function(e,t){return e+"</"+t+">"},"");this.formatting_=[],this[this.mode_](e,t)}},Vo.prototype.popOn=function(e,t){var i=this.nonDisplayed_[this.row_];i+=t,this.nonDisplayed_[this.row_]=i},Vo.prototype.rollUp=function(e,t){var i=this.displayed_[this.row_];i+=t,this.displayed_[this.row_]=i},Vo.prototype.shiftRowsUp_=function(){var e;for(e=0;e<this.topRow_;e++)this.displayed_[e]="";for(e=this.row_+1;e<15;e++)this.displayed_[e]="";for(e=this.topRow_;e<this.row_;e++)this.displayed_[e]=this.displayed_[e+1];this.displayed_[this.row_]=""},Vo.prototype.paintOn=function(e,t){var i=this.displayed_[this.row_];i+=t,this.displayed_[this.row_]=i};function qo(e,t){for(var i=e,n=0;n<t.length;n++){var r=t[n];if(i<r.size)return r;i-=r.size}return null}function Wo(e,c){var n=Ao.findBox(e,["moof","traf"]),t=Ao.findBox(e,["mdat"]),h={},r=[];return t.forEach(function(e,t){var i=n[t];r.push({mdat:e,traf:i})}),r.forEach(function(e){var t,i=e.mdat,n=e.traf,r=Ao.findBox(n,["tfhd"]),a=Lo.parseTfhd(r[0]),s=a.trackId,o=Ao.findBox(n,["tfdt"]),u=0<o.length?Lo.parseTfdt(o[0]).baseMediaDecodeTime:0,l=Ao.findBox(n,["trun"]);c===s&&0<l.length&&(t=function(e,t,i){var n,r,a,s,o=new DataView(e.buffer,e.byteOffset,e.byteLength),u=[];for(r=0;r+4<e.length;r+=a)if(a=o.getUint32(r),r+=4,!(a<=0))switch(31&e[r]){case 6:var l=e.subarray(r+1,r+1+a),c=qo(r,t);n={nalUnitType:"sei_rbsp",size:a,data:l,escapedRBSP:$o(l),trackId:i},c?(n.pts=c.pts,n.dts=c.dts,s=c):(n.pts=s.pts,n.dts=s.dts),u.push(n)}return u}(i,function(e,t,i){var n=t,r=i.defaultSampleDuration||0,a=i.defaultSampleSize||0,s=i.trackId,o=[];return e.forEach(function(e){var t=Lo.parseTrun(e).samples;t.forEach(function(e){void 0===e.duration&&(e.duration=r),void 0===e.size&&(e.size=a),e.trackId=s,e.dts=n,void 0===e.compositionTimeOffset&&(e.compositionTimeOffset=0),e.pts=n+e.compositionTimeOffset,n+=e.duration}),o=o.concat(t)}),o}(l,u,a),s),h[s]||(h[s]=[]),h[s]=h[s].concat(t))}),h}function zo(e,t){var i=1;for(t<e&&(i=-1);4294967296<Math.abs(t-e);)e+=8589934592*i;return e}var $o=Uo,Go=Mo,Xo=function(){var t,a,s,o,u,i,n=!1;this.isInitialized=function(){return n},this.init=function(e){t=new Go,n=!0,i=!!e&&e.isPartial,t.on("data",function(e){e.startTime=e.startPts/o,e.endTime=e.endPts/o,u.captions.push(e),u.captionStreams[e.stream]=!0})},this.isNewInit=function(e,t){return!(e&&0===e.length||t&&"object"==typeof t&&0===Object.keys(t).length)&&(s!==e[0]||o!==t[s])},this.parse=function(e,t,i){var n;if(!this.isInitialized())return null;if(!t||!i)return null;if(this.isNewInit(t,i))s=t[0],o=i[s];else if(null===s||!o)return a.push(e),null;for(;0<a.length;){var r=a.shift();this.parse(r,t,i)}return null!==(n=function(e,t,i){return null===t?null:{seiNals:Wo(e,t)[t],timescale:i}}(e,s,o))&&n.seiNals?(this.pushNals(n.seiNals),this.flushStream(),u):null},this.pushNals=function(e){if(!this.isInitialized()||!e||0===e.length)return null;e.forEach(function(e){t.push(e)})},this.flushStream=function(){if(!this.isInitialized())return null;i?t.partialFlush():t.flush()},this.clearParsedCaptions=function(){u.captions=[],u.captionStreams={}},this.resetCaptionStream=function(){if(!this.isInitialized())return null;t.reset()},this.clearAllCaptions=function(){this.clearParsedCaptions(),this.resetCaptionStream()},this.reset=function(){a=[],o=s=null,u?this.clearParsedCaptions():u={captions:[],captionStreams:{}},this.resetCaptionStream()},this.reset()},Ko=27,Yo=15,Qo=21;new No;function Jo(e){var t=31&e[1];return t<<=8,t|=e[2]}function Zo(e){return!!(64&e[1])}function eu(e){var t=0;return 1<(48&e[3])>>>4&&(t+=e[4]+1),t}function tu(e){switch(e){case 5:return"slice_layer_without_partitioning_rbsp_idr";case 6:return"sei_rbsp";case 7:return"seq_parameter_set_rbsp";case 8:return"pic_parameter_set_rbsp";case 9:return"access_unit_delimiter_rbsp";default:return null}}function iu(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]}var nu,ru,au,su,ou={parseType:function(e,t){var i=Jo(e);return 0===i?"pat":i===t?"pmt":t?"pes":null},parsePat:function(e){var t=Zo(e),i=4+eu(e);return t&&(i+=e[i]+1),(31&e[i+10])<<8|e[i+11]},parsePmt:function(e){var t={},i=Zo(e),n=4+eu(e);if(i&&(n+=e[n]+1),1&e[n+5]){var r;r=3+((15&e[n+1])<<8|e[n+2])-4;for(var a=12+((15&e[n+10])<<8|e[n+11]);a<r;){var s=n+a;t[(31&e[s+1])<<8|e[s+2]]=e[s],a+=5+((15&e[s+3])<<8|e[s+4])}return t}},parsePayloadUnitStartIndicator:Zo,parsePesType:function(e,t){switch(t[Jo(e)]){case Ko:return"video";case Yo:return"audio";case Qo:return"timed-metadata";default:return null}},parsePesTime:function(e){if(!Zo(e))return null;var t=4+eu(e);if(t>=e.byteLength)return null;var i,n=null;return 192&(i=e[t+7])&&((n={}).pts=(14&e[t+9])<<27|(255&e[t+10])<<20|(254&e[t+11])<<12|(255&e[t+12])<<5|(254&e[t+13])>>>3,n.pts*=4,n.pts+=(6&e[t+13])>>>1,n.dts=n.pts,64&i&&(n.dts=(14&e[t+14])<<27|(255&e[t+15])<<20|(254&e[t+16])<<12|(255&e[t+17])<<5|(254&e[t+18])>>>3,n.dts*=4,n.dts+=(6&e[t+18])>>>1)),n},videoPacketContainsKeyFrame:function(e){for(var t=4+eu(e),i=e.subarray(t),n=0,r=0,a=!1;r<i.byteLength-3;r++)if(1===i[r+2]){n=r+5;break}for(;n<i.byteLength;)switch(i[n]){case 0:if(0!==i[n-1]){n+=2;break}if(0!==i[n-2]){n++;break}for(r+3!==n-2&&"slice_layer_without_partitioning_rbsp_idr"===tu(31&i[r+3])&&(a=!0);1!==i[++n]&&n<i.length;);r=n-2,n+=3;break;case 1:if(0!==i[n-1]||0!==i[n-2]){n+=3;break}"slice_layer_without_partitioning_rbsp_idr"===tu(31&i[r+3])&&(a=!0),r=n-2,n+=3;break;default:n+=3}return i=i.subarray(r),n-=r,r=0,i&&3<i.byteLength&&"slice_layer_without_partitioning_rbsp_idr"===tu(31&i[r+3])&&(a=!0),a}},uu=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],lu={isLikelyAacData:function(e){return e[0]==="I".charCodeAt(0)&&e[1]==="D".charCodeAt(0)&&e[2]==="3".charCodeAt(0)},parseId3TagSize:function(e,t){var i=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9];return(16&e[t+5])>>4?20+i:10+i},parseAdtsSize:function(e,t){var i=(224&e[t+5])>>5,n=e[t+4]<<3;return 6144&e[t+3]|n|i},parseType:function(e,t){return e[t]==="I".charCodeAt(0)&&e[t+1]==="D".charCodeAt(0)&&e[t+2]==="3".charCodeAt(0)?"timed-metadata":!0&e[t]&&240==(240&e[t+1])?"audio":null},parseSampleRate:function(e){for(var t=0;t+5<e.length;){if(255===e[t]&&240==(246&e[t+1]))return uu[(60&e[t+2])>>>2];t++}return null},parseAacTimestamp:function(e){var t,i,n;t=10,64&e[5]&&(t+=4,t+=iu(e.subarray(10,14)));do{if((i=iu(e.subarray(t+4,t+8)))<1)return null;if("PRIV"===String.fromCharCode(e[t],e[t+1],e[t+2],e[t+3])){n=e.subarray(t+10,t+i+10);for(var r=0;r<n.byteLength;r++)if(0===n[r]){if("com.apple.streaming.transportStreamTimestamp"!==unescape(function(e,t,i){var n,r="";for(n=t;n<i;n++)r+="%"+("00"+e[n].toString(16)).slice(-2);return r}(n,0,r)))break;var a=n.subarray(r+1),s=(1&a[3])<<30|a[4]<<22|a[5]<<14|a[6]<<6|a[7]>>>2;return s*=4,s+=3&a[7]}}t+=10,t+=i}while(t<e.byteLength);return null}},cu=9e4,hu=(nu=function(e){return 9e4*e},ru=function(e,t){return e*t},au=function(e){return e/9e4},su=function(e,t){return e/t},zo),du={};du.ts=ou,du.aac=lu;function pu(e,t,i){for(var n,r,a,s,o=0,u=yu,l=!1;u<=e.byteLength;)if(71!==e[o]||71!==e[u]&&u!==e.byteLength)o++,u++;else{switch(n=e.subarray(o,u),du.ts.parseType(n,t.pid)){case"pes":r=du.ts.parsePesType(n,t.table),a=du.ts.parsePayloadUnitStartIndicator(n),"audio"===r&&a&&(s=du.ts.parsePesTime(n))&&(s.type="audio",i.audio.push(s),l=!0)}if(l)break;o+=yu,u+=yu}for(o=(u=e.byteLength)-yu,l=!1;0<=o;)if(71!==e[o]||71!==e[u]&&u!==e.byteLength)o--,u--;else{switch(n=e.subarray(o,u),du.ts.parseType(n,t.pid)){case"pes":r=du.ts.parsePesType(n,t.table),a=du.ts.parsePayloadUnitStartIndicator(n),"audio"===r&&a&&(s=du.ts.parsePesTime(n))&&(s.type="audio",i.audio.push(s),l=!0)}if(l)break;o-=yu,u-=yu}}function fu(e,t,i){for(var n,r,a,s,o,u,l,c=0,h=yu,d=!1,p={data:[],size:0};h<e.byteLength;)if(71!==e[c]||71!==e[h])c++,h++;else{switch(n=e.subarray(c,h),du.ts.parseType(n,t.pid)){case"pes":if(r=du.ts.parsePesType(n,t.table),a=du.ts.parsePayloadUnitStartIndicator(n),"video"===r&&(a&&!d&&(s=du.ts.parsePesTime(n))&&(s.type="video",i.video.push(s),d=!0),!i.firstKeyFrame)){if(a&&0!==p.size){for(o=new Uint8Array(p.size),u=0;p.data.length;)l=p.data.shift(),o.set(l,u),u+=l.byteLength;if(du.ts.videoPacketContainsKeyFrame(o)){var f=du.ts.parsePesTime(o);f&&(i.firstKeyFrame=f,i.firstKeyFrame.type="video")}p.size=0}p.data.push(n),p.size+=n.byteLength}}if(d&&i.firstKeyFrame)break;c+=yu,h+=yu}for(c=(h=e.byteLength)-yu,d=!1;0<=c;)if(71!==e[c]||71!==e[h])c--,h--;else{switch(n=e.subarray(c,h),du.ts.parseType(n,t.pid)){case"pes":r=du.ts.parsePesType(n,t.table),a=du.ts.parsePayloadUnitStartIndicator(n),"video"===r&&a&&(s=du.ts.parsePesTime(n))&&(s.type="video",i.video.push(s),d=!0)}if(d)break;c-=yu,h-=yu}}function mu(e){var t={pid:null,table:null},i={};for(var n in function(e,t){for(var i,n=0,r=yu;r<e.byteLength;)if(71!==e[n]||71!==e[r])n++,r++;else{switch(i=e.subarray(n,r),du.ts.parseType(i,t.pid)){case"pat":t.pid||(t.pid=du.ts.parsePat(i));break;case"pmt":t.table||(t.table=du.ts.parsePmt(i))}if(t.pid&&t.table)return;n+=yu,r+=yu}}(e,t),t.table){if(t.table.hasOwnProperty(n))switch(t.table[n]){case Ko:i.video=[],fu(e,t,i),0===i.video.length&&delete i.video;break;case Yo:i.audio=[],pu(e,t,i),0===i.audio.length&&delete i.audio}}return i}var gu=cu,yu=188,vu=function(e,t){var i;return(i=du.aac.isLikelyAacData(e)?function(e){for(var t,i=!1,n=0,r=null,a=null,s=0,o=0;3<=e.length-o;){switch(du.aac.parseType(e,o)){case"timed-metadata":if(e.length-o<10){i=!0;break}if((s=du.aac.parseId3TagSize(e,o))>e.length){i=!0;break}null===a&&(t=e.subarray(o,o+s),a=du.aac.parseAacTimestamp(t)),o+=s;break;case"audio":if(e.length-o<7){i=!0;break}if((s=du.aac.parseAdtsSize(e,o))>e.length){i=!0;break}null===r&&(t=e.subarray(o,o+s),r=du.aac.parseSampleRate(t)),n++,o+=s;break;default:o++}if(i)return null}if(null===r||null===a)return null;var u=gu/r;return{audio:[{type:"audio",dts:a,pts:a},{type:"audio",dts:a+1024*n*u,pts:a+1024*n*u}]}}(e):mu(e))&&(i.audio||i.video)?(function(e,t){if(e.audio&&e.audio.length){var i=t;"undefined"==typeof i&&(i=e.audio[0].dts),e.audio.forEach(function(e){e.dts=hu(e.dts,i),e.pts=hu(e.pts,i),e.dtsTime=e.dts/gu,e.ptsTime=e.pts/gu})}if(e.video&&e.video.length){var n=t;if("undefined"==typeof n&&(n=e.video[0].dts),e.video.forEach(function(e){e.dts=hu(e.dts,n),e.pts=hu(e.pts,n),e.dtsTime=e.dts/gu,e.ptsTime=e.pts/gu}),e.firstKeyFrame){var r=e.firstKeyFrame;r.dts=hu(r.dts,n),r.pts=hu(r.pts,n),r.dtsTime=r.dts/gu,r.ptsTime=r.dts/gu}}}(i,t),i):null};function _u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var bu=function(e,t,i){return t&&Tu(e.prototype,t),i&&Tu(e,i),e};function Tu(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var Su=null,ku=(Cu.prototype.decrypt=function(e,t,i,n,r,a){var s=this._key[1],o=e^s[0],u=n^s[1],l=i^s[2],c=t^s[3],h=void 0,d=void 0,p=void 0,f=s.length/4-2,m=void 0,g=4,y=this._tables[1],v=y[0],_=y[1],b=y[2],T=y[3],S=y[4];for(m=0;m<f;m++)h=v[o>>>24]^_[u>>16&255]^b[l>>8&255]^T[255&c]^s[g],d=v[u>>>24]^_[l>>16&255]^b[c>>8&255]^T[255&o]^s[g+1],p=v[l>>>24]^_[c>>16&255]^b[o>>8&255]^T[255&u]^s[g+2],c=v[c>>>24]^_[o>>16&255]^b[u>>8&255]^T[255&l]^s[g+3],g+=4,o=h,u=d,l=p;for(m=0;m<4;m++)r[(3&-m)+a]=S[o>>>24]<<24^S[u>>16&255]<<16^S[l>>8&255]<<8^S[255&c]^s[g++],h=o,o=u,u=l,l=c,c=h},Cu);function Cu(e){_u(this,Cu),Su=Su||function(){var e=[[[],[],[],[],[]],[[],[],[],[],[]]],t=e[0],i=e[1],n=t[4],r=i[4],a=void 0,s=void 0,o=void 0,u=[],l=[],c=void 0,h=void 0,d=void 0,p=void 0,f=void 0;for(a=0;a<256;a++)l[(u[a]=a<<1^283*(a>>7))^a]=a;for(s=o=0;!n[s];s^=c||1,o=l[o]||1)for(d=(d=o^o<<1^o<<2^o<<3^o<<4)>>8^255&d^99,f=16843009*u[h=u[c=u[r[n[s]=d]=s]]]^65537*h^257*c^16843008*s,p=257*u[d]^16843008*d,a=0;a<4;a++)t[a][s]=p=p<<24^p>>>8,i[a][d]=f=f<<24^f>>>8;for(a=0;a<5;a++)t[a]=t[a].slice(0),i[a]=i[a].slice(0);return e}(),this._tables=[[Su[0][0].slice(),Su[0][1].slice(),Su[0][2].slice(),Su[0][3].slice(),Su[0][4].slice()],[Su[1][0].slice(),Su[1][1].slice(),Su[1][2].slice(),Su[1][3].slice(),Su[1][4].slice()]];var t=void 0,i=void 0,n=void 0,r=void 0,a=void 0,s=this._tables[0][4],o=this._tables[1],u=e.length,l=1;if(4!==u&&6!==u&&8!==u)throw new Error("Invalid aes key size");for(r=e.slice(0),a=[],this._key=[r,a],t=u;t<4*u+28;t++)n=r[t-1],(t%u==0||8===u&&t%u==4)&&(n=s[n>>>24]<<24^s[n>>16&255]<<16^s[n>>8&255]<<8^s[255&n],t%u==0&&(n=n<<8^n>>>24^l<<24,l=l<<1^283*(l>>7))),r[t]=r[t-u]^n;for(i=0;t;i++,t--)n=r[3&i?t:t-4],a[i]=t<=4||i<4?n:o[0][s[n>>>24]]^o[1][s[n>>16&255]]^o[2][s[n>>8&255]]^o[3][s[255&n]]}var Eu=(wu.prototype.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},wu.prototype.off=function(e,t){if(!this.listeners[e])return!1;var i=this.listeners[e].indexOf(t);return this.listeners[e].splice(i,1),-1<i},wu.prototype.trigger=function(e,t){var i=this.listeners[e];if(i)if(2===arguments.length)for(var n=i.length,r=0;r<n;++r)i[r].call(this,t);else for(var a=Array.prototype.slice.call(arguments,1),s=i.length,o=0;o<s;++o)i[o].apply(this,a)},wu.prototype.dispose=function(){this.listeners={}},wu.prototype.pipe=function(t){this.on("data",function(e){t.push(e)})},wu);function wu(){_u(this,wu),this.listeners={}}var Au,Pu=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(Iu,Au=Eu),Iu.prototype.processJob_=function(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null},Iu.prototype.push=function(e){this.jobs.push(e),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))},Iu);function Iu(){_u(this,Iu);var e=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,Au.call(this,Eu));return e.jobs=[],e.delay=1,e.timeout_=null,e}function Lu(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24}function Ou(e,t,i){var n=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),r=new ku(Array.prototype.slice.call(t)),a=new Uint8Array(e.byteLength),s=new Int32Array(a.buffer),o=void 0,u=void 0,l=void 0,c=void 0,h=void 0,d=void 0,p=void 0,f=void 0,m=void 0;for(o=i[0],u=i[1],l=i[2],c=i[3],m=0;m<n.length;m+=4)h=Lu(n[m]),d=Lu(n[m+1]),p=Lu(n[m+2]),f=Lu(n[m+3]),r.decrypt(h,d,p,f,s,m),s[m]=Lu(s[m]^o),s[m+1]=Lu(s[m+1]^u),s[m+2]=Lu(s[m+2]^l),s[m+3]=Lu(s[m+3]^c),o=h,u=d,l=p,c=f;return a}var xu=(Du.prototype.decryptChunk_=function(t,i,n,r){return function(){var e=Ou(t,i,n);r.set(e,t.byteOffset)}},bu(Du,null,[{key:"STEP",get:function(){return 32e3}}]),Du);function Du(e,t,i,n){_u(this,Du);var r=Du.STEP,a=new Int32Array(e.buffer),s=new Uint8Array(e.byteLength),o=0;for(this.asyncStream_=new Pu,this.asyncStream_.push(this.decryptChunk_(a.subarray(o,o+r),t,i,s)),o=r;o<a.length;o+=r)i=new Uint32Array([Lu(a[o-4]),Lu(a[o-3]),Lu(a[o-2]),Lu(a[o-1])]),this.asyncStream_.push(this.decryptChunk_(a.subarray(o,o+r),t,i,s));this.asyncStream_.push(function(){n(null,function(e){return e.subarray(0,e.byteLength-e[e.byteLength-1])}(s))})}function Uu(e,t){return/^[a-z]+:/i.test(t)?t:(/\/\//i.test(e)||(e=ys.buildAbsoluteURL(v.location.href,e)),ys.buildAbsoluteURL(e,t))}function Ru(e,t,i){return e&&i.responseURL&&t!==i.responseURL?i.responseURL:t}function Mu(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}
/**
   * @videojs/http-streaming
   * @version 1.10.6
   * @copyright 2019 Brightcove, Inc
   * @license Apache-2.0
   */
var Nu=function(e,t,i){return t&&Bu(e.prototype,t),i&&Bu(e,i),e};function Bu(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ju(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function Fu(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function Hu(r,a){["AUDIO","SUBTITLES"].forEach(function(e){for(var t in r.mediaGroups[e])for(var i in r.mediaGroups[e][t]){var n=r.mediaGroups[e][t][i];a(n,e,t,i)}})}function Vu(e,t){var i=Gu(e,{}),n=i.playlists[t.uri];if(!n)return null;if(n.segments&&t.segments&&n.segments.length===t.segments.length&&n.endList===t.endList&&n.mediaSequence===t.mediaSequence)return null;var r=Gu(n,t);n.segments&&(r.segments=function(e,t,i){var n=t.slice();i=i||0;for(var r=Math.min(e.length,t.length+i),a=i;a<r;a++)n[a-i]=Gu(e[a],n[a-i]);return n}(n.segments,t.segments,t.mediaSequence-n.mediaSequence)),r.segments.forEach(function(e){!function(e,t){e.resolvedUri||(e.resolvedUri=Uu(t,e.uri)),e.key&&!e.key.resolvedUri&&(e.key.resolvedUri=Uu(t,e.key.uri)),e.map&&!e.map.resolvedUri&&(e.map.resolvedUri=Uu(t,e.map.uri))}(e,r.resolvedUri)});for(var a=0;a<i.playlists.length;a++)i.playlists[a].uri===t.uri&&(i.playlists[a]=r);return i.playlists[t.uri]=r,i}function qu(e){for(var t=e.playlists.length;t--;){var i=e.playlists[t];(e.playlists[i.uri]=i).resolvedUri=Uu(e.uri,i.uri),i.id=t,i.attributes||(i.attributes={},Xu.warn("Invalid playlist STREAM-INF detected. Missing BANDWIDTH attribute."))}}function Wu(t){Hu(t,function(e){e.uri&&(e.resolvedUri=Uu(t.uri,e.uri))})}function zu(e,t){var i=e.segments[e.segments.length-1];return t&&i&&i.duration?1e3*i.duration:500*(e.targetDuration||10)}var $u=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var i=[],n=!0,r=!1,a=void 0;try{for(var s,o=e[Symbol.iterator]();!(n=(s=o.next()).done)&&(i.push(s.value),!t||i.length!==t);n=!0);}catch(e){r=!0,a=e}finally{try{!n&&o.return&&o.return()}finally{if(r)throw a}}return i}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")},Gu=fs.mergeOptions,Xu=fs.log,Ku=(ju(Yu,fs.EventTarget),Nu(Yu,[{key:"playlistRequestError",value:function(e,t,i){this.request=null,i&&(this.state=i),this.error={playlist:this.master.playlists[t],status:e.status,message:"HLS playlist request error at URL: "+t+".",responseText:e.responseText,code:500<=e.status?4:2},this.trigger("error")}},{key:"haveMetadata",value:function(e,t){var i=this;this.request=null,this.state="HAVE_METADATA";var n=new Ps;this.customTagParsers.forEach(function(e){return n.addParser(e)}),this.customTagMappers.forEach(function(e){return n.addTagMapper(e)}),n.push(e.responseText),n.end(),n.manifest.uri=t,n.manifest.attributes=n.manifest.attributes||{};var r=Vu(this.master,n.manifest);this.targetDuration=n.manifest.targetDuration,r?(this.master=r,this.media_=this.master.playlists[n.manifest.uri]):this.trigger("playlistunchanged"),this.media().endList||(v.clearTimeout(this.mediaUpdateTimeout),this.mediaUpdateTimeout=v.setTimeout(function(){i.trigger("mediaupdatetimeout")},zu(this.media(),!!r))),this.trigger("loadedplaylist")}},{key:"dispose",value:function(){this.stopRequest(),v.clearTimeout(this.mediaUpdateTimeout),v.clearTimeout(this.finalRenditionTimeout)}},{key:"stopRequest",value:function(){if(this.request){var e=this.request;this.request=null,e.onreadystatechange=null,e.abort()}}},{key:"media",value:function(i,e){var n=this;if(!i)return this.media_;if("HAVE_NOTHING"===this.state)throw new Error("Cannot switch media playlist from "+this.state);if("string"==typeof i){if(!this.master.playlists[i])throw new Error("Unknown playlist URI: "+i);i=this.master.playlists[i]}if(v.clearTimeout(this.finalRenditionTimeout),e){var t=i.targetDuration/2*1e3||5e3;this.finalRenditionTimeout=v.setTimeout(this.media.bind(this,i,!1),t)}else{var r=this.state,a=!this.media_||i.uri!==this.media_.uri;if(this.master.playlists[i.uri].endList)return this.request&&(this.request.onreadystatechange=null,this.request.abort(),this.request=null),this.state="HAVE_METADATA",this.media_=i,void(a&&(this.trigger("mediachanging"),this.trigger("mediachange")));if(a){if(this.state="SWITCHING_MEDIA",this.request){if(i.resolvedUri===this.request.url)return;this.request.onreadystatechange=null,this.request.abort(),this.request=null}this.media_&&this.trigger("mediachanging"),this.request=this.hls_.xhr({uri:i.resolvedUri,withCredentials:this.withCredentials},function(e,t){if(n.request){if(i.resolvedUri=Ru(n.handleManifestRedirects,i.resolvedUri,t),e)return n.playlistRequestError(n.request,i.uri,r);n.haveMetadata(t,i.uri),"HAVE_MASTER"===r?n.trigger("loadedmetadata"):n.trigger("mediachange")}})}}}},{key:"pause",value:function(){this.stopRequest(),v.clearTimeout(this.mediaUpdateTimeout),"HAVE_NOTHING"===this.state&&(this.started=!1),"SWITCHING_MEDIA"===this.state?this.media_?this.state="HAVE_METADATA":this.state="HAVE_MASTER":"HAVE_CURRENT_METADATA"===this.state&&(this.state="HAVE_METADATA")}},{key:"load",value:function(e){var t=this;v.clearTimeout(this.mediaUpdateTimeout);var i=this.media();if(e){var n=i?i.targetDuration/2*1e3:5e3;this.mediaUpdateTimeout=v.setTimeout(function(){return t.load()},n)}else this.started?i&&!i.endList?this.trigger("mediaupdatetimeout"):this.trigger("loadedplaylist"):this.start()}},{key:"start",value:function(){var n=this;this.started=!0,this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(e,t){if(n.request){if(n.request=null,e)return n.error={status:t.status,message:"HLS playlist request error at URL: "+n.srcUrl+".",responseText:t.responseText,code:2},"HAVE_NOTHING"===n.state&&(n.started=!1),n.trigger("error");var i=new Ps;return n.customTagParsers.forEach(function(e){return i.addParser(e)}),n.customTagMappers.forEach(function(e){return i.addTagMapper(e)}),i.push(t.responseText),i.end(),n.state="HAVE_MASTER",n.srcUrl=Ru(n.handleManifestRedirects,n.srcUrl,t),i.manifest.uri=n.srcUrl,i.manifest.playlists?(n.master=i.manifest,qu(n.master),Wu(n.master),n.trigger("loadedplaylist"),void(n.request||n.media(i.manifest.playlists[0]))):(n.master={mediaGroups:{AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},uri:v.location.href,playlists:[{uri:n.srcUrl,id:0,resolvedUri:n.srcUrl,attributes:{}}]},n.master.playlists[n.srcUrl]=n.master.playlists[0],n.haveMetadata(t,n.srcUrl),n.trigger("loadedmetadata"))}})}}]),Yu);function Yu(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};Mu(this,Yu);var n=Fu(this,(Yu.__proto__||Object.getPrototypeOf(Yu)).call(this)),r=i.withCredentials,a=void 0!==r&&r,s=i.handleManifestRedirects,o=void 0!==s&&s;n.srcUrl=e,n.hls_=t,n.withCredentials=a,n.handleManifestRedirects=o;var u=t.options_;if(n.customTagParsers=u&&u.customTagParsers||[],n.customTagMappers=u&&u.customTagMappers||[],!n.srcUrl)throw new Error("A non-empty playlist URL is required");return n.state="HAVE_NOTHING",n.on("mediaupdatetimeout",function(){"HAVE_METADATA"===n.state&&(n.state="HAVE_CURRENT_METADATA",n.request=n.hls_.xhr({uri:Uu(n.master.uri,n.media().uri),withCredentials:n.withCredentials},function(e,t){if(n.request)return e?n.playlistRequestError(n.request,n.media().uri,"HAVE_METADATA"):void n.haveMetadata(n.request,n.media().uri)}))}),n}function Qu(e,t,i){var n,r;return"undefined"==typeof t&&(t=e.mediaSequence+e.segments.length),t<e.mediaSequence?0:(n=function(e,t){var i=0,n=t-e.mediaSequence,r=e.segments[n];if(r){if("undefined"!=typeof r.start)return{result:r.start,precise:!0};if("undefined"!=typeof r.end)return{result:r.end-r.duration,precise:!0}}for(;n--;){if("undefined"!=typeof(r=e.segments[n]).end)return{result:i+r.end,precise:!0};if(i+=r.duration,"undefined"!=typeof r.start)return{result:i+r.start,precise:!0}}return{result:i,precise:!1}}(e,t)).precise?n.result:(r=function(e,t){for(var i=0,n=void 0,r=t-e.mediaSequence;r<e.segments.length;r++){if("undefined"!=typeof(n=e.segments[r]).start)return{result:n.start-i,precise:!0};if(i+=n.duration,"undefined"!=typeof n.end)return{result:n.end-i,precise:!0}}return{result:-1,precise:!1}}(e,t)).precise?r.result:n.result+i}function Ju(e,t,i){if(!e)return 0;if("number"!=typeof i&&(i=0),"undefined"==typeof t){if(e.totalDuration)return e.totalDuration;if(!e.endList)return v.Infinity}return Qu(e,t,i)}function Zu(e,t,i){var n=0;if(i<t){var r=[i,t];t=r[0],i=r[1]}if(t<0){for(var a=t;a<Math.min(0,i);a++)n+=e.targetDuration;t=0}for(var s=t;s<i;s++)n+=e.segments[s].duration;return n}function el(e){if(!e.segments.length)return 0;for(var t=e.segments.length-1,i=e.segments[t].duration||e.targetDuration,n=i+2*e.targetDuration;t--&&!(n<=(i+=e.segments[t].duration)););return Math.max(0,t)}function tl(e,t,i){if(!e||!e.segments)return null;if(e.endList)return Ju(e);if(null===t)return null;t=t||0;var n=i?el(e):e.segments.length;return Qu(e,e.mediaSequence+n,t)}function il(e){return e-Math.floor(e)==0}function nl(e,t){if(il(t))return t+.1*e;for(var i=t.toString().split(".")[1].length,n=1;n<=i;n++){var r=Math.pow(10,n),a=t*r;if(il(a)||n===i)return(a+e)/r}}function rl(e){return e.excludeUntil&&e.excludeUntil>Date.now()}function al(e){return e.excludeUntil&&e.excludeUntil===1/0}function sl(e){var t=rl(e);return!e.disabled&&!t}function ol(e,t){return t.attributes&&t.attributes[e]}function ul(e,t){if(1===e.playlists.length)return!0;var i=t.attributes.BANDWIDTH||Number.MAX_VALUE;return 0===e.playlists.filter(function(e){return!!sl(e)&&(e.attributes.BANDWIDTH||0)<i}).length}function ll(){return function e(t,n){t=xl({timeout:45e3},t);var i=e.beforeRequest||fs.Hls.xhr.beforeRequest;if(i&&"function"==typeof i){var r=i(t);r&&(t=r)}var a=Ol(t,function(e,t){var i=a.response;!e&&i&&(a.responseTime=Date.now(),a.roundTripTime=a.responseTime-a.requestTime,a.bytesReceived=i.byteLength||i.length,a.bandwidth||(a.bandwidth=Math.floor(a.bytesReceived/a.roundTripTime*8*1e3))),t.headers&&(a.responseHeaders=t.headers),e&&"ETIMEDOUT"===e.code&&(a.timedout=!0),e||a.aborted||200===t.statusCode||206===t.statusCode||0===t.statusCode||(e=new Error("XHR Failed with a response of: "+(a&&(i||a.responseText)))),n(e,a)}),s=a.abort;return a.abort=function(){return a.aborted=!0,s.apply(a,arguments)},a.uri=t.uri,a.requestTime=Date.now(),a}}function cl(e){var t={};return e.byterange&&(t.Range=function(e){var t;return t=e.offset+e.length-1,"bytes="+e.offset+"-"+t}(e.byterange)),t}function hl(e,t){var i=e.toString(16);return"00".substring(0,2-i.length)+i+(t%2?" ":"")}function dl(e){return 32<=e&&e<126?String.fromCharCode(e):"."}function pl(i){var n={};return Object.keys(i).forEach(function(e){var t=i[e];ArrayBuffer.isView(t)?n[e]={bytes:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength}:n[e]=t}),n}function fl(e){var t=e.byterange||{length:1/0,offset:0};return[t.length,t.offset,e.resolvedUri].join(",")}function ml(e){return e.resolvedUri}function gl(e){for(var t=Array.prototype.slice.call(e),i="",n=0;n<t.length/16;n++)i+=t.slice(16*n,16*n+16).map(hl).join("")+" "+t.slice(16*n,16*n+16).map(dl).join("")+"\n";return i}function yl(e){var t=e.playlist,i=e.time,n=void 0===i?void 0:i,r=e.callback;if(!r)throw new Error("getProgramTime: callback must be provided");if(!t||void 0===n)return r({message:"getProgramTime: playlist and time must be provided"});var a=function(e,t){if(!t||!t.segments||0===t.segments.length)return null;for(var i=0,n=void 0,r=0;r<t.segments.length&&!(e<=(i=(n=t.segments[r]).videoTimingInfo?n.videoTimingInfo.transmuxedPresentationEnd:i+n.duration));r++);var a=t.segments[t.segments.length-1];if(a.videoTimingInfo&&a.videoTimingInfo.transmuxedPresentationEnd<e)return null;if(i<e){if(e>i+.25*a.duration)return null;n=a}return{segment:n,estimatedStart:n.videoTimingInfo?n.videoTimingInfo.transmuxedPresentationStart:i-n.duration,type:n.videoTimingInfo?"accurate":"estimate"}}(n,t);if(!a)return r({message:"valid programTime was not found"});if("estimate"===a.type)return r({message:"Accurate programTime could not be determined. Please seek to e.seekTime and try again",seekTime:a.estimatedStart});var s={mediaSeconds:n},o=function(e,t){if(!t.dateTimeObject)return null;var i=t.videoTimingInfo.transmuxerPrependedSeconds,n=e-(t.videoTimingInfo.transmuxedPresentationStart+i);return new Date(t.dateTimeObject.getTime()+1e3*n)}(n,a.segment);return o&&(s.programDateTime=o.toISOString()),r(null,s)}function vl(e){var t=e.programTime,i=e.playlist,n=e.retryCount,r=void 0===n?2:n,a=e.seekTo,s=e.pauseAfterSeek,o=void 0===s||s,u=e.tech,l=e.callback;if(!l)throw new Error("seekToProgramTime: callback must be provided");if("undefined"==typeof t||!i||!a)return l({message:"seekToProgramTime: programTime, seekTo and playlist must be provided"});if(!i.endList&&!u.hasStarted_)return l({message:"player must be playing a live stream to start buffering"});if(!function(e){if(!e.segments||0===e.segments.length)return!1;for(var t=0;t<e.segments.length;t++){if(!e.segments[t].dateTimeObject)return!1}return!0}(i))return l({message:"programDateTime tags must be provided in the manifest "+i.resolvedUri});var c=function(e,t){var i=void 0;try{i=new Date(e)}catch(e){return null}if(!t||!t.segments||0===t.segments.length)return null;var n=t.segments[0];if(i<n.dateTimeObject)return null;for(var r=0;r<t.segments.length-1;r++){if(n=t.segments[r],i<t.segments[r+1].dateTimeObject)break}var a=t.segments[t.segments.length-1],s=a.dateTimeObject,o=a.videoTimingInfo?function(e){return e.transmuxedPresentationEnd-e.transmuxedPresentationStart-e.transmuxerPrependedSeconds}(a.videoTimingInfo):a.duration+.25*a.duration;return new Date(s.getTime()+1e3*o)<i?null:(s<i&&(n=a),{segment:n,estimatedStart:n.videoTimingInfo?n.videoTimingInfo.transmuxedPresentationStart:Ll.duration(t,t.mediaSequence+t.segments.indexOf(n)),type:n.videoTimingInfo?"accurate":"estimate"})}(t,i);if(!c)return l({message:t+" was not found in the stream"});var h=c.segment,d=function(e,t){var i=void 0,n=void 0;try{i=new Date(e),n=new Date(t)}catch(e){}var r=i.getTime();return(n.getTime()-r)/1e3}(h.dateTimeObject,t);if("estimate"===c.type)return 0===r?l({message:t+" is not buffered yet. Try again"}):(a(c.estimatedStart+d),void u.one("seeked",function(){vl({programTime:t,playlist:i,retryCount:r-1,seekTo:a,pauseAfterSeek:o,tech:u,callback:l})}));var p=h.start+d;u.one("seeked",function(){return l(null,u.currentTime())}),o&&u.pause(),a(p)}function _l(e,t){var i=[],n=void 0;if(e&&e.length)for(n=0;n<e.length;n++)t(e.start(n),e.end(n))&&i.push([e.start(n),e.end(n)]);return fs.createTimeRanges(i)}function bl(e,i){return _l(e,function(e,t){return e-.1<=i&&i<=t+.1})}function Tl(e,t){return _l(e,function(e){return t<=e-1/30})}function Sl(e){var t=[];if(!e||!e.length)return"";for(var i=0;i<e.length;i++)t.push(e.start(i)+" => "+e.end(i));return t.join(", ")}function kl(e){for(var t=[],i=0;i<e.length;i++)t.push({start:e.start(i),end:e.end(i)});return t}function Cl(e,t,i){var n=void 0,r=void 0;if(i&&i.cues)for(n=i.cues.length;n--;)(r=i.cues[n]).startTime<=t&&r.endTime>=e&&i.removeCue(r)}function El(e){return isNaN(e)||Math.abs(e)===1/0?Number.MAX_VALUE:e}function wl(e,t,i){var n=v.WebKitDataCue||v.VTTCue;if(t&&t.forEach(function(e){var t=e.stream;this.inbandTextTracks_[t].addCue(new n(e.startTime+this.timestampOffset,e.endTime+this.timestampOffset,e.text))},e),i){var r=El(e.mediaSource_.duration);if(i.forEach(function(e){var i=e.cueTime+this.timestampOffset;!("number"!=typeof i||v.isNaN(i)||i<0)&&i<1/0&&e.frames.forEach(function(e){var t=new n(i,i,e.value||e.url||e.data||"");t.frame=e,t.value=e,function(e){Object.defineProperties(e.frame,{id:{get:function(){return fs.log.warn("cue.frame.id is deprecated. Use cue.value.key instead."),e.value.key}},value:{get:function(){return fs.log.warn("cue.frame.value is deprecated. Use cue.value.data instead."),e.value.data}},privateData:{get:function(){return fs.log.warn("cue.frame.privateData is deprecated. Use cue.value.data instead."),e.value.data}}})}(t),this.metadataTrack_.addCue(t)},this)},e),e.metadataTrack_&&e.metadataTrack_.cues&&e.metadataTrack_.cues.length){for(var a=e.metadataTrack_.cues,s=[],o=0;o<a.length;o++)a[o]&&s.push(a[o]);var u=s.reduce(function(e,t){var i=e[t.startTime]||[];return i.push(t),e[t.startTime]=i,e},{}),l=Object.keys(u).sort(function(e,t){return Number(e)-Number(t)});l.forEach(function(e,t){var i=u[e],n=Number(l[t+1])||r;i.forEach(function(e){e.endTime=n})})}}}var Al=fs.createTimeRange,Pl=nl.bind(null,1),Il=nl.bind(null,-1),Ll={duration:Ju,seekable:function(e,t){var i=t||0,n=tl(e,t,!0);return null===n?Al():Al(i,n)},safeLiveIndex:el,getMediaInfoForTime:function(e,t,i,n){var r=void 0,a=void 0,s=e.segments.length,o=t-n;if(o<0){if(0<i)for(r=i-1;0<=r;r--)if(a=e.segments[r],0<(o+=Il(a.duration)))return{mediaIndex:r,startTime:n-Zu(e,i,r)};return{mediaIndex:0,startTime:t}}if(i<0){for(r=i;r<0;r++)if((o-=e.targetDuration)<0)return{mediaIndex:0,startTime:t};i=0}for(r=i;r<s;r++)if(a=e.segments[r],(o-=Pl(a.duration))<0)return{mediaIndex:r,startTime:n+Zu(e,i,r)};return{mediaIndex:s-1,startTime:t}},isEnabled:sl,isDisabled:function(e){return e.disabled},isBlacklisted:rl,isIncompatible:al,playlistEnd:tl,isAes:function(e){for(var t=0;t<e.segments.length;t++)if(e.segments[t].key)return!0;return!1},isFmp4:function(e){for(var t=0;t<e.segments.length;t++)if(e.segments[t].map)return!0;return!1},hasAttribute:ol,estimateSegmentRequestTime:function(e,t,i,n){var r=3<arguments.length&&void 0!==n?n:0;return ol("BANDWIDTH",i)?(e*i.attributes.BANDWIDTH-8*r)/t:NaN},isLowestEnabledRendition:ul},Ol=fs.xhr,xl=fs.mergeOptions,Dl=Object.freeze({createTransferableMessage:pl,initSegmentId:fl,segmentKeyId:ml,hexDump:gl,tagDump:function(e){var t=e.bytes;return gl(t)},textRanges:function(e){var t,i,n="",r=void 0;for(r=0;r<e.length;r++)n+=(i=r,(t=e).start(i)+"-"+t.end(i)+" ");return n}}),Ul="undefined"!=typeof window?window:{},Rl="undefined"==typeof Symbol?"__target":Symbol(),Ml="application/javascript",Nl=Ul.BlobBuilder||Ul.WebKitBlobBuilder||Ul.MozBlobBuilder||Ul.MSBlobBuilder,Bl=Ul.URL||Ul.webkitURL||Bl&&Bl.msURL,jl=Ul.Worker;function Fl(r,a){return function(e){var t=this;if(!a)return new jl(r);if(jl&&!e){var i=Wl(a.toString().replace(/^function.+?{/,"").slice(0,-1));return this[Rl]=new jl(i),function(e,t){if(!e||!t)return;var i=e.terminate;e.objURL=t,e.terminate=function(){e.objURL&&Bl.revokeObjectURL(e.objURL),i.call(e)}}(this[Rl],i),this[Rl]}var n={postMessage:function(e){t.onmessage&&setTimeout(function(){t.onmessage({data:e,target:n})})}};a.call(n),this.postMessage=function(e){setTimeout(function(){n.onmessage({data:e,target:t})})},this.isThisThread=!0}}if(jl){var Hl,Vl=Wl("self.onmessage = function () {}"),ql=new Uint8Array(1);try{(Hl=new jl(Vl)).postMessage(ql,[ql.buffer])}catch(e){jl=null}finally{Bl.revokeObjectURL(Vl),Hl&&Hl.terminate()}}function Wl(t){try{return Bl.createObjectURL(new Blob([t],{type:Ml}))}catch(e){var i=new Nl;return i.append(t),Bl.createObjectURL(i.getBlob(type))}}function zl(e){return e.map(function(e){return e.replace(/avc1\.(\d+)\.(\d+)/i,function(e,t,i){return"avc1."+("00"+Number(t).toString(16)).slice(-2)+"00"+("00"+Number(i).toString(16)).slice(-2)})})}function $l(e){var t,i=0<arguments.length&&void 0!==e?e:"",n={codecCount:0};return n.codecCount=i.split(",").length,n.codecCount=n.codecCount||2,(t=/(^|\s|,)+(avc[13])([^ ,]*)/i.exec(i))&&(n.videoCodec=t[2],n.videoObjectTypeIndicator=t[3]),n.audioProfile=/(^|\s|,)+mp4a.[0-9A-Fa-f]+\.([0-9A-Fa-f]+)/i.exec(i),n.audioProfile=n.audioProfile&&n.audioProfile[2],n}function Gl(e,t,i){return e+"/"+t+'; codecs="'+i.filter(function(e){return!!e}).join(", ")+'"'}function Xl(e,t){var i=function(e){return e.segments&&e.segments.length&&e.segments[0].map?"mp4":"mp2t"}(t),n=function(e){var t=e.attributes||{};return t.CODECS?$l(t.CODECS):Jl}(t),r=t.attributes||{},a=!0,s=!1;if(!t)return[];if(e.mediaGroups.AUDIO&&r.AUDIO){var o=e.mediaGroups.AUDIO[r.AUDIO];if(o)for(var u in a=!(s=!0),o)if(!o[u].uri&&!o[u].playlists){a=!0;break}}s&&!n.audioProfile&&(a||(n.audioProfile=function(e,t){if(!e.mediaGroups.AUDIO||!t)return null;var i=e.mediaGroups.AUDIO[t];if(!i)return null;for(var n in i){var r=i[n];if(r.default&&r.playlists)return $l(r.playlists[0].attributes.CODECS).audioProfile}return null}(e,r.AUDIO)),n.audioProfile||(fs.log.warn("Multiple audio tracks present but no audio codec string is specified. Attempting to use the default audio codec (mp4a.40.2)"),n.audioProfile=Jl.audioProfile));var l={};n.videoCodec&&(l.video=""+n.videoCodec+n.videoObjectTypeIndicator),n.audioProfile&&(l.audio="mp4a.40."+n.audioProfile);var c=Gl("audio",i,[l.audio]),h=Gl("video",i,[l.video]),d=Gl("video",i,[l.video,l.audio]);return s?!a&&l.video?[h,c]:a||l.video?[d,c]:[c,c]:l.video?[d]:[c]}function Kl(e){return/mp4a\.\d+.\d+/i.test(e)}function Yl(e){return/avc1\.[\da-f]+/i.test(e)}var Ql=new Fl("./transmuxer-worker.worker.js",function(e,t){var kt=this;!function(){function e(){this.init=function(){var a={};this.on=function(e,t){a[e]||(a[e]=[]),a[e]=a[e].concat(t)},this.off=function(e,t){var i;return!!a[e]&&(i=a[e].indexOf(t),a[e]=a[e].slice(),a[e].splice(i,1),-1<i)},this.trigger=function(e){var t,i,n,r;if(t=a[e])if(2===arguments.length)for(n=t.length,i=0;i<n;++i)t[i].call(this,arguments[1]);else{for(r=[],i=arguments.length,i=1;i<arguments.length;++i)r.push(arguments[i]);for(n=t.length,i=0;i<n;++i)t[i].apply(this,r)}},this.dispose=function(){a={}}}}e.prototype.pipe=function(t){return this.on("data",function(e){t.push(e)}),this.on("done",function(e){t.flush(e)}),this.on("partialdone",function(e){t.partialFlush(e)}),this.on("endedtimeline",function(e){t.endTimeline(e)}),this.on("reset",function(e){t.reset(e)}),t},e.prototype.push=function(e){this.trigger("data",e)},e.prototype.flush=function(e){this.trigger("done",e)},e.prototype.partialFlush=function(e){this.trigger("partialdone",e)},e.prototype.endTimeline=function(e){this.trigger("endedtimeline",e)},e.prototype.reset=function(e){this.trigger("reset",e)};var l,t,i,r,a,n,s,o,u,c,h,d,p,f,m,g,y,v,_,b,T,S,k,C,E,w,A,P,I,L,O,x,D,U,R,M,N,B,j,F,H=e,V=Math.pow(2,32)-1;!function(){var e;if(S={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],pasp:[],sdtp:[],smhd:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],styp:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[]},"undefined"!=typeof Uint8Array){for(e in S)S.hasOwnProperty(e)&&(S[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);k=new Uint8Array(["i".charCodeAt(0),"s".charCodeAt(0),"o".charCodeAt(0),"m".charCodeAt(0)]),E=new Uint8Array(["a".charCodeAt(0),"v".charCodeAt(0),"c".charCodeAt(0),"1".charCodeAt(0)]),C=new Uint8Array([0,0,0,1]),w=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),A=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),P={video:w,audio:A},O=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),L=new Uint8Array([0,0,0,0,0,0,0,0]),x=new Uint8Array([0,0,0,0,0,0,0,0]),D=x,U=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),R=x,I=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}}(),l=function(e){var t,i,n=[],r=0;for(t=1;t<arguments.length;t++)n.push(arguments[t]);for(t=n.length;t--;)r+=n[t].byteLength;for(i=new Uint8Array(r+8),new DataView(i.buffer,i.byteOffset,i.byteLength).setUint32(0,i.byteLength),i.set(e,4),t=0,r=8;t<n.length;t++)i.set(n[t],r),r+=n[t].byteLength;return i},t=function(){return l(S.dinf,l(S.dref,O))},i=function(e){return l(S.esds,new Uint8Array([0,0,0,0,3,25,0,0,0,4,17,64,21,0,6,0,0,0,218,192,0,0,218,192,5,2,e.audioobjecttype<<3|e.samplingfrequencyindex>>>1,e.samplingfrequencyindex<<7|e.channelcount<<3,6,1,2]))},m=function(e){return l(S.hdlr,P[e])},f=function(e){var t=new Uint8Array([0,0,0,0,0,0,0,2,0,0,0,3,0,1,95,144,e.duration>>>24&255,e.duration>>>16&255,e.duration>>>8&255,255&e.duration,85,196,0,0]);return e.samplerate&&(t[12]=e.samplerate>>>24&255,t[13]=e.samplerate>>>16&255,t[14]=e.samplerate>>>8&255,t[15]=255&e.samplerate),l(S.mdhd,t)},p=function(e){return l(S.mdia,f(e),m(e.type),n(e))},a=function(e){return l(S.mfhd,new Uint8Array([0,0,0,0,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e]))},n=function(e){return l(S.minf,"video"===e.type?l(S.vmhd,I):l(S.smhd,L),t(),y(e))},s=function(e,t){for(var i=[],n=t.length;n--;)i[n]=_(t[n]);return l.apply(null,[S.moof,a(e)].concat(i))},o=function(e){for(var t=e.length,i=[];t--;)i[t]=h(e[t]);return l.apply(null,[S.moov,c(4294967295)].concat(i).concat(u(e)))},u=function(e){for(var t=e.length,i=[];t--;)i[t]=b(e[t]);return l.apply(null,[S.mvex].concat(i))},c=function(e){var t=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,2,0,1,95,144,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return l(S.mvhd,t)},g=function(e){var t,i,n=e.samples||[],r=new Uint8Array(4+n.length);for(i=0;i<n.length;i++)t=n[i].flags,r[i+4]=t.dependsOn<<4|t.isDependedOn<<2|t.hasRedundancy;return l(S.sdtp,r)},y=function(e){return l(S.stbl,v(e),l(S.stts,R),l(S.stsc,D),l(S.stsz,U),l(S.stco,x))},v=function(e){return l(S.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),"video"===e.type?M(e):N(e))},M=function(e){var t,i,n=e.sps||[],r=e.pps||[],a=[],s=[];for(t=0;t<n.length;t++)a.push((65280&n[t].byteLength)>>>8),a.push(255&n[t].byteLength),a=a.concat(Array.prototype.slice.call(n[t]));for(t=0;t<r.length;t++)s.push((65280&r[t].byteLength)>>>8),s.push(255&r[t].byteLength),s=s.concat(Array.prototype.slice.call(r[t]));if(i=[S.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,118,105,100,101,111,106,115,45,99,111,110,116,114,105,98,45,104,108,115,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),l(S.avcC,new Uint8Array([1,e.profileIdc,e.profileCompatibility,e.levelIdc,255].concat([n.length],a,[r.length],s))),l(S.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192]))],e.sarRatio){var o=e.sarRatio[0],u=e.sarRatio[1];i.push(l(S.pasp,new Uint8Array([(4278190080&o)>>24,(16711680&o)>>16,(65280&o)>>8,255&o,(4278190080&u)>>24,(16711680&u)>>16,(65280&u)>>8,255&u])))}return l.apply(null,i)},N=function(e){return l(S.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&e.channelcount)>>8,255&e.channelcount,(65280&e.samplesize)>>8,255&e.samplesize,0,0,0,0,(65280&e.samplerate)>>8,255&e.samplerate,0,0]),i(e))},d=function(e){var t=new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,0,(4278190080&e.duration)>>24,(16711680&e.duration)>>16,(65280&e.duration)>>8,255&e.duration,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,(65280&e.width)>>8,255&e.width,0,0,(65280&e.height)>>8,255&e.height,0,0]);return l(S.tkhd,t)},_=function(e){var t,i,n,r,a,s;return t=l(S.tfhd,new Uint8Array([0,0,0,58,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0])),a=Math.floor(e.baseMediaDecodeTime/(1+V)),s=Math.floor(e.baseMediaDecodeTime%(1+V)),i=l(S.tfdt,new Uint8Array([1,0,0,0,a>>>24&255,a>>>16&255,a>>>8&255,255&a,s>>>24&255,s>>>16&255,s>>>8&255,255&s])),92,"audio"===e.type?(n=T(e,92),l(S.traf,t,i,n)):(r=g(e),n=T(e,r.length+92),l(S.traf,t,i,n,r))},h=function(e){return e.duration=e.duration||4294967295,l(S.trak,d(e),p(e))},b=function(e){var t=new Uint8Array([0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return"video"!==e.type&&(t[t.length-1]=0),l(S.trex,t)},F=function(e,t){var i=0,n=0,r=0,a=0;return e.length&&(void 0!==e[0].duration&&(i=1),void 0!==e[0].size&&(n=2),void 0!==e[0].flags&&(r=4),void 0!==e[0].compositionTimeOffset&&(a=8)),[0,0,i|n|r|a,1,(4278190080&e.length)>>>24,(16711680&e.length)>>>16,(65280&e.length)>>>8,255&e.length,(4278190080&t)>>>24,(16711680&t)>>>16,(65280&t)>>>8,255&t]},j=function(e,t){var i,n,r,a;for(t+=20+16*(n=e.samples||[]).length,i=F(n,t),a=0;a<n.length;a++)r=n[a],i=i.concat([(4278190080&r.duration)>>>24,(16711680&r.duration)>>>16,(65280&r.duration)>>>8,255&r.duration,(4278190080&r.size)>>>24,(16711680&r.size)>>>16,(65280&r.size)>>>8,255&r.size,r.flags.isLeading<<2|r.flags.dependsOn,r.flags.isDependedOn<<6|r.flags.hasRedundancy<<4|r.flags.paddingValue<<1|r.flags.isNonSyncSample,61440&r.flags.degradationPriority,15&r.flags.degradationPriority,(4278190080&r.compositionTimeOffset)>>>24,(16711680&r.compositionTimeOffset)>>>16,(65280&r.compositionTimeOffset)>>>8,255&r.compositionTimeOffset]);return l(S.trun,new Uint8Array(i))},B=function(e,t){var i,n,r,a;for(t+=20+8*(n=e.samples||[]).length,i=F(n,t),a=0;a<n.length;a++)r=n[a],i=i.concat([(4278190080&r.duration)>>>24,(16711680&r.duration)>>>16,(65280&r.duration)>>>8,255&r.duration,(4278190080&r.size)>>>24,(16711680&r.size)>>>16,(65280&r.size)>>>8,255&r.size]);return l(S.trun,new Uint8Array(i))},T=function(e,t){return"audio"===e.type?B(e,t):j(e,t)};r=function(){return l(S.ftyp,k,C,k,E)};function q(e,t){var i={size:0,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0,degradationPriority:0,isNonSyncSample:1}};return i.dataOffset=t,i.compositionTimeOffset=e.pts-e.dts,i.duration=e.duration,i.size=4*e.length,i.size+=e.byteLength,e.keyFrame&&(i.flags.dependsOn=2,i.flags.isNonSyncSample=0),i}function W(e){for(var t=[];e--;)t.push(0);return t}function z(){z.prototype.init.call(this),this.captionPackets_=[],this.ccStreams_=[new Ie(0,0),new Ie(0,1),new Ie(1,0),new Ie(1,1)],this.reset(),this.ccStreams_.forEach(function(e){e.on("data",this.trigger.bind(this,"data")),e.on("partialdone",this.trigger.bind(this,"partialdone")),e.on("done",this.trigger.bind(this,"done"))},this)}var $,G,X,K,Y,Q=function(e){return l(S.mdat,e)},J=s,Z=function(e){var t,i=r(),n=o(e);return(t=new Uint8Array(i.byteLength+n.byteLength)).set(i),t.set(n,i.byteLength),t},ee=function(e){var t,i,n=[],r=[];for(r.byteLength=0,r.nalCount=0,r.duration=0,t=n.byteLength=0;t<e.length;t++)"access_unit_delimiter_rbsp"===(i=e[t]).nalUnitType?(n.length&&(n.duration=i.dts-n.dts,r.byteLength+=n.byteLength,r.nalCount+=n.length,r.duration+=n.duration,r.push(n)),(n=[i]).byteLength=i.data.byteLength,n.pts=i.pts,n.dts=i.dts):("slice_layer_without_partitioning_rbsp_idr"===i.nalUnitType&&(n.keyFrame=!0),n.duration=i.dts-n.dts,n.byteLength+=i.data.byteLength,n.push(i));return r.length&&(!n.duration||n.duration<=0)&&(n.duration=r[r.length-1].duration),r.byteLength+=n.byteLength,r.nalCount+=n.length,r.duration+=n.duration,r.push(n),r},te=function(e){var t,i,n=[],r=[];for(n.byteLength=0,n.nalCount=0,n.duration=0,n.pts=e[0].pts,n.dts=e[0].dts,r.byteLength=0,r.nalCount=0,r.duration=0,r.pts=e[0].pts,r.dts=e[0].dts,t=0;t<e.length;t++)(i=e[t]).keyFrame?(n.length&&(r.push(n),r.byteLength+=n.byteLength,r.nalCount+=n.nalCount,r.duration+=n.duration),(n=[i]).nalCount=i.length,n.byteLength=i.byteLength,n.pts=i.pts,n.dts=i.dts,n.duration=i.duration):(n.duration+=i.duration,n.nalCount+=i.length,n.byteLength+=i.byteLength,n.push(i));return r.length&&n.duration<=0&&(n.duration=r[r.length-1].duration),r.byteLength+=n.byteLength,r.nalCount+=n.nalCount,r.duration+=n.duration,r.push(n),r},ie=function(e){var t;return!e[0][0].keyFrame&&1<e.length&&(t=e.shift(),e.byteLength-=t.byteLength,e.nalCount-=t.nalCount,e[0][0].dts=t.dts,e[0][0].pts=t.pts,e[0][0].duration+=t.duration),e},ne=function(e,t){var i,n,r,a,s,o=t||0,u=[];for(i=0;i<e.length;i++)for(a=e[i],n=0;n<a.length;n++)s=a[n],o+=(r=q(s,o)).size,u.push(r);return u},re=function(e){var t,i,n,r,a,s,o=0,u=e.byteLength,l=e.nalCount,c=new Uint8Array(u+4*l),h=new DataView(c.buffer);for(t=0;t<e.length;t++)for(r=e[t],i=0;i<r.length;i++)for(a=r[i],n=0;n<a.length;n++)s=a[n],h.setUint32(o,s.data.byteLength),o+=4,c.set(s.data,o),o+=s.data.byteLength;return c},ae=[33,16,5,32,164,27],se=[33,65,108,84,1,2,4,8,168,2,4,8,17,191,252],oe={96e3:[ae,[227,64],W(154),[56]],88200:[ae,[231],W(170),[56]],64e3:[ae,[248,192],W(240),[56]],48e3:[ae,[255,192],W(268),[55,148,128],W(54),[112]],44100:[ae,[255,192],W(268),[55,163,128],W(84),[112]],32e3:[ae,[255,192],W(268),[55,234],W(226),[112]],24e3:[ae,[255,192],W(268),[55,255,128],W(268),[111,112],W(126),[224]],16e3:[ae,[255,192],W(268),[55,255,128],W(268),[111,255],W(269),[223,108],W(195),[1,192]],12e3:[se,W(268),[3,127,248],W(268),[6,255,240],W(268),[13,255,224],W(268),[27,253,128],W(259),[56]],11025:[se,W(268),[3,127,248],W(268),[6,255,240],W(268),[13,255,224],W(268),[27,255,192],W(268),[55,175,128],W(108),[112]],8e3:[se,W(268),[3,121,16],W(47),[7]]},ue=($=oe,Object.keys($).reduce(function(e,t){return e[t]=new Uint8Array($[t].reduce(function(e,t){return e.concat(t)},[])),e},{})),le=9e4,ce=(G=function(e){return 9e4*e},X=function(e,t){return e*t},K=function(e){return e/9e4},Y=function(e,t){return e/t},function(e,t){return G(Y(e,t))}),he=function(e,t){return X(K(e),t)},de=function(e,t,i){return K(i?e:e-t)},pe=function(e,t,i,n){var r,a,s,o,u,l=0,c=0,h=0;if(t.length&&(r=ce(e.baseMediaDecodeTime,e.samplerate),a=Math.ceil(le/(e.samplerate/1024)),i&&n&&(l=r-Math.max(i,n),h=(c=Math.floor(l/a))*a),!(c<1||le/2<h))){for(s=(s=ue[e.samplerate])||t[0].data,o=0;o<c;o++)u=t[0],t.splice(0,0,{data:s,dts:u.dts-a,pts:u.pts-a});e.baseMediaDecodeTime-=Math.floor(he(h,e.samplerate))}},fe=function(e,t,i){return t.minSegmentDts>=i?e:(t.minSegmentDts=1/0,e.filter(function(e){return e.dts>=i&&(t.minSegmentDts=Math.min(t.minSegmentDts,e.dts),t.minSegmentPts=t.minSegmentDts,!0)}))},me=function(e){var t,i,n=[];for(t=0;t<e.length;t++)i=e[t],n.push({size:i.data.byteLength,duration:1024});return n},ge=function(e){var t,i,n=0,r=new Uint8Array(function(e){var t,i=0;for(t=0;t<e.length;t++)i+=e[t].data.byteLength;return i}(e));for(t=0;t<e.length;t++)i=e[t],r.set(i.data,n),n+=i.data.byteLength;return r},ye=le,ve=function(e){delete e.minSegmentDts,delete e.maxSegmentDts,delete e.minSegmentPts,delete e.maxSegmentPts},_e=function(e,t){var i,n=e.minSegmentDts;return t||(n-=e.timelineStartInfo.dts),i=e.timelineStartInfo.baseMediaDecodeTime,i+=n,i=Math.max(0,i),"audio"===e.type&&(i*=e.samplerate/ye,i=Math.floor(i)),i},be=function(e,t){"number"==typeof t.pts&&(void 0===e.timelineStartInfo.pts&&(e.timelineStartInfo.pts=t.pts),void 0===e.minSegmentPts?e.minSegmentPts=t.pts:e.minSegmentPts=Math.min(e.minSegmentPts,t.pts),void 0===e.maxSegmentPts?e.maxSegmentPts=t.pts:e.maxSegmentPts=Math.max(e.maxSegmentPts,t.pts)),"number"==typeof t.dts&&(void 0===e.timelineStartInfo.dts&&(e.timelineStartInfo.dts=t.dts),void 0===e.minSegmentDts?e.minSegmentDts=t.dts:e.minSegmentDts=Math.min(e.minSegmentDts,t.dts),void 0===e.maxSegmentDts?e.maxSegmentDts=t.dts:e.maxSegmentDts=Math.max(e.maxSegmentDts,t.dts))},Te=function(e){for(var t=0,i={payloadType:-1,payloadSize:0},n=0,r=0;t<e.byteLength&&128!==e[t];){for(;255===e[t];)n+=255,t++;for(n+=e[t++];255===e[t];)r+=255,t++;if(r+=e[t++],!i.payload&&4===n){i.payloadType=n,i.payloadSize=r,i.payload=e.subarray(t,t+r);break}t+=r,r=n=0}return i},Se=function(e){return 181!==e.payload[0]?null:49!=(e.payload[1]<<8|e.payload[2])?null:"GA94"!==String.fromCharCode(e.payload[3],e.payload[4],e.payload[5],e.payload[6])?null:3!==e.payload[7]?null:e.payload.subarray(8,e.payload.length-1)},ke=function(e,t){var i,n,r,a,s=[];if(!(64&t[0]))return s;for(n=31&t[0],i=0;i<n;i++)a={type:3&t[2+(r=3*i)],pts:e},4&t[2+r]&&(a.ccData=t[3+r]<<8|t[4+r],s.push(a));return s},Ce=4;(z.prototype=new H).push=function(e){var t,i,n;if("sei_rbsp"===e.nalUnitType&&(t=Te(e.escapedRBSP)).payloadType===Ce&&(i=Se(t)))if(e.dts<this.latestDts_)this.ignoreNextEqualDts_=!0;else{if(e.dts===this.latestDts_&&this.ignoreNextEqualDts_)return this.numSameDts_--,void(this.numSameDts_||(this.ignoreNextEqualDts_=!1));n=ke(e.pts,i),this.captionPackets_=this.captionPackets_.concat(n),this.latestDts_!==e.dts&&(this.numSameDts_=0),this.numSameDts_++,this.latestDts_=e.dts}},z.prototype.flushCCStreams=function(t){this.ccStreams_.forEach(function(e){return"flush"===t?e.flush():e.partialFlush()},this)},z.prototype.flushStream=function(e){this.captionPackets_.length&&(this.captionPackets_.forEach(function(e,t){e.presortIndex=t}),this.captionPackets_.sort(function(e,t){return e.pts===t.pts?e.presortIndex-t.presortIndex:e.pts-t.pts}),this.captionPackets_.forEach(function(e){e.type<2&&this.dispatchCea608Packet(e)},this),this.captionPackets_.length=0),this.flushCCStreams(e)},z.prototype.flush=function(){return this.flushStream("flush")},z.prototype.partialFlush=function(){return this.flushStream("partialFlush")},z.prototype.reset=function(){this.latestDts_=null,this.ignoreNextEqualDts_=!1,this.numSameDts_=0,this.activeCea608Channel_=[null,null],this.ccStreams_.forEach(function(e){e.reset()})},z.prototype.dispatchCea608Packet=function(e){this.setsTextOrXDSActive(e)?this.activeCea608Channel_[e.type]=null:this.setsChannel1Active(e)?this.activeCea608Channel_[e.type]=0:this.setsChannel2Active(e)&&(this.activeCea608Channel_[e.type]=1),null!==this.activeCea608Channel_[e.type]&&this.ccStreams_[(e.type<<1)+this.activeCea608Channel_[e.type]].push(e)},z.prototype.setsChannel1Active=function(e){return 4096==(30720&e.ccData)},z.prototype.setsChannel2Active=function(e){return 6144==(30720&e.ccData)},z.prototype.setsTextOrXDSActive=function(e){return 256==(28928&e.ccData)||4138==(30974&e.ccData)||6186==(30974&e.ccData)};function Ee(e){return null===e?"":(e=Ae[e]||e,String.fromCharCode(e))}function we(){for(var e=[],t=15;t--;)e.push("");return e}var Ae={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,304:174,305:176,306:189,307:191,308:8482,309:162,310:163,311:9834,312:224,313:160,314:232,315:226,316:234,317:238,318:244,319:251,544:193,545:201,546:211,547:218,548:220,549:252,550:8216,551:161,552:42,553:39,554:8212,555:169,556:8480,557:8226,558:8220,559:8221,560:192,561:194,562:199,563:200,564:202,565:203,566:235,567:206,568:207,569:239,570:212,571:217,572:249,573:219,574:171,575:187,800:195,801:227,802:205,803:204,804:236,805:210,806:242,807:213,808:245,809:123,810:125,811:92,812:94,813:95,814:124,815:126,816:196,817:228,818:214,819:246,820:223,821:165,822:164,823:9474,824:197,825:229,826:216,827:248,828:9484,829:9488,830:9492,831:9496},Pe=[4352,4384,4608,4640,5376,5408,5632,5664,5888,5920,4096,4864,4896,5120,5152],Ie=function e(t,i){e.prototype.init.call(this),this.field_=t||0,this.dataChannel_=i||0,this.name_="CC"+(1+(this.field_<<1|this.dataChannel_)),this.setConstants(),this.reset(),this.push=function(e){var t,i,n,r,a;if((t=32639&e.ccData)!==this.lastControlCode_){if(4096==(61440&t)?this.lastControlCode_=t:t!==this.PADDING_&&(this.lastControlCode_=null),n=t>>>8,r=255&t,t!==this.PADDING_)if(t===this.RESUME_CAPTION_LOADING_)this.mode_="popOn";else if(t===this.END_OF_CAPTION_)this.mode_="popOn",this.clearFormatting(e.pts),this.flushDisplayed(e.pts),i=this.displayed_,this.displayed_=this.nonDisplayed_,this.nonDisplayed_=i,this.startPts_=e.pts;else if(t===this.ROLL_UP_2_ROWS_)this.rollUpRows_=2,this.setRollUp(e.pts);else if(t===this.ROLL_UP_3_ROWS_)this.rollUpRows_=3,this.setRollUp(e.pts);else if(t===this.ROLL_UP_4_ROWS_)this.rollUpRows_=4,this.setRollUp(e.pts);else if(t===this.CARRIAGE_RETURN_)this.clearFormatting(e.pts),this.flushDisplayed(e.pts),this.shiftRowsUp_(),this.startPts_=e.pts;else if(t===this.BACKSPACE_)"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[this.row_]=this.displayed_[this.row_].slice(0,-1);else if(t===this.ERASE_DISPLAYED_MEMORY_)this.flushDisplayed(e.pts),this.displayed_=we();else if(t===this.ERASE_NON_DISPLAYED_MEMORY_)this.nonDisplayed_=we();else if(t===this.RESUME_DIRECT_CAPTIONING_)"paintOn"!==this.mode_&&(this.flushDisplayed(e.pts),this.displayed_=we()),this.mode_="paintOn",this.startPts_=e.pts;else if(this.isSpecialCharacter(n,r))a=Ee((n=(3&n)<<8)|r),this[this.mode_](e.pts,a),this.column_++;else if(this.isExtCharacter(n,r))"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[this.row_]=this.displayed_[this.row_].slice(0,-1),a=Ee((n=(3&n)<<8)|r),this[this.mode_](e.pts,a),this.column_++;else if(this.isMidRowCode(n,r))this.clearFormatting(e.pts),this[this.mode_](e.pts," "),this.column_++,14==(14&r)&&this.addFormatting(e.pts,["i"]),1==(1&r)&&this.addFormatting(e.pts,["u"]);else if(this.isOffsetControlCode(n,r))this.column_+=3&r;else if(this.isPAC(n,r)){var s=Pe.indexOf(7968&t);"rollUp"===this.mode_&&(s-this.rollUpRows_+1<0&&(s=this.rollUpRows_-1),this.setRollUp(e.pts,s)),s!==this.row_&&(this.clearFormatting(e.pts),this.row_=s),1&r&&-1===this.formatting_.indexOf("u")&&this.addFormatting(e.pts,["u"]),16==(16&t)&&(this.column_=4*((14&t)>>1)),this.isColorPAC(r)&&14==(14&r)&&this.addFormatting(e.pts,["i"])}else this.isNormalChar(n)&&(0===r&&(r=null),a=Ee(n),a+=Ee(r),this[this.mode_](e.pts,a),this.column_+=a.length)}else this.lastControlCode_=null}};Ie.prototype=new H,Ie.prototype.flushDisplayed=function(e){var t=this.displayed_.map(function(e){try{return e.trim()}catch(e){return""}}).join("\n").replace(/^\n+|\n+$/g,"");t.length&&this.trigger("data",{startPts:this.startPts_,endPts:e,text:t,stream:this.name_})},Ie.prototype.reset=function(){this.mode_="popOn",this.topRow_=0,this.startPts_=0,this.displayed_=we(),this.nonDisplayed_=we(),this.lastControlCode_=null,this.column_=0,this.row_=14,this.rollUpRows_=2,this.formatting_=[]},Ie.prototype.setConstants=function(){0===this.dataChannel_?(this.BASE_=16,this.EXT_=17,this.CONTROL_=(20|this.field_)<<8,this.OFFSET_=23):1===this.dataChannel_&&(this.BASE_=24,this.EXT_=25,this.CONTROL_=(28|this.field_)<<8,this.OFFSET_=31),this.PADDING_=0,this.RESUME_CAPTION_LOADING_=32|this.CONTROL_,this.END_OF_CAPTION_=47|this.CONTROL_,this.ROLL_UP_2_ROWS_=37|this.CONTROL_,this.ROLL_UP_3_ROWS_=38|this.CONTROL_,this.ROLL_UP_4_ROWS_=39|this.CONTROL_,this.CARRIAGE_RETURN_=45|this.CONTROL_,this.RESUME_DIRECT_CAPTIONING_=41|this.CONTROL_,this.BACKSPACE_=33|this.CONTROL_,this.ERASE_DISPLAYED_MEMORY_=44|this.CONTROL_,this.ERASE_NON_DISPLAYED_MEMORY_=46|this.CONTROL_},Ie.prototype.isSpecialCharacter=function(e,t){return e===this.EXT_&&48<=t&&t<=63},Ie.prototype.isExtCharacter=function(e,t){return(e===this.EXT_+1||e===this.EXT_+2)&&32<=t&&t<=63},Ie.prototype.isMidRowCode=function(e,t){return e===this.EXT_&&32<=t&&t<=47},Ie.prototype.isOffsetControlCode=function(e,t){return e===this.OFFSET_&&33<=t&&t<=35},Ie.prototype.isPAC=function(e,t){return e>=this.BASE_&&e<this.BASE_+8&&64<=t&&t<=127},Ie.prototype.isColorPAC=function(e){return 64<=e&&e<=79||96<=e&&e<=127},Ie.prototype.isNormalChar=function(e){return 32<=e&&e<=127},Ie.prototype.setRollUp=function(e,t){if("rollUp"!==this.mode_&&(this.row_=14,this.mode_="rollUp",this.flushDisplayed(e),this.nonDisplayed_=we(),this.displayed_=we()),void 0!==t&&t!==this.row_)for(var i=0;i<this.rollUpRows_;i++)this.displayed_[t-i]=this.displayed_[this.row_-i],this.displayed_[this.row_-i]="";void 0===t&&(t=this.row_),this.topRow_=t-this.rollUpRows_+1},Ie.prototype.addFormatting=function(e,t){this.formatting_=this.formatting_.concat(t);var i=t.reduce(function(e,t){return e+"<"+t+">"},"");this[this.mode_](e,i)},Ie.prototype.clearFormatting=function(e){if(this.formatting_.length){var t=this.formatting_.reverse().reduce(function(e,t){return e+"</"+t+">"},"");this.formatting_=[],this[this.mode_](e,t)}},Ie.prototype.popOn=function(e,t){var i=this.nonDisplayed_[this.row_];i+=t,this.nonDisplayed_[this.row_]=i},Ie.prototype.rollUp=function(e,t){var i=this.displayed_[this.row_];i+=t,this.displayed_[this.row_]=i},Ie.prototype.shiftRowsUp_=function(){var e;for(e=0;e<this.topRow_;e++)this.displayed_[e]="";for(e=this.row_+1;e<15;e++)this.displayed_[e]="";for(e=this.topRow_;e<this.row_;e++)this.displayed_[e]=this.displayed_[e+1];this.displayed_[this.row_]=""},Ie.prototype.paintOn=function(e,t){var i=this.displayed_[this.row_];i+=t,this.displayed_[this.row_]=i};function Le(e,t){var i=1;for(t<e&&(i=-1);4294967296<Math.abs(t-e);)e+=8589934592*i;return e}function Oe(e){var t,i;Oe.prototype.init.call(this),this.type_=e||"shared",this.push=function(e){"shared"!==this.type_&&e.type!==this.type_||(void 0===i&&(i=e.dts),e.dts=Le(e.dts,i),e.pts=Le(e.pts,i),t=e.dts,this.trigger("data",e))},this.flush=function(){i=t,this.trigger("done")},this.endTimeline=function(){this.flush(),this.trigger("endedtimeline")},this.discontinuity=function(){t=i=void 0},this.reset=function(){this.discontinuity(),this.trigger("reset")}}var xe={CaptionStream:z,Cea608Stream:Ie},De={H264_STREAM_TYPE:27,ADTS_STREAM_TYPE:15,METADATA_STREAM_TYPE:21};Oe.prototype=new H;function Ue(e,t,i){var n,r="";for(n=t;n<i;n++)r+="%"+("00"+e[n].toString(16)).slice(-2);return r}function Re(e,t,i){return decodeURIComponent(Ue(e,t,i))}function Me(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]}var Ne,Be=Oe,je={TXXX:function(e){var t;if(3===e.data[0]){for(t=1;t<e.data.length;t++)if(0===e.data[t]){e.description=Re(e.data,1,t),e.value=Re(e.data,t+1,e.data.length).replace(/\0*$/,"");break}e.data=e.value}},WXXX:function(e){var t;if(3===e.data[0])for(t=1;t<e.data.length;t++)if(0===e.data[t]){e.description=Re(e.data,1,t),e.url=Re(e.data,t+1,e.data.length);break}},PRIV:function(e){var t,i;for(t=0;t<e.data.length;t++)if(0===e.data[t]){e.owner=(i=e.data,unescape(Ue(i,0,t)));break}e.privateData=e.data.subarray(t+1),e.data=e.privateData}};(Ne=function(e){var t,u={debug:!(!e||!e.debug),descriptor:e&&e.descriptor},l=0,c=[],h=0;if(Ne.prototype.init.call(this),this.dispatchType=De.METADATA_STREAM_TYPE.toString(16),u.descriptor)for(t=0;t<u.descriptor.length;t++)this.dispatchType+=("00"+u.descriptor[t].toString(16)).slice(-2);this.push=function(e){var t,i,n,r,a;if("timed-metadata"===e.type)if(e.dataAlignmentIndicator&&(h=0,c.length=0),0===c.length&&(e.data.length<10||e.data[0]!=="I".charCodeAt(0)||e.data[1]!=="D".charCodeAt(0)||e.data[2]!=="3".charCodeAt(0)))u.debug;else if(c.push(e),h+=e.data.byteLength,1===c.length&&(l=Me(e.data.subarray(6,10)),l+=10),!(h<l)){for(t={data:new Uint8Array(l),frames:[],pts:c[0].pts,dts:c[0].dts},a=0;a<l;)t.data.set(c[0].data.subarray(0,l-a),a),a+=c[0].data.byteLength,h-=c[0].data.byteLength,c.shift();i=10,64&t.data[5]&&(i+=4,i+=Me(t.data.subarray(10,14)),l-=Me(t.data.subarray(16,20)));do{if((n=Me(t.data.subarray(i+4,i+8)))<1)return;if((r={id:String.fromCharCode(t.data[i],t.data[i+1],t.data[i+2],t.data[i+3]),data:t.data.subarray(i+10,i+n+10)}).key=r.id,je[r.id]&&(je[r.id](r),"com.apple.streaming.transportStreamTimestamp"===r.owner)){var s=r.data,o=(1&s[3])<<30|s[4]<<22|s[5]<<14|s[6]<<6|s[7]>>>2;o*=4,o+=3&s[7],r.timeStamp=o,void 0===t.pts&&void 0===t.dts&&(t.pts=r.timeStamp,t.dts=r.timeStamp),this.trigger("timestamp",r)}t.frames.push(r),i+=10,i+=n}while(i<l);this.trigger("data",t)}}}).prototype=new H;var Fe,He,Ve,qe=Ne,We=Be;(Fe=function(){var r=new Uint8Array(188),a=0;Fe.prototype.init.call(this),this.push=function(e){var t,i=0,n=188;for(a?((t=new Uint8Array(e.byteLength+a)).set(r.subarray(0,a)),t.set(e,a),a=0):t=e;n<t.byteLength;)71!==t[i]||71!==t[n]?(i++,n++):(this.trigger("data",t.subarray(i,n)),i+=188,n+=188);i<t.byteLength&&(r.set(t.subarray(i),0),a=t.byteLength-i)},this.flush=function(){188===a&&71===r[0]&&(this.trigger("data",r),a=0),this.trigger("done")},this.endTimeline=function(){this.flush(),this.trigger("endedtimeline")},this.reset=function(){a=0,this.trigger("reset")}}).prototype=new H,(He=function(){var n,r,a,s;He.prototype.init.call(this),(s=this).packetsWaitingForPmt=[],this.programMapTable=void 0,n=function(e,t){var i=0;t.payloadUnitStartIndicator&&(i+=e[i]+1),"pat"===t.type?r(e.subarray(i),t):a(e.subarray(i),t)},r=function(e,t){t.section_number=e[7],t.last_section_number=e[8],s.pmtPid=(31&e[10])<<8|e[11],t.pmtPid=s.pmtPid},a=function(e,t){var i,n;if(1&e[5]){for(s.programMapTable={video:null,audio:null,"timed-metadata":{}},i=3+((15&e[1])<<8|e[2])-4,n=12+((15&e[10])<<8|e[11]);n<i;){var r=e[n],a=(31&e[n+1])<<8|e[n+2];r===De.H264_STREAM_TYPE&&null===s.programMapTable.video?s.programMapTable.video=a:r===De.ADTS_STREAM_TYPE&&null===s.programMapTable.audio?s.programMapTable.audio=a:r===De.METADATA_STREAM_TYPE&&(s.programMapTable["timed-metadata"][a]=r),n+=5+((15&e[n+3])<<8|e[n+4])}t.programMapTable=s.programMapTable}},this.push=function(e){var t={},i=4;if(t.payloadUnitStartIndicator=!!(64&e[1]),t.pid=31&e[1],t.pid<<=8,t.pid|=e[2],1<(48&e[3])>>>4&&(i+=e[i]+1),0===t.pid)t.type="pat",n(e.subarray(i),t),this.trigger("data",t);else if(t.pid===this.pmtPid)for(t.type="pmt",n(e.subarray(i),t),this.trigger("data",t);this.packetsWaitingForPmt.length;)this.processPes_.apply(this,this.packetsWaitingForPmt.shift());else void 0===this.programMapTable?this.packetsWaitingForPmt.push([e,i,t]):this.processPes_(e,i,t)},this.processPes_=function(e,t,i){i.pid===this.programMapTable.video?i.streamType=De.H264_STREAM_TYPE:i.pid===this.programMapTable.audio?i.streamType=De.ADTS_STREAM_TYPE:i.streamType=this.programMapTable["timed-metadata"][i.pid],i.type="pes",i.data=e.subarray(t),this.trigger("data",i)}}).prototype=new H,He.STREAM_TYPES={h264:27,adts:15},(Ve=function(){function n(e,t,i){var n,r,a=new Uint8Array(e.size),s={type:t},o=0,u=0;if(e.data.length&&!(e.size<9)){for(s.trackId=e.data[0].pid,o=0;o<e.data.length;o++)r=e.data[o],a.set(r.data,u),u+=r.data.byteLength;!function(e,t){var i;t.packetLength=6+(e[4]<<8|e[5]),t.dataAlignmentIndicator=0!=(4&e[6]),192&(i=e[7])&&(t.pts=(14&e[9])<<27|(255&e[10])<<20|(254&e[11])<<12|(255&e[12])<<5|(254&e[13])>>>3,t.pts*=4,t.pts+=(6&e[13])>>>1,t.dts=t.pts,64&i&&(t.dts=(14&e[14])<<27|(255&e[15])<<20|(254&e[16])<<12|(255&e[17])<<5|(254&e[18])>>>3,t.dts*=4,t.dts+=(6&e[18])>>>1)),t.data=e.subarray(9+e[8])}(a,s),n="video"===t||s.packetLength<=e.size,(i||n)&&(e.size=0,e.data.length=0),n&&l.trigger("data",s)}}var t,l=this,r={data:[],size:0},a={data:[],size:0},s={data:[],size:0};Ve.prototype.init.call(this),this.push=function(i){({pat:function(){},pes:function(){var e,t;switch(i.streamType){case De.H264_STREAM_TYPE:case De.H264_STREAM_TYPE:e=r,t="video";break;case De.ADTS_STREAM_TYPE:e=a,t="audio";break;case De.METADATA_STREAM_TYPE:e=s,t="timed-metadata";break;default:return}i.payloadUnitStartIndicator&&n(e,t,!0),e.data.push(i),e.size+=i.data.byteLength},pmt:function(){var e={type:"metadata",tracks:[]};null!==(t=i.programMapTable).video&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+t.video,codec:"avc",type:"video"}),null!==t.audio&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+t.audio,codec:"adts",type:"audio"}),l.trigger("data",e)}})[i.type]()},this.reset=function(){r.size=0,r.data.length=0,a.size=0,a.data.length=0,this.trigger("reset")},this.flushStreams_=function(){n(r,"video"),n(a,"audio"),n(s,"timed-metadata")},this.flush=function(){this.flushStreams_(),this.trigger("done")}}).prototype=new H;var ze={PAT_PID:0,MP2T_PACKET_LENGTH:188,TransportPacketStream:Fe,TransportParseStream:He,ElementaryStream:Ve,TimestampRolloverStream:We,CaptionStream:xe.CaptionStream,Cea608Stream:xe.Cea608Stream,MetadataStream:qe};for(var $e in De)De.hasOwnProperty($e)&&(ze[$e]=De[$e]);var Ge,Xe=ze,Ke=le,Ye=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];(Ge=function(u){var l,c=0;Ge.prototype.init.call(this),this.push=function(e){var t,i,n,r,a,s,o=0;if(u||(c=0),"audio"===e.type)for(l?(r=l,(l=new Uint8Array(r.byteLength+e.data.byteLength)).set(r),l.set(e.data,r.byteLength)):l=e.data;o+5<l.length;)if(255===l[o]&&240==(246&l[o+1])){if(i=2*(1&~l[o+1]),t=(3&l[o+3])<<11|l[o+4]<<3|(224&l[o+5])>>5,s=(a=1024*(1+(3&l[o+6])))*Ke/Ye[(60&l[o+2])>>>2],n=o+t,l.byteLength<n)return;if(this.trigger("data",{pts:e.pts+c*s,dts:e.dts+c*s,sampleCount:a,audioobjecttype:1+(l[o+2]>>>6&3),channelcount:(1&l[o+2])<<2|(192&l[o+3])>>>6,samplerate:Ye[(60&l[o+2])>>>2],samplingfrequencyindex:(60&l[o+2])>>>2,samplesize:16,data:l.subarray(o+7+i,n)}),c++,l.byteLength===n)return void(l=void 0);l=l.subarray(n)}else o++},this.flush=function(){c=0,this.trigger("done")},this.reset=function(){l=void 0,this.trigger("reset")},this.endTimeline=function(){l=void 0,this.trigger("endedtimeline")}}).prototype=new H;var Qe,Je,Ze,et=Ge,tt=function(n){var r=n.byteLength,a=0,s=0;this.length=function(){return 8*r},this.bitsAvailable=function(){return 8*r+s},this.loadWord=function(){var e=n.byteLength-r,t=new Uint8Array(4),i=Math.min(4,r);if(0===i)throw new Error("no bytes available");t.set(n.subarray(e,e+i)),a=new DataView(t.buffer).getUint32(0),s=8*i,r-=i},this.skipBits=function(e){var t;e<s||(e-=s,e-=8*(t=Math.floor(e/8)),r-=t,this.loadWord()),a<<=e,s-=e},this.readBits=function(e){var t=Math.min(s,e),i=a>>>32-t;return 0<(s-=t)?a<<=t:0<r&&this.loadWord(),0<(t=e-t)?i<<t|this.readBits(t):i},this.skipLeadingZeros=function(){var e;for(e=0;e<s;++e)if(0!=(a&2147483648>>>e))return a<<=e,s-=e,e;return this.loadWord(),e+this.skipLeadingZeros()},this.skipUnsignedExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.skipExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.readUnsignedExpGolomb=function(){var e=this.skipLeadingZeros();return this.readBits(e+1)-1},this.readExpGolomb=function(){var e=this.readUnsignedExpGolomb();return 1&e?1+e>>>1:-1*(e>>>1)},this.readBoolean=function(){return 1===this.readBits(1)},this.readUnsignedByte=function(){return this.readBits(8)},this.loadWord()};(Je=function(){var n,r,a=0;Je.prototype.init.call(this),this.push=function(e){for(var t,i=(r=r?((t=new Uint8Array(r.byteLength+e.data.byteLength)).set(r),t.set(e.data,r.byteLength),t):e.data).byteLength;a<i-3;a++)if(1===r[a+2]){n=a+5;break}for(;n<i;)switch(r[n]){case 0:if(0!==r[n-1]){n+=2;break}if(0!==r[n-2]){n++;break}for(a+3!==n-2&&this.trigger("data",r.subarray(a+3,n-2));1!==r[++n]&&n<i;);a=n-2,n+=3;break;case 1:if(0!==r[n-1]||0!==r[n-2]){n+=3;break}this.trigger("data",r.subarray(a+3,n-2)),a=n-2,n+=3;break;default:n+=3}r=r.subarray(a),n-=a,a=0},this.reset=function(){r=null,a=0,this.trigger("reset")},this.flush=function(){r&&3<r.byteLength&&this.trigger("data",r.subarray(a+3)),r=null,a=0,this.trigger("done")},this.endTimeline=function(){this.flush(),this.trigger("endedtimeline")}}).prototype=new H,Ze={100:!0,110:!0,122:!0,244:!0,44:!0,83:!0,86:!0,118:!0,128:!0,138:!0,139:!0,134:!0},(Qe=function(){var i,n,r,a,s,o,_,t=new Je;Qe.prototype.init.call(this),(i=this).push=function(e){"video"===e.type&&(n=e.trackId,r=e.pts,a=e.dts,t.push(e))},t.on("data",function(e){var t={trackId:n,pts:r,dts:a,data:e};switch(31&e[0]){case 5:t.nalUnitType="slice_layer_without_partitioning_rbsp_idr";break;case 6:t.nalUnitType="sei_rbsp",t.escapedRBSP=s(e.subarray(1));break;case 7:t.nalUnitType="seq_parameter_set_rbsp",t.escapedRBSP=s(e.subarray(1)),t.config=o(t.escapedRBSP);break;case 8:t.nalUnitType="pic_parameter_set_rbsp";break;case 9:t.nalUnitType="access_unit_delimiter_rbsp"}i.trigger("data",t)}),t.on("done",function(){i.trigger("done")}),t.on("partialdone",function(){i.trigger("partialdone")}),t.on("reset",function(){i.trigger("reset")}),t.on("endedtimeline",function(){i.trigger("endedtimeline")}),this.flush=function(){t.flush()},this.partialFlush=function(){t.partialFlush()},this.reset=function(){t.reset()},this.endTimeline=function(){t.endTimeline()},_=function(e,t){var i,n=8,r=8;for(i=0;i<e;i++)0!==r&&(r=(n+t.readExpGolomb()+256)%256),n=0===r?n:r},s=function(e){for(var t,i,n=e.byteLength,r=[],a=1;a<n-2;)0===e[a]&&0===e[a+1]&&3===e[a+2]?(r.push(a+2),a+=2):a++;if(0===r.length)return e;t=n-r.length,i=new Uint8Array(t);var s=0;for(a=0;a<t;s++,a++)s===r[0]&&(s++,r.shift()),i[a]=e[s];return i},o=function(e){var t,i,n,r,a,s,o,u,l,c,h,d,p,f=0,m=0,g=0,y=0,v=1;if(i=(t=new tt(e)).readUnsignedByte(),r=t.readUnsignedByte(),n=t.readUnsignedByte(),t.skipUnsignedExpGolomb(),Ze[i]&&(3===(a=t.readUnsignedExpGolomb())&&t.skipBits(1),t.skipUnsignedExpGolomb(),t.skipUnsignedExpGolomb(),t.skipBits(1),t.readBoolean()))for(h=3!==a?8:12,p=0;p<h;p++)t.readBoolean()&&_(p<6?16:64,t);if(t.skipUnsignedExpGolomb(),0===(s=t.readUnsignedExpGolomb()))t.readUnsignedExpGolomb();else if(1===s)for(t.skipBits(1),t.skipExpGolomb(),t.skipExpGolomb(),o=t.readUnsignedExpGolomb(),p=0;p<o;p++)t.skipExpGolomb();if(t.skipUnsignedExpGolomb(),t.skipBits(1),u=t.readUnsignedExpGolomb(),l=t.readUnsignedExpGolomb(),0===(c=t.readBits(1))&&t.skipBits(1),t.skipBits(1),t.readBoolean()&&(f=t.readUnsignedExpGolomb(),m=t.readUnsignedExpGolomb(),g=t.readUnsignedExpGolomb(),y=t.readUnsignedExpGolomb()),t.readBoolean()&&t.readBoolean()){switch(t.readUnsignedByte()){case 1:d=[1,1];break;case 2:d=[12,11];break;case 3:d=[10,11];break;case 4:d=[16,11];break;case 5:d=[40,33];break;case 6:d=[24,11];break;case 7:d=[20,11];break;case 8:d=[32,11];break;case 9:d=[80,33];break;case 10:d=[18,11];break;case 11:d=[15,11];break;case 12:d=[64,33];break;case 13:d=[160,99];break;case 14:d=[4,3];break;case 15:d=[3,2];break;case 16:d=[2,1];break;case 255:d=[t.readUnsignedByte()<<8|t.readUnsignedByte(),t.readUnsignedByte()<<8|t.readUnsignedByte()]}d&&(v=d[0]/d[1])}return{profileIdc:i,levelIdc:n,profileCompatibility:r,width:Math.ceil((16*(u+1)-2*f-2*m)*v),height:(2-c)*(l+1)*16-2*g-2*y,sarRatio:d}}}).prototype=new H;var it,nt={H264Stream:Qe,NalByteStream:Je},rt=function(e){return e[0]==="I".charCodeAt(0)&&e[1]==="D".charCodeAt(0)&&e[2]==="3".charCodeAt(0)},at=function(e,t){var i=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9];return(16&e[t+5])>>4?20+i:10+i},st=function(e,t){var i=(224&e[t+5])>>5,n=e[t+4]<<3;return 6144&e[t+3]|n|i};(it=function(){var o=new Uint8Array,u=0;it.prototype.init.call(this),this.setTimestamp=function(e){u=e},this.push=function(e){var t,i,n,r,a=0,s=0;for(o.length?(r=o.length,(o=new Uint8Array(e.byteLength+r)).set(o.subarray(0,r)),o.set(e,r)):o=e;3<=o.length-s;)if(o[s]!=="I".charCodeAt(0)||o[s+1]!=="D".charCodeAt(0)||o[s+2]!=="3".charCodeAt(0))if(255!=(255&o[s])||240!=(240&o[s+1]))s++;else{if(o.length-s<7)break;if(s+(a=st(o,s))>o.length)break;n={type:"audio",data:o.subarray(s,s+a),pts:u,dts:u},this.trigger("data",n),s+=a}else{if(o.length-s<10)break;if(s+(a=at(o,s))>o.length)break;i={type:"timed-metadata",data:o.subarray(s,s+a)},this.trigger("data",i),s+=a}t=o.length-s,o=0<t?o.subarray(s):new Uint8Array},this.reset=function(){o=new Uint8Array,this.trigger("reset")},this.endTimeline=function(){o=new Uint8Array,this.trigger("endedtimeline")}}).prototype=new H;function ot(e,t){var i;if(e.length!==t.length)return!1;for(i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0}function ut(e,t,i,n,r,a){return{start:{dts:e,pts:e+(i-t)},end:{dts:e+(n-t),pts:e+(r-i)},prependedContentDuration:a,baseMediaDecodeTime:e}}var lt,ct,ht,dt,pt=it,ft=nt.H264Stream,mt=rt,gt=le,yt=["audioobjecttype","channelcount","samplerate","samplingfrequencyindex","samplesize"],vt=["width","height","profileIdc","levelIdc","profileCompatibility","sarRatio"];(ct=function(a,s){var o=[],u=0,l=0,c=0,h=1/0;s=s||{},ct.prototype.init.call(this),this.push=function(t){be(a,t),a&&yt.forEach(function(e){a[e]=t[e]}),o.push(t)},this.setEarliestDts=function(e){l=e-a.timelineStartInfo.baseMediaDecodeTime},this.setVideoBaseMediaDecodeTime=function(e){h=e},this.setAudioAppendStart=function(e){c=e},this.flush=function(){var e,t,i,n,r;0!==o.length&&(e=fe(o,a,l),a.baseMediaDecodeTime=_e(a,s.keepOriginalTimestamps),pe(a,e,c,h),a.samples=me(e),i=Q(ge(e)),o=[],t=J(u,[a]),n=new Uint8Array(t.byteLength+i.byteLength),u++,n.set(t),n.set(i,t.byteLength),ve(a),r=Math.ceil(1024*gt/a.samplerate),e.length&&this.trigger("timingInfo",{start:e[0].dts,end:e[0].dts+e.length*r}),this.trigger("data",{track:a,boxes:n})),this.trigger("done","AudioSegmentStream")},this.reset=function(){ve(a),o=[],this.trigger("reset")}}).prototype=new H,(lt=function(c,h){var t,i,d=0,p=[],f=[];h=h||{},lt.prototype.init.call(this),delete c.minPTS,this.gopCache_=[],this.push=function(e){be(c,e),"seq_parameter_set_rbsp"!==e.nalUnitType||t||(t=e.config,c.sps=[e.data],vt.forEach(function(e){c[e]=t[e]},this)),"pic_parameter_set_rbsp"!==e.nalUnitType||i||(i=e.data,c.pps=[e.data]),p.push(e)},this.flush=function(){for(var e,t,i,n,r,a,s,o,u=0;p.length&&"access_unit_delimiter_rbsp"!==p[0].nalUnitType;)p.shift();if(0===p.length)return this.resetStream_(),void this.trigger("done","VideoSegmentStream");if(e=ee(p),(i=te(e))[0][0].keyFrame||((t=this.getGopForFusion_(p[0],c))?(u=t.duration,i.unshift(t),i.byteLength+=t.byteLength,i.nalCount+=t.nalCount,i.pts=t.pts,i.dts=t.dts,i.duration+=t.duration):i=ie(i)),f.length){var l;if(!(l=h.alignGopsAtEnd?this.alignGopsAtEnd_(i):this.alignGopsAtStart_(i)))return this.gopCache_.unshift({gop:i.pop(),pps:c.pps,sps:c.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),p=[],this.resetStream_(),void this.trigger("done","VideoSegmentStream");ve(c),i=l}be(c,i),c.samples=ne(i),r=Q(re(i)),c.baseMediaDecodeTime=_e(c,h.keepOriginalTimestamps),this.trigger("processedGopsInfo",i.map(function(e){return{pts:e.pts,dts:e.dts,byteLength:e.byteLength}})),s=i[0],o=i[i.length-1],this.trigger("segmentTimingInfo",ut(c.baseMediaDecodeTime,s.dts,s.pts,o.dts+o.duration,o.pts+o.duration,u)),this.trigger("timingInfo",{start:i[0].dts,end:i[i.length-1].dts+i[i.length-1].duration}),this.gopCache_.unshift({gop:i.pop(),pps:c.pps,sps:c.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),p=[],this.trigger("baseMediaDecodeTime",c.baseMediaDecodeTime),this.trigger("timelineStartInfo",c.timelineStartInfo),n=J(d,[c]),a=new Uint8Array(n.byteLength+r.byteLength),d++,a.set(n),a.set(r,n.byteLength),this.trigger("data",{track:c,boxes:a}),this.resetStream_(),this.trigger("done","VideoSegmentStream")},this.reset=function(){this.resetStream_(),p=[],this.gopCache_.length=0,f.length=0,this.trigger("reset")},this.resetStream_=function(){ve(c),i=t=void 0},this.getGopForFusion_=function(e){var t,i,n,r,a,s=1/0;for(a=0;a<this.gopCache_.length;a++)n=(r=this.gopCache_[a]).gop,c.pps&&ot(c.pps[0],r.pps[0])&&c.sps&&ot(c.sps[0],r.sps[0])&&(n.dts<c.timelineStartInfo.dts||-1e4<=(t=e.dts-n.dts-n.duration)&&t<=45e3&&(!i||t<s)&&(i=r,s=t));return i?i.gop:null},this.alignGopsAtStart_=function(e){var t,i,n,r,a,s,o,u;for(a=e.byteLength,s=e.nalCount,o=e.duration,t=i=0;t<f.length&&i<e.length&&(n=f[t],r=e[i],n.pts!==r.pts);)r.pts>n.pts?t++:(i++,a-=r.byteLength,s-=r.nalCount,o-=r.duration);return 0===i?e:i===e.length?null:((u=e.slice(i)).byteLength=a,u.duration=o,u.nalCount=s,u.pts=u[0].pts,u.dts=u[0].dts,u)},this.alignGopsAtEnd_=function(e){var t,i,n,r,a,s,o;for(t=f.length-1,i=e.length-1,a=null,s=!1;0<=t&&0<=i;){if(n=f[t],r=e[i],n.pts===r.pts){s=!0;break}n.pts>r.pts?t--:(t===f.length-1&&(a=i),i--)}if(!s&&null===a)return null;if(0===(o=s?i:a))return e;var u=e.slice(o),l=u.reduce(function(e,t){return e.byteLength+=t.byteLength,e.duration+=t.duration,e.nalCount+=t.nalCount,e},{byteLength:0,duration:0,nalCount:0});return u.byteLength=l.byteLength,u.duration=l.duration,u.nalCount=l.nalCount,u.pts=u[0].pts,u.dts=u[0].dts,u},this.alignGopsWith=function(e){f=e}}).prototype=new H,(dt=function(e,t){this.numberOfTracks=0,this.metadataStream=t,"undefined"!=typeof(e=e||{}).remux?this.remuxTracks=!!e.remux:this.remuxTracks=!0,"boolean"==typeof e.keepOriginalTimestamps?this.keepOriginalTimestamps=e.keepOriginalTimestamps:this.keepOriginalTimestamps=!1,this.pendingTracks=[],this.videoTrack=null,this.pendingBoxes=[],this.pendingCaptions=[],this.pendingMetadata=[],this.pendingBytes=0,this.emittedTracks=0,dt.prototype.init.call(this),this.push=function(e){return e.text?this.pendingCaptions.push(e):e.frames?this.pendingMetadata.push(e):(this.pendingTracks.push(e.track),this.pendingBytes+=e.boxes.byteLength,"video"===e.track.type&&(this.videoTrack=e.track,this.pendingBoxes.push(e.boxes)),void("audio"===e.track.type&&(this.audioTrack=e.track,this.pendingBoxes.unshift(e.boxes))))}}).prototype=new H,dt.prototype.flush=function(e){var t,i,n,r,a=0,s={captions:[],captionStreams:{},metadata:[],info:{}},o=0;if(this.pendingTracks.length<this.numberOfTracks){if("VideoSegmentStream"!==e&&"AudioSegmentStream"!==e)return;if(this.remuxTracks)return;if(0===this.pendingTracks.length)return this.emittedTracks++,void(this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0))}if(this.videoTrack?(o=this.videoTrack.timelineStartInfo.pts,vt.forEach(function(e){s.info[e]=this.videoTrack[e]},this)):this.audioTrack&&(o=this.audioTrack.timelineStartInfo.pts,yt.forEach(function(e){s.info[e]=this.audioTrack[e]},this)),this.videoTrack||this.audioTrack){for(1===this.pendingTracks.length?s.type=this.pendingTracks[0].type:s.type="combined",this.emittedTracks+=this.pendingTracks.length,n=Z(this.pendingTracks),s.initSegment=new Uint8Array(n.byteLength),s.initSegment.set(n),s.data=new Uint8Array(this.pendingBytes),r=0;r<this.pendingBoxes.length;r++)s.data.set(this.pendingBoxes[r],a),a+=this.pendingBoxes[r].byteLength;for(r=0;r<this.pendingCaptions.length;r++)(t=this.pendingCaptions[r]).startTime=de(t.startPts,o,this.keepOriginalTimestamps),t.endTime=de(t.endPts,o,this.keepOriginalTimestamps),s.captionStreams[t.stream]=!0,s.captions.push(t);for(r=0;r<this.pendingMetadata.length;r++)(i=this.pendingMetadata[r]).cueTime=de(i.pts,o,this.keepOriginalTimestamps),s.metadata.push(i);for(s.metadata.dispatchType=this.metadataStream.dispatchType,this.pendingTracks.length=0,this.videoTrack=null,this.pendingBoxes.length=0,this.pendingCaptions.length=0,this.pendingBytes=0,this.pendingMetadata.length=0,this.trigger("data",s),r=0;r<s.captions.length;r++)t=s.captions[r],this.trigger("caption",t);for(r=0;r<s.metadata.length;r++)i=s.metadata[r],this.trigger("id3Frame",i)}this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0)},dt.prototype.setRemux=function(e){this.remuxTracks=e},(ht=function(n){var r,a,s=this,i=!0;ht.prototype.init.call(this),n=n||{},this.baseMediaDecodeTime=n.baseMediaDecodeTime||0,this.transmuxPipeline_={},this.setupAacPipeline=function(){var t={};(this.transmuxPipeline_=t).type="aac",t.metadataStream=new Xe.MetadataStream,t.aacStream=new pt,t.audioTimestampRolloverStream=new Xe.TimestampRolloverStream("audio"),t.timedMetadataTimestampRolloverStream=new Xe.TimestampRolloverStream("timed-metadata"),t.adtsStream=new et,t.coalesceStream=new dt(n,t.metadataStream),t.headOfPipeline=t.aacStream,t.aacStream.pipe(t.audioTimestampRolloverStream).pipe(t.adtsStream),t.aacStream.pipe(t.timedMetadataTimestampRolloverStream).pipe(t.metadataStream).pipe(t.coalesceStream),t.metadataStream.on("timestamp",function(e){t.aacStream.setTimestamp(e.timeStamp)}),t.aacStream.on("data",function(e){"timed-metadata"!==e.type||t.audioSegmentStream||(a=a||{timelineStartInfo:{baseMediaDecodeTime:s.baseMediaDecodeTime},codec:"adts",type:"audio"},t.coalesceStream.numberOfTracks++,t.audioSegmentStream=new ct(a,n),t.audioSegmentStream.on("timingInfo",s.trigger.bind(s,"audioTimingInfo")),t.adtsStream.pipe(t.audioSegmentStream).pipe(t.coalesceStream)),s.trigger("trackinfo",{hasAudio:!!a,hasVideo:!!r})}),t.coalesceStream.on("data",this.trigger.bind(this,"data")),t.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setupTsPipeline=function(){var i={};(this.transmuxPipeline_=i).type="ts",i.metadataStream=new Xe.MetadataStream,i.packetStream=new Xe.TransportPacketStream,i.parseStream=new Xe.TransportParseStream,i.elementaryStream=new Xe.ElementaryStream,i.timestampRolloverStream=new Xe.TimestampRolloverStream,i.adtsStream=new et,i.h264Stream=new ft,i.captionStream=new Xe.CaptionStream,i.coalesceStream=new dt(n,i.metadataStream),i.headOfPipeline=i.packetStream,i.packetStream.pipe(i.parseStream).pipe(i.elementaryStream).pipe(i.timestampRolloverStream),i.timestampRolloverStream.pipe(i.h264Stream),i.timestampRolloverStream.pipe(i.adtsStream),i.timestampRolloverStream.pipe(i.metadataStream).pipe(i.coalesceStream),i.h264Stream.pipe(i.captionStream).pipe(i.coalesceStream),i.elementaryStream.on("data",function(e){var t;if("metadata"===e.type){for(t=e.tracks.length;t--;)r||"video"!==e.tracks[t].type?a||"audio"!==e.tracks[t].type||((a=e.tracks[t]).timelineStartInfo.baseMediaDecodeTime=s.baseMediaDecodeTime):(r=e.tracks[t]).timelineStartInfo.baseMediaDecodeTime=s.baseMediaDecodeTime;r&&!i.videoSegmentStream&&(i.coalesceStream.numberOfTracks++,i.videoSegmentStream=new lt(r,n),i.videoSegmentStream.on("timelineStartInfo",function(e){a&&(a.timelineStartInfo=e,i.audioSegmentStream.setEarliestDts(e.dts))}),i.videoSegmentStream.on("processedGopsInfo",s.trigger.bind(s,"gopInfo")),i.videoSegmentStream.on("segmentTimingInfo",s.trigger.bind(s,"videoSegmentTimingInfo")),i.videoSegmentStream.on("baseMediaDecodeTime",function(e){a&&i.audioSegmentStream.setVideoBaseMediaDecodeTime(e)}),i.videoSegmentStream.on("timingInfo",s.trigger.bind(s,"videoTimingInfo")),i.h264Stream.pipe(i.videoSegmentStream).pipe(i.coalesceStream)),a&&!i.audioSegmentStream&&(i.coalesceStream.numberOfTracks++,i.audioSegmentStream=new ct(a,n),i.audioSegmentStream.on("timingInfo",s.trigger.bind(s,"audioTimingInfo")),i.adtsStream.pipe(i.audioSegmentStream).pipe(i.coalesceStream)),s.trigger("trackinfo",{hasAudio:!!a,hasVideo:!!r})}}),i.coalesceStream.on("data",this.trigger.bind(this,"data")),i.coalesceStream.on("id3Frame",function(e){e.dispatchType=i.metadataStream.dispatchType,s.trigger("id3Frame",e)}),i.coalesceStream.on("caption",this.trigger.bind(this,"caption")),i.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setBaseMediaDecodeTime=function(e){var t=this.transmuxPipeline_;n.keepOriginalTimestamps||(this.baseMediaDecodeTime=e),a&&(a.timelineStartInfo.dts=void 0,a.timelineStartInfo.pts=void 0,ve(a),n.keepOriginalTimestamps||(a.timelineStartInfo.baseMediaDecodeTime=e),t.audioTimestampRolloverStream&&t.audioTimestampRolloverStream.discontinuity()),r&&(t.videoSegmentStream&&(t.videoSegmentStream.gopCache_=[]),r.timelineStartInfo.dts=void 0,r.timelineStartInfo.pts=void 0,ve(r),t.captionStream.reset(),n.keepOriginalTimestamps||(r.timelineStartInfo.baseMediaDecodeTime=e)),t.timestampRolloverStream&&t.timestampRolloverStream.discontinuity()},this.setAudioAppendStart=function(e){a&&this.transmuxPipeline_.audioSegmentStream.setAudioAppendStart(e)},this.setRemux=function(e){var t=this.transmuxPipeline_;n.remux=e,t&&t.coalesceStream&&t.coalesceStream.setRemux(e)},this.alignGopsWith=function(e){r&&this.transmuxPipeline_.videoSegmentStream&&this.transmuxPipeline_.videoSegmentStream.alignGopsWith(e)},this.push=function(e){if(i){var t=mt(e);t&&"aac"!==this.transmuxPipeline_.type?this.setupAacPipeline():t||"ts"===this.transmuxPipeline_.type||this.setupTsPipeline(),i=!1}this.transmuxPipeline_.headOfPipeline.push(e)},this.flush=function(){i=!0,this.transmuxPipeline_.headOfPipeline.flush()},this.endTimeline=function(){this.transmuxPipeline_.headOfPipeline.endTimeline()},this.reset=function(){this.transmuxPipeline_.headOfPipeline&&this.transmuxPipeline_.headOfPipeline.reset()},this.resetCaptions=function(){this.transmuxPipeline_.captionStream&&this.transmuxPipeline_.captionStream.reset()}}).prototype=new H;var _t={Transmuxer:ht,VideoSegmentStream:lt,AudioSegmentStream:ct,AUDIO_PROPERTIES:yt,VIDEO_PROPERTIES:vt,generateVideoSegmentTimingInfo:ut};function bt(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var Tt=(function(e,t,i){return t&&bt(e.prototype,t),i&&bt(e,i),e}(St,[{key:"init",value:function(){this.transmuxer&&this.transmuxer.dispose(),this.transmuxer=new _t.Transmuxer(this.options),function(n,e){e.on("data",function(e){var t=e.initSegment;e.initSegment={data:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength};var i=e.data;e.data=i.buffer,n.postMessage({action:"data",segment:e,byteOffset:i.byteOffset,byteLength:i.byteLength},[e.data])}),e.captionStream&&e.captionStream.on("data",function(e){n.postMessage({action:"caption",data:e})}),e.on("done",function(e){n.postMessage({action:"done"})}),e.on("gopInfo",function(e){n.postMessage({action:"gopInfo",gopInfo:e})}),e.on("videoSegmentTimingInfo",function(e){n.postMessage({action:"videoSegmentTimingInfo",videoSegmentTimingInfo:e})})}(this.self,this.transmuxer)}},{key:"push",value:function(e){var t=new Uint8Array(e.data,e.byteOffset,e.byteLength);this.transmuxer.push(t)}},{key:"reset",value:function(){this.init()}},{key:"setTimestampOffset",value:function(e){var t=e.timestampOffset||0;this.transmuxer.setBaseMediaDecodeTime(Math.round(9e4*t))}},{key:"setAudioAppendStart",value:function(e){this.transmuxer.setAudioAppendStart(Math.ceil(9e4*e.appendStart))}},{key:"flush",value:function(e){this.transmuxer.flush()}},{key:"resetCaptions",value:function(){this.transmuxer.resetCaptions()}},{key:"alignGopsWith",value:function(e){this.transmuxer.alignGopsWith(e.gopsToAlignWith.slice())}}]),St);function St(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,St),this.options=t||{},this.self=e,this.init()}new function(t){t.onmessage=function(e){"init"===e.data.action&&e.data.options?this.messageHandlers=new Tt(t,e.data.options):(this.messageHandlers||(this.messageHandlers=new Tt(t)),e.data&&e.data.action&&"init"!==e.data.action&&this.messageHandlers[e.data.action]&&this.messageHandlers[e.data.action](e.data))}}(kt)}()}),Jl={videoCodec:"avc1",videoObjectTypeIndicator:".4d400d",audioProfile:"2"},Zl=(ju(ec,fs.EventTarget),Nu(ec,[{key:"data_",value:function(e){var t=e.data.segment;t.data=new Uint8Array(t.data,e.data.byteOffset,e.data.byteLength),t.initSegment=new Uint8Array(t.initSegment.data,t.initSegment.byteOffset,t.initSegment.byteLength),function(e,t,i){var n=t.player_;if(i.captions&&i.captions.length)for(var r in e.inbandTextTracks_||(e.inbandTextTracks_={}),i.captionStreams)if(!e.inbandTextTracks_[r]){n.tech_.trigger({type:"usage",name:"hls-608"});var a=n.textTracks().getTrackById(r);e.inbandTextTracks_[r]=a||n.addRemoteTextTrack({kind:"captions",id:r,label:r},!1).track}i.metadata&&i.metadata.length&&!e.metadataTrack_&&(e.metadataTrack_=n.addRemoteTextTrack({kind:"metadata",label:"Timed Metadata"},!1).track,e.metadataTrack_.inBandMetadataTrackDispatchType=i.metadata.dispatchType)}(this,this.mediaSource_,t),this.pendingBuffers_.push(t)}},{key:"done_",value:function(e){"closed"!==this.mediaSource_.readyState?this.processPendingSegments_():this.pendingBuffers_.length=0}},{key:"videoSegmentTimingInfo_",value:function(e){var t={start:{decode:e.start.dts/9e4,presentation:e.start.pts/9e4},end:{decode:e.end.dts/9e4,presentation:e.end.pts/9e4},baseMediaDecodeTime:e.baseMediaDecodeTime/9e4};e.prependedContentDuration&&(t.prependedContentDuration=e.prependedContentDuration/9e4),this.trigger({type:"videoSegmentTimingInfo",videoSegmentTimingInfo:t})}},{key:"createRealSourceBuffers_",value:function(){var n=this,r=["audio","video"];r.forEach(function(t){if(n[t+"Codec_"]&&!n[t+"Buffer_"]){var i=null;if(n.mediaSource_[t+"Buffer_"])(i=n.mediaSource_[t+"Buffer_"]).updating=!1;else{var e=t+'/mp4;codecs="'+n[t+"Codec_"]+'"';i=function(e,t){var i=e.addSourceBuffer(t),n=Object.create(null);function r(t){"function"==typeof i[t]?n[t]=function(){return i[t].apply(i,arguments)}:"undefined"==typeof n[t]&&Object.defineProperty(n,t,{get:function(){return i[t]},set:function(e){return i[t]=e}})}for(var a in n.updating=!1,n.realBuffer_=i)r(a);return n}(n.mediaSource_.nativeMediaSource_,e),n.mediaSource_[t+"Buffer_"]=i}n[t+"Buffer_"]=i,["update","updatestart","updateend"].forEach(function(e){i.addEventListener(e,function(){if("audio"!==t||!n.audioDisabled_)return"updateend"===e&&(n[t+"Buffer_"].updating=!1),r.every(function(e){return!("audio"!==e||!n.audioDisabled_)||t===e||!n[e+"Buffer_"]||!n[e+"Buffer_"].updating})?n.trigger(e):void 0})})}})}},{key:"appendBuffer",value:function(e){if(this.bufferUpdating_=!0,this.audioBuffer_&&this.audioBuffer_.buffered.length){var t=this.audioBuffer_.buffered;this.transmuxer_.postMessage({action:"setAudioAppendStart",appendStart:t.end(t.length-1)})}this.videoBuffer_&&this.transmuxer_.postMessage({action:"alignGopsWith",gopsToAlignWith:function(e,t,i){if("undefined"==typeof t||null===t||!e.length)return[];var n=Math.ceil(9e4*(t-i+3)),r=void 0;for(r=0;r<e.length&&!(e[r].pts>n);r++);return e.slice(r)}(this.gopBuffer_,this.mediaSource_.player_?this.mediaSource_.player_.currentTime():null,this.timeMapping_)}),this.transmuxer_.postMessage({action:"push",data:e.buffer,byteOffset:e.byteOffset,byteLength:e.byteLength},[e.buffer]),this.transmuxer_.postMessage({action:"flush"})}},{key:"appendGopInfo_",value:function(e){this.gopBuffer_=function(e,t,i){if(!t.length)return e;if(i)return t.slice();for(var n=t[0].pts,r=0;r<e.length&&!(e[r].pts>=n);r++);return e.slice(0,r).concat(t)}(this.gopBuffer_,e.data.gopInfo,this.safeAppend_)}},{key:"remove",value:function(e,t){if(this.videoBuffer_&&(this.videoBuffer_.updating=!0,this.videoBuffer_.remove(e,t),this.gopBuffer_=function(e,t,i,n){for(var r=Math.ceil(9e4*(t-n)),a=Math.ceil(9e4*(i-n)),s=e.slice(),o=e.length;o--&&!(e[o].pts<=a););if(-1===o)return s;for(var u=o+1;u--&&!(e[u].pts<=r););return u=Math.max(u,0),s.splice(u,o-u+1),s}(this.gopBuffer_,e,t,this.timeMapping_)),!this.audioDisabled_&&this.audioBuffer_&&(this.audioBuffer_.updating=!0,this.audioBuffer_.remove(e,t)),Cl(e,t,this.metadataTrack_),this.inbandTextTracks_)for(var i in this.inbandTextTracks_)Cl(e,t,this.inbandTextTracks_[i])}},{key:"processPendingSegments_",value:function(){var e={video:{segments:[],bytes:0},audio:{segments:[],bytes:0},captions:[],metadata:[]};if(!this.pendingBuffers_.length)return this.trigger("updateend"),void(this.bufferUpdating_=!1);e=this.pendingBuffers_.reduce(function(e,t){var i=t.type,n=t.data,r=t.initSegment;return e[i].segments.push(n),e[i].bytes+=n.byteLength,e[i].initSegment=r,t.captions&&(e.captions=e.captions.concat(t.captions)),t.info&&(e[i].info=t.info),t.metadata&&(e.metadata=e.metadata.concat(t.metadata)),e},e),this.videoBuffer_||this.audioBuffer_||(0===e.video.bytes&&(this.videoCodec_=null),0===e.audio.bytes&&(this.audioCodec_=null),this.createRealSourceBuffers_()),e.audio.info&&this.mediaSource_.trigger({type:"audioinfo",info:e.audio.info}),e.video.info&&this.mediaSource_.trigger({type:"videoinfo",info:e.video.info}),this.appendAudioInitSegment_&&(!this.audioDisabled_&&this.audioBuffer_&&(e.audio.segments.unshift(e.audio.initSegment),e.audio.bytes+=e.audio.initSegment.byteLength),this.appendAudioInitSegment_=!1);var t=!1;this.videoBuffer_&&e.video.bytes?(e.video.segments.unshift(e.video.initSegment),e.video.bytes+=e.video.initSegment.byteLength,this.concatAndAppendSegments_(e.video,this.videoBuffer_)):!this.videoBuffer_||!this.audioDisabled_&&this.audioBuffer_||(t=!0),wl(this,e.captions,e.metadata),!this.audioDisabled_&&this.audioBuffer_&&this.concatAndAppendSegments_(e.audio,this.audioBuffer_),this.pendingBuffers_.length=0,t&&this.trigger("updateend"),this.bufferUpdating_=!1}},{key:"concatAndAppendSegments_",value:function(e,t){var i=0,n=void 0;if(e.bytes){n=new Uint8Array(e.bytes),e.segments.forEach(function(e){n.set(e,i),i+=e.byteLength});try{t.updating=!0,t.appendBuffer(n)}catch(e){this.mediaSource_.player_&&this.mediaSource_.player_.error({code:-3,type:"APPEND_BUFFER_ERR",message:e.message,originalError:e})}}}},{key:"abort",value:function(){this.videoBuffer_&&this.videoBuffer_.abort(),!this.audioDisabled_&&this.audioBuffer_&&this.audioBuffer_.abort(),this.transmuxer_&&this.transmuxer_.postMessage({action:"reset"}),this.pendingBuffers_.length=0,this.bufferUpdating_=!1}}]),ec);function ec(e,t){Mu(this,ec);var i=Fu(this,(ec.__proto__||Object.getPrototypeOf(ec)).call(this,fs.EventTarget));i.timestampOffset_=0,i.pendingBuffers_=[],i.bufferUpdating_=!1,i.mediaSource_=e,i.codecs_=t,i.audioCodec_=null,i.videoCodec_=null,i.audioDisabled_=!1,i.appendAudioInitSegment_=!0,i.gopBuffer_=[],i.timeMapping_=0,i.safeAppend_=11<=fs.browser.IE_VERSION;var n={remux:!1,alignGopsAtEnd:i.safeAppend_};return i.codecs_.forEach(function(e){Kl(e)?i.audioCodec_=e:Yl(e)&&(i.videoCodec_=e)}),i.transmuxer_=new Ql,i.transmuxer_.postMessage({action:"init",options:n}),i.transmuxer_.onmessage=function(e){return"data"===e.data.action?i.data_(e):"done"===e.data.action?i.done_(e):"gopInfo"===e.data.action?i.appendGopInfo_(e):"videoSegmentTimingInfo"===e.data.action?i.videoSegmentTimingInfo_(e.data.videoSegmentTimingInfo):void 0},Object.defineProperty(i,"timestampOffset",{get:function(){return this.timestampOffset_},set:function(e){"number"==typeof e&&0<=e&&(this.timestampOffset_=e,this.appendAudioInitSegment_=!0,this.gopBuffer_.length=0,this.timeMapping_=0,this.transmuxer_.postMessage({action:"setTimestampOffset",timestampOffset:e}))}}),Object.defineProperty(i,"appendWindowStart",{get:function(){return(this.videoBuffer_||this.audioBuffer_).appendWindowStart},set:function(e){this.videoBuffer_&&(this.videoBuffer_.appendWindowStart=e),this.audioBuffer_&&(this.audioBuffer_.appendWindowStart=e)}}),Object.defineProperty(i,"updating",{get:function(){return!!(this.bufferUpdating_||!this.audioDisabled_&&this.audioBuffer_&&this.audioBuffer_.updating||this.videoBuffer_&&this.videoBuffer_.updating)}}),Object.defineProperty(i,"buffered",{get:function(){return function(e,t,i){var n=null,r=null,a=0,s=[],o=[];if(!e&&!t)return fs.createTimeRange();if(!e)return t.buffered;if(!t)return e.buffered;if(i)return e.buffered;if(0===e.buffered.length&&0===t.buffered.length)return fs.createTimeRange();for(var u=e.buffered,l=t.buffered,c=u.length;c--;)s.push({time:u.start(c),type:"start"}),s.push({time:u.end(c),type:"end"});for(c=l.length;c--;)s.push({time:l.start(c),type:"start"}),s.push({time:l.end(c),type:"end"});for(s.sort(function(e,t){return e.time-t.time}),c=0;c<s.length;c++)"start"===s[c].type?2===++a&&(n=s[c].time):"end"===s[c].type&&1===--a&&(r=s[c].time),null!==n&&null!==r&&(o.push([n,r]),r=n=null);return fs.createTimeRanges(o)}(this.videoBuffer_,this.audioBuffer_,this.audioDisabled_)}}),i}var tc=(ju(ic,fs.EventTarget),Nu(ic,[{key:"addSeekableRange_",value:function(e,t){var i=void 0;if(this.duration!==1/0)throw(i=new Error("MediaSource.addSeekableRange() can only be invoked when the duration is Infinity")).name="InvalidStateError",i.code=11,i;(t>this.nativeMediaSource_.duration||isNaN(this.nativeMediaSource_.duration))&&(this.nativeMediaSource_.duration=t)}},{key:"addSourceBuffer",value:function(e){var t=void 0,i=function(e){var r={type:"",parameters:{}},t=e.trim().split(";");return r.type=t.shift().trim(),t.forEach(function(e){var t=e.trim().split("=");if(1<t.length){var i=t[0].replace(/"/g,"").trim(),n=t[1].replace(/"/g,"").trim();r.parameters[i]=n}}),r}(e);if(/^(video|audio)\/mp2t$/i.test(i.type)){var n=[];i.parameters&&i.parameters.codecs&&(n=i.parameters.codecs.split(","),n=(n=zl(n)).filter(function(e){return Kl(e)||Yl(e)})),0===n.length&&(n=["avc1.4d400d","mp4a.40.2"]),t=new Zl(this,n),0!==this.sourceBuffers.length&&(this.sourceBuffers[0].createRealSourceBuffers_(),t.createRealSourceBuffers_(),this.sourceBuffers[0].audioDisabled_=!0)}else t=this.nativeMediaSource_.addSourceBuffer(e);return this.sourceBuffers.push(t),t}}]),ic);function ic(){Mu(this,ic);var a=Fu(this,(ic.__proto__||Object.getPrototypeOf(ic)).call(this)),e=void 0;for(e in a.nativeMediaSource_=new v.MediaSource,a.nativeMediaSource_)e in ic.prototype||"function"!=typeof a.nativeMediaSource_[e]||(a[e]=a.nativeMediaSource_[e].bind(a.nativeMediaSource_));return a.duration_=NaN,Object.defineProperty(a,"duration",{get:function(){return this.duration_===1/0?this.duration_:this.nativeMediaSource_.duration},set:function(e){(this.duration_=e)===1/0||(this.nativeMediaSource_.duration=e)}}),Object.defineProperty(a,"seekable",{get:function(){return this.duration_===1/0?fs.createTimeRanges([[0,this.nativeMediaSource_.duration]]):this.nativeMediaSource_.seekable}}),Object.defineProperty(a,"readyState",{get:function(){return this.nativeMediaSource_.readyState}}),Object.defineProperty(a,"activeSourceBuffers",{get:function(){return this.activeSourceBuffers_}}),a.sourceBuffers=[],a.activeSourceBuffers_=[],a.updateActiveSourceBuffers_=function(){if(a.activeSourceBuffers_.length=0,1===a.sourceBuffers.length){var e=a.sourceBuffers[0];return e.appendAudioInitSegment_=!0,e.audioDisabled_=!e.audioCodec_,void a.activeSourceBuffers_.push(e)}for(var i=!1,n=!0,t=0;t<a.player_.audioTracks().length;t++){var r=a.player_.audioTracks()[t];if(r.enabled&&"main"!==r.kind){n=!(i=!0);break}}a.sourceBuffers.forEach(function(e,t){if(e.appendAudioInitSegment_=!0,e.videoCodec_&&e.audioCodec_)e.audioDisabled_=i;else if(e.videoCodec_&&!e.audioCodec_)e.audioDisabled_=!0,n=!1;else if(!e.videoCodec_&&e.audioCodec_&&(e.audioDisabled_=t?n:!n,e.audioDisabled_))return;a.activeSourceBuffers_.push(e)})},a.onPlayerMediachange_=function(){a.sourceBuffers.forEach(function(e){e.appendAudioInitSegment_=!0})},a.onHlsReset_=function(){a.sourceBuffers.forEach(function(e){e.transmuxer_&&e.transmuxer_.postMessage({action:"resetCaptions"})})},a.onHlsSegmentTimeMapping_=function(t){a.sourceBuffers.forEach(function(e){return e.timeMapping_=t.mapping})},["sourceopen","sourceclose","sourceended"].forEach(function(e){this.nativeMediaSource_.addEventListener(e,this.trigger.bind(this))},a),a.on("sourceopen",function(e){var t=h.querySelector('[src="'+a.url_+'"]');t&&(a.player_=fs(t.parentNode),a.player_&&(a.player_.tech_.on("hls-reset",a.onHlsReset_),a.player_.tech_.on("hls-segment-time-mapping",a.onHlsSegmentTimeMapping_),a.player_.audioTracks&&a.player_.audioTracks()&&(a.player_.audioTracks().on("change",a.updateActiveSourceBuffers_),a.player_.audioTracks().on("addtrack",a.updateActiveSourceBuffers_),a.player_.audioTracks().on("removetrack",a.updateActiveSourceBuffers_)),a.player_.on("mediachange",a.onPlayerMediachange_)))}),a.on("sourceended",function(e){for(var t=El(a.duration),i=0;i<a.sourceBuffers.length;i++){var n=a.sourceBuffers[i],r=n.metadataTrack_&&n.metadataTrack_.cues;r&&r.length&&(r[r.length-1].endTime=t)}}),a.on("sourceclose",function(e){this.sourceBuffers.forEach(function(e){e.transmuxer_&&e.transmuxer_.terminate()}),this.sourceBuffers.length=0,this.player_&&(this.player_.audioTracks&&this.player_.audioTracks()&&(this.player_.audioTracks().off("change",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("addtrack",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("removetrack",this.updateActiveSourceBuffers_)),this.player_.el_&&this.player_.off("mediachange",this.onPlayerMediachange_),this.player_.tech_&&this.player_.tech_.el_&&(this.player_.tech_.off("hls-reset",this.onHlsReset_),this.player_.tech_.off("hls-segment-time-mapping",this.onHlsSegmentTimeMapping_)))}),a}var nc=0;fs.mediaSources={};function rc(e,t){var i=fs.mediaSources[e];if(!i)throw new Error("Media Source not found (Video.js)");i.trigger({type:"sourceopen",swfId:t})}function ac(){return!!v.MediaSource&&!!v.MediaSource.isTypeSupported&&v.MediaSource.isTypeSupported('video/mp4;codecs="avc1.4d400d,mp4a.40.2"')}function sc(){if(this.MediaSource={open:rc,supportsNativeMediaSources:ac},ac())return new tc;throw new Error("Cannot use create a virtual MediaSource for this video")}sc.open=rc,sc.supportsNativeMediaSources=ac;var oc={createObjectURL:function(e){var t=void 0;return e instanceof tc?(t=v.URL.createObjectURL(e.nativeMediaSource_),e.url_=t):e instanceof tc?(t="blob:vjs-media-source/"+nc,nc++,fs.mediaSources[t]=e,t):(t=v.URL.createObjectURL(e),e.url_=t)}};fs.MediaSource=sc,fs.URL=oc;function uc(e,t){for(var s=void 0,o=dc(e,{duration:t.duration,minimumUpdatePeriod:t.minimumUpdatePeriod}),i=0;i<t.playlists.length;i++){var n=Vu(o,t.playlists[i]);n?o=n:s=!0}return Hu(t,function(e,t,i,n){if(e.playlists&&e.playlists.length){var r=e.playlists[0].uri,a=Vu(o,e.playlists[0]);a&&((o=a).mediaGroups[t][i][n].playlists[0]=o.playlists[r],s=!1)}}),s?null:o}function lc(e){var t=e.byterange.offset+e.byterange.length-1;return e.uri+"-"+e.byterange.offset+"-"+t}function cc(e,t){var i,n,r={};for(var a in e){var s=e[a].sidx;if(s){var o=lc(s);if(!t[o])break;var u=t[o].sidxInfo;i=u,n=s,(Boolean(!i.map&&!n.map)||Boolean(i.map&&n.map&&i.map.byterange.offset===n.map.byterange.offset&&i.map.byterange.length===n.map.byterange.length))&&i.uri===n.uri&&i.byterange.offset===n.byterange.offset&&i.byterange.length===n.byterange.length&&(r[o]=t[o])}}return r}function hc(e,t,i,n,r){var a={uri:Ru(n.handleManifestRedirects,e.resolvedUri),byterange:e.byterange,playlist:t};return i(fs.mergeOptions(a,{responseType:"arraybuffer",headers:cl(a)}),r)}var dc=fs.mergeOptions,pc=(ju(fc,fs.EventTarget),Nu(fc,[{key:"setupChildLoader",value:function(e,t){this.masterPlaylistLoader_=e,this.childPlaylist_=t}},{key:"dispose",value:function(){this.stopRequest(),this.loadedPlaylists_={},v.clearTimeout(this.minimumUpdatePeriodTimeout_),v.clearTimeout(this.mediaRequest_),v.clearTimeout(this.mediaUpdateTimeout)}},{key:"hasPendingRequest",value:function(){return this.request||this.mediaRequest_}},{key:"stopRequest",value:function(){if(this.request){var e=this.request;this.request=null,e.onreadystatechange=null,e.abort()}}},{key:"sidxRequestFinished_",value:function(r,a,s,o){var u=this;return function(e,t){if(u.request){if(u.request=null,e)return u.error={status:t.status,message:"DASH playlist request error at URL: "+r.uri,response:t.response,code:2},s&&(u.state=s),u.trigger("error"),o(a,null);var i=new Uint8Array(t.response),n=Lo.parseSidx(i.subarray(8));return o(a,n)}}}},{key:"media",value:function(i){var n=this;if(!i)return this.media_;if("HAVE_NOTHING"===this.state)throw new Error("Cannot switch media playlist from "+this.state);var r=this.state;if("string"==typeof i){if(!this.master.playlists[i])throw new Error("Unknown playlist URI: "+i);i=this.master.playlists[i]}var e=!this.media_||i.uri!==this.media_.uri;if(e&&this.loadedPlaylists_[i.uri]&&this.loadedPlaylists_[i.uri].endList)return this.state="HAVE_METADATA",this.media_=i,void(e&&(this.trigger("mediachanging"),this.trigger("mediachange")));if(e)if(this.media_&&this.trigger("mediachanging"),i.sidx){var t=void 0,a=void 0;a=this.masterPlaylistLoader_?(t=this.masterPlaylistLoader_.master,this.masterPlaylistLoader_.sidxMapping_):(t=this.master,this.sidxMapping_);var s=lc(i.sidx);a[s]={sidxInfo:i.sidx},this.request=hc(i.sidx,i,this.hls_.xhr,{handleManifestRedirects:this.handleManifestRedirects},this.sidxRequestFinished_(i,t,r,function(e,t){if(!e||!t)throw new Error("failed to request sidx");a[s].sidx=t,n.haveMetadata({startingState:r,playlist:e.playlists[i.uri]})}))}else this.mediaRequest_=v.setTimeout(this.haveMetadata.bind(this,{startingState:r,playlist:i}),0)}},{key:"haveMetadata",value:function(e){var t=e.startingState,i=e.playlist;this.state="HAVE_METADATA",this.loadedPlaylists_[i.uri]=i,this.mediaRequest_=null,this.refreshMedia_(i.uri),"HAVE_MASTER"===t?this.trigger("loadedmetadata"):this.trigger("mediachange")}},{key:"pause",value:function(){this.stopRequest(),v.clearTimeout(this.mediaUpdateTimeout),v.clearTimeout(this.minimumUpdatePeriodTimeout_),"HAVE_NOTHING"===this.state&&(this.started=!1)}},{key:"load",value:function(e){var t=this;v.clearTimeout(this.mediaUpdateTimeout),v.clearTimeout(this.minimumUpdatePeriodTimeout_);var i=this.media();if(e){var n=i?i.targetDuration/2*1e3:5e3;this.mediaUpdateTimeout=v.setTimeout(function(){return t.load()},n)}else this.started?this.trigger("loadedplaylist"):this.start()}},{key:"parseMasterXml",value:function(){var a=co(this.masterXml_,{manifestUri:this.srcUrl,clientOffset:this.clientOffset_,sidxMapping:this.sidxMapping_});a.uri=this.srcUrl;for(var e=0;e<a.playlists.length;e++){var t="placeholder-uri-"+e;a.playlists[e].uri=t,a.playlists[t]=a.playlists[e]}return Hu(a,function(e,t,i,n){if(e.playlists&&e.playlists.length){var r="placeholder-uri-"+t+"-"+i+"-"+n;e.playlists[0].uri=r,a.playlists[r]=e.playlists[0]}}),qu(a),Wu(a),a}},{key:"start",value:function(){var i=this;this.started=!0,this.masterPlaylistLoader_?this.mediaRequest_=v.setTimeout(this.haveMaster_.bind(this),0):this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(e,t){if(i.request){if(i.request=null,e)return i.error={status:t.status,message:"DASH playlist request error at URL: "+i.srcUrl,responseText:t.responseText,code:2},"HAVE_NOTHING"===i.state&&(i.started=!1),i.trigger("error");i.masterXml_=t.responseText,t.responseHeaders&&t.responseHeaders.date?i.masterLoaded_=Date.parse(t.responseHeaders.date):i.masterLoaded_=Date.now(),i.srcUrl=Ru(i.handleManifestRedirects,i.srcUrl,t),i.syncClientServerClock_(i.onClientServerClockSync_.bind(i))}})}},{key:"syncClientServerClock_",value:function(n){var r=this,a=ho(this.masterXml_);return null===a?(this.clientOffset_=this.masterLoaded_-Date.now(),n()):"DIRECT"===a.method?(this.clientOffset_=a.value-Date.now(),n()):void(this.request=this.hls_.xhr({uri:Uu(this.srcUrl,a.value),method:a.method,withCredentials:this.withCredentials},function(e,t){if(r.request){if(e)return r.clientOffset_=r.masterLoaded_-Date.now(),n();var i=void 0;i="HEAD"===a.method?t.responseHeaders&&t.responseHeaders.date?Date.parse(t.responseHeaders.date):r.masterLoaded_:Date.parse(t.responseText),r.clientOffset_=i-Date.now(),n()}}))}},{key:"haveMaster_",value:function(){this.state="HAVE_MASTER",this.mediaRequest_=null,this.masterPlaylistLoader_?this.media_||this.media(this.childPlaylist_):(this.master=this.parseMasterXml(),this.trigger("loadedplaylist"))}},{key:"onClientServerClockSync_",value:function(){var e=this;this.haveMaster_(),this.hasPendingRequest()||this.media_||this.media(this.master.playlists[0]),this.master&&this.master.minimumUpdatePeriod&&(this.minimumUpdatePeriodTimeout_=v.setTimeout(function(){e.trigger("minimumUpdatePeriod")},this.master.minimumUpdatePeriod))}},{key:"refreshXml_",value:function(){var o=this;this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(e,t){if(o.request){if(o.request=null,e)return o.error={status:t.status,message:"DASH playlist request error at URL: "+o.srcUrl,responseText:t.responseText,code:2},"HAVE_NOTHING"===o.state&&(o.started=!1),o.trigger("error");o.masterXml_=t.responseText,o.sidxMapping_=function(e,t,i,a){var n=co(e,{manifestUri:t,clientOffset:i}),s=cc(n.playlists,a);return Hu(n,function(e,t,i,n){if(e.playlists&&e.playlists.length){var r=e.playlists;s=dc(s,cc(r,a))}}),s}(o.masterXml_,o.srcUrl,o.clientOffset_,o.sidxMapping_);var i=o.parseMasterXml(),n=uc(o.master,i),r=o.media().sidx;if(n)if(r){var a=lc(r);if(!o.sidxMapping_[a]){var s=o.media();o.request=hc(s.sidx,s,o.hls_.xhr,{handleManifestRedirects:o.handleManifestRedirects},o.sidxRequestFinished_(s,i,o.state,function(e,t){if(!e||!t)throw new Error("failed to request sidx on minimumUpdatePeriod");o.sidxMapping_[a].sidx=t,o.minimumUpdatePeriodTimeout_=v.setTimeout(function(){o.trigger("minimumUpdatePeriod")},o.master.minimumUpdatePeriod),o.refreshMedia_(o.media().uri)}))}}else o.master=n;o.minimumUpdatePeriodTimeout_=v.setTimeout(function(){o.trigger("minimumUpdatePeriod")},o.master.minimumUpdatePeriod)}})}},{key:"refreshMedia_",value:function(e){var t=this;if(!e)throw new Error("refreshMedia_ must take a media uri");var i=void 0,n=void 0;n=this.masterPlaylistLoader_?(i=this.masterPlaylistLoader_.master,this.masterPlaylistLoader_.parseMasterXml()):(i=this.master,this.parseMasterXml());var r=uc(i,n);r?(this.masterPlaylistLoader_?this.masterPlaylistLoader_.master=r:this.master=r,this.media_=r.playlists[e]):(this.media_=n.playlists[e],this.trigger("playlistunchanged")),this.media().endList||(this.mediaUpdateTimeout=v.setTimeout(function(){t.trigger("mediaupdatetimeout")},zu(this.media(),!!r))),this.trigger("loadedplaylist")}}]),fc);function fc(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},n=arguments[3];Mu(this,fc);var r=Fu(this,(fc.__proto__||Object.getPrototypeOf(fc)).call(this)),a=i.withCredentials,s=void 0!==a&&a,o=i.handleManifestRedirects,u=void 0!==o&&o;if(r.hls_=t,r.withCredentials=s,r.handleManifestRedirects=u,!e)throw new Error("A non-empty playlist URL or playlist is required");return r.on("minimumUpdatePeriod",function(){r.refreshXml_()}),r.on("mediaupdatetimeout",function(){r.refreshMedia_(r.media().uri)}),r.state="HAVE_NOTHING",r.loadedPlaylists_={},"string"==typeof e?(r.srcUrl=e,r.sidxMapping_={},Fu(r)):(r.setupChildLoader(n,e),r)}function mc(e){return fs.log.debug?fs.log.debug.bind(fs,"VHS:",e+" >"):function(){}}function gc(){}var yc=(Nu(vc,[{key:"createSourceBuffer_",value:function(e,t){var i=this;this.sourceBuffer_=this.mediaSource.addSourceBuffer(e),this.logger_("created SourceBuffer"),t&&(t.trigger("sourcebufferadded"),this.mediaSource.sourceBuffers.length<2)?t.on("sourcebufferadded",function(){i.start_()}):this.start_()}},{key:"start_",value:function(){var t=this;this.started_=!0,this.onUpdateendCallback_=function(){var e=t.pendingCallback_;t.pendingCallback_=null,t.sourceBuffer_.removing=!1,t.logger_("buffered ["+Sl(t.buffered())+"]"),e&&e(),t.runCallback_()},this.sourceBuffer_.addEventListener("updateend",this.onUpdateendCallback_),this.runCallback_()}},{key:"abort",value:function(e){var t=this;this.processedAppend_&&this.queueCallback_(function(){t.sourceBuffer_.abort()},e)}},{key:"appendBuffer",value:function(e,t){var i=this;this.processedAppend_=!0,this.queueCallback_(function(){e.videoSegmentTimingInfoCallback&&i.sourceBuffer_.addEventListener("videoSegmentTimingInfo",e.videoSegmentTimingInfoCallback),i.sourceBuffer_.appendBuffer(e.bytes)},function(){e.videoSegmentTimingInfoCallback&&i.sourceBuffer_.removeEventListener("videoSegmentTimingInfo",e.videoSegmentTimingInfoCallback),t()})}},{key:"buffered",value:function(){return this.sourceBuffer_?this.sourceBuffer_.buffered:fs.createTimeRanges()}},{key:"remove",value:function(e,t,i){var n=this,r=2<arguments.length&&void 0!==i?i:gc;this.processedAppend_&&this.queueCallback_(function(){n.logger_("remove ["+e+" => "+t+"]"),n.sourceBuffer_.removing=!0,n.sourceBuffer_.remove(e,t)},r)}},{key:"updating",value:function(){return!this.sourceBuffer_||this.sourceBuffer_.updating||!!this.pendingCallback_&&this.pendingCallback_!==gc}},{key:"timestampOffset",value:function(e){var t=this;return"undefined"!=typeof e&&(this.queueCallback_(function(){t.sourceBuffer_.timestampOffset=e,t.runCallback_()}),this.timestampOffset_=e),this.timestampOffset_}},{key:"queueCallback_",value:function(e,t){this.callbacks_.push([e.bind(this),t]),this.runCallback_()}},{key:"runCallback_",value:function(){var e=void 0;!this.updating()&&this.callbacks_.length&&this.started_&&(e=this.callbacks_.shift(),this.pendingCallback_=e[1],e[0]())}},{key:"dispose",value:function(){function e(){t.sourceBuffer_&&"open"===t.mediaSource.readyState&&t.sourceBuffer_.abort(),t.sourceBuffer_.removeEventListener("updateend",e)}var t=this;this.sourceBuffer_.removeEventListener("updateend",this.onUpdateendCallback_),this.sourceBuffer_.removing?this.sourceBuffer_.addEventListener("updateend",e):e()}}]),vc);function vc(e,t,i,n){Mu(this,vc),this.callbacks_=[],this.pendingCallback_=null,this.timestampOffset_=0,this.mediaSource=e,this.processedAppend_=!1,this.type_=i,this.mimeType_=t,this.logger_=mc("SourceUpdater["+i+"]["+t+"]"),"closed"===e.readyState?e.addEventListener("sourceopen",this.createSourceBuffer_.bind(this,t,n)):this.createSourceBuffer_(t,n)}function _c(e){e.forEach(function(e){e.abort()})}function bc(e,t){return t.timedout?{status:t.status,message:"HLS request timed-out at URL: "+t.uri,code:Lc,xhr:t}:t.aborted?{status:t.status,message:"HLS request aborted at URL: "+t.uri,code:Oc,xhr:t}:e?{status:t.status,message:"HLS request errored at URL: "+t.uri,code:Ic,xhr:t}:null}function Tc(i,n,r){var a=0,s=!1;return function(e,t){if(!s)return e?(s=!0,_c(i),r(e,t)):(a+=1)===i.length?(t.endOfAllRequests=Date.now(),t.encryptedBytes?function(n,r,a){n.addEventListener("message",function e(t){if(t.data.source===r.requestId){n.removeEventListener("message",e);var i=t.data.decrypted;return r.bytes=new Uint8Array(i.bytes,i.byteOffset,i.byteLength),a(null,r)}});var e=void 0;e=r.key.bytes.slice?r.key.bytes.slice():new Uint32Array(Array.prototype.slice.call(r.key.bytes)),n.postMessage(pl({source:r.requestId,encrypted:r.encryptedBytes,key:e,iv:r.key.iv}),[r.encryptedBytes.buffer,e.buffer])}(n,t,r):r(null,t)):void 0}}function Sc(t,i){return function(e){return t.stats=fs.mergeOptions(t.stats,function(e){var t=e.target,i={bandwidth:1/0,bytesReceived:0,roundTripTime:Date.now()-t.requestTime||0};return i.bytesReceived=e.loaded,i.bandwidth=Math.floor(i.bytesReceived/i.roundTripTime*8*1e3),i}(e)),!t.stats.firstBytesReceivedAt&&t.stats.bytesReceived&&(t.stats.firstBytesReceivedAt=Date.now()),i(e,t)}}function kc(e,t,i,n,r,a,s){var o=[],u=Tc(o,i,s);if(r.key&&!r.key.bytes){var l=e(fs.mergeOptions(t,{uri:r.key.resolvedUri,responseType:"arraybuffer"}),function(a,s){return function(e,t){var i=t.response,n=bc(e,t);if(n)return s(n,a);if(16!==i.byteLength)return s({status:t.status,message:"Invalid HLS key at URL: "+t.uri,code:Ic,xhr:t},a);var r=new DataView(i);return a.key.bytes=new Uint32Array([r.getUint32(0),r.getUint32(4),r.getUint32(8),r.getUint32(12)]),s(null,a)}}(r,u));o.push(l)}if(r.map&&!r.map.bytes){var c=e(fs.mergeOptions(t,{uri:r.map.resolvedUri,responseType:"arraybuffer",headers:cl(r.map)}),function(r,a,s){return function(e,t){var i=t.response,n=bc(e,t);return n?s(n,r):0===i.byteLength?s({status:t.status,message:"Empty HLS segment content at URL: "+t.uri,code:Ic,xhr:t},r):(r.map.bytes=new Uint8Array(t.response),a&&!a.isInitialized()&&a.init(),r.map.timescales=Ao.timescale(r.map.bytes),r.map.videoTrackIds=Ao.videoTrackIds(r.map.bytes),s(null,r))}}(r,n,u));o.push(c)}var h=e(fs.mergeOptions(t,{uri:r.resolvedUri,responseType:"arraybuffer",headers:cl(r)}),function(a,s,o){return function(e,t){var i=t.response,n=bc(e,t),r=void 0;return n?o(n,a):0===i.byteLength?o({status:t.status,message:"Empty HLS segment content at URL: "+t.uri,code:Ic,xhr:t},a):(a.stats=function(e){return{bandwidth:e.bandwidth,bytesReceived:e.bytesReceived||0,roundTripTime:e.roundTripTime||0}}(t),a.key?a.encryptedBytes=new Uint8Array(t.response):a.bytes=new Uint8Array(t.response),s&&a.map&&a.map.bytes&&(s.isInitialized()||s.init(),(r=s.parse(a.bytes,a.map.videoTrackIds,a.map.timescales))&&r.captions&&(a.captionStreams=r.captionStreams,a.fmp4Captions=r.captions)),o(null,a))}}(r,n,u));return h.addEventListener("progress",Sc(r,a)),o.push(h),function(){return _c(o)}}function Cc(e,t){var i;return e&&(i=v.getComputedStyle(e))?i[t]:""}function Ec(e,n){var r=e.slice();e.sort(function(e,t){var i=n(e,t);return 0===i?r.indexOf(e)-r.indexOf(t):i})}function wc(e,t){var i=void 0,n=void 0;return e.attributes.BANDWIDTH&&(i=e.attributes.BANDWIDTH),i=i||v.Number.MAX_VALUE,t.attributes.BANDWIDTH&&(n=t.attributes.BANDWIDTH),i-(n=n||v.Number.MAX_VALUE)}function Ac(e){return"number"==typeof e&&isFinite(e)}var Pc={GOAL_BUFFER_LENGTH:30,MAX_GOAL_BUFFER_LENGTH:60,GOAL_BUFFER_LENGTH_RATE:1,INITIAL_BANDWIDTH:4194304,BANDWIDTH_VARIANCE:1.2,BUFFER_LOW_WATER_LINE:0,MAX_BUFFER_LOW_WATER_LINE:30,BUFFER_LOW_WATER_LINE_RATE:1},Ic=2,Lc=-101,Oc=-102,xc=(ju(Dc,fs.EventTarget),Nu(Dc,[{key:"resetStats_",value:function(){this.mediaBytesTransferred=0,this.mediaRequests=0,this.mediaRequestsAborted=0,this.mediaRequestsTimedout=0,this.mediaRequestsErrored=0,this.mediaTransferDuration=0,this.mediaSecondsLoaded=0}},{key:"dispose",value:function(){this.state="DISPOSED",this.pause(),this.abort_(),this.sourceUpdater_&&this.sourceUpdater_.dispose(),this.resetStats_(),this.captionParser_&&this.captionParser_.reset()}},{key:"abort",value:function(){"WAITING"===this.state?(this.abort_(),this.state="READY",this.paused()||this.monitorBuffer_()):this.pendingSegment_&&(this.pendingSegment_=null)}},{key:"abort_",value:function(){this.pendingSegment_&&this.pendingSegment_.abortRequests(),this.pendingSegment_=null}},{key:"error",value:function(e){return"undefined"!=typeof e&&(this.error_=e),this.pendingSegment_=null,this.error_}},{key:"endOfStream",value:function(){this.ended_=!0,this.pause(),this.trigger("ended")}},{key:"buffered_",value:function(){return this.sourceUpdater_?this.sourceUpdater_.buffered():fs.createTimeRanges()}},{key:"initSegment",value:function(e,t){var i=1<arguments.length&&void 0!==t&&t;if(!e)return null;var n=fl(e),r=this.initSegments_[n];return i&&!r&&e.bytes&&(this.initSegments_[n]=r={resolvedUri:e.resolvedUri,byterange:e.byterange,bytes:e.bytes,timescales:e.timescales,videoTrackIds:e.videoTrackIds}),r||e}},{key:"segmentKey",value:function(e,t){var i=1<arguments.length&&void 0!==t&&t;if(!e)return null;var n=ml(e),r=this.keyCache_[n];this.cacheEncryptionKeys_&&i&&!r&&e.bytes&&(this.keyCache_[n]=r={resolvedUri:e.resolvedUri,bytes:e.bytes});var a={resolvedUri:(r||e).resolvedUri};return r&&(a.bytes=r.bytes),a}},{key:"couldBeginLoading_",value:function(){return this.playlist_&&(this.sourceUpdater_||this.mimeType_&&"INIT"===this.state)&&!this.paused()}},{key:"load",value:function(){if(this.monitorBuffer_(),this.playlist_){if(this.syncController_.setDateTimeMapping(this.playlist_),"INIT"===this.state&&this.couldBeginLoading_())return this.init_();!this.couldBeginLoading_()||"READY"!==this.state&&"INIT"!==this.state||(this.state="READY")}}},{key:"init_",value:function(){return this.state="READY",this.sourceUpdater_=new yc(this.mediaSource_,this.mimeType_,this.loaderType_,this.sourceBufferEmitter_),this.resetEverything(),this.monitorBuffer_()}},{key:"playlist",value:function(e,t){var i=1<arguments.length&&void 0!==t?t:{};if(e){var n=this.playlist_,r=this.pendingSegment_;this.playlist_=e,this.xhrOptions_=i,this.hasPlayed_()||(e.syncInfo={mediaSequence:e.mediaSequence,time:0});var a=null;if(n&&(n.id?a=n.id:n.uri&&(a=n.uri)),this.logger_("playlist update ["+a+" => "+(e.id||e.uri)+"]"),this.trigger("syncinfoupdate"),"INIT"===this.state&&this.couldBeginLoading_())return this.init_();if(n&&n.uri===e.uri){var s=e.mediaSequence-n.mediaSequence;this.logger_("live window shift ["+s+"]"),null!==this.mediaIndex&&(this.mediaIndex-=s),r&&(r.mediaIndex-=s,0<=r.mediaIndex&&(r.segment=e.segments[r.mediaIndex])),this.syncController_.saveExpiredSegmentInfo(n,e)}else null!==this.mediaIndex&&this.resyncLoader()}}},{key:"pause",value:function(){this.checkBufferTimeout_&&(v.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=null)}},{key:"paused",value:function(){return null===this.checkBufferTimeout_}},{key:"mimeType",value:function(e,t){this.mimeType_||(this.mimeType_=e,this.sourceBufferEmitter_=t,"INIT"===this.state&&this.couldBeginLoading_()&&this.init_())}},{key:"resetEverything",value:function(e){this.ended_=!1,this.resetLoader(),this.remove(0,this.duration_(),e),this.captionParser_&&this.captionParser_.clearAllCaptions(),this.trigger("reseteverything")}},{key:"resetLoader",value:function(){this.fetchAtBuffer_=!1,this.resyncLoader()}},{key:"resyncLoader",value:function(){this.mediaIndex=null,this.syncPoint_=null,this.abort()}},{key:"remove",value:function(e,t,i){if(this.sourceUpdater_&&this.sourceUpdater_.remove(e,t,i),Cl(e,t,this.segmentMetadataTrack_),this.inbandTextTracks_)for(var n in this.inbandTextTracks_)Cl(e,t,this.inbandTextTracks_[n])}},{key:"monitorBuffer_",value:function(){this.checkBufferTimeout_&&v.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=v.setTimeout(this.monitorBufferTick_.bind(this),1)}},{key:"monitorBufferTick_",value:function(){"READY"===this.state&&this.fillBuffer_(),this.checkBufferTimeout_&&v.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=v.setTimeout(this.monitorBufferTick_.bind(this),500)}},{key:"fillBuffer_",value:function(){if(!this.sourceUpdater_.updating()){this.syncPoint_||(this.syncPoint_=this.syncController_.getSyncPoint(this.playlist_,this.duration_(),this.currentTimeline_,this.currentTime_()));var e=this.checkBuffer_(this.buffered_(),this.playlist_,this.mediaIndex,this.hasPlayed_(),this.currentTime_(),this.syncPoint_);e&&(this.isEndOfStream_(e.mediaIndex)?this.endOfStream():e.mediaIndex===this.playlist_.segments.length-1&&"ended"===this.mediaSource_.readyState&&!this.seeking_()||(e.timeline!==this.currentTimeline_&&(this.syncController_.reset(),e.timestampOffset=e.startOfSegment,this.captionParser_&&this.captionParser_.clearAllCaptions()),this.loadSegment_(e)))}}},{key:"isEndOfStream_",value:function(e,t){return function(e,t,i){if(!e||!t)return!1;var n=i===e.segments.length;return e.endList&&"open"===t.readyState&&n}(1<arguments.length&&void 0!==t?t:this.playlist_,this.mediaSource_,e)&&!this.sourceUpdater_.updating()}},{key:"checkBuffer_",value:function(e,t,i,n,r,a){var s=0,o=void 0;e.length&&(s=e.end(e.length-1));var u=Math.max(0,s-r);if(!t.segments.length)return null;if(u>=this.goalBufferLength_())return null;if(!n&&1<=u)return null;if(null===a)return i=this.getSyncSegmentCandidate_(t),this.generateSegmentInfo_(t,i,null,!0);if(null!==i)return t.segments[i],o=s,this.generateSegmentInfo_(t,i+1,o,!1);if(this.fetchAtBuffer_){var l=Ll.getMediaInfoForTime(t,s,a.segmentIndex,a.time);i=l.mediaIndex,o=l.startTime}else{var c=Ll.getMediaInfoForTime(t,r,a.segmentIndex,a.time);i=c.mediaIndex,o=c.startTime}return this.generateSegmentInfo_(t,i,o,!1)}},{key:"getSyncSegmentCandidate_",value:function(e){var t=this;if(-1===this.currentTimeline_)return 0;var i=e.segments.map(function(e,t){return{timeline:e.timeline,segmentIndex:t}}).filter(function(e){return e.timeline===t.currentTimeline_});return i.length?i[Math.min(i.length-1,1)].segmentIndex:Math.max(e.segments.length-1,0)}},{key:"generateSegmentInfo_",value:function(e,t,i,n){if(t<0||t>=e.segments.length)return null;var r=e.segments[t];return{requestId:"segment-loader-"+Math.random(),uri:r.resolvedUri,mediaIndex:t,isSyncRequest:n,startOfSegment:i,playlist:e,bytes:null,encryptedBytes:null,timestampOffset:null,timeline:r.timeline,duration:r.duration,segment:r}}},{key:"abortRequestEarly_",value:function(e){if(this.hls_.tech_.paused()||!this.xhrOptions_.timeout||!this.playlist_.attributes.BANDWIDTH)return!1;if(Date.now()-(e.firstBytesReceivedAt||Date.now())<1e3)return!1;var t=this.currentTime_(),i=e.bandwidth,n=this.pendingSegment_.duration,r=Ll.estimateSegmentRequestTime(n,i,this.playlist_,e.bytesReceived),a=function(e,t,i){var n=2<arguments.length&&void 0!==i?i:1;return((e.length?e.end(e.length-1):0)-t)/n}(this.buffered_(),t,this.hls_.tech_.playbackRate())-1;if(r<=a)return!1;var s=function(e){var t=e.master,i=e.currentTime,n=e.bandwidth,r=e.duration,a=e.segmentDuration,s=e.timeUntilRebuffer,o=e.currentTimeline,u=e.syncController,l=t.playlists.filter(function(e){return!Ll.isIncompatible(e)}),c=l.filter(Ll.isEnabled);c.length||(c=l.filter(function(e){return!Ll.isDisabled(e)}));var h=c.filter(Ll.hasAttribute.bind(null,"BANDWIDTH")).map(function(e){var t=u.getSyncPoint(e,r,o,i)?1:2;return{playlist:e,rebufferingImpact:Ll.estimateSegmentRequestTime(a,n,e)*t-s}}),d=h.filter(function(e){return e.rebufferingImpact<=0});return Ec(d,function(e,t){return wc(t.playlist,e.playlist)}),d.length?d[0]:(Ec(h,function(e,t){return e.rebufferingImpact-t.rebufferingImpact}),h[0]||null)}({master:this.hls_.playlists.master,currentTime:t,bandwidth:i,duration:this.duration_(),segmentDuration:n,timeUntilRebuffer:a,currentTimeline:this.currentTimeline_,syncController:this.syncController_});if(s){var o=r-a-s.rebufferingImpact,u=.5;return a<=1/30&&(u=1),!(!s.playlist||s.playlist.uri===this.playlist_.uri||o<u)&&(this.bandwidth=s.playlist.attributes.BANDWIDTH*Pc.BANDWIDTH_VARIANCE+1,this.abort(),this.trigger("earlyabort"),!0)}}},{key:"handleProgress_",value:function(e,t){this.pendingSegment_&&t.requestId===this.pendingSegment_.requestId&&!this.abortRequestEarly_(t.stats)&&this.trigger("progress")}},{key:"loadSegment_",value:function(e){this.state="WAITING",this.pendingSegment_=e,this.trimBackBuffer_(e),e.abortRequests=kc(this.hls_.xhr,this.xhrOptions_,this.decrypter_,this.captionParser_,this.createSimplifiedSegmentObj_(e),this.handleProgress_.bind(this),this.segmentRequestFinished_.bind(this))}},{key:"trimBackBuffer_",value:function(e){var t=function(e,t,i){var n=void 0;return n=e.length&&0<e.start(0)&&e.start(0)<t?e.start(0):t-30,Math.min(n,t-i)}(this.seekable_(),this.currentTime_(),this.playlist_.targetDuration||10);0<t&&this.remove(0,t)}},{key:"createSimplifiedSegmentObj_",value:function(e){var t=e.segment,i={resolvedUri:t.resolvedUri,byterange:t.byterange,requestId:e.requestId};if(t.key){var n=t.key.iv||new Uint32Array([0,0,0,e.mediaIndex+e.playlist.mediaSequence]);i.key=this.segmentKey(t.key),i.key.iv=n}return t.map&&(i.map=this.initSegment(t.map)),i}},{key:"segmentRequestFinished_",value:function(e,t){if(this.mediaRequests+=1,t.stats&&(this.mediaBytesTransferred+=t.stats.bytesReceived,this.mediaTransferDuration+=t.stats.roundTripTime),this.pendingSegment_){if(t.requestId===this.pendingSegment_.requestId){if(e)return this.pendingSegment_=null,this.state="READY",e.code===Oc?void(this.mediaRequestsAborted+=1):(this.pause(),e.code===Lc?(this.mediaRequestsTimedout+=1,this.bandwidth=1,this.roundTrip=NaN,void this.trigger("bandwidthupdate")):(this.mediaRequestsErrored+=1,this.error(e),void this.trigger("error")));this.bandwidth=t.stats.bandwidth,this.roundTrip=t.stats.roundTripTime,t.map&&(t.map=this.initSegment(t.map,!0)),t.key&&this.segmentKey(t.key,!0),this.processSegmentResponse_(t)}}else this.mediaRequestsAborted+=1}},{key:"processSegmentResponse_",value:function(e){var t=this.pendingSegment_;t.bytes=e.bytes,e.map&&(t.segment.map.bytes=e.map.bytes),t.endOfAllRequests=e.endOfAllRequests,e.fmp4Captions&&(function(e,t,i){for(var n in i)if(!e[n]){t.trigger({type:"usage",name:"hls-608"});var r=t.textTracks().getTrackById(n);e[n]=r||t.addRemoteTextTrack({kind:"captions",id:n,label:n},!1).track}}(this.inbandTextTracks_,this.hls_.tech_,e.captionStreams),function(e){var r=e.inbandTextTracks,t=e.captionArray,a=e.timestampOffset;if(t){var s=window.WebKitDataCue||window.VTTCue;t.forEach(function(e){var t=e.stream,i=e.startTime,n=e.endTime;r[t]&&(i+=a,n+=a,r[t].addCue(new s(i,n,e.text)))})}}({inbandTextTracks:this.inbandTextTracks_,captionArray:e.fmp4Captions,timestampOffset:0}),this.captionParser_&&this.captionParser_.clearParsedCaptions()),this.handleSegment_()}},{key:"handleSegment_",value:function(){var e=this;if(this.pendingSegment_){var t=this.pendingSegment_,i=t.segment,n=this.syncController_.probeSegmentInfo(t);"undefined"==typeof this.startingMedia_&&n&&(n.containsAudio||n.containsVideo)&&(this.startingMedia_={containsAudio:n.containsAudio,containsVideo:n.containsVideo});var r=function(e,t,i){return"main"===e&&t&&i?i.containsAudio||i.containsVideo?t.containsVideo&&!i.containsVideo?"Only audio found in segment when we expected video. We can't switch to audio only from a stream that had video. To get rid of this message, please add codec information to the manifest.":!t.containsVideo&&i.containsVideo?"Video found in segment when we expected only audio. We can't switch to a stream with video from an audio only stream. To get rid of this message, please add codec information to the manifest.":null:"Neither audio nor video found in segment.":null}(this.loaderType_,this.startingMedia_,n);if(r)return this.error({message:r,blacklistDuration:1/0}),void this.trigger("error");if(t.isSyncRequest)return this.trigger("syncinfoupdate"),this.pendingSegment_=null,void(this.state="READY");if(null!==t.timestampOffset&&t.timestampOffset!==this.sourceUpdater_.timestampOffset()){if(n&&n.segmentTimestampInfo){var a=n.segmentTimestampInfo[0].ptsTime,s=n.segmentTimestampInfo[0].dtsTime;t.timestampOffset-=a-s}this.sourceUpdater_.timestampOffset(t.timestampOffset),this.trigger("timestampoffset")}var o=this.syncController_.mappingForTimeline(t.timeline);if(null!==o&&this.trigger({type:"segmenttimemapping",mapping:o}),this.state="APPENDING",i.map){var u=fl(i.map);if(!this.activeInitSegmentId_||this.activeInitSegmentId_!==u){var l=this.initSegment(i.map);this.sourceUpdater_.appendBuffer({bytes:l.bytes},function(){e.activeInitSegmentId_=u})}}t.byteLength=t.bytes.byteLength,"number"==typeof i.start&&"number"==typeof i.end?this.mediaSecondsLoaded+=i.end-i.start:this.mediaSecondsLoaded+=i.duration,this.logger_(function(e){var t=e.segment,i=t.start,n=t.end,r=e.playlist,a=r.mediaSequence,s=r.id,o=r.segments,u=void 0===o?[]:o,l=e.mediaIndex,c=e.timeline;return["appending ["+l+"] of ["+a+", "+(a+u.length)+"] from playlist ["+s+"]","["+i+" => "+n+"] in timeline ["+c+"]"].join(" ")}(t)),this.sourceUpdater_.appendBuffer({bytes:t.bytes,videoSegmentTimingInfoCallback:this.handleVideoSegmentTimingInfo_.bind(this,t.requestId)},this.handleUpdateEnd_.bind(this))}else this.state="READY"}},{key:"handleVideoSegmentTimingInfo_",value:function(e,t){if(this.pendingSegment_&&e===this.pendingSegment_.requestId){var i=this.pendingSegment_.segment;i.videoTimingInfo||(i.videoTimingInfo={}),i.videoTimingInfo.transmuxerPrependedSeconds=t.videoSegmentTimingInfo.prependedContentDuration||0,i.videoTimingInfo.transmuxedPresentationStart=t.videoSegmentTimingInfo.start.presentation,i.videoTimingInfo.transmuxedPresentationEnd=t.videoSegmentTimingInfo.end.presentation,i.videoTimingInfo.baseMediaDecodeTime=t.videoSegmentTimingInfo.baseMediaDecodeTime}}},{key:"handleUpdateEnd_",value:function(){if(!this.pendingSegment_)return this.state="READY",void(this.paused()||this.monitorBuffer_());var e=this.pendingSegment_,t=e.segment,i=null!==this.mediaIndex;this.pendingSegment_=null,this.recordThroughput_(e),this.addSegmentMetadataCue_(e),this.state="READY",this.mediaIndex=e.mediaIndex,this.fetchAtBuffer_=!0,this.currentTimeline_=e.timeline,this.trigger("syncinfoupdate"),t.end&&this.currentTime_()-t.end>3*e.playlist.targetDuration?this.resetEverything():(i&&this.trigger("bandwidthupdate"),this.trigger("progress"),this.isEndOfStream_(e.mediaIndex+1,e.playlist)&&this.endOfStream(),this.paused()||this.monitorBuffer_())}},{key:"recordThroughput_",value:function(e){var t=this.throughput.rate,i=Date.now()-e.endOfAllRequests+1,n=Math.floor(e.byteLength/i*8*1e3);this.throughput.rate+=(n-t)/++this.throughput.count}},{key:"addSegmentMetadataCue_",value:function(e){if(this.segmentMetadataTrack_){var t=e.segment,i=t.start,n=t.end;if(Ac(i)&&Ac(n)){Cl(i,n,this.segmentMetadataTrack_);var r=v.WebKitDataCue||v.VTTCue,a={custom:t.custom,dateTimeObject:t.dateTimeObject,dateTimeString:t.dateTimeString,bandwidth:e.playlist.attributes.BANDWIDTH,resolution:e.playlist.attributes.RESOLUTION,codecs:e.playlist.attributes.CODECS,byteLength:e.byteLength,uri:e.uri,timeline:e.timeline,playlist:e.playlist.uri,start:i,end:n},s=new r(i,n,JSON.stringify(a));s.value=a,this.segmentMetadataTrack_.addCue(s)}}}}]),Dc);function Dc(e){Mu(this,Dc);var t=Fu(this,(Dc.__proto__||Object.getPrototypeOf(Dc)).call(this));if(!e)throw new TypeError("Initialization settings are required");if("function"!=typeof e.currentTime)throw new TypeError("No currentTime getter specified");if(!e.mediaSource)throw new TypeError("No MediaSource specified");return t.bandwidth=e.bandwidth,t.throughput={rate:0,count:0},t.roundTrip=NaN,t.resetStats_(),t.mediaIndex=null,t.hasPlayed_=e.hasPlayed,t.currentTime_=e.currentTime,t.seekable_=e.seekable,t.seeking_=e.seeking,t.duration_=e.duration,t.mediaSource_=e.mediaSource,t.hls_=e.hls,t.loaderType_=e.loaderType,t.startingMedia_=void 0,t.segmentMetadataTrack_=e.segmentMetadataTrack,t.goalBufferLength_=e.goalBufferLength,t.sourceType_=e.sourceType,t.inbandTextTracks_=e.inbandTextTracks,t.state_="INIT",t.checkBufferTimeout_=null,t.error_=void 0,t.currentTimeline_=-1,t.pendingSegment_=null,t.mimeType_=null,t.sourceUpdater_=null,t.xhrOptions_=null,t.activeInitSegmentId_=null,t.initSegments_={},t.cacheEncryptionKeys_=e.cacheEncryptionKeys,t.keyCache_={},"main"===t.loaderType_?t.captionParser_=new Xo:t.captionParser_=null,t.decrypter_=e.decrypter,t.syncController_=e.syncController,t.syncPoint_={segmentIndex:0,time:0},t.syncController_.on("syncinfoupdate",function(){return t.trigger("syncinfoupdate")}),t.mediaSource_.addEventListener("sourceopen",function(){return t.ended_=!1}),t.fetchAtBuffer_=!1,t.logger_=mc("SegmentLoader["+t.loaderType_+"]"),Object.defineProperty(t,"state",{get:function(){return this.state_},set:function(e){e!==this.state_&&(this.logger_(this.state_+" -> "+e),this.state_=e)}}),t}function Uc(e){return decodeURIComponent(escape(String.fromCharCode.apply(null,e)))}var Rc=new Uint8Array("\n\n".split("").map(function(e){return e.charCodeAt(0)})),Mc=(ju(Nc,xc),Nu(Nc,[{key:"buffered_",value:function(){if(!this.subtitlesTrack_||!this.subtitlesTrack_.cues.length)return fs.createTimeRanges();var e=this.subtitlesTrack_.cues,t=e[0].startTime,i=e[e.length-1].startTime;return fs.createTimeRanges([[t,i]])}},{key:"initSegment",value:function(e,t){var i=1<arguments.length&&void 0!==t&&t;if(!e)return null;var n=fl(e),r=this.initSegments_[n];if(i&&!r&&e.bytes){var a=Rc.byteLength+e.bytes.byteLength,s=new Uint8Array(a);s.set(e.bytes),s.set(Rc,e.bytes.byteLength),this.initSegments_[n]=r={resolvedUri:e.resolvedUri,byterange:e.byterange,bytes:s}}return r||e}},{key:"couldBeginLoading_",value:function(){return this.playlist_&&this.subtitlesTrack_&&!this.paused()}},{key:"init_",value:function(){return this.state="READY",this.resetEverything(),this.monitorBuffer_()}},{key:"track",value:function(e){return"undefined"==typeof e||(this.subtitlesTrack_=e,"INIT"===this.state&&this.couldBeginLoading_()&&this.init_()),this.subtitlesTrack_}},{key:"remove",value:function(e,t){Cl(e,t,this.subtitlesTrack_)}},{key:"fillBuffer_",value:function(){var e=this;this.syncPoint_||(this.syncPoint_=this.syncController_.getSyncPoint(this.playlist_,this.duration_(),this.currentTimeline_,this.currentTime_()));var t=this.checkBuffer_(this.buffered_(),this.playlist_,this.mediaIndex,this.hasPlayed_(),this.currentTime_(),this.syncPoint_);if(t=this.skipEmptySegments_(t)){if(null===this.syncController_.timestampOffsetForTimeline(t.timeline))return this.syncController_.one("timestampoffset",function(){e.state="READY",e.paused()||e.monitorBuffer_()}),void(this.state="WAITING_ON_TIMELINE");this.loadSegment_(t)}}},{key:"skipEmptySegments_",value:function(e){for(;e&&e.segment.empty;)e=this.generateSegmentInfo_(e.playlist,e.mediaIndex+1,e.startOfSegment+e.duration,e.isSyncRequest);return e}},{key:"handleSegment_",value:function(){var t=this;if(this.pendingSegment_&&this.subtitlesTrack_){this.state="APPENDING";var e=this.pendingSegment_,i=e.segment;if("function"!=typeof v.WebVTT&&this.subtitlesTrack_&&this.subtitlesTrack_.tech_){function n(){t.subtitlesTrack_.tech_.off("vttjsloaded",r),t.error({message:"Error loading vtt.js"}),t.state="READY",t.pause(),t.trigger("error")}var r=void 0;return r=function(){t.subtitlesTrack_.tech_.off("vttjserror",n),t.handleSegment_()},this.state="WAITING_ON_VTTJS",this.subtitlesTrack_.tech_.one("vttjsloaded",r),void this.subtitlesTrack_.tech_.one("vttjserror",n)}i.requested=!0;try{this.parseVTTCues_(e)}catch(e){return this.error({message:e.message}),this.state="READY",this.pause(),this.trigger("error")}if(this.updateTimeMapping_(e,this.syncController_.timelines[e.timeline],this.playlist_),e.isSyncRequest)return this.trigger("syncinfoupdate"),this.pendingSegment_=null,void(this.state="READY");e.byteLength=e.bytes.byteLength,this.mediaSecondsLoaded+=i.duration,e.cues.length&&this.remove(e.cues[0].endTime,e.cues[e.cues.length-1].endTime),e.cues.forEach(function(e){t.subtitlesTrack_.addCue(e)}),this.handleUpdateEnd_()}else this.state="READY"}},{key:"parseVTTCues_",value:function(t){var e=void 0,i=!1;"function"==typeof v.TextDecoder?e=new v.TextDecoder("utf8"):(e=v.WebVTT.StringDecoder(),i=!0);var n=new v.WebVTT.Parser(v,v.vttjs,e);if(t.cues=[],t.timestampmap={MPEGTS:0,LOCAL:0},n.oncue=t.cues.push.bind(t.cues),n.ontimestampmap=function(e){return t.timestampmap=e},n.onparsingerror=function(e){fs.log.warn("Error encountered when parsing cues: "+e.message)},t.segment.map){var r=t.segment.map.bytes;i&&(r=Uc(r)),n.parse(r)}var a=t.bytes;i&&(a=Uc(a)),n.parse(a),n.flush()}},{key:"updateTimeMapping_",value:function(e,t,i){var n=e.segment;if(t)if(e.cues.length){var r=e.timestampmap,a=r.MPEGTS/9e4-r.LOCAL+t.mapping;if(e.cues.forEach(function(e){e.startTime+=a,e.endTime+=a}),!i.syncInfo){var s=e.cues[0].startTime,o=e.cues[e.cues.length-1].startTime;i.syncInfo={mediaSequence:i.mediaSequence+e.mediaIndex,time:Math.min(s,o-n.duration)}}}else n.empty=!0}}]),Nc);function Nc(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};Mu(this,Nc);var i=Fu(this,(Nc.__proto__||Object.getPrototypeOf(Nc)).call(this,e,t));return i.mediaSource_=null,i.subtitlesTrack_=null,i}function Bc(e,t){for(var i=e.cues,n=0;n<i.length;n++){var r=i[n];if(t>=r.adStartTime&&t<=r.adEndTime)return r}return null}var jc=vu,Fc=[{name:"VOD",run:function(e,t,i,n,r){if(i===1/0)return null;return{time:0,segmentIndex:0}}},{name:"ProgramDateTime",run:function(e,t,i,n,r){if(!e.datetimeToDisplayTime)return null;var a=t.segments||[],s=null,o=null;r=r||0;for(var u=0;u<a.length;u++){var l=a[u];if(l.dateTimeObject){var c=l.dateTimeObject.getTime()/1e3+e.datetimeToDisplayTime,h=Math.abs(r-c);if(null!==o&&(0===h||o<h))break;o=h,s={time:c,segmentIndex:u}}}return s}},{name:"Segment",run:function(e,t,i,n,r){var a=t.segments||[],s=null,o=null;r=r||0;for(var u=0;u<a.length;u++){var l=a[u];if(l.timeline===n&&"undefined"!=typeof l.start){var c=Math.abs(r-l.start);if(null!==o&&o<c)break;(!s||null===o||c<=o)&&(o=c,s={time:l.start,segmentIndex:u})}}return s}},{name:"Discontinuity",run:function(e,t,i,n,r){var a=null;if(r=r||0,t.discontinuityStarts&&t.discontinuityStarts.length)for(var s=null,o=0;o<t.discontinuityStarts.length;o++){var u=t.discontinuityStarts[o],l=t.discontinuitySequence+o+1,c=e.discontinuities[l];if(c){var h=Math.abs(r-c.time);if(null!==s&&s<h)break;(!a||null===s||h<=s)&&(s=h,a={time:c.time,segmentIndex:u})}}return a}},{name:"Playlist",run:function(e,t,i,n,r){return t.syncInfo?{time:t.syncInfo.time,segmentIndex:t.syncInfo.mediaSequence-t.mediaSequence}:null}}],Hc=(ju(Vc,fs.EventTarget),Nu(Vc,[{key:"getSyncPoint",value:function(e,t,i,n){var r=this.runStrategies_(e,t,i,n);return r.length?this.selectSyncPoint_(r,{key:"time",value:n}):null}},{key:"getExpiredTime",value:function(e,t){if(!e||!e.segments)return null;var i=this.runStrategies_(e,t,e.discontinuitySequence,0);if(!i.length)return null;var n=this.selectSyncPoint_(i,{key:"segmentIndex",value:0});return 0<n.segmentIndex&&(n.time*=-1),Math.abs(n.time+Zu(e,n.segmentIndex,0))}},{key:"runStrategies_",value:function(e,t,i,n){for(var r=[],a=0;a<Fc.length;a++){var s=Fc[a],o=s.run(this,e,t,i,n);o&&(o.strategy=s.name,r.push({strategy:s.name,syncPoint:o}))}return r}},{key:"selectSyncPoint_",value:function(e,t){for(var i=e[0].syncPoint,n=Math.abs(e[0].syncPoint[t.key]-t.value),r=e[0].strategy,a=1;a<e.length;a++){var s=Math.abs(e[a].syncPoint[t.key]-t.value);s<n&&(n=s,i=e[a].syncPoint,r=e[a].strategy)}return this.logger_("syncPoint for ["+t.key+": "+t.value+"] chosen with strategy ["+r+"]: [time:"+i.time+", segmentIndex:"+i.segmentIndex+"]"),i}},{key:"saveExpiredSegmentInfo",value:function(e,t){for(var i=t.mediaSequence-e.mediaSequence-1;0<=i;i--){var n=e.segments[i];if(n&&"undefined"!=typeof n.start){t.syncInfo={mediaSequence:e.mediaSequence+i,time:n.start},this.logger_("playlist refresh sync: [time:"+t.syncInfo.time+", mediaSequence: "+t.syncInfo.mediaSequence+"]"),this.trigger("syncinfoupdate");break}}}},{key:"setDateTimeMapping",value:function(e){if(!this.datetimeToDisplayTime&&e.segments&&e.segments.length&&e.segments[0].dateTimeObject){var t=e.segments[0].dateTimeObject.getTime()/1e3;this.datetimeToDisplayTime=-t}}},{key:"reset",value:function(){this.inspectCache_=void 0}},{key:"probeSegmentInfo",value:function(e){var t=e.segment,i=e.playlist,n=void 0;return(n=t.map?this.probeMp4Segment_(e):this.probeTsSegment_(e))&&this.calculateSegmentTimeMapping_(e,n)&&(this.saveDiscontinuitySyncInfo_(e),i.syncInfo||(i.syncInfo={mediaSequence:i.mediaSequence+e.mediaIndex,time:t.start})),n}},{key:"probeMp4Segment_",value:function(e){var t=e.segment,i=Ao.timescale(t.map.bytes),n=Ao.startTime(i,e.bytes);return null!==e.timestampOffset&&(e.timestampOffset-=n),{start:n,end:n+t.duration}}},{key:"probeTsSegment_",value:function(e){var t=jc(e.bytes,this.inspectCache_),i=void 0,n=void 0,r=void 0;return t?(t.video&&2===t.video.length?(this.inspectCache_=t.video[1].dts,i=t.video[0].dtsTime,n=t.video[1].dtsTime,r=t.video):t.audio&&2===t.audio.length&&(this.inspectCache_=t.audio[1].dts,i=t.audio[0].dtsTime,n=t.audio[1].dtsTime,r=t.audio),{segmentTimestampInfo:r,start:i,end:n,containsVideo:t.video&&2===t.video.length,containsAudio:t.audio&&2===t.audio.length}):null}},{key:"timestampOffsetForTimeline",value:function(e){return"undefined"==typeof this.timelines[e]?null:this.timelines[e].time}},{key:"mappingForTimeline",value:function(e){return"undefined"==typeof this.timelines[e]?null:this.timelines[e].mapping}},{key:"calculateSegmentTimeMapping_",value:function(e,t){var i=e.segment,n=this.timelines[e.timeline];if(null!==e.timestampOffset)n={time:e.startOfSegment,mapping:e.startOfSegment-t.start},this.timelines[e.timeline]=n,this.trigger("timestampoffset"),this.logger_("time mapping for timeline "+e.timeline+": [time: "+n.time+"] [mapping: "+n.mapping+"]"),i.start=e.startOfSegment,i.end=t.end+n.mapping;else{if(!n)return!1;i.start=t.start+n.mapping,i.end=t.end+n.mapping}return!0}},{key:"saveDiscontinuitySyncInfo_",value:function(e){var t=e.playlist,i=e.segment;if(i.discontinuity)this.discontinuities[i.timeline]={time:i.start,accuracy:0};else if(t.discontinuityStarts&&t.discontinuityStarts.length)for(var n=0;n<t.discontinuityStarts.length;n++){var r=t.discontinuityStarts[n],a=t.discontinuitySequence+n+1,s=r-e.mediaIndex,o=Math.abs(s);if(!this.discontinuities[a]||this.discontinuities[a].accuracy>o){var u=void 0;u=s<0?i.start-Zu(t,e.mediaIndex,r):i.end+Zu(t,e.mediaIndex+1,r),this.discontinuities[a]={time:u,accuracy:o}}}}}]),Vc);function Vc(){Mu(this,Vc);var e=Fu(this,(Vc.__proto__||Object.getPrototypeOf(Vc)).call(this));return e.inspectCache_=void 0,e.timelines=[],e.discontinuities=[],e.datetimeToDisplayTime=null,e.logger_=mc("SyncController"),e}function qc(e,t){e.abort(),e.pause(),t&&t.activePlaylistLoader&&(t.activePlaylistLoader.pause(),t.activePlaylistLoader=null)}function Wc(e,t){(t.activePlaylistLoader=e).load()}function zc(t){["AUDIO","SUBTITLES","CLOSED-CAPTIONS"].forEach(function(e){Kc[e](e,t)});var i=t.mediaTypes,e=t.masterPlaylistLoader,n=t.tech,r=t.hls;["AUDIO","SUBTITLES"].forEach(function(e){i[e].activeGroup=function(a,s){return function(t){var e=s.masterPlaylistLoader,i=s.mediaTypes[a].groups,n=e.media();if(!n)return null;var r=null;return n.attributes[a]&&(r=i[n.attributes[a]]),r=r||i.main,"undefined"==typeof t?r:null===t?null:r.filter(function(e){return e.id===t.id})[0]||null}}(e,t),i[e].activeTrack=Yc[e](e,t),i[e].onGroupChanged=function(o,u){return function(){var e=u.segmentLoaders,t=e[o],i=e.main,n=u.mediaTypes[o],r=n.activeTrack(),a=n.activeGroup(r),s=n.activePlaylistLoader;qc(t,n),a&&(a.playlistLoader?(t.resyncLoader(),Wc(a.playlistLoader,n)):s&&i.resetEverything())}}(e,t),i[e].onTrackChanged=function(o,u){return function(){var e=u.segmentLoaders,t=e[o],i=e.main,n=u.mediaTypes[o],r=n.activeTrack(),a=n.activeGroup(r),s=n.activePlaylistLoader;qc(t,n),a&&(a.playlistLoader?(s!==a.playlistLoader&&(t.track&&t.track(r),t.resetEverything()),Wc(a.playlistLoader,n)):i.resetEverything())}}(e,t)});var a=i.AUDIO.activeGroup(),s=(a.filter(function(e){return e.default})[0]||a[0]).id;function o(){i.AUDIO.onTrackChanged(),n.trigger({type:"usage",name:"hls-audio-change"})}for(var u in i.AUDIO.tracks[s].enabled=!0,i.AUDIO.onTrackChanged(),e.on("mediachange",function(){["AUDIO","SUBTITLES"].forEach(function(e){return i[e].onGroupChanged()})}),n.audioTracks().addEventListener("change",o),n.remoteTextTracks().addEventListener("change",i.SUBTITLES.onTrackChanged),r.on("dispose",function(){n.audioTracks().removeEventListener("change",o),n.remoteTextTracks().removeEventListener("change",i.SUBTITLES.onTrackChanged)}),n.clearTracks("audio"),i.AUDIO.tracks)n.audioTracks().addTrack(i.AUDIO.tracks[u])}var $c=new Fl("./decrypter-worker.worker.js",function(e,t){var i,c,g,n,r,u,s,a=this;i=function(e,t,i){return t&&o(e.prototype,t),i&&o(e,i),e},c=null,d.prototype.decrypt=function(e,t,i,n,r,a){var s=this._key[1],o=e^s[0],u=n^s[1],l=i^s[2],c=t^s[3],h=void 0,d=void 0,p=void 0,f=s.length/4-2,m=void 0,g=4,y=this._tables[1],v=y[0],_=y[1],b=y[2],T=y[3],S=y[4];for(m=0;m<f;m++)h=v[o>>>24]^_[u>>16&255]^b[l>>8&255]^T[255&c]^s[g],d=v[u>>>24]^_[l>>16&255]^b[c>>8&255]^T[255&o]^s[g+1],p=v[l>>>24]^_[c>>16&255]^b[o>>8&255]^T[255&u]^s[g+2],c=v[c>>>24]^_[o>>16&255]^b[u>>8&255]^T[255&l]^s[g+3],g+=4,o=h,u=d,l=p;for(m=0;m<4;m++)r[(3&-m)+a]=S[o>>>24]<<24^S[u>>16&255]<<16^S[l>>8&255]<<8^S[255&c]^s[g++],h=o,o=u,u=l,l=c,c=h},g=d,l.prototype.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},l.prototype.off=function(e,t){if(!this.listeners[e])return!1;var i=this.listeners[e].indexOf(t);return this.listeners[e].splice(i,1),-1<i},l.prototype.trigger=function(e,t){var i=this.listeners[e];if(i)if(2===arguments.length)for(var n=i.length,r=0;r<n;++r)i[r].call(this,t);else for(var a=Array.prototype.slice.call(arguments,1),s=i.length,o=0;o<s;++o)i[o].apply(this,a)},l.prototype.dispose=function(){this.listeners={}},l.prototype.pipe=function(t){this.on("data",function(e){t.push(e)})},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(p,r=n=l),p.prototype.processJob_=function(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null},p.prototype.push=function(e){this.jobs.push(e),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))},u=p,f.prototype.decryptChunk_=function(t,i,n,r){return function(){var e=function(e,t,i){var n=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),r=new g(Array.prototype.slice.call(t)),a=new Uint8Array(e.byteLength),s=new Int32Array(a.buffer),o=void 0,u=void 0,l=void 0,c=void 0,h=void 0,d=void 0,p=void 0,f=void 0,m=void 0;for(o=i[0],u=i[1],l=i[2],c=i[3],m=0;m<n.length;m+=4)h=y(n[m]),d=y(n[m+1]),p=y(n[m+2]),f=y(n[m+3]),r.decrypt(h,d,p,f,s,m),s[m]=y(s[m]^o),s[m+1]=y(s[m+1]^u),s[m+2]=y(s[m+2]^l),s[m+3]=y(s[m+3]^c),o=h,u=d,l=p,c=f;return a}(t,i,n);r.set(e,t.byteOffset)}},i(f,null,[{key:"STEP",get:function(){return 32e3}}]),s=f,new function(a){a.onmessage=function(e){var i=e.data,t=new Uint8Array(i.encrypted.bytes,i.encrypted.byteOffset,i.encrypted.byteLength),n=new Uint32Array(i.key.bytes,i.key.byteOffset,i.key.byteLength/4),r=new Uint32Array(i.iv.bytes,i.iv.byteOffset,i.iv.byteLength/4);new s(t,n,r,function(e,t){a.postMessage(function(i){var n={};return Object.keys(i).forEach(function(e){var t=i[e];ArrayBuffer.isView(t)?n[e]={bytes:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength}:n[e]=t}),n}({source:i.source,decrypted:t}),[t.buffer])})}}(a);function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function d(e){h(this,d),c=c||function(){var e=[[[],[],[],[],[]],[[],[],[],[],[]]],t=e[0],i=e[1],n=t[4],r=i[4],a=void 0,s=void 0,o=void 0,u=[],l=[],c=void 0,h=void 0,d=void 0,p=void 0,f=void 0;for(a=0;a<256;a++)l[(u[a]=a<<1^283*(a>>7))^a]=a;for(s=o=0;!n[s];s^=c||1,o=l[o]||1)for(d=(d=o^o<<1^o<<2^o<<3^o<<4)>>8^255&d^99,f=16843009*u[h=u[c=u[r[n[s]=d]=s]]]^65537*h^257*c^16843008*s,p=257*u[d]^16843008*d,a=0;a<4;a++)t[a][s]=p=p<<24^p>>>8,i[a][d]=f=f<<24^f>>>8;for(a=0;a<5;a++)t[a]=t[a].slice(0),i[a]=i[a].slice(0);return e}(),this._tables=[[c[0][0].slice(),c[0][1].slice(),c[0][2].slice(),c[0][3].slice(),c[0][4].slice()],[c[1][0].slice(),c[1][1].slice(),c[1][2].slice(),c[1][3].slice(),c[1][4].slice()]];var t=void 0,i=void 0,n=void 0,r=void 0,a=void 0,s=this._tables[0][4],o=this._tables[1],u=e.length,l=1;if(4!==u&&6!==u&&8!==u)throw new Error("Invalid aes key size");for(r=e.slice(0),a=[],this._key=[r,a],t=u;t<4*u+28;t++)n=r[t-1],(t%u==0||8===u&&t%u==4)&&(n=s[n>>>24]<<24^s[n>>16&255]<<16^s[n>>8&255]<<8^s[255&n],t%u==0&&(n=n<<8^n>>>24^l<<24,l=l<<1^283*(l>>7))),r[t]=r[t-u]^n;for(i=0;t;i++,t--)n=r[3&i?t:t-4],a[i]=t<=4||i<4?n:o[0][s[n>>>24]]^o[1][s[n>>16&255]]^o[2][s[n>>8&255]]^o[3][s[255&n]]}function l(){h(this,l),this.listeners={}}function p(){h(this,p);var e=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,r.call(this,n));return e.jobs=[],e.delay=1,e.timeout_=null,e}function y(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24}function f(e,t,i,n){h(this,f);var r=f.STEP,a=new Int32Array(e.buffer),s=new Uint8Array(e.byteLength),o=0;for(this.asyncStream_=new u,this.asyncStream_.push(this.decryptChunk_(a.subarray(o,o+r),t,i,s)),o=r;o<a.length;o+=r)i=new Uint32Array([y(a[o-4]),y(a[o-3]),y(a[o-2]),y(a[o-1])]),this.asyncStream_.push(this.decryptChunk_(a.subarray(o,o+r),t,i,s));this.asyncStream_.push(function(){n(null,function(e){return e.subarray(0,e.byteLength-e[e.byteLength-1])}(s))})}}),Gc={AUDIO:function(u,l){return function(){var e=l.segmentLoaders[u],t=l.mediaTypes[u],i=l.blacklistCurrentPlaylist;qc(e,t);var n=t.activeTrack(),r=t.activeGroup(),a=(r.filter(function(e){return e.default})[0]||r[0]).id,s=t.tracks[a];if(n!==s){for(var o in fs.log.warn("Problem encountered loading the alternate audio track.Switching back to default."),t.tracks)t.tracks[o].enabled=t.tracks[o]===s;t.onTrackChanged()}else i({message:"Problem encountered loading the default audio track."})}},SUBTITLES:function(n,r){return function(){var e=r.segmentLoaders[n],t=r.mediaTypes[n];fs.log.warn("Problem encountered loading the subtitle track.Disabling subtitle track."),qc(e,t);var i=t.activeTrack();i&&(i.mode="disabled"),t.onTrackChanged()}}},Xc={AUDIO:function(e,t,i){if(t){var n=i.tech,r=i.requestOptions,a=i.segmentLoaders[e];t.on("loadedmetadata",function(){var e=t.media();a.playlist(e,r),(!n.paused()||e.endList&&"none"!==n.preload())&&a.load()}),t.on("loadedplaylist",function(){a.playlist(t.media(),r),n.paused()||a.load()}),t.on("error",Gc[e](e,i))}},SUBTITLES:function(e,t,i){var n=i.tech,r=i.requestOptions,a=i.segmentLoaders[e],s=i.mediaTypes[e];t.on("loadedmetadata",function(){var e=t.media();a.playlist(e,r),a.track(s.activeTrack()),(!n.paused()||e.endList&&"none"!==n.preload())&&a.load()}),t.on("loadedplaylist",function(){a.playlist(t.media(),r),n.paused()||a.load()}),t.on("error",Gc[e](e,i))}},Kc={AUDIO:function(e,t){var i,n,r=t.hls,a=t.sourceType,s=t.segmentLoaders[e],o=t.requestOptions,u=t.master.mediaGroups,l=t.mediaTypes[e],c=l.groups,h=l.tracks,d=t.masterPlaylistLoader;for(var p in u[e]&&0!==Object.keys(u[e]).length||(u[e]={main:{default:{default:!0}}}),u[e])for(var f in c[p]||(c[p]=[]),u[e][p]){var m=u[e][p][f],g=void 0;if(g=m.resolvedUri?new Ku(m.resolvedUri,r,o):m.playlists&&"dash"===a?new pc(m.playlists[0],r,o,d):null,m=fs.mergeOptions({id:f,playlistLoader:g},m),Xc[e](e,m.playlistLoader,t),c[p].push(m),"undefined"==typeof h[f]){var y=new fs.AudioTrack({id:f,kind:(i=m,n=void 0,n=i.default?"main":"alternative",i.characteristics&&0<=i.characteristics.indexOf("public.accessibility.describes-video")&&(n="main-desc"),n),enabled:!1,language:m.language,default:m.default,label:f});h[f]=y}}s.on("error",Gc[e](e,t))},SUBTITLES:function(e,t){var i=t.tech,n=t.hls,r=t.sourceType,a=t.segmentLoaders[e],s=t.requestOptions,o=t.master.mediaGroups,u=t.mediaTypes[e],l=u.groups,c=u.tracks,h=t.masterPlaylistLoader;for(var d in o[e])for(var p in l[d]||(l[d]=[]),o[e][d])if(!o[e][d][p].forced){var f=o[e][d][p],m=void 0;if("hls"===r?m=new Ku(f.resolvedUri,n,s):"dash"===r&&(m=new pc(f.playlists[0],n,s,h)),f=fs.mergeOptions({id:p,playlistLoader:m},f),Xc[e](e,f.playlistLoader,t),l[d].push(f),"undefined"==typeof c[p]){var g=i.addRemoteTextTrack({id:p,kind:"subtitles",default:f.default&&f.autoselect,language:f.language,label:p},!1).track;c[p]=g}}a.on("error",Gc[e](e,t))},"CLOSED-CAPTIONS":function(e,t){var i=t.tech,n=t.master.mediaGroups,r=t.mediaTypes[e],a=r.groups,s=r.tracks;for(var o in n[e])for(var u in a[o]||(a[o]=[]),n[e][o]){var l=n[e][o][u];if(l.instreamId.match(/CC\d/)&&(a[o].push(fs.mergeOptions({id:u},l)),"undefined"==typeof s[u])){var c=i.addRemoteTextTrack({id:l.instreamId,kind:"captions",default:l.default&&l.autoselect,language:l.language,label:u},!1).track;s[u]=c}}}},Yc={AUDIO:function(i,n){return function(){var e=n.mediaTypes[i].tracks;for(var t in e)if(e[t].enabled)return e[t];return null}},SUBTITLES:function(i,n){return function(){var e=n.mediaTypes[i].tracks;for(var t in e)if("showing"===e[t].mode)return e[t];return null}}},Qc=void 0,Jc=["mediaRequests","mediaRequestsAborted","mediaRequestsTimedout","mediaRequestsErrored","mediaTransferDuration","mediaBytesTransferred"],Zc=(ju(eh,fs.EventTarget),Nu(eh,[{key:"setupMasterPlaylistLoaderListeners_",value:function(){var n=this;this.masterPlaylistLoader_.on("loadedmetadata",function(){var e=n.masterPlaylistLoader_.media(),t=1.5*e.targetDuration*1e3;ul(n.masterPlaylistLoader_.master,n.masterPlaylistLoader_.media())?n.requestOptions_.timeout=0:n.requestOptions_.timeout=t,e.endList&&"none"!==n.tech_.preload()&&(n.mainSegmentLoader_.playlist(e,n.requestOptions_),n.mainSegmentLoader_.load()),zc({sourceType:n.sourceType_,segmentLoaders:{AUDIO:n.audioSegmentLoader_,SUBTITLES:n.subtitleSegmentLoader_,main:n.mainSegmentLoader_},tech:n.tech_,requestOptions:n.requestOptions_,masterPlaylistLoader:n.masterPlaylistLoader_,hls:n.hls_,master:n.master(),mediaTypes:n.mediaTypes_,blacklistCurrentPlaylist:n.blacklistCurrentPlaylist.bind(n)}),n.triggerPresenceUsage_(n.master(),e);try{n.setupSourceBuffers_()}catch(e){return fs.log.warn("Failed to create SourceBuffers",e),n.mediaSource.endOfStream("decode")}n.setupFirstPlay(),!n.mediaTypes_.AUDIO.activePlaylistLoader||n.mediaTypes_.AUDIO.activePlaylistLoader.media()?n.trigger("selectedinitialmedia"):n.mediaTypes_.AUDIO.activePlaylistLoader.one("loadedmetadata",function(){n.trigger("selectedinitialmedia")})}),this.masterPlaylistLoader_.on("loadedplaylist",function(){var e=n.masterPlaylistLoader_.media();if(!e){n.excludeUnsupportedVariants_();var t=void 0;return n.enableLowInitialPlaylist&&(t=n.selectInitialPlaylist()),t=t||n.selectPlaylist(),n.initialMedia_=t,void n.masterPlaylistLoader_.media(n.initialMedia_)}if(n.useCueTags_&&n.updateAdCues_(e),n.mainSegmentLoader_.playlist(e,n.requestOptions_),n.updateDuration(),n.tech_.paused()||(n.mainSegmentLoader_.load(),n.audioSegmentLoader_&&n.audioSegmentLoader_.load()),!e.endList){function i(){var e=n.seekable();0!==e.length&&n.mediaSource.addSeekableRange_(e.start(0),e.end(0))}n.duration()!==1/0?n.tech_.one("durationchange",function e(){n.duration()===1/0?i():n.tech_.one("durationchange",e)}):i()}}),this.masterPlaylistLoader_.on("error",function(){n.blacklistCurrentPlaylist(n.masterPlaylistLoader_.error)}),this.masterPlaylistLoader_.on("mediachanging",function(){n.mainSegmentLoader_.abort(),n.mainSegmentLoader_.pause()}),this.masterPlaylistLoader_.on("mediachange",function(){var e=n.masterPlaylistLoader_.media(),t=1.5*e.targetDuration*1e3;ul(n.masterPlaylistLoader_.master,n.masterPlaylistLoader_.media())?n.requestOptions_.timeout=0:n.requestOptions_.timeout=t,n.mainSegmentLoader_.playlist(e,n.requestOptions_),n.mainSegmentLoader_.load(),n.tech_.trigger({type:"mediachange",bubbles:!0})}),this.masterPlaylistLoader_.on("playlistunchanged",function(){var e=n.masterPlaylistLoader_.media();n.stuckAtPlaylistEnd_(e)&&(n.blacklistCurrentPlaylist({message:"Playlist no longer updating."}),n.tech_.trigger("playliststuck"))}),this.masterPlaylistLoader_.on("renditiondisabled",function(){n.tech_.trigger({type:"usage",name:"hls-rendition-disabled"})}),this.masterPlaylistLoader_.on("renditionenabled",function(){n.tech_.trigger({type:"usage",name:"hls-rendition-enabled"})})}},{key:"triggerPresenceUsage_",value:function(e,t){var i=e.mediaGroups||{},n=!0,r=Object.keys(i.AUDIO);for(var a in i.AUDIO)for(var s in i.AUDIO[a])i.AUDIO[a][s].uri||(n=!1);n&&this.tech_.trigger({type:"usage",name:"hls-demuxed"}),Object.keys(i.SUBTITLES).length&&this.tech_.trigger({type:"usage",name:"hls-webvtt"}),Qc.Playlist.isAes(t)&&this.tech_.trigger({type:"usage",name:"hls-aes"}),Qc.Playlist.isFmp4(t)&&this.tech_.trigger({type:"usage",name:"hls-fmp4"}),r.length&&1<Object.keys(i.AUDIO[r[0]]).length&&this.tech_.trigger({type:"usage",name:"hls-alternate-audio"}),this.useCueTags_&&this.tech_.trigger({type:"usage",name:"hls-playlist-cue-tags"})}},{key:"setupSegmentLoaderListeners_",value:function(){var a=this;this.mainSegmentLoader_.on("bandwidthupdate",function(){var e=a.selectPlaylist(),t=a.masterPlaylistLoader_.media(),i=a.tech_.buffered(),n=i.length?i.end(i.length-1)-a.tech_.currentTime():0,r=a.bufferLowWaterLine();(!t.endList||a.duration()<Pc.MAX_BUFFER_LOW_WATER_LINE||e.attributes.BANDWIDTH<t.attributes.BANDWIDTH||r<=n)&&a.masterPlaylistLoader_.media(e),a.tech_.trigger("bandwidthupdate")}),this.mainSegmentLoader_.on("progress",function(){a.trigger("progress")}),this.mainSegmentLoader_.on("error",function(){a.blacklistCurrentPlaylist(a.mainSegmentLoader_.error())}),this.mainSegmentLoader_.on("syncinfoupdate",function(){a.onSyncInfoUpdate_()}),this.mainSegmentLoader_.on("timestampoffset",function(){a.tech_.trigger({type:"usage",name:"hls-timestamp-offset"})}),this.audioSegmentLoader_.on("syncinfoupdate",function(){a.onSyncInfoUpdate_()}),this.mainSegmentLoader_.on("ended",function(){a.onEndOfStream()}),this.mainSegmentLoader_.on("earlyabort",function(){a.blacklistCurrentPlaylist({message:"Aborted early because there isn't enough bandwidth to complete the request without rebuffering."},120)}),this.mainSegmentLoader_.on("reseteverything",function(){a.tech_.trigger("hls-reset")}),this.mainSegmentLoader_.on("segmenttimemapping",function(e){a.tech_.trigger({type:"hls-segment-time-mapping",mapping:e.mapping})}),this.audioSegmentLoader_.on("ended",function(){a.onEndOfStream()})}},{key:"mediaSecondsLoaded_",value:function(){return Math.max(this.audioSegmentLoader_.mediaSecondsLoaded+this.mainSegmentLoader_.mediaSecondsLoaded)}},{key:"load",value:function(){this.mainSegmentLoader_.load(),this.mediaTypes_.AUDIO.activePlaylistLoader&&this.audioSegmentLoader_.load(),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&this.subtitleSegmentLoader_.load()}},{key:"smoothQualityChange_",value:function(){var e=this.selectPlaylist();e!==this.masterPlaylistLoader_.media()&&(this.masterPlaylistLoader_.media(e),this.mainSegmentLoader_.resetLoader())}},{key:"fastQualityChange_",value:function(){var e=this,t=this.selectPlaylist();t!==this.masterPlaylistLoader_.media()&&(this.masterPlaylistLoader_.media(t),this.mainSegmentLoader_.resetEverything(function(){fs.browser.IE_VERSION||fs.browser.IS_EDGE?e.tech_.setCurrentTime(e.tech_.currentTime()+.04):e.tech_.setCurrentTime(e.tech_.currentTime())}))}},{key:"play",value:function(){if(!this.setupFirstPlay()){this.tech_.ended()&&this.seekTo_(0),this.hasPlayed_()&&this.load();var e=this.tech_.seekable();return this.tech_.duration()===1/0&&this.tech_.currentTime()<e.start(0)?this.seekTo_(e.end(e.length-1)):void 0}}},{key:"setupFirstPlay",value:function(){var e=this,t=this.masterPlaylistLoader_.media();if(!t||this.tech_.paused()||this.hasPlayed_())return!1;if(!t.endList){var i=this.seekable();if(!i.length)return!1;if(fs.browser.IE_VERSION&&0===this.tech_.readyState())return this.tech_.one("loadedmetadata",function(){e.trigger("firstplay"),e.seekTo_(i.end(0)),e.hasPlayed_=function(){return!0}}),!1;this.trigger("firstplay"),this.seekTo_(i.end(0))}return this.hasPlayed_=function(){return!0},this.load(),!0}},{key:"handleSourceOpen_",value:function(){try{this.setupSourceBuffers_()}catch(e){return fs.log.warn("Failed to create Source Buffers",e),this.mediaSource.endOfStream("decode")}if(this.tech_.autoplay()){var e=this.tech_.play();"undefined"!=typeof e&&"function"==typeof e.then&&e.then(null,function(e){})}this.trigger("sourceopen")}},{key:"onEndOfStream",value:function(){var e=this.mainSegmentLoader_.ended_;if(this.mediaTypes_.AUDIO.activePlaylistLoader&&(e=!this.mainSegmentLoader_.startingMedia_||this.mainSegmentLoader_.startingMedia_.containsVideo?e&&this.audioSegmentLoader_.ended_:this.audioSegmentLoader_.ended_),e){this.logger_("calling mediaSource.endOfStream()");try{this.mediaSource.endOfStream()}catch(e){fs.log.warn("Failed to call media source endOfStream",e)}}}},{key:"stuckAtPlaylistEnd_",value:function(e){if(!this.seekable().length)return!1;var t=this.syncController_.getExpiredTime(e,this.mediaSource.duration);if(null===t)return!1;var i=Qc.Playlist.playlistEnd(e,t),n=this.tech_.currentTime(),r=this.tech_.buffered();if(!r.length)return i-n<=.1;var a=r.end(r.length-1);return a-n<=.1&&i-a<=.1}},{key:"blacklistCurrentPlaylist",value:function(e,t){var i,n=0<arguments.length&&void 0!==e?e:{},r=t,a=void 0;if(a=n.playlist||this.masterPlaylistLoader_.media(),r=r||n.blacklistDuration||this.blacklistDuration,!a){this.error=n;try{return this.mediaSource.endOfStream("network")}catch(e){return this.trigger("error")}}var s=1===this.masterPlaylistLoader_.master.playlists.filter(sl).length,o=this.masterPlaylistLoader_.master.playlists;return 1===o.length?(fs.log.warn("Problem encountered with the current HLS playlist. Trying again since it is the only playlist."),this.tech_.trigger("retryplaylist"),this.masterPlaylistLoader_.load(s)):(s&&(fs.log.warn("Removing all playlists from the blacklist because the last rendition is about to be blacklisted."),o.forEach(function(e){e.excludeUntil!==1/0&&delete e.excludeUntil}),this.tech_.trigger("retryplaylist")),a.excludeUntil=Date.now()+1e3*r,this.tech_.trigger("blacklistplaylist"),this.tech_.trigger({type:"usage",name:"hls-rendition-blacklisted"}),i=this.selectPlaylist(),fs.log.warn("Problem encountered with the current HLS playlist."+(n.message?" "+n.message:"")+" Switching to another playlist."),this.masterPlaylistLoader_.media(i,s))}},{key:"pauseLoading",value:function(){this.mainSegmentLoader_.pause(),this.mediaTypes_.AUDIO.activePlaylistLoader&&this.audioSegmentLoader_.pause(),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&this.subtitleSegmentLoader_.pause()}},{key:"setCurrentTime",value:function(e){var t=bl(this.tech_.buffered(),e);return this.masterPlaylistLoader_&&this.masterPlaylistLoader_.media()&&this.masterPlaylistLoader_.media().segments?t&&t.length?e:(this.mainSegmentLoader_.resetEverything(),this.mainSegmentLoader_.abort(),this.mediaTypes_.AUDIO.activePlaylistLoader&&(this.audioSegmentLoader_.resetEverything(),this.audioSegmentLoader_.abort()),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&(this.subtitleSegmentLoader_.resetEverything(),this.subtitleSegmentLoader_.abort()),void this.load()):0}},{key:"duration",value:function(){return this.masterPlaylistLoader_?this.mediaSource?this.mediaSource.duration:Qc.Playlist.duration(this.masterPlaylistLoader_.media()):0}},{key:"seekable",value:function(){return this.seekable_}},{key:"onSyncInfoUpdate_",value:function(){var e=void 0,t=void 0;if(this.masterPlaylistLoader_){var i=this.masterPlaylistLoader_.media();if(i){var n=this.syncController_.getExpiredTime(i,this.mediaSource.duration);if(null!==n&&0!==(e=Qc.Playlist.seekable(i,n)).length){if(this.mediaTypes_.AUDIO.activePlaylistLoader){if(i=this.mediaTypes_.AUDIO.activePlaylistLoader.media(),null===(n=this.syncController_.getExpiredTime(i,this.mediaSource.duration)))return;if(0===(t=Qc.Playlist.seekable(i,n)).length)return}var r=void 0,a=void 0;this.seekable_&&this.seekable_.length&&(r=this.seekable_.end(0),a=this.seekable_.start(0)),t?t.start(0)>e.end(0)||e.start(0)>t.end(0)?this.seekable_=e:this.seekable_=fs.createTimeRanges([[t.start(0)>e.start(0)?t.start(0):e.start(0),t.end(0)<e.end(0)?t.end(0):e.end(0)]]):this.seekable_=e,this.seekable_&&this.seekable_.length&&this.seekable_.end(0)===r&&this.seekable_.start(0)===a||(this.logger_("seekable updated ["+Sl(this.seekable_)+"]"),this.tech_.trigger("seekablechanged"))}}}}},{key:"updateDuration",value:function(){function e(){t.logger_("Setting duration from "+t.mediaSource.duration+" => "+n);try{t.mediaSource.duration=n}catch(e){fs.log.warn("Failed to set media source duration",e)}t.tech_.trigger("durationchange"),t.mediaSource.removeEventListener("sourceopen",e)}var t=this,i=this.mediaSource.duration,n=Qc.Playlist.duration(this.masterPlaylistLoader_.media()),r=this.tech_.buffered();0<r.length&&(n=Math.max(n,r.end(r.length-1))),i!==n&&("open"!==this.mediaSource.readyState?this.mediaSource.addEventListener("sourceopen",e):e())}},{key:"dispose",value:function(){var n=this;this.decrypter_.terminate(),this.masterPlaylistLoader_.dispose(),this.mainSegmentLoader_.dispose(),["AUDIO","SUBTITLES"].forEach(function(e){var t=n.mediaTypes_[e].groups;for(var i in t)t[i].forEach(function(e){e.playlistLoader&&e.playlistLoader.dispose()})}),this.audioSegmentLoader_.dispose(),this.subtitleSegmentLoader_.dispose()}},{key:"master",value:function(){return this.masterPlaylistLoader_.master}},{key:"media",value:function(){return this.masterPlaylistLoader_.media()||this.initialMedia_}},{key:"setupSourceBuffers_",value:function(){var e,t=this.masterPlaylistLoader_.media();if(t&&"open"===this.mediaSource.readyState){if((e=Xl(this.masterPlaylistLoader_.master,t)).length<1)return this.error="No compatible SourceBuffer configuration for the variant stream:"+t.resolvedUri,this.mediaSource.endOfStream("decode");this.configureLoaderMimeTypes_(e),this.excludeIncompatibleVariants_(t)}}},{key:"configureLoaderMimeTypes_",value:function(e){var t=1<e.length&&-1===e[0].indexOf(",")&&e[0]!==e[1]?new fs.EventTarget:null;this.mainSegmentLoader_.mimeType(e[0],t),e[1]&&this.audioSegmentLoader_.mimeType(e[1],t)}},{key:"excludeUnsupportedVariants_",value:function(){this.master().playlists.forEach(function(e){e.attributes.CODECS&&v.MediaSource&&v.MediaSource.isTypeSupported&&!v.MediaSource.isTypeSupported('video/mp4; codecs="'+function(e){return e.replace(/avc1\.(\d+)\.(\d+)/i,function(e){return zl([e])[0]})}(e.attributes.CODECS)+'"')&&(e.excludeUntil=1/0)})}},{key:"excludeIncompatibleVariants_",value:function(e){var i=2,n=null,t=void 0;e.attributes.CODECS&&(t=$l(e.attributes.CODECS),n=t.videoCodec,i=t.codecCount),this.master().playlists.forEach(function(e){var t={codecCount:2,videoCodec:null};e.attributes.CODECS&&(t=$l(e.attributes.CODECS)),t.codecCount!==i&&(e.excludeUntil=1/0),t.videoCodec!==n&&(e.excludeUntil=1/0)})}},{key:"updateAdCues_",value:function(e){var t=0,i=this.seekable();i.length&&(t=i.start(0)),function(e,t,i){var n=2<arguments.length&&void 0!==i?i:0;if(e.segments)for(var r=n,a=void 0,s=0;s<e.segments.length;s++){var o=e.segments[s];if(a=a||Bc(t,r+o.duration/2)){if("cueIn"in o){a.endTime=r,a.adEndTime=r,r+=o.duration,a=null;continue}if(r<a.endTime){r+=o.duration;continue}a.endTime+=o.duration}else if("cueOut"in o&&((a=new v.VTTCue(r,r+o.duration,o.cueOut)).adStartTime=r,a.adEndTime=r+parseFloat(o.cueOut),t.addCue(a)),"cueOutCont"in o){var u,l,c=o.cueOutCont.split("/").map(parseFloat),h=$u(c,2);u=h[0],l=h[1],(a=new v.VTTCue(r,r+o.duration,"")).adStartTime=r-u,a.adEndTime=a.adStartTime+l,t.addCue(a)}r+=o.duration}}(e,this.cueTagsTrack_,t)}},{key:"goalBufferLength",value:function(){var e=this.tech_.currentTime(),t=Pc.GOAL_BUFFER_LENGTH,i=Pc.GOAL_BUFFER_LENGTH_RATE,n=Math.max(t,Pc.MAX_GOAL_BUFFER_LENGTH);return Math.min(t+e*i,n)}},{key:"bufferLowWaterLine",value:function(){var e=this.tech_.currentTime(),t=Pc.BUFFER_LOW_WATER_LINE,i=Pc.BUFFER_LOW_WATER_LINE_RATE,n=Math.max(t,Pc.MAX_BUFFER_LOW_WATER_LINE);return Math.min(t+e*i,n)}}]),eh);function eh(e){Mu(this,eh);var t=Fu(this,(eh.__proto__||Object.getPrototypeOf(eh)).call(this)),i=e.url,n=e.handleManifestRedirects,r=e.withCredentials,a=e.tech,s=e.bandwidth,o=e.externHls,u=e.useCueTags,l=e.blacklistDuration,c=e.enableLowInitialPlaylist,h=e.sourceType,d=e.seekTo,p=e.cacheEncryptionKeys;if(!i)throw new Error("A non-empty playlist URL is required");Qc=o,t.withCredentials=r,t.tech_=a,t.hls_=a.hls,t.seekTo_=d,t.sourceType_=h,t.useCueTags_=u,t.blacklistDuration=l,t.enableLowInitialPlaylist=c,t.useCueTags_&&(t.cueTagsTrack_=t.tech_.addTextTrack("metadata","ad-cues"),t.cueTagsTrack_.inBandMetadataTrackDispatchType=""),t.requestOptions_={withCredentials:r,handleManifestRedirects:n,timeout:null},t.mediaTypes_=function(){var t={};return["AUDIO","SUBTITLES","CLOSED-CAPTIONS"].forEach(function(e){t[e]={groups:{},tracks:{},activePlaylistLoader:null,activeGroup:gc,activeTrack:gc,onGroupChanged:gc,onTrackChanged:gc}}),t}(),t.mediaSource=new fs.MediaSource,t.mediaSource.addEventListener("sourceopen",t.handleSourceOpen_.bind(t)),t.seekable_=fs.createTimeRanges(),t.hasPlayed_=function(){return!1},t.syncController_=new Hc(e),t.segmentMetadataTrack_=a.addRemoteTextTrack({kind:"metadata",label:"segment-metadata"},!1).track,t.decrypter_=new $c,t.inbandTextTracks_={};var f={hls:t.hls_,mediaSource:t.mediaSource,currentTime:t.tech_.currentTime.bind(t.tech_),seekable:function(){return t.seekable()},seeking:function(){return t.tech_.seeking()},duration:function(){return t.mediaSource.duration},hasPlayed:function(){return t.hasPlayed_()},goalBufferLength:function(){return t.goalBufferLength()},bandwidth:s,syncController:t.syncController_,decrypter:t.decrypter_,sourceType:t.sourceType_,inbandTextTracks:t.inbandTextTracks_,cacheEncryptionKeys:p};return t.masterPlaylistLoader_="dash"===t.sourceType_?new pc(i,t.hls_,t.requestOptions_):new Ku(i,t.hls_,t.requestOptions_),t.setupMasterPlaylistLoaderListeners_(),t.mainSegmentLoader_=new xc(fs.mergeOptions(f,{segmentMetadataTrack:t.segmentMetadataTrack_,loaderType:"main"}),e),t.audioSegmentLoader_=new xc(fs.mergeOptions(f,{loaderType:"audio"}),e),t.subtitleSegmentLoader_=new Mc(fs.mergeOptions(f,{loaderType:"vtt"}),e),t.setupSegmentLoaderListeners_(),Jc.forEach(function(e){t[e+"_"]=function(e){return this.audioSegmentLoader_[e]+this.mainSegmentLoader_[e]}.bind(t,e)}),t.logger_=mc("MPC"),t.masterPlaylistLoader_.load(),t}function th(e,t,i){Mu(this,th);var n=e.masterPlaylistController_,r=n[(e.options_.smoothQualityChange?"smooth":"fast")+"QualityChange_"].bind(n);if(t.attributes.RESOLUTION){var a=t.attributes.RESOLUTION;this.width=a.width,this.height=a.height}this.bandwidth=t.attributes.BANDWIDTH,this.id=i,this.enabled=function(r,a,s){return function(e){var t=r.master.playlists[a],i=al(t),n=sl(t);return"undefined"==typeof e?n:(e?delete t.disabled:t.disabled=!0,e===n||i||(s(),e?r.trigger("renditionenabled"):r.trigger("renditiondisabled")),e)}}(e.playlists,t.uri,r)}var ih=["seeking","seeked","pause","playing","error"],nh=(Nu(rh,[{key:"monitorCurrentTime_",value:function(){this.checkCurrentTime_(),this.checkCurrentTimeTimeout_&&v.clearTimeout(this.checkCurrentTimeTimeout_),this.checkCurrentTimeTimeout_=v.setTimeout(this.monitorCurrentTime_.bind(this),250)}},{key:"checkCurrentTime_",value:function(){if(this.tech_.seeking()&&this.fixesBadSeeks_())return this.consecutiveUpdates=0,void(this.lastRecordedTime=this.tech_.currentTime());if(!this.tech_.paused()&&!this.tech_.seeking()){var e=this.tech_.currentTime(),t=this.tech_.buffered();if(this.lastRecordedTime===e&&(!t.length||e+.1>=t.end(t.length-1)))return this.techWaiting_();5<=this.consecutiveUpdates&&e===this.lastRecordedTime?(this.consecutiveUpdates++,this.waiting_()):e===this.lastRecordedTime?this.consecutiveUpdates++:(this.consecutiveUpdates=0,this.lastRecordedTime=e)}}},{key:"cancelTimer_",value:function(){this.consecutiveUpdates=0,this.timer_&&(this.logger_("cancelTimer_"),clearTimeout(this.timer_)),this.timer_=null}},{key:"fixesBadSeeks_",value:function(){if(!this.tech_.seeking())return!1;var e=this.seekable(),t=this.tech_.currentTime(),i=void 0;return this.afterSeekableWindow_(e,t,this.media(),this.allowSeeksWithinUnsafeLiveWindow)&&(i=e.end(e.length-1)),this.beforeSeekableWindow_(e,t)&&(i=e.start(0)+.1),"undefined"!=typeof i&&(this.logger_("Trying to seek outside of seekable at time "+t+" with seekable range "+Sl(e)+". Seeking to "+i+"."),this.seekTo(i),!0)}},{key:"waiting_",value:function(){if(!this.techWaiting_()){var e=this.tech_.currentTime(),t=this.tech_.buffered(),i=bl(t,e);return i.length&&e+3<=i.end(0)?(this.cancelTimer_(),this.seekTo(e),this.logger_("Stopped at "+e+" while inside a buffered region ["+i.start(0)+" -> "+i.end(0)+"]. Attempting to resume playback by seeking to the current time."),void this.tech_.trigger({type:"usage",name:"hls-unknown-waiting"})):void 0}}},{key:"techWaiting_",value:function(){var e=this.seekable(),t=this.tech_.currentTime();if(this.tech_.seeking()&&this.fixesBadSeeks_())return!0;if(this.tech_.seeking()||null!==this.timer_)return!0;if(this.beforeSeekableWindow_(e,t)){var i=e.end(e.length-1);return this.logger_("Fell out of live window at time "+t+". Seeking to live point (seekable end) "+i),this.cancelTimer_(),this.seekTo(i),this.tech_.trigger({type:"usage",name:"hls-live-resync"}),!0}var n=this.tech_.buffered(),r=Tl(n,t);if(this.videoUnderflow_(r,n,t))return this.cancelTimer_(),this.seekTo(t),this.tech_.trigger({type:"usage",name:"hls-video-underflow"}),!0;if(0<r.length){var a=r.start(0)-t;return this.logger_("Stopped at "+t+", setting timer for "+a+", seeking to "+r.start(0)),this.timer_=setTimeout(this.skipTheGap_.bind(this),1e3*a,t),!0}return!1}},{key:"afterSeekableWindow_",value:function(e,t,i,n){var r=3<arguments.length&&void 0!==n&&n;if(!e.length)return!1;var a=e.end(e.length-1)+.1;return!i.endList&&r&&(a=e.end(e.length-1)+3*i.targetDuration),a<t}},{key:"beforeSeekableWindow_",value:function(e,t){return!!(e.length&&0<e.start(0)&&t<e.start(0)-.1)}},{key:"videoUnderflow_",value:function(e,t,i){if(0===e.length){var n=this.gapFromVideoUnderflow_(t,i);if(n)return this.logger_("Encountered a gap in video from "+n.start+" to "+n.end+". Seeking to current time "+i),!0}return!1}},{key:"skipTheGap_",value:function(e){var t=this.tech_.buffered(),i=this.tech_.currentTime(),n=Tl(t,i);this.cancelTimer_(),0!==n.length&&i===e&&(this.logger_("skipTheGap_:","currentTime:",i,"scheduled currentTime:",e,"nextRange start:",n.start(0)),this.seekTo(n.start(0)+1/30),this.tech_.trigger({type:"usage",name:"hls-gap-skip"}))}},{key:"gapFromVideoUnderflow_",value:function(e,t){for(var i=function(e){if(e.length<2)return fs.createTimeRanges();for(var t=[],i=1;i<e.length;i++){var n=e.end(i-1),r=e.start(i);t.push([n,r])}return fs.createTimeRanges(t)}(e),n=0;n<i.length;n++){var r=i.start(n),a=i.end(n);if(t-r<4&&2<t-r)return{start:r,end:a}}return null}}]),rh);function rh(e){var t=this;Mu(this,rh),this.tech_=e.tech,this.seekable=e.seekable,this.seekTo=e.seekTo,this.allowSeeksWithinUnsafeLiveWindow=e.allowSeeksWithinUnsafeLiveWindow,this.media=e.media,this.consecutiveUpdates=0,this.lastRecordedTime=null,this.timer_=null,this.checkCurrentTimeTimeout_=null,this.logger_=mc("PlaybackWatcher"),this.logger_("initialize");function i(){return t.monitorCurrentTime_()}function n(){return t.techWaiting_()}function r(){return t.cancelTimer_()}function a(){return t.fixesBadSeeks_()}this.tech_.on("seekablechanged",a),this.tech_.on("waiting",n),this.tech_.on(ih,r),this.tech_.on("canplay",i),this.dispose=function(){t.logger_("dispose"),t.tech_.off("seekablechanged",a),t.tech_.off("waiting",n),t.tech_.off(ih,r),t.tech_.off("canplay",i),t.checkCurrentTimeTimeout_&&v.clearTimeout(t.checkCurrentTimeTimeout_),t.cancelTimer_()}}function ah(e){!function t(i,e){var n=0,r=0,a=fs.mergeOptions(sh,e);i.ready(function(){i.trigger({type:"usage",name:"hls-error-reload-initialized"})});function s(){r&&i.currentTime(r)}function o(e){null!=e&&(r=i.duration()!==1/0&&i.currentTime()||0,i.one("loadedmetadata",s),i.src(e),i.trigger({type:"usage",name:"hls-error-reload"}),i.play())}function u(){if(Date.now()-n<1e3*a.errorInterval)i.trigger({type:"usage",name:"hls-error-reload-canceled"});else{if(a.getSource&&"function"==typeof a.getSource)return n=Date.now(),a.getSource.call(i,o);fs.log.error("ERROR: reloadSourceOnError - The option getSource must be a function!")}}function l(){i.off("loadedmetadata",s),i.off("error",u),i.off("dispose",l)}i.on("error",u),i.on("dispose",l),i.reloadSourceOnError=function(e){l(),t(i,e)}}(this,e)}var sh={errorInterval:30,getSource:function(e){return e(this.tech({IWillNotUseThisInPlugins:!0}).currentSource_)}};fs.use("*",function(t){return{setSource:function(e,t){t(null,e)},setCurrentTime:function(e){return t.vhs&&t.currentSource().src===t.vhs.source_.src&&t.vhs.setCurrentTime(e),e},play:function(){t.vhs&&t.currentSource().src===t.vhs.source_.src&&t.vhs.setCurrentTime(t.tech_.currentTime())}}});var oh={PlaylistLoader:Ku,Playlist:Ll,Decrypter:xu,AsyncStream:Pu,decrypt:Ou,utils:Dl,STANDARD_PLAYLIST_SELECTOR:function(){return function(e,t,i,n,r){var a=e.playlists.map(function(e){var t,i;return t=e.attributes.RESOLUTION&&e.attributes.RESOLUTION.width,i=e.attributes.RESOLUTION&&e.attributes.RESOLUTION.height,{bandwidth:e.attributes.BANDWIDTH||v.Number.MAX_VALUE,width:t,height:i,playlist:e}});Ec(a,function(e,t){return e.bandwidth-t.bandwidth});var s=(a=a.filter(function(e){return!Ll.isIncompatible(e.playlist)})).filter(function(e){return Ll.isEnabled(e.playlist)});s.length||(s=a.filter(function(e){return!Ll.isDisabled(e.playlist)}));var o=s.filter(function(e){return e.bandwidth*Pc.BANDWIDTH_VARIANCE<t}),u=o[o.length-1],l=o.filter(function(e){return e.bandwidth===u.bandwidth})[0];if(!1===r){var c=l||s[0]||a[0];return c?c.playlist:null}var h=o.filter(function(e){return e.width&&e.height});Ec(h,function(e,t){return e.width-t.width});var d=h.filter(function(e){return e.width===i&&e.height===n});u=d[d.length-1];var p=d.filter(function(e){return e.bandwidth===u.bandwidth})[0],f=void 0,m=void 0,g=void 0;p||(m=(f=h.filter(function(e){return e.width>i||e.height>n})).filter(function(e){return e.width===f[0].width&&e.height===f[0].height}),u=m[m.length-1],g=m.filter(function(e){return e.bandwidth===u.bandwidth})[0]);var y=g||p||l||s[0]||a[0];return y?y.playlist:null}(this.playlists.master,this.systemBandwidth,parseInt(Cc(this.tech_.el(),"width"),10),parseInt(Cc(this.tech_.el(),"height"),10),this.limitRenditionByPlayerDimensions)},INITIAL_PLAYLIST_SELECTOR:function(){var e=this.playlists.master.playlists.filter(Ll.isEnabled);return Ec(e,function(e,t){return wc(e,t)}),e.filter(function(e){return $l(e.attributes.CODECS).videoCodec})[0]||null},comparePlaylistBandwidth:wc,comparePlaylistResolution:function(e,t){var i=void 0,n=void 0;return e.attributes.RESOLUTION&&e.attributes.RESOLUTION.width&&(i=e.attributes.RESOLUTION.width),i=i||v.Number.MAX_VALUE,t.attributes.RESOLUTION&&t.attributes.RESOLUTION.width&&(n=t.attributes.RESOLUTION.width),i===(n=n||v.Number.MAX_VALUE)&&e.attributes.BANDWIDTH&&t.attributes.BANDWIDTH?e.attributes.BANDWIDTH-t.attributes.BANDWIDTH:i-n},xhr:ll()};["GOAL_BUFFER_LENGTH","MAX_GOAL_BUFFER_LENGTH","GOAL_BUFFER_LENGTH_RATE","BUFFER_LOW_WATER_LINE","MAX_BUFFER_LOW_WATER_LINE","BUFFER_LOW_WATER_LINE_RATE","BANDWIDTH_VARIANCE"].forEach(function(t){Object.defineProperty(oh,t,{get:function(){return fs.log.warn("using Hls."+t+" is UNSAFE be sure you know what you are doing"),Pc[t]},set:function(e){fs.log.warn("using Hls."+t+" is UNSAFE be sure you know what you are doing"),"number"!=typeof e||e<0?fs.log.warn("value of Hls."+t+" must be greater than or equal to 0"):Pc[t]=e}})});function uh(e){return/^(audio|video|application)\/(x-|vnd\.apple\.)?mpegurl/i.test(e)?"hls":/^application\/dash\+xml/i.test(e)?"dash":null}function lh(e,t){for(var i=t.media(),n=-1,r=0;r<e.length;r++)if(e[r].id===i.uri){n=r;break}e.selectedIndex_=n,e.trigger({selectedIndex:n,type:"change"})}var ch="videojs-vhs";oh.canPlaySource=function(){return fs.log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")};function hh(e){if("dash"===e.options_.sourceType){var t=fs.players[e.tech_.options_.playerId];if(t.eme){var i=function(e,t,i){if(!e)return e;var n={};for(var r in e)n[r]={audioContentType:'audio/mp4; codecs="'+i.attributes.CODECS+'"',videoContentType:'video/mp4; codecs="'+t.attributes.CODECS+'"'},t.contentProtection&&t.contentProtection[r]&&t.contentProtection[r].pssh&&(n[r].pssh=t.contentProtection[r].pssh),"string"==typeof e[r]&&(n[r].url=e[r]);return fs.mergeOptions(e,n)}(e.source_.keySystems,e.playlists.media(),e.masterPlaylistController_.mediaTypes_.AUDIO.activePlaylistLoader.media());i&&(t.currentSource().keySystems=i,t.eme.initializeMediaKeys&&t.eme.initializeMediaKeys())}}}function dh(){if(!window.localStorage)return null;var e=window.localStorage.getItem(ch);if(!e)return null;try{return JSON.parse(e)}catch(e){return null}}oh.supportsNativeHls=function(){var t=h.createElement("video");if(!fs.getTech("Html5").isSupported())return!1;return["application/vnd.apple.mpegurl","audio/mpegurl","audio/x-mpegurl","application/x-mpegurl","video/x-mpegurl","video/mpegurl","application/mpegurl"].some(function(e){return/maybe|probably/i.test(t.canPlayType(e))})}(),oh.supportsNativeDash=!!fs.getTech("Html5").isSupported()&&/maybe|probably/i.test(h.createElement("video").canPlayType("application/dash+xml")),oh.supportsTypeNatively=function(e){return"hls"===e?oh.supportsNativeHls:"dash"===e&&oh.supportsNativeDash},oh.isSupported=function(){return fs.log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")};var ph=fs.getComponent("Component"),fh=(ju(mh,ph),Nu(mh,[{key:"setOptions_",value:function(){var t=this;if(this.options_.withCredentials=this.options_.withCredentials||!1,this.options_.handleManifestRedirects=this.options_.handleManifestRedirects||!1,this.options_.limitRenditionByPlayerDimensions=!1!==this.options_.limitRenditionByPlayerDimensions,this.options_.smoothQualityChange=this.options_.smoothQualityChange||!1,this.options_.useBandwidthFromLocalStorage="undefined"!=typeof this.source_.useBandwidthFromLocalStorage?this.source_.useBandwidthFromLocalStorage:this.options_.useBandwidthFromLocalStorage||!1,this.options_.customTagParsers=this.options_.customTagParsers||[],this.options_.customTagMappers=this.options_.customTagMappers||[],this.options_.cacheEncryptionKeys=this.options_.cacheEncryptionKeys||!1,"number"!=typeof this.options_.blacklistDuration&&(this.options_.blacklistDuration=300),"number"!=typeof this.options_.bandwidth&&this.options_.useBandwidthFromLocalStorage){var e=dh();e&&e.bandwidth&&(this.options_.bandwidth=e.bandwidth,this.tech_.trigger({type:"usage",name:"hls-bandwidth-from-local-storage"})),e&&e.throughput&&(this.options_.throughput=e.throughput,this.tech_.trigger({type:"usage",name:"hls-throughput-from-local-storage"}))}"number"!=typeof this.options_.bandwidth&&(this.options_.bandwidth=Pc.INITIAL_BANDWIDTH),this.options_.enableLowInitialPlaylist=this.options_.enableLowInitialPlaylist&&this.options_.bandwidth===Pc.INITIAL_BANDWIDTH,["withCredentials","limitRenditionByPlayerDimensions","bandwidth","smoothQualityChange","customTagParsers","customTagMappers","handleManifestRedirects","cacheEncryptionKeys"].forEach(function(e){"undefined"!=typeof t.source_[e]&&(t.options_[e]=t.source_[e])}),this.limitRenditionByPlayerDimensions=this.options_.limitRenditionByPlayerDimensions}},{key:"src",value:function(e,t){var i=this;e&&(this.setOptions_(),this.options_.url=this.source_.src,this.options_.tech=this.tech_,this.options_.externHls=oh,this.options_.sourceType=uh(t),this.options_.seekTo=function(e){i.tech_.setCurrentTime(e),i.setCurrentTime(e)},this.masterPlaylistController_=new Zc(this.options_),this.playbackWatcher_=new nh(fs.mergeOptions(this.options_,{seekable:function(){return i.seekable()},media:function(){return i.masterPlaylistController_.media()}})),this.masterPlaylistController_.on("error",function(){fs.players[i.tech_.options_.playerId].error(i.masterPlaylistController_.error)}),this.masterPlaylistController_.selectPlaylist=this.selectPlaylist?this.selectPlaylist.bind(this):oh.STANDARD_PLAYLIST_SELECTOR.bind(this),this.masterPlaylistController_.selectInitialPlaylist=oh.INITIAL_PLAYLIST_SELECTOR.bind(this),this.playlists=this.masterPlaylistController_.masterPlaylistLoader_,this.mediaSource=this.masterPlaylistController_.mediaSource,Object.defineProperties(this,{selectPlaylist:{get:function(){return this.masterPlaylistController_.selectPlaylist},set:function(e){this.masterPlaylistController_.selectPlaylist=e.bind(this)}},throughput:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.throughput.rate},set:function(e){this.masterPlaylistController_.mainSegmentLoader_.throughput.rate=e,this.masterPlaylistController_.mainSegmentLoader_.throughput.count=1}},bandwidth:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.bandwidth},set:function(e){this.masterPlaylistController_.mainSegmentLoader_.bandwidth=e,this.masterPlaylistController_.mainSegmentLoader_.throughput={rate:0,count:0}}},systemBandwidth:{get:function(){var e=1/(this.bandwidth||1),t=void 0;return t=0<this.throughput?1/this.throughput:0,Math.floor(1/(e+t))},set:function(){fs.log.error('The "systemBandwidth" property is read-only')}}}),this.options_.bandwidth&&(this.bandwidth=this.options_.bandwidth),this.options_.throughput&&(this.throughput=this.options_.throughput),Object.defineProperties(this.stats,{bandwidth:{get:function(){return i.bandwidth||0},enumerable:!0},mediaRequests:{get:function(){return i.masterPlaylistController_.mediaRequests_()||0},enumerable:!0},mediaRequestsAborted:{get:function(){return i.masterPlaylistController_.mediaRequestsAborted_()||0},enumerable:!0},mediaRequestsTimedout:{get:function(){return i.masterPlaylistController_.mediaRequestsTimedout_()||0},enumerable:!0},mediaRequestsErrored:{get:function(){return i.masterPlaylistController_.mediaRequestsErrored_()||0},enumerable:!0},mediaTransferDuration:{get:function(){return i.masterPlaylistController_.mediaTransferDuration_()||0},enumerable:!0},mediaBytesTransferred:{get:function(){return i.masterPlaylistController_.mediaBytesTransferred_()||0},enumerable:!0},mediaSecondsLoaded:{get:function(){return i.masterPlaylistController_.mediaSecondsLoaded_()||0},enumerable:!0},buffered:{get:function(){return kl(i.tech_.buffered())},enumerable:!0},currentTime:{get:function(){return i.tech_.currentTime()},enumerable:!0},currentSource:{get:function(){return i.tech_.currentSource_},enumerable:!0},currentTech:{get:function(){return i.tech_.name_},enumerable:!0},duration:{get:function(){return i.tech_.duration()},enumerable:!0},master:{get:function(){return i.playlists.master},enumerable:!0},playerDimensions:{get:function(){return i.tech_.currentDimensions()},enumerable:!0},seekable:{get:function(){return kl(i.tech_.seekable())},enumerable:!0},timestamp:{get:function(){return Date.now()},enumerable:!0},videoPlaybackQuality:{get:function(){return i.tech_.getVideoPlaybackQuality()},enumerable:!0}}),this.tech_.one("canplay",this.masterPlaylistController_.setupFirstPlay.bind(this.masterPlaylistController_)),this.tech_.on("bandwidthupdate",function(){i.options_.useBandwidthFromLocalStorage&&function(e){if(window.localStorage){var t=dh();t=t?fs.mergeOptions(t,e):e;try{window.localStorage.setItem(ch,JSON.stringify(t))}catch(e){return}}}({bandwidth:i.bandwidth,throughput:Math.round(i.throughput)})}),this.masterPlaylistController_.on("selectedinitialmedia",function(){!function(i){var e=i.playlists;i.representations=function(){return e.master.playlists.filter(function(e){return!al(e)}).map(function(e,t){return new th(i,e,e.uri)})}}(i),hh(i)}),this.on(this.masterPlaylistController_,"progress",function(){this.tech_.trigger("progress")}),this.tech_.ready(function(){return i.setupQualityLevels_()}),this.tech_.el()&&this.tech_.src(fs.URL.createObjectURL(this.masterPlaylistController_.mediaSource)))}},{key:"setupQualityLevels_",value:function(){var e=this,t=fs.players[this.tech_.options_.playerId];t&&t.qualityLevels&&(this.qualityLevels_=t.qualityLevels(),this.masterPlaylistController_.on("selectedinitialmedia",function(){!function(t,e){e.representations().forEach(function(e){t.addQualityLevel(e)}),lh(t,e.playlists)}(e.qualityLevels_,e)}),this.playlists.on("mediachange",function(){lh(e.qualityLevels_,e.playlists)}))}},{key:"play",value:function(){this.masterPlaylistController_.play()}},{key:"setCurrentTime",value:function(e){this.masterPlaylistController_.setCurrentTime(e)}},{key:"duration",value:function(){return this.masterPlaylistController_.duration()}},{key:"seekable",value:function(){return this.masterPlaylistController_.seekable()}},{key:"dispose",value:function(){this.playbackWatcher_&&this.playbackWatcher_.dispose(),this.masterPlaylistController_&&this.masterPlaylistController_.dispose(),this.qualityLevels_&&this.qualityLevels_.dispose(),this.player_&&(delete this.player_.vhs,delete this.player_.dash,delete this.player_.hls),this.tech_&&this.tech_.hls&&delete this.tech_.hls,function e(t,i,n){null===t&&(t=Function.prototype);var r=Object.getOwnPropertyDescriptor(t,i);if(void 0===r){var a=Object.getPrototypeOf(t);return null===a?void 0:e(a,i,n)}if("value"in r)return r.value;var s=r.get;return void 0!==s?s.call(n):void 0}(mh.prototype.__proto__||Object.getPrototypeOf(mh.prototype),"dispose",this).call(this)}},{key:"convertToProgramTime",value:function(e,t){return yl({playlist:this.masterPlaylistController_.media(),time:e,callback:t})}},{key:"seekToProgramTime",value:function(e,t,i,n){var r=!(2<arguments.length&&void 0!==i)||i,a=3<arguments.length&&void 0!==n?n:2;return vl({programTime:e,playlist:this.masterPlaylistController_.media(),retryCount:a,pauseAfterSeek:r,seekTo:this.options_.seekTo,tech:this.options_.tech,callback:t})}}]),mh);function mh(e,t,i){Mu(this,mh);var n=Fu(this,(mh.__proto__||Object.getPrototypeOf(mh)).call(this,t,i.hls));if(t.options_&&t.options_.playerId){var r=fs(t.options_.playerId);r.hasOwnProperty("hls")||Object.defineProperty(r,"hls",{get:function(){return fs.log.warn("player.hls is deprecated. Use player.tech().hls instead."),t.trigger({type:"usage",name:"hls-player-access"}),n},configurable:!0}),r.vhs=n,(r.dash=n).player_=r}if(n.tech_=t,n.source_=e,n.stats={},n.setOptions_(),n.options_.overrideNative&&t.overrideNativeAudioTracks&&t.overrideNativeVideoTracks)t.overrideNativeAudioTracks(!0),t.overrideNativeVideoTracks(!0);else if(n.options_.overrideNative&&(t.featuresNativeVideoTracks||t.featuresNativeAudioTracks))throw new Error("Overriding native HLS requires emulated tracks. See https://git.io/vMpjB");return n.on(h,["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"],function(e){var t=h.fullscreenElement||h.webkitFullscreenElement||h.mozFullScreenElement||h.msFullscreenElement;t&&t.contains(n.tech_.el())&&n.masterPlaylistController_.smoothQualityChange_()}),n.on(n.tech_,"seeking",function(){0===this.tech_.currentTime()&&this.tech_.player_.loop()&&this.setCurrentTime(0)}),n.on(n.tech_,"error",function(){this.masterPlaylistController_&&this.masterPlaylistController_.pauseLoading()}),n.on(n.tech_,"play",n.play),n}var gh={name:"videojs-http-streaming",VERSION:"1.10.6",canHandleSource:function(e,t){var i=fs.mergeOptions(fs.options,1<arguments.length&&void 0!==t?t:{});return gh.canPlayType(e.type,i)},handleSource:function(e,t,i){var n=fs.mergeOptions(fs.options,2<arguments.length&&void 0!==i?i:{});return t.hls=new fh(e,t,n),t.hls.xhr=ll(),t.hls.src(e.src,e.type),t.hls},canPlayType:function(e,t){var i=fs.mergeOptions(fs.options,1<arguments.length&&void 0!==t?t:{}).hls.overrideNative,n=uh(e);return n&&(!oh.supportsTypeNatively(n)||i)?"maybe":""}};return"undefined"!=typeof fs.MediaSource&&"undefined"!=typeof fs.URL||(fs.MediaSource=sc,fs.URL=oc),sc.supportsNativeMediaSources()&&fs.getTech("Html5").registerSourceHandler(gh,0),fs.HlsHandler=fh,fs.HlsSourceHandler=gh,fs.Hls=oh,fs.use||fs.registerComponent("Hls",oh),fs.options.hls=fs.options.hls||{},fs.registerPlugin?fs.registerPlugin("reloadSourceOnError",ah):fs.plugin("reloadSourceOnError",ah),fs});