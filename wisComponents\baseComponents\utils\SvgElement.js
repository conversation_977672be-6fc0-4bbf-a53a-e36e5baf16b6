/**
 * Created by gavin on 2017/11/2.
 */
(function () {
    var arrow = "0,0 -11,-4 -11,-2 -16,-2 -16,2 -11,2 -11,4";
    var arrowSpeed = 30;

    /* wisLine
    * 根据路径画线
    * container 线所在容器
    * path 线路径
    * pathID 线id
    * width 线宽
    * color 线色
    * */
    var wisLine = function(container,path,pathID,width,color){
        var lwidth = width || 3;//默认线宽为3px
        var lcolor = color || "gray";//默认线色为灰色

        if(!container || !path || !pathID)//检查必填参数
        {
            console.error("params error");
        }

        //生成path
        container.append("path")
            .attr("id",pathID)
            .attr("d",path)
            .attr("stroke-width",lwidth)
            .attr("stroke",lcolor)
            .attr("fill","none");
    };

    /* wisAnimation
     * 根据路径生成对象动画
     * animationObject 动画对象
     * animationPath 动画路径
     * animationDuration 动画周期
     * */
    var wisAnimation = function(animationObject,animationPath,animationBegin,animationDuration){

        if(!animationObject || !animationPath || !animationDuration)//检查必填参数
        {
            console.error("params error");
        }

        animationObject.append("animateMotion")
            .attr("path",animationPath)
            .attr("begin",animationBegin+"s")
            .attr("dur",animationDuration+"s")
            .attr("rotate","auto")
            .attr("repeatCount","indefinite");
    };

    var wisArrowAnimation = function(container,path,length,color,count){
        var acount = count || 1;
        var sec = length/arrowSpeed;
        var animationObject;
        for(var i=0;i<acount;i++)
        {
            animationObject =
                container.append("polygon")
                    .attr("points",arrow)
                    .attr("fill",color || "green");
            wisAnimation(animationObject,path,i*sec/acount,sec);
        }
    };

    /* wisArrowAnimationWithPath
     * 根据路径生成箭头动画
     * container 箭头所在容器
     * path 箭头动画路径
     * color 箭头颜色
     * count 箭头数
     * */
    var wisArrowAnimationWithPath = function(container,path,color,count){
        var length = getPathLength(path);//path length
        wisArrowAnimation(container,path,length,color,count)
    };

    /* wisArrowAnimationWithPathID
     * 根据路径及path id生成箭头动画
     * container 箭头所在容器
     * path 箭头动画路径
     * pathID 箭头动画路径id
     * color 箭头颜色
     * count 箭头数
     * */
    var wisArrowAnimationWithPathID = function(container,path,pathID,color,count){
        var length = d3.select("#"+pathID).node().getTotalLength();
        wisArrowAnimation(container,path,length,color,count);
    };

    var wisElementAnimation = function(container,path,length,element,count){
        var acount = count || 1;
        var sec = length/arrowSpeed;
        var animationObject;
        for(var i=0;i<acount;i++)
        {
            animationObject = container
                .append("g");


            animationObject.attr("transform","translate(0,0)")
                .node()
                .appendChild(element.cloneNode(true));

            wisAnimation(animationObject,path,i*sec/acount,sec);
        }
    };

    /* wisElementAnimationWithPath
     * 根据路径及path生成元素对象动画
     * container 元素所在容器
     * path 元素动画路径
     * element 元素对象
     * count 元素数
     * */
    var wisElementAnimationWithPath = function(container,path,element,count){
        var length = getPathLength(path);
        wisElementAnimation(container,path,length,element,count)

    };

    /* wisElementAnimationWithPathID
     * 根据路径及path id生成元素对象动画
     * container 元素所在容器
     * path 元素动画路径
     * pathID 元素动画路径id
     * element 元素对象
     * count 元素数
     * */
    var wisElementAnimationWithPathID = function(container,path,pathID,element,count){
        var length = d3.select("#"+pathID).node().getTotalLength();
        wisElementAnimation(container,path,length,element,count)

    };

    /*getPathLength
    * 根据路径获取路径长度
    * path 路径
    * */
    var pathToLength;
    var getPathLength = function (path){
        //根据path生成一条隐藏线，用于计算path长度
        if(pathToLength)
        {
            pathToLength.attr("d",path);
        }
        else
        {
            pathToLength = d3.select("svg").append("path")
                .attr("d",path)
                .attr("stroke-width",1)
                .attr("fill","none")
                .attr("display","none");
        }

        return pathToLength.node().getTotalLength();
    };

    var wisPointsToLine = function(points) {
        var pointsArr = points.replace(/\(/g, "").replace(/\)/g, ",").replace(/\|/g, "").split(",");
        var len = Math.floor(pointsArr.length/2);
        var arr = [];
        for(var i = 0;i<len;i++)
        {
            arr[i] = [];
            arr[i][0] = Number(pointsArr[2*i]);
            arr[i][1] = Number(pointsArr[2*i+1]);
        }
        var linePath = d3.line();

        return linePath(arr);
    };

    var WisSvg = {};
    WisSvg.wisLine = wisLine;
    WisSvg.wisArrowAnimationWithPath = wisArrowAnimationWithPath;
    WisSvg.wisArrowAnimationWithPathID = wisArrowAnimationWithPathID;
    WisSvg.wisElementAnimationWithPath = wisElementAnimationWithPath;
    WisSvg.wisElementAnimationWithPathID = wisElementAnimationWithPathID;
    WisSvg.wisPointsToLine = wisPointsToLine;

    window.WisSvg = WisSvg;
})();