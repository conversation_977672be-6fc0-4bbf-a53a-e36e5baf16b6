/**
 * @description 换流站
 * <AUTHOR>
 * @date 2023-05-31
 * @class BaseExchStation
 * @extends {SVGComponentBase}
 */
/**
 * 换流站组件
 */
class BaseExchStation {
  constructor(id, code, con, workMode, option) {
    this.id = id;
    this.con = con;
    this.option = option;
    this._foldPath = WisUtil.scriptPath("BaseExchStation");
    this._draw();
  }

  _draw() {
    // this._setSize(this.option.size || 120);
    // this.con.attr("viewBox", "0 0 246 246");

    this.con
      .append("div")
      .style("width", `${this.option.size}px`)
      .style("height", `${this.option.size}px`)
      .style("background-size", "100%")
      .style("background-image", `${this._foldPath}/images/exchStation_l.svg`);
  }

  _setSize(l) {
    this.con
      //   .attr("x", this.option.x - (l - this.option.w) / 2)
      //   .attr("y", this.option.y - (l - this.option.h) / 2)
      .attr("width", l)
      .attr("height", l);
  }
}
